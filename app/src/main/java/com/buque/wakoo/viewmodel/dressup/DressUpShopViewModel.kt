package com.buque.wakoo.viewmodel.dressup

import com.buque.wakoo.bean.DressUpListResponse
import com.buque.wakoo.bean.DressUpPropItem
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.DressupApiService
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.viewmodel.ListPaginateViewModel

class DressUpShopViewModel(
    val type: Int,
) : ListPaginateViewModel<Any, Int, DressUpPropItem, DressUpListResponse>() {
    private val operationRepo = DressupOperationRepo()

    val diamondState = operationRepo.diamondBalance
    val integralState = operationRepo.integralBalance

    override fun getFirstPageKey(dataKey: Any): Int = 1

    override fun getDistinctSelector(): (DressUpPropItem) -> String =
        {
            it.id.toString()
        }

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: DressUpListResponse,
        distinctAddSize: Int,
    ): Int? {
//        if (response.list.isNotEmpty()) {
//            return pageKey + 1
//        } else {
//            return null
//        }
        return null
    }

    override fun getDataListFromResponse(
        dataKey: Any,
        response: DressUpListResponse,
    ): List<DressUpPropItem> {
        operationRepo.updateBalance(diamond = response.diamond, integral = response.banlance)
        return response.list
    }

    override suspend fun getData(
        reqKey: Any,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<DressUpListResponse> = DressupApiService.instance.getPropList(type, pageKey, pageSize)

    override fun getNextPageKey(
        cState: CState<List<DressUpPropItem>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = (pageKey ?: 0) + 1

    /**
     * @param payway 0钻石 1金币
     */
    suspend fun purchase(
        item: DressUpPropItem,
        days: Int,
        payway: Int,
    ) = operationRepo.purchaseDressUpPropItem(item.propInfo.id.toString(), item.propInfo.propType, days, payway)
}
