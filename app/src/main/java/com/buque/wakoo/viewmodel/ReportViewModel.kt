package com.buque.wakoo.viewmodel

import androidx.core.net.toUri
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.MediaInfo
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.CommonApiService
import com.buque.wakoo.network.executeApiCall
import com.buque.wakoo.ui.screens.settings.ReportReason
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ReportViewModel : BaseViewModel() {
    private val apiService
        get() = CommonApiService.instance

    private val cosTransferHelper = CosTransferHelper()

    suspend fun report(
        type: Int,
        targetId: String,
        reason: ReportReason,
        desc: String,
        uris: List<MediaItem>,
    ): Boolean {
        return withContext(Dispatchers.IO) {
            val urls: List<MediaInfo>? =
                if (uris.isNotEmpty()) {
                    uris.map {
                        val uri = it.uriString.toUri()
                        val path =
                            UploadUtils.generateOSSPath(WakooApplication.instance, uri, UploadUtils.DEFAULT_REPORT_PATH)
                        val currentTaskWrapper =
                            cosTransferHelper.upload(
                                cosPath = path,
                                uri = uri,
                            )

                        // 挂起等待上传结果
                        when (val result = currentTaskWrapper.await()) {
                            is TransferResult.Success -> {
                                return@map MediaInfo(result.url, it.width, it.height, it.sizeBytes)
                            }

                            is TransferResult.Failure -> {
                                showToast("${"图片上传失败".localized}, ${result.exception}")
                                return@withContext false
                            }
                        }
                    }
                } else {
                    null
                }

            executeApiCall {
                apiService
                    .report(
                        buildMap {
                            put("target_type", type.toString())
                            put("target_id", targetId)
                            put("accusation_type", reason.value.toString())
                            if (desc.isNotBlank()) {
                                put("note", desc)
                            }
                            if (!urls.isNullOrEmpty()) {
                                put("media_list", AppJson.encodeToString(urls))
                            }
                        },
                    )
            }.isSuccess
        }
    }
}
