package com.buque.wakoo.viewmodel.liveroom

import com.buque.wakoo.bean.UserPages
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserListResponse
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.viewmodel.ListPaginateViewModel

class RoomOnlineListViewModel : ListPaginateViewModel<String, Int, UserPages, UserListResponse>() {
    private val apiService
        get() = VoiceRoomApiService.instance

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<UserPages>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int =
        cState.requireData
            .last()
            .id
            .toInt()

    override suspend fun getData(
        reqKey: String,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<UserListResponse> = apiService.getVoiceRoomOnlineList(reqKey, pageKey)

    override fun getDataListFromResponse(
        dataKey: Any,
        response: UserListResponse,
    ): List<UserPages> =
        response.list.map {
            UserPages.fromResponse(it)
        }

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: UserListResponse,
        distinctAddSize: Int,
    ): Int? =
        if (response.hasNext) {
            response.list.lastOrNull()?.relationId
        } else {
            null
        }

    override fun getDistinctSelector(): (UserPages) -> String =
        {
            it.id
        }
}
