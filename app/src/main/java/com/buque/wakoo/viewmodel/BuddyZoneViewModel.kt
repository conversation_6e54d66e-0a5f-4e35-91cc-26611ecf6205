package com.buque.wakoo.viewmodel

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.user.CpRelationInfo
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.ui.widget.state.CState
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class BuddyZoneViewModel : BaseViewModel() {
    val cpInfoFlow: StateFlow<CState<CpRelationInfo>> =
        AccountManager.userStateFlow
            .map {
                if (it?.cpRelationInfo == null) {
                    CState.Loading()
                } else {
                    CState.Success(it.cpRelationInfo)
                }
            }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(stopTimeoutMillis = 5000), CState.Idle)

    fun refresh() {
        UserManager.refreshSelfUserInfo()
    }
}
