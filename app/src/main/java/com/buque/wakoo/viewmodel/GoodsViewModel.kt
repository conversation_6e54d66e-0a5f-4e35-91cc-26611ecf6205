package com.buque.wakoo.viewmodel

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.consts.Pay
import com.buque.wakoo.core.pay.AppPayCoreKit
import com.buque.wakoo.core.pay.GoogleBillingManager
import com.buque.wakoo.core.pay.IPurchasesUpdatedListener
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.network.api.bean.ChargeDataEntity
import com.buque.wakoo.network.api.bean.VipInfo
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.utils.AsyncValue
import com.buque.wakoo.utils.toAsyncData
import com.buque.wakoo.utils.toAsyncError
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive

sealed class GoodsEvent {
    data object Fetch : GoodsEvent()

    data object FetchVipConfig : GoodsEvent()

    data class DrawGold(val bonusIndex: Int) : GoodsEvent()
}

open class GoodsViewModel : ViewModel(), IPurchasesUpdatedListener {
    companion object {
        /**
         * 打开了bi shang c2c
         */
        const val ACTION_OPEN_AGENT_CHAT = "action_open_agent_chat"
    }

    private val repo = GlobalRepository.walletRepo

    private val _loading: MutableState<Boolean> = mutableStateOf(false)
    val loading: State<Boolean> = _loading

    private var isLoading: Boolean
        get() = _loading.value
        set(value) {
            _loading.value = value
        }

    private val _actionFlow = MutableSharedFlow<String>()
    val actionFlow = _actionFlow.asSharedFlow()


    private val _chargeItemsFlow =
        MutableStateFlow<AsyncValue<ChargeDataEntity>>(AsyncValue.Loading())
    val rechargeGoodsState: StateFlow<AsyncValue<ChargeDataEntity>> = _chargeItemsFlow


    private val _vipInfoFlow = MutableStateFlow<AsyncValue<VipInfo>>(AsyncValue.Loading())
    val vipConfigState: StateFlow<AsyncValue<VipInfo>> = _vipInfoFlow


    private fun handleEvent(event: GoodsEvent) {
        when (event) {
            is GoodsEvent.Fetch -> {
                fetchRechargeList()
            }


            is GoodsEvent.FetchVipConfig -> {
                fetchActivateVipConfig()
            }

            is GoodsEvent.DrawGold -> {
                viewModelScope.launch {
                    isLoading = true
                    repo.getGold(event.bonusIndex).onSuccess {
                        it["hint"]?.jsonPrimitive?.contentOrNull?.also { hint -> showToast(hint) }
                        sendEvent(GoodsEvent.FetchVipConfig)
                    }.onFailure {
                        it.toast()
                    }
                    isLoading = false
                }
            }

            else -> {}
        }
    }

    fun sendEvent(event: GoodsEvent) {
        handleEvent(event)
    }


    override fun onPurchasesUpdated(completed: Boolean) {
        viewModelScope.launch {
            if (completed) {
                isLoading = false
                GoogleBillingManager.clearListener()
            } else {
                isLoading = true
            }
        }
    }

    override fun onPurchasesMessage(message: String) {
        showToast(message)
    }

    override fun onCleared() {
        super.onCleared()
        GoogleBillingManager.clearListener()
    }

    private fun fetchRechargeList() {
        viewModelScope.launch {
            repo.fetchRechargeList().onSuccess { data ->
                val chargeItems = data.chargeItems
//                val containers = chargeItems.filter { it.fkChannel == Pay.CALL_TYPE_GOOGLE_SDK }
//                val items = buildList {
//                    containers.forEach { c ->
//                        addAll(c.chargeItems)
//                    }
//                }
                GoogleBillingManager.fetchProductDetails(chargeItems)
                _chargeItemsFlow.value = data.toAsyncData()
            }.onFailure {
                _chargeItemsFlow.value = it.toAsyncError()
            }.toastWhenError()
        }
    }

    private fun fetchActivateVipConfig() {
        viewModelScope.launch {
            repo.getActivateVipConfig()
                .onSuccess { vipInfo ->
                    val options = vipInfo.activateOption
                    GoogleBillingManager.fetchProductDetails(options)
                    _vipInfoFlow.value = vipInfo.toAsyncData()
                }.onFailure {
                    _vipInfoFlow.value = it.toAsyncError()
                }.toastWhenError()
        }
    }

}