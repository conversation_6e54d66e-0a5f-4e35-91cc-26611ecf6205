package com.buque.wakoo.viewmodel.chatgroup

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.utils.CacheKeys
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.putCache
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive

class ChatGroupSettingsViewModel(
    chatGroupId: String,
    chatGroupBean: ChatGroupBean? = null,
) : ChatGroupDetailViewModel(chatGroupId), IMCompatListener {
    init {
        val group = chatGroupBean
        if (group != null) {
            rawState.value = CState.Success(group)
        }
        IMCompatCore.addIMListener(this)
    }

    private val _eventFlow = MutableSharedFlow<String>()
    val eventFlow = _eventFlow.asSharedFlow()

    private fun updateCache(chatGroupBean: ChatGroupBean) {
        putCache(CacheKeys.SELF_CHAT_GROUP, currentUserKV, chatGroupBean)
    }

    suspend fun update(
        name: String? = null,
        avatar: String? = null,
        bulletin: String? = null,
        enableDontDisturb: Boolean? = null,
    ) = repo
        .updateGroupInfo(groupId, name, bulletin, avatar, enableDontDisturb)
        .onSuccess {
            if (name != null) {
                updateState {
                    it.copy(name = name).also { ng -> updateCache(ng) }
                }
            }
            if (avatar != null) {
                updateState {
                    it.copy(avatarUrl = avatar).also { ng -> updateCache(ng) }
                }
            }
            if (bulletin != null) {
                updateState {
                    it.copy(bulletin = bulletin).also { ng -> updateCache(ng) }
                }
            }
            if (enableDontDisturb != null) {
                updateState {
                    it.copy(iEnableDontDisturb = enableDontDisturb).also { ng -> updateCache(ng) }
                }
            }
        }.toastWhenError()

    private fun updateState(action: (ChatGroupBean) -> ChatGroupBean) {
        rawState.value.dataOrNull?.also {
            rawState.value = CState.Success(action(it))
        }
    }

    suspend fun exit(iAmOwner: Boolean = false) =
        // 群主->解散，其他->退出
        repo
            .let {
                if (iAmOwner) {
                    it.destroyGroup(groupId)
                } else {
                    it.exitGroup(groupId)
                }
            }.onSuccess {
                putCache(CacheKeys.SELF_CHAT_GROUP, currentUserKV, null)
                AccountManager.updateSelfOtherInfo {
                    it.copy(myGroup = null)
                }
            }


    override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
        val cmd = message.cmd
        LogUtils.d("==custom event== ${message.cmd}\n${message.customJson}")
        when (cmd) {
            IMEvent.TRIBE_DESTROYED -> _eventFlow.tryEmit(cmd)
            IMEvent.MEMBER_QUIT -> {
                message.user?.also {
                    if (it.sIsSelf) {
                        _eventFlow.tryEmit(cmd)
                    } else {
                        refreshState()
                    }
                }
            }

            IMEvent.MEMBER_KICKED_OUT -> {
                message.customJson.parseValue<UserResponse>("kicked_user")?.also {
                    if (it.id == SelfUser?.id) {
                        viewModelScope.launch {
                            _eventFlow.emit(cmd)
                        }
                    } else {
                        refreshState()
                    }
                }

            }

            IMEvent.MEMBER_APPLY_CNT_CHANGE -> {
                message.customJson.getOrNull("member_apply_cnt")?.jsonPrimitive?.intOrNull?.also { cnt ->
                    updateState {
                        it.copy(memberApplyWaitCnt = cnt)
                    }
                }
            }

            IMEvent.NAME_UPDATE, IMEvent.BULLETIN_UPDATE -> refreshState()
        }
    }

    override fun onCleared() {
        super.onCleared()
        IMCompatCore.removeIMListener(this)
    }

    suspend fun applyJoin() = repo.applyJoinGroup(groupId).onSuccess {
        updateState {
            it.copy(relationWithMe = 5)
        }
    }


}
