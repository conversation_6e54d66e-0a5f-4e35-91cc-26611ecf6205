package com.buque.wakoo.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.MyLiveRoomInfo
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.CommonApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject

data class SearchResultBean<T>(
    val name: String,
    val avatar: String,
    // 0 用户 1语音房 2群组
    val type: Int,
    val id: String,
    val origin: T,
)

class SearchViewModel : ViewModel() {
    companion object {
        private val SEARCH_INTERVAL = 1000L
    }

    private var debounceJob: Job? = null

    private var inputQuery = ""

    private val _searchResultsState = MutableStateFlow(listOf<SearchResultBean<*>>())
    val searchResultsState = _searchResultsState.asStateFlow()

    fun updateInput(text: String) {
        inputQuery = text
        debounceJob?.cancel()
        debounceJob =
            viewModelScope.launch {
//                delay(SEARCH_INTERVAL)
                // 这里调用接口请求
                searchUser(inputQuery)
                    .onSuccess {
                        updateSearchResult(it)
                    }.toastWhenError()
            }
    }

    private fun updateSearchResult(jobj: JsonObject) {
        val results = mutableListOf<SearchResultBean<*>>()
        val arr = jobj.get("search_result")?.jsonArray
        if (arr.isNullOrEmpty()) {
            showToast("搜索的id不存在".localized)
        } else {
            arr.forEach {
                val curObj = it.jsonObject
                when (curObj.getStringOrNull("type")) {
                    "user" -> {
                        curObj["user"]?.let {
                            (

                                try {
                                    AppJson.decodeFromJsonElement<BasicUser>(it)
                                } catch (e: Exception) {
                                    null
                                }
                            )?.apply {
                                results.add(SearchResultBean(this.name, this.avatar, 0, this.publishId, this))
                            }
                        }
                    }

                    "room" -> {
                        curObj.get("room")?.let {
                            (
                                try {
                                    AppJson.decodeFromJsonElement<MyLiveRoomInfo>(it)
                                } catch (e: Exception) {
                                    null
                                }
                            )?.apply {
                                results.add(SearchResultBean(this.roomName, this.avatarUrl, 1, this.publicId, this))
                            }
                        }
                    }

                    "tribe" -> {
                        curObj.get("tribe")?.let {
                            (
                                try {
                                    AppJson.decodeFromJsonElement<ChatGroupBean>(it)
                                } catch (e: Exception) {
                                    null
                                }
                            )?.apply {
                                results.add(SearchResultBean(this.name, this.avatarUrl, 2, this.publicId, this))
                            }
                        }
                    }

                    else -> {}
                }
            }
        }

        viewModelScope.launch {
            _searchResultsState.emit(results)
        }
    }

    // 伪接口方法，实际请替换为你的网络请求
    private suspend fun searchUser(query: String) =
        executeApiCallExpectingData {
            CommonApiService.instance.searchAllById(query)
        }
}
