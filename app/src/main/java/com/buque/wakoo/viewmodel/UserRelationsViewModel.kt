package com.buque.wakoo.viewmodel

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.UserRelations
import com.buque.wakoo.navigation.RelationsKey
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.UserListResponse
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.handleOptimisticRequest
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class UserRelationsViewModel : TabListPaginateViewModel<String, Int, RelationsKey, UserRelations, UserListResponse>() {
    private val userRepository = UserRepository()

    override fun getFirstPageKey(tabKey: RelationsKey): Int = 0

    override fun getNextPageKey(
        cState: CState<List<UserRelations>>,
        tabKey: RelationsKey,
        pageKey: Int?,
    ): Int =
        cState.requireData
            .last()
            .pageKey

    override suspend fun getData(
        reqKey: String,
        tabKey: RelationsKey,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<UserListResponse> =
        when (tabKey) {
            RelationsKey.Following -> UserApiService.instance.getUserFollowingList("", pageKey)
            RelationsKey.Followers -> UserApiService.instance.getUserFollowerList("", pageKey)
            RelationsKey.Friends -> UserApiService.instance.getFriendList(pageKey)
            RelationsKey.BlackList -> UserApiService.instance.getBlackList("", pageKey)
            else -> {
                ApiResponse(0, null, data = UserListResponse(false, listOf()))
            }
        }

    override fun getDataListFromResponse(
        tabKey: RelationsKey,
        response: UserListResponse,
    ): List<UserRelations> =
        response.list.map {
            UserRelations.fromResponse(
                it,
                if (tabKey is RelationsKey.Following) true else it.iFollowBack,
            )
        }

    override fun getHasNextPageKeyFromResponse(
        tabKey: RelationsKey,
        pageKey: Int,
        response: UserListResponse,
        distinctAddSize: Int,
    ): Int? =
        if (response.hasNext) {
            response.list.lastOrNull()?.relationId
        } else {
            null
        }

    override fun getDistinctSelector(): (UserRelations) -> String =
        {
            it.id
        }

    fun toggleFollowState(
        userId: String,
        tab: RelationsKey,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                handleOptimisticRequest(
                    itemId = userId,
                    items = state.data,
                    findItemIdFromData = { it.id },
                    getProperty = { it.hasRelations },
                    isRequesting = { it.relationRequesting },
                    copy = { item, isFollow, isRequesting ->
                        item.copy(hasRelations = isFollow, relationRequesting = isRequesting)
                    },
                    apiCall = { isFollow ->
                        userRepository
                            .updateFollowState(
                                userId,
                                isFollow,
                            ).isSuccess
                    },
                )
            }
        }
    }

    fun cancelBlackState(userId: String) {
        viewModelScope.launch {
            val state = tryGetState(RelationsKey.BlackList)
            if (state is CState.Success) {
                val index = state.data.indexOfFirst { it.id == userId }
                if (index != -1) {
                    state.data.removeAt(index)
                    userRepository.updateBlackState(userId, false).onFailure {
                        withContext(NonCancellable) {
                            delay(2000)
                            userRepository.updateBlackState(userId, false)
                        }
                    }
                }
            }
        }
    }
}
