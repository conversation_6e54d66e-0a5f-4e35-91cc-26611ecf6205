package com.buque.wakoo.viewmodel.liveroom

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.UserPages
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.viewmodel.ListPaginateViewModel
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject

class RoomReqMicListViewModel : ListPaginateViewModel<String, Int, UserPages, JsonObject>() {
    private val apiService
        get() = VoiceRoomApiService.instance

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<UserPages>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = 0

    override suspend fun getData(
        reqKey: String,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<JsonObject> = apiService.getVoiceRoomMicPendingList(reqKey)

    override fun getDataListFromResponse(
        dataKey: Any,
        response: JsonObject,
    ): List<UserPages> =
        response.parseValue<JsonArray>("applies")?.mapNotNull {
            it.jsonObject.parseValue<BasicUser>("apply_user")?.let { user ->
                UserPages(user, 0)
            }
        } ?: emptyList()

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: JsonObject,
        distinctAddSize: Int,
    ): Int? = null

    override fun getDistinctSelector(): (UserPages) -> String =
        {
            it.id
        }

    fun deleteItem(userId: String) {
        viewModelScope.launch {
            val state = getCState(Unit)
            if (state is CState.Success) {
                val index = state.data.indexOfFirst { it.id == userId }
                if (index > -1) {
                    state.data.removeAt(index)
                }
            }
        }
    }
}
