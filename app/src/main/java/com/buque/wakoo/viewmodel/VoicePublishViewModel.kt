package com.buque.wakoo.viewmodel

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateSetOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AudioRecordManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.network.api.bean.PublishVoiceRequest
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.network.onBusinessFailure
import com.buque.wakoo.repository.VoiceRepository
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SingleActionDialog
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.preload.PublishVoicePreload
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadCallbacks
import com.buque.wakoo.utils.upload.UploadUtils

@SuppressLint("StaticFieldLeak")
class VoicePublishViewModel(
    val applicationContext: Context,
) : BaseViewModel() {
    private val voiceRepository = VoiceRepository()

    class Factory(
        private val context: Context,
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = VoicePublishViewModel(context.applicationContext) as T
    }

    private val cosTransferHelper = CosTransferHelper()

    val audioRecordManager: AudioRecordManager
        get() = AudioRecordManager.singletonInstance

    val configStateFlow = PublishVoicePreload.getPublishConfigFlow(true)

    var selectedId by mutableIntStateOf(-1)

    val selectedTagIds = mutableStateSetOf<Int>()

    val selectedItem by derivedStateOf {
        configStateFlow.value.requireData.background.run {
            find {
                it.id == selectedId
            } ?: first().also {
                selectedId = it.id
            }
        }
    }

    fun loadPublishConfig() {
        PublishVoicePreload.refreshPublishConfig(false)
    }

    fun toggleSelectedByTag(tag: VoiceTag) {
        if (selectedTagIds.contains(tag.id)) {
            selectedTagIds.remove(tag.id)
        } else {
            selectedTagIds.add(tag.id)
        }
    }

    fun startPreview(
        content: String,
        isPublish: Boolean,
        filePath: String,
        duration: Int,
    ) = VoiceCardItem(
        user = SelfUser!!.user,
        title = content,
        tags =
            configStateFlow.value.requireData.tags
                .filter { selectedTagIds.contains(it.id) },
        visibility = if (isPublish) 1 else 2,
        duration = duration,
        background = selectedItem.resource,
    ).also {
        it.filePath = filePath
    }

    suspend fun publish(
        item: VoiceCardItem,
        dialogController: DialogController,
    ): Boolean {
        // 定义回调
        val callbacks =
            UploadCallbacks(
                onProgress = { progress, max ->
                    val percentage = if (max > 0) (progress * 100 / max).toInt() else 0
                },
                onStateChanged = { state ->
                },
                onInitUploadId = { uploadId ->
                },
            )

        val path = UploadUtils.generateOSSPath(item.filePath, UploadUtils.DEFAULT_VOICE_PATH)
        val currentTaskWrapper =
            cosTransferHelper.upload(
                cosPath = path,
                filePath = item.filePath,
                callbacks = callbacks,
            )

        val uploadFileUrl = item.uploadFileUrl
        if (uploadFileUrl?.first == item.filePath) {
            return publish(uploadFileUrl.second, item, dialogController)
        }

        // 挂起等待上传结果
        return when (val result = currentTaskWrapper.await()) {
            is TransferResult.Success -> {
                item.uploadFileUrl = item.filePath to result.url
                publish(result.url, item, dialogController)
            }

            is TransferResult.Failure -> {
                showToast("上传失败".localized)
                false
            }
        }
    }

    private suspend fun publish(
        url: String,
        item: VoiceCardItem,
        dialogController: DialogController,
    ): Boolean =
        voiceRepository
            .publishVoice(
                PublishVoiceRequest(
                    resource = url,
                    duration = item.duration,
                    title = item.title,
                    tags = item.tags.joinToString(",") { it.id.toString() },
                    visibility = item.visibility,
                    backgroundId = selectedItem.id,
                ),
            ).onBusinessFailure { e ->
                if (e.businessCode == 1) {
                    dialogController.easyPost {
                        // 充值钻石弹窗
                        SingleActionDialog(
                            content = e.businessMessage.orEmpty(),
                            buttonConfig = DialogButtonStyles.Primary.copy(text = "充值钻石".localized),
                            onButtonClick = {
                                dismiss()
                                LocalAppNavController.useRoot?.push(Route.Recharge)
                            },
                        )
                    }
                } else if (e.businessCode == 2) {
                    dialogController.easyPost {
                        // VIP弹窗
                        SingleActionDialog(
                            content = e.businessMessage.orEmpty(),
                            buttonConfig = DialogButtonStyles.VIP.copy(text = "开通会员".localized),
                            onButtonClick = {
                                dismiss()
                                LocalAppNavController.useRoot?.push(Route.Member)
                            },
                        )
                    }
                }
            }.isSuccess
}
