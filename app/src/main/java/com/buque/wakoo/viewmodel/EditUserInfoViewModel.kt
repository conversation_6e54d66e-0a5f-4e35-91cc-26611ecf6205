package com.buque.wakoo.viewmodel

import android.content.Context
import android.net.Uri
import androidx.compose.runtime.Stable
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.CompactJson
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.EnumEntity
import com.buque.wakoo.bean.MediaInfo
import com.buque.wakoo.bean.ProfileEnum
import com.buque.wakoo.bean.ProfileEnums
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadUtils
import com.buque.wakoo.bean.CityData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.decodeFromStream

@Stable
enum class ChangeableProperty(
    val postKey: String,
) {
    AVATAR("avatar_url"),
    NICKNAME("nickname"),
    BIRTHDAY("birthday"),
    INTRO("short_intro"),
    GENDER("gender"),
    LIVE_ADDRESS("city_code"),
    BORN_ADDRESS("birth_city_code"),
    ACADEMY("educational_history"),
    JOB("job"),
    HEIGHT("height"),
    BODY_SIZE("body_type"),
    MARRY_HISTORY("marital_history"),
    SMOKE("tobacco"),
    DATE_INTENT("dating_hope"),
    NONE(""),
}

class EditUserInfoViewModel : BaseViewModel() {
    private var profileEnums: ProfileEnums? = null

    init {
        requestSelectorOptions()
    }

    private fun requestSelectorOptions() {
        if (SelfUser?.isJP == true && profileEnums == null) {
            viewModelScope.launch {
                launch {
                    addressOptions = loadJapanCityData(WakooApplication.instance)
                }
                executeApiCallExpectingData {
                    UserApiService.instance.getJPProfileOptions()
                }.onSuccess {
                    profileEnums = it
                }
            }
        }
    }

    private val heightOptions: ProfileEnum by lazy {
        buildList {
            for (i in 140 until 201) {
                add(EnumEntity(i.toString(), i.toString()))
            }
        }
    }

    fun getSelectorOptions(property: ChangeableProperty): ProfileEnum =
        when (property) {
            ChangeableProperty.HEIGHT -> heightOptions
            ChangeableProperty.ACADEMY -> profileEnums?.educationalHistory ?: emptyList()
            ChangeableProperty.JOB -> profileEnums?.job ?: emptyList()
            ChangeableProperty.BODY_SIZE -> profileEnums?.bodyType ?: emptyList()
            else -> emptyList()
        }

    //region 日区地址选择相关

    suspend fun loadJapanCityData(context: Context) =
        try {
            withContext(Dispatchers.IO) {
                context.assets.open("jp_cities.json").use {
                    AppJson.decodeFromStream<List<CityData>>(it)
                }
            }.sortedBy {
                it.id
            }
        } catch (e: Exception) {
            emptyList()
        }

    private var addressOptions: List<CityData> = listOf()

    fun getAddressSelectorOptions(): List<CityData> = addressOptions

    fun getAddressIndexs(entity: EnumEntity): List<Int> {
        val arr = entity.name.split("·")
        val cityName = arr.getOrNull(0).orEmpty()
        val cityIndex = addressOptions.indexOfFirst { it.name == cityName }.takeIf { it != -1 } ?: 0

        val areaName = arr.getOrNull(1).orEmpty()
        val areaList = addressOptions[cityIndex].city
        val areaIndex = areaList.indexOfFirst { it.areaName == areaName }.takeIf { it != -1 } ?: 0

        return listOf(cityIndex, areaIndex)
    }

    suspend fun updateAddress(
        prop: ChangeableProperty,
        selectedIndexs: List<Int>,
    ) {
        try {
            val province = addressOptions[selectedIndexs.firstOrNull() ?: 0]

            val params =
                if (prop == ChangeableProperty.LIVE_ADDRESS) {
                    val area = province.city[selectedIndexs.lastOrNull() ?: 0]
                    EnumEntity(
                        area.areaCode,
                        "${province.name}·${area.areaName}",
                    )
                } else {
                    EnumEntity(
                        province.id,
                        province.name,
                    )
                }

            updateUserInfo(prop, params)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //endregion

    private val cosTransferHelper = CosTransferHelper()

    private val userRepository = UserRepository()

    suspend fun updateUserInfo(
        prop: ChangeableProperty,
        changeValue: Any,
    ) {
        val value =
            when (changeValue) {
                is EnumEntity -> changeValue.code
                is String -> changeValue
                is Int -> changeValue.toString()
                else -> ""
            }
        val entity =
            when (changeValue) {
                is EnumEntity -> changeValue
                else -> EnumEntity(name = value)
            }
        userRepository.updateUserInfo(mapOf(prop.postKey to value)).onSuccess { _ ->
            when (prop) {
                ChangeableProperty.NICKNAME -> {
                    AccountManager.updateSelfBasicInfo {
                        it.copy(name = value)
                    }
                }

                ChangeableProperty.BIRTHDAY -> {
                    AccountManager.updateSelfBasicInfo {
                        it.copy(birthday = value)
                    }
                }

                ChangeableProperty.INTRO -> {
                    AccountManager.updateSelfBasicInfo {
                        it.copy(intro = value)
                    }
                }

                ChangeableProperty.GENDER -> {
                    AccountManager.updateSelfBasicInfo {
                        it.copy(gender = value.toInt())
                    }
                }

                ChangeableProperty.HEIGHT -> {
                    AccountManager.updateSelfBasicInfo {
                        it.copy(height = value.toInt())
                    }
                }

                ChangeableProperty.LIVE_ADDRESS -> {
                    AccountManager.updateSelfSocialInfo {
                        it.copy(
                            nativeProfile =
                                it.nativeProfile.copy(
                                    cityCode = entity,
                                ),
                        )
                    }
                }

                ChangeableProperty.BORN_ADDRESS -> {
                    AccountManager.updateSelfSocialInfo {
                        it.copy(
                            nativeProfile =
                                it.nativeProfile.copy(
                                    birthCityCode = entity,
                                ),
                        )
                    }
                }

                ChangeableProperty.ACADEMY -> {
                    AccountManager.updateSelfSocialInfo {
                        it.copy(
                            nativeProfile =
                                it.nativeProfile.copy(
                                    educationalHistory = entity,
                                ),
                        )
                    }
                }

                ChangeableProperty.JOB -> {
                    AccountManager.updateSelfSocialInfo {
                        it.copy(
                            nativeProfile =
                                it.nativeProfile.copy(
                                    job = entity,
                                ),
                        )
                    }
                }

                ChangeableProperty.BODY_SIZE -> {
                    AccountManager.updateSelfSocialInfo {
                        it.copy(
                            nativeProfile =
                                it.nativeProfile.copy(
                                    bodyType = entity,
                                ),
                        )
                    }
                }

//                ChangeableProperty.MARRY_HISTORY -> TODO()
//                ChangeableProperty.SMOKE -> TODO()
//                ChangeableProperty.DATE_INTENT -> TODO()
//                ChangeableProperty.NONE -> TODO()
                else -> {
                }
            }
        }
    }

    suspend fun updateAvatar(uri: Uri) {
        // 定义回调

        val path = UploadUtils.generateOSSPath(WakooApplication.instance, uri, UploadUtils.DEFAULT_AVATAR_PATH)
        val currentTaskWrapper =
            cosTransferHelper.upload(
                cosPath = path,
                uri = uri,
//                callbacks = callbacks
            )

        // 挂起等待上传结果
        when (val result = currentTaskWrapper.await()) {
            is TransferResult.Success -> {
                userRepository.updateUserInfo(mapOf("avatar_url" to result.url)).onSuccess {
                    AccountManager.updateSelfBasicInfo {
                        it.copy(avatar = result.url)
                    }
                    showToast("更新成功".localized)
                }
            }

            is TransferResult.Failure -> {
                showToast("头像上传失败".localized)
            }
        }
    }

    suspend fun updateAlbum(items: List<MediaItem>) {
        // 1. 先取现在的
        val list = SelfUser?.albums ?: emptyList()

        val newList =
            items.mapNotNull { item ->
                val uri = item.uriString.toUri()
                val path = UploadUtils.generateOSSPath(WakooApplication.instance, uri, UploadUtils.DEFAULT_ALBUM_PATH)
                when (val ret = cosTransferHelper.upload(cosPath = path, uri = uri).await()) {
                    is TransferResult.Success -> {
                        MediaInfo(ret.url, item.width, item.height, item.sizeBytes)
                    }

                    is TransferResult.Failure -> {
                        null
                    }
                }
            }

        val newAlbumList =
            buildList {
                addAll(list)
                addAll(newList)
            }
        val newStr = CompactJson.encodeToString(newAlbumList)

        userRepository.updateUserInfo(mapOf("album" to newStr)).onSuccess {
            AccountManager.updateSelfSocialInfo {
                it.copy(albums = newAlbumList)
            }
        }
    }

    suspend fun deletePictureFromAlbum(pic: MediaInfo) {
        val newList = SelfUser?.albums?.filter { it.mediaUrl != pic.mediaUrl }
        if (newList != null) {
            val param = CompactJson.encodeToString(newList)
            userRepository.updateUserInfo(mapOf("album" to param)).onSuccess {
                AccountManager.updateSelfSocialInfo {
                    it.copy(albums = newList)
                }
            }
        }
    }
}
