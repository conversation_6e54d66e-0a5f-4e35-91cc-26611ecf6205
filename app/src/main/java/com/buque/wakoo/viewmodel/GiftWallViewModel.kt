package com.buque.wakoo.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.GiftWall
import com.buque.wakoo.bean.GiftWallSummaryBean
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.network.api.service.GiftApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.gift.BottomRadiusItem
import com.buque.wakoo.ui.widget.gift.CategoryTitleItem
import com.buque.wakoo.ui.widget.gift.GiftItem
import com.buque.wakoo.ui.widget.gift.GiftWallItem
import com.buque.wakoo.ui.widget.gift.SpaceItem
import com.buque.wakoo.ui.widget.gift.SpanItem
import com.buque.wakoo.ui.widget.gift.TopRadiusItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray
import java.util.concurrent.ConcurrentHashMap

class GiftWallViewModel(
    val sceneType: Int?,
    val sceneId: Int,
    val refreshWhenCreate: Boolean = false,
) : ViewModel() {
    private val userId
        get() = sceneId

    init {
        refresh()
        // todo 礼物墙 监听送礼 实时刷新
//        viewModelScope.launch {
//            GiftRepository.flowSentGiftToUser.collect {
//                if (userId == it.first) {
//                    updateGiftData(it.second)
//                }
//            }
//        }
    }

    companion object {
        // 对应带tab的数据接口
        fun convertGiftWall(
            bean: List<GiftWall.SeriesGift>,
            columnInt: Int = 4,
        ): List<GiftWallItem> {
            val list =
                buildList {
                    bean.forEachIndexed { index, it ->
                        add(TopRadiusItem(columnInt * 2))
                        val listSize = it.gifts.size
                        var seriesId = index
                        if (it.seriesName.isNotBlank()) {
                            add(CategoryTitleItem(it.seriesName, it.starCount, it.totalCount, seriesId, columnInt * 2))
                        } else {
                            seriesId = -1
                        }

                        val tailSize = listSize % columnInt

                        it.gifts.forEachIndexed { i, gift ->
                            val direction =
                                if (i in Math.max(
                                        0,
                                        listSize - tailSize,
                                    ) until listSize
                                ) {
                                    -1
                                } else if (i % columnInt == 0) {
                                    1
                                } else if (i % columnInt == (columnInt - 1)) {
                                    3
                                } else {
                                    -1
                                }
                            add(GiftItem(gift.gift, gift.count, direction, seriesId, i < 4))
                        }
                        val lastLineCount = it.gifts.size.rem(columnInt)
                        if (lastLineCount != 0) {
                            val leftSpan = columnInt.minus(lastLineCount)
                            add(
                                size.minus(lastLineCount),
                                SpanItem(leftSpan, isStart = true, it.gifts.size < columnInt),
                            )
                            add(SpanItem(leftSpan, isFirstLine = it.gifts.size < columnInt))
                        }
                        add(BottomRadiusItem(columnInt * 2))
                        add(SpaceItem(16, columnInt * 2))
                    }
                }
            return list
        }
    }

    fun refresh() {
        loadWallSummary()
    }

    private val _giftWallSummary = MutableStateFlow(GiftWallSummaryBean())
    val giftWallSummary = _giftWallSummary.asStateFlow()

    //region 新版本, 数据分类别加载

    /**
     * 加载礼物墙总览分类
     *
     */
    private fun loadWallSummary() {
        viewModelScope.launch {
            executeApiCallExpectingData {
                GiftApiService.instance.getWallSummaryInfo(userId, sceneType, sceneId.toString())
            }.onSuccess {
                _giftWallSummary.emit(it)
                summaryTabStateData.forEach {
                    it.value.value = State.InitialState
                }
            }
        }
    }

    private val summaryTabStateData = ConcurrentHashMap<Int, MutableStateFlow<State>>()
    private val summaryTabData = ConcurrentHashMap<Int, StateFlow<State>>()

    fun getState(
        summaryTab: GiftWallSummaryBean.Tab,
        columnInt: Int = 4,
    ): StateFlow<State> {
        synchronized(summaryTab) {
            val stateFlow =
                summaryTabStateData.getOrPut(summaryTab.t) {
                    MutableStateFlow(State.InitialState)
                }

            val finalResult =
                summaryTabData.getOrPut(summaryTab.t) {
                    stateFlow.asStateFlow()
                }

            if (finalResult.value == State.InitialState) {
                stateFlow.value = (State.LoadingState)
                viewModelScope.launch {
                    executeApiCallExpectingData {
                        GiftApiService.instance.getWallTabInfo(userId, summaryTab.t, sceneType, sceneId.toString())
                    }.onSuccess {
                        val list =
                            withContext(Dispatchers.IO) {
                                when (it.getIntOrNull("ui_t")) {
                                    1 -> { // 普通礼物,取gift
                                        it.getOrNull("gifts")?.jsonArray?.let {
                                            val list = AppJson.decodeFromJsonElement<List<GiftWall.GiftWrapper>>(it)
                                            convertGiftWall(listOf(GiftWall.SeriesGift(gifts = list)), columnInt = columnInt)
                                        } ?: emptyList()
                                    }

                                    2 -> { // 盲盒礼物,取series
                                        it.getOrNull("series")?.jsonArray?.let {
                                            val list = AppJson.decodeFromJsonElement<List<GiftWall.SeriesGift>>(it)
                                            convertGiftWall(list, columnInt = columnInt)
                                        } ?: emptyList()
                                    }

                                    else -> {
                                        emptyList()
                                    }
                                }
                            }
                        stateFlow.emit(State.LoadSucceedState(list))
                    }.onFailure {
                        stateFlow.emit(State.LoadFailed)
                    }
                }
            }

            return finalResult
        }
    }
    //endregion

    private fun updateGiftData(sentGiftIds: List<Int>) {
        viewModelScope.launch {
            withContext(Dispatchers.Default) {
                // 总数新增
                val sentGifts = sentGiftIds.groupBy { it }.map { it.key to it.value.size }.toMap()
                val sentUniqeIds = sentGiftIds.distinct().toMutableSet() // 发送的礼物id, 去重

                // 顶部统计数据
                var summaryBean = _giftWallSummary.value.copy()

                summaryTabStateData.forEach {
                    val tabId = it.key // tab id
                    val state = it.value // MutableState
                    val stateValue = it.value.value // 具体的state数据

                    // 新的数据列表

                    if (stateValue is State.LoadSucceedState) { // 如果是已加载的state才有数据
                        val operationList: MutableList<GiftWallItem> = stateValue.list.toMutableList()
                        var needEmit = false
                        while (sentUniqeIds.isNotEmpty()) {
                            val giftItem =
                                operationList.find { it is GiftItem && it.gift.id in sentUniqeIds } as? GiftItem
                            // 如果找到了这个礼物
                            if (giftItem != null) {
                                needEmit = true
                                // 礼物pos
                                val itemPos = operationList.indexOf(giftItem)

                                val needReCount = giftItem.count == 0 // 如果之前是空的则需要对系列进行重新计算

                                // 将赠送出去的数量加上
                                operationList.set(itemPos, giftItem.updateLightCount(sentGifts[giftItem.gift.id] ?: 0))

                                if (needReCount) {
                                    if (giftItem.seriesId != -1) { // 说明有系列, 还要把系列里的数量加上
                                        (
                                            operationList.find {
                                                it is CategoryTitleItem && it.seriesId == giftItem.seriesId
                                            } as? CategoryTitleItem
                                        )?.let {
                                            val seriesPos = operationList.indexOf(it)
                                            operationList.set(seriesPos, it.updateLightCount(1))
                                        }
                                    }
                                    // 最顶部的tab需要更新
                                    summaryBean = summaryBean.updateTabCount(tabId, 1)
                                }

                                sentUniqeIds.remove(giftItem.gift.id)
                            } else {
                                break
                            }
                        }
                        if (needEmit) {
                            withContext(Dispatchers.Main) {
                                state.emit(State.LoadSucceedState(operationList))
                            }
                        }
                    }
                }
                withContext(Dispatchers.Main) {
                    _giftWallSummary.emit(summaryBean)
                }
            }
        }
    }

    sealed interface State {
        data object InitialState : State

        data object LoadingState : State

        data object LoadFailed : State

        data class LoadSucceedState(
            val list: List<GiftWallItem>,
        ) : State
    }
}
