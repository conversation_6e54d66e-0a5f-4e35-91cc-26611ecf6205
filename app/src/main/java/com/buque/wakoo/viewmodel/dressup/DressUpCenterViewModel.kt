package com.buque.wakoo.viewmodel.dressup

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.DressUpMineProp
import com.buque.wakoo.bean.DressUpMinePropResponse
import com.buque.wakoo.bean.DressUpMineTab
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.DressupApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.handleOptimisticRequest
import com.buque.wakoo.viewmodel.TabListPaginateViewModel
import kotlinx.coroutines.launch

class DressUpCenterViewModel :
    TabListPaginateViewModel<String, Int, DressUpMineTab, Dr<PERSON><PERSON><PERSON><PERSON>ineProp, DressUpMinePropResponse>() {
    fun onWearPropFunc(
        tab: DressUpMineTab,
        item: DressUpMineProp,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                handleOptimisticRequest(
                    itemId = item.prop.id,
                    items = state.data,
                    findItemIdFromData = { it.prop.id },
                    getProperty = {
                        it.isUsing
                    },
                    isRequesting = { it.useInterfaceRequesting },
                    copy = { item, isUsing, isRequesting ->
                        item.copy(isUsing = isUsing, useInterfaceRequesting = isRequesting)
                    },
                    apiCall = { isUsing ->
                        executeApiCallExpectingData {
                            DressupApiService.instance.setMineDressupUse(
                                tab.type,
                                item.prop.id,
                                isUsing
                            )
                        }.isSuccess
                    },
                )
            }
        }
    }

    override fun getFirstPageKey(dataKey: DressUpMineTab): Int = 0

    override fun getNextPageKey(
        cState: CState<List<DressUpMineProp>>,
        dataKey: DressUpMineTab,
        pageKey: Int?,
    ): Int = cState.requireData.last().id

    override suspend fun getData(
        reqKey: String,
        dataKey: DressUpMineTab,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<DressUpMinePropResponse> =
        DressupApiService.instance.getMineDressupList(dataKey.type, pageKey)

    override fun getDataListFromResponse(
        dataKey: DressUpMineTab,
        response: DressUpMinePropResponse,
    ): List<DressUpMineProp> = response.list

    override fun getHasNextPageKeyFromResponse(
        dataKey: DressUpMineTab,
        pageKey: Int,
        response: DressUpMinePropResponse,
        distinctAddSize: Int,
    ): Int? =
        if (response.list.isNotEmpty()) {
            response.list.last().id
        } else {
            null
        }

    override fun getDistinctSelector(): (DressUpMineProp) -> String =
        {
            it.prop.id.toString()
        }
}
