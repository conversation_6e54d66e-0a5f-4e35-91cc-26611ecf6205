package com.buque.wakoo.viewmodel.liveroom

import com.buque.wakoo.bean.RecommendLiveRoom
import com.buque.wakoo.bean.RecommendLiveRoomResponse
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.viewmodel.ListPaginateViewModel

class LiveRoomRecommendViewModel :
    ListPaginateViewModel<Any, Int, RecommendLiveRoom, RecommendLiveRoomResponse>() {
    override fun getFirstPageKey(dataKey: Any): Int {
        return 0
    }

    override fun getNextPageKey(
        cState: CState<List<RecommendLiveRoom>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return 0
    }

    override suspend fun getData(
        reqKey: Any,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int
    ): ApiResponse<RecommendLiveRoomResponse> {
        return VoiceRoomApiService.instance.getRecommendRoomList()
    }

    override fun getDataListFromResponse(
        dataKey: Any,
        response: RecommendLiveRoomResponse
    ): List<RecommendLiveRoom> {
        return response.data
    }

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: RecommendLiveRoomResponse,
        distinctAddSize: Int
    ): Int? {
        return null
    }

    override fun getDistinctSelector(): (RecommendLiveRoom) -> String {
        return {
            it.id.toString()
        }
    }
}

