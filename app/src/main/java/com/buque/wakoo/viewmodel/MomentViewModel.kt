package com.buque.wakoo.viewmodel

import android.Manifest
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.CompactJson
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.MediaInfo
import com.buque.wakoo.bean.MomentItem
import com.buque.wakoo.bean.MomentListResponse
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.LocationManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.MomentApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.data.common.VideoItem
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.PermissionUtils
import com.buque.wakoo.utils.handleOptimisticRequest
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadUtils
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

class MomentPublishViewModel : BaseViewModel() {
    val selectPicture = mutableStateListOf<MediaItem>()
    val inputValue = mutableStateOf("")
    val inputAddress = mutableStateOf<String?>(null)
    private val cosTransferHelper = CosTransferHelper()
    private var country = ""

    val permissions =
        arrayOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
        )

    fun addPicture(pics: List<MediaItem>) {
        selectPicture.clear()
        selectPicture.addAll(pics)
    }

    fun removePicture(vararg pics: MediaItem) {
        selectPicture.removeAll(pics)
    }

    fun hasPermission(): Boolean =
        PermissionUtils.hasPermissions(
            WakooApplication.instance,
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
            ),
        )

    init {
        viewModelScope.launch {
            val data =
                LocationManager.locationFlow
                    .distinctUntilChangedBy {
                        "${it?.lat}-${it?.lat}-${it?.city}-${it?.country}"
                    }.filterNotNull()
                    .first()
            // 但是国家会一直改变
            country = data.country
            // 只有第一次才会改变,后面地址改变也不会刷新选中的地址
            inputAddress.value = data.getFullAddress()
        }
    }

    suspend fun publish(): Boolean {
        if (selectPicture.isEmpty()) {
            showToast("请至少选择一个媒体".localized)
            return false
        } else if (inputValue.value.isEmpty()) {
            showToast("请输入内容".localized)
            return false
        }

        val items = selectPicture.toList()
        val newList =
            items.mapNotNull { item ->
                val uri = item.uriString.toUri()
                val path =
                    UploadUtils.generateOSSPath(
                        WakooApplication.instance,
                        uri,
                        UploadUtils.DEFAULT_MOMENT_PATH,
                    )
                when (val ret = cosTransferHelper.upload(cosPath = path, uri = uri).await()) {
                    is TransferResult.Success -> {
                        MediaInfo(
                            ret.url,
                            item.width,
                            item.height,
                            item.sizeBytes,
                            isVideo = item is VideoItem,
                        )
                    }

                    is TransferResult.Failure -> {
                        null
                    }
                }
            }
        val images = newList.filter { !it.isVideo }
        val videos = newList.filter { it.isVideo }

        return executeApiCallExpectingData {
            MomentApiService.instance.publishMoment(
                buildMap {
                    put("text", inputValue.value)
                    if (images.isNotEmpty()) {
                        put("images", CompactJson.encodeToString(images))
                    }
                    if (videos.isNotEmpty()) {
                        put("video", CompactJson.encodeToString(videos))
                    }
                    put("location", inputAddress.value.orEmpty())
                    put("country", country)
                },
            )
        }.onSuccess {
            AccountManager.updateSelfSocialInfo {
                val moments =
                    buildList {
                        it.moments?.also { list ->
                            addAll(list)
                        }
                        addAll(newList)
                    }
                it.copy(
                    moments = moments,
                    momentTotalCount = it.momentTotalCount + newList.size,
                )
            }
            showToast("发布成功".localized)
        }.isSuccess
    }
}

class UserMomentListViewModel(
    private val userId: String,
) : ListPaginateViewModel<Any, Int, MomentItem, MomentListResponse>() {
    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<MomentItem>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = cState.requireData.last().id

    override suspend fun getData(
        reqKey: Any,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<MomentListResponse> =
        if (userId == SelfUser?.id) {
            MomentApiService.instance.getMyMoments(lastId = pageKey)
        } else {
            MomentApiService.instance.getMomentList(userId, lastId = pageKey)
        }

    override fun getDataListFromResponse(
        dataKey: Any,
        response: MomentListResponse,
    ): List<MomentItem> = response.list

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: MomentListResponse,
        distinctAddSize: Int,
    ): Int? =
        if (response.list.isNotEmpty()) {
            response.list.lastOrNull()?.id
        } else {
            null
        }

    override fun getDistinctSelector(): (MomentItem) -> String =
        {
            it.id.toString()
        }

    /**
     * 点赞瞬间或取消点赞
     */
    fun thumbUpOrNotMoment(item: MomentItem) {
        viewModelScope.launch {
            val state = getCState(Unit)
            if (state is CState.Success) {
                handleOptimisticRequest(
                    itemId = item.id,
                    items = state.data,
                    findItemIdFromData = { it.id },
                    getProperty = { it.iHaveLiked },
                    isRequesting = { it.isRequesting },
                    copy = { item, newHaveLiked, isRequesting ->

                        val newCnt =
                            if (newHaveLiked != item.lastLikeStatus) {
                                item.lastLikeStatus = newHaveLiked
                                if (newHaveLiked) item.likeCnt + 1 else item.likeCnt - 1
                            } else {
                                item.likeCnt
                            }

                        item.copy(
                            iHaveLiked = newHaveLiked,
                            likeCnt = newCnt,
                            isRequesting = isRequesting,
                        )
                    },
                    apiCall = { newHaveLiked ->
                        executeApiCallExpectingData {
                            MomentApiService.instance.likeMoment(
                                mapOf(
                                    "trend_id" to item.id.toString(),
                                    "favorite" to newHaveLiked.toString(),
                                ),
                            )
                        }.isSuccess
                    },
                )
            }
        }
    }

    suspend fun deleteMoment(item: MomentItem) {
        val state = getCState(Unit)
        if (state is CState.Success) {
            executeApiCallExpectingData {
                MomentApiService.instance.deleteMoment(
                    mapOf(
                        "trend_id" to item.id.toString(),
                    ),
                )
            }.onSuccess {
                val items = state.data
                val index = items.indexOfFirst { it.id == item.id }
                if (index > -1) {
                    items.removeAt(index)
                }
            }
        }
    }
}
