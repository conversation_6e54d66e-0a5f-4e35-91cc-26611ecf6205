package com.buque.wakoo.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.FeedItemData
import com.buque.wakoo.bean.LiveRoomCardItem
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.navigation.VoiceListTab
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.IIFeedItemResponseConvertListOwner
import com.buque.wakoo.network.api.bean.VoiceFeedResponse
import com.buque.wakoo.network.api.bean.VoiceItemResponse
import com.buque.wakoo.network.api.bean.VoiceItemWrapperResponse
import com.buque.wakoo.network.api.bean.VoiceListResponse
import com.buque.wakoo.network.api.service.VoiceApiService
import com.buque.wakoo.repository.UserRepository
import com.buque.wakoo.repository.VoiceRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData
import com.buque.wakoo.utils.FileUtils
import com.buque.wakoo.utils.handleOptimisticRequest
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class VoiceListViewModel(
    applicationContext: Context,
) : TabListPaginateViewModel<String, Int, VoiceListTab, FeedItemData, IIFeedItemResponseConvertListOwner>() {
    private val voiceRepository = VoiceRepository()

    private val userRepository = UserRepository()

    class Factory(
        private val context: Context,
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = VoiceListViewModel(context.applicationContext) as T
    }

    val downloadPath: String by lazy {
        FileUtils.createDefaultVoiceCardDownloadOutputFile(applicationContext).absolutePath
    }

    override fun getFirstPageKey(tabKey: VoiceListTab): Int = if (tabKey is VoiceListTab.Recommend) 1 else 0

    override fun getNextPageKey(
        cState: CState<List<FeedItemData>>,
        tabKey: VoiceListTab,
        pageKey: Int?,
    ): Int =
        if (tabKey is VoiceListTab.Recommend) {
            2
        } else {
            cState.requireData
                .last()
                .id
                .toInt()
        }

    override suspend fun getData(
        reqKey: String,
        tabKey: VoiceListTab,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<IIFeedItemResponseConvertListOwner> =
        when (tabKey) {
            VoiceListTab.Recommend -> VoiceApiService.instance.getVoiceRecommendListV2()
            VoiceListTab.Follow -> VoiceApiService.instance.getVoiceFollowingList(pageKey)
            is VoiceListTab.MyPublish ->
                VoiceApiService.instance.getVoicePublishList(
                    tabKey.userId,
                    pageKey,
                )

            is VoiceListTab.Like ->
                VoiceApiService.instance
                    .getVoiceLikedList(
                        tabKey.userId,
                        pageKey,
                    ).toFeed()

            is VoiceListTab.Favorite ->
                VoiceApiService.instance
                    .getVoiceFavoriteList(
                        tabKey.userId,
                        pageKey,
                    ).toFeed()
        }

    private fun ApiResponse<VoiceListResponse>.toFeed(): ApiResponse<IIFeedItemResponseConvertListOwner> {
        val resp = this
        return ApiResponse<IIFeedItemResponseConvertListOwner>(
            code = resp.code,
            message = resp.message,
            data =
                if (resp.isSuccessful()) {
                    VoiceFeedResponse(
                        hasNext = resp.requireData.hasNext,
                        list =
                            resp.requireData.list.map {
                                it.sound
                            },
                    )
                } else {
                    null
                },
        )
    }

    override fun getDataListFromResponse(
        tabKey: VoiceListTab,
        response: IIFeedItemResponseConvertListOwner,
    ): List<FeedItemData> =
        response.list.map {
            it.convert().apply {
                if (this is VoiceCardItem) {
                    if (it is VoiceItemResponse) {
                        fileName = FileUtils.createFileNameFromUrl(it.resource)
                    } else if (it is VoiceItemWrapperResponse) {
                        fileName = FileUtils.createFileNameFromUrl(it.data.resource)
                    }
                }
            }
        }

    override fun getHasNextPageKeyFromResponse(
        tabKey: VoiceListTab,
        pageKey: Int,
        response: IIFeedItemResponseConvertListOwner,
        distinctAddSize: Int,
    ): Int? =
        if (response.hasNext) {
            if (tabKey is VoiceListTab.Recommend) {
                if (distinctAddSize == 0) {
                    null
                } else {
                    pageKey + 1
                }
            } else {
                response.list.lastOrNull()?.id
            }
        } else {
            null
        }

    override fun getDistinctSelector(): (FeedItemData) -> String =
        {
            it.identityId
        }

    fun requestLikeVoice(
        item: VoiceCardItem,
        tab: VoiceListTab,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                handleOptimisticRequest(
                    itemId = item.identityId,
                    items = state.data,
                    findItemIdFromData = { it.identityId },
                    getProperty = { (it as VoiceCardItem).isLike },
                    isRequesting = { (it as VoiceCardItem).likeRequesting },
                    copy = { item, isLike, isRequesting ->
                        item as VoiceCardItem
                        item.copy(
                            isLike = isLike,
                            likeCount =
                                item.likeCount +
                                    if (item.isLike == isLike) {
                                        0
                                    } else {
                                        if (isLike) 1 else -1
                                    },
                            likeRequesting = isRequesting,
                        )
                    },
                    apiCall = { isLike -> voiceRepository.likeVoice(item.id, isLike).isSuccess },
                )
            }
        }
    }

    fun requestFavoriteVoice(
        item: VoiceCardItem,
        tab: VoiceListTab,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                handleOptimisticRequest(
                    itemId = item.identityId,
                    items = state.data,
                    findItemIdFromData = { it.identityId },
                    getProperty = { (it as VoiceCardItem).isFavorite },
                    isRequesting = { (it as VoiceCardItem).favoriteRequesting },
                    copy = { item, isFavorite, isRequesting ->
                        item as VoiceCardItem
                        item.copy(
                            isFavorite = isFavorite,
                            favoriteCount =
                                item.favoriteCount +
                                    if (item.isFavorite == isFavorite) {
                                        0
                                    } else {
                                        if (isFavorite) 1 else -1
                                    },
                            favoriteRequesting = isRequesting,
                        )
                    },
                    apiCall = { isFavorite ->
                        voiceRepository
                            .favoriteVoice(
                                item.id,
                                isFavorite,
                            ).isSuccess
                    },
                )
            }
        }
    }

    fun requestFollowUser(
        item: VoiceCardItem,
        tab: VoiceListTab,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                handleOptimisticRequest(
                    itemId = item.identityId,
                    items = state.data,
                    findItemIdFromData = { it.identityId },
                    getProperty = { (it as VoiceCardItem).isFollow },
                    isRequesting = { (it as VoiceCardItem).followRequesting },
                    copy = { item, isFollow, isRequesting ->
                        item as VoiceCardItem
                        item.copy(isFollow = isFollow, followRequesting = isRequesting)
                    },
                    apiCall = { isFollow ->
                        userRepository.updateFollowState(item.user.id, isFollow).isSuccess
                    },
                )
            }
        }
    }

    fun requestMarkAsBored(
        item: FeedItemData,
        tab: VoiceListTab,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                val index = state.data.indexOfFirst { it.identityId == item.identityId }
                if (index > -1) {
                    state.data.removeAt(index)
                    when (item) {
                        is VoiceCardItem -> voiceRepository.markAsBored(item.id)
                        is LiveRoomCardItem -> voiceRepository.markAsBoredRoom(item.id)
                    }
                }
            }
        }
    }

    fun requestDeleteVoice(
        item: VoiceCardItem,
        tab: VoiceListTab,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                val index = state.data.indexOfFirst { it.identityId == item.identityId }
                if (index > -1) {
                    state.data.removeAt(index)
                    voiceRepository.deleteVoice(item.id).onFailure {
                        withContext(NonCancellable) {
                            delay(2000)
                            voiceRepository.deleteVoice(item.id)
                        }
                    }
                }
            }
        }
    }

    fun blackUser(userId: String) {
        viewModelScope.launch { userRepository.updateBlackState(userId, true) }
    }

    fun refreshRoomState(
        item: LiveRoomCardItem,
        tab: VoiceListTab,
    ) {
        viewModelScope.launch {
            val state = tryGetState(tab)
            if (state is CState.Success) {
                voiceRepository.getLiveRoomPreviewInfo(item.id).onSuccess { info ->
                    val index = state.data.indexOfFirst { it.identityId == item.identityId }
                    if (index > -1) {
                        state.data[index] = info
                    }
                }
            }
        }
    }
}
