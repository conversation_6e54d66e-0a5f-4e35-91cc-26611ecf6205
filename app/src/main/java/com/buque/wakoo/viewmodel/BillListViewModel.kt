package com.buque.wakoo.viewmodel

import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.BillRecord
import com.buque.wakoo.network.api.bean.Record
import com.buque.wakoo.network.api.service.WalletApi
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData

class BillListViewModel : ListPaginateViewModel<Any, Int, Record, BillRecord>() {

    private val api = ApiClient.createuserApiService<WalletApi>()

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getDistinctSelector(): (Record) -> String {
        return {
            it.id.toString()
        }
    }

    // This function overrides the getHasNextPageKeyFromResponse function and takes in three parameters: dataKey, pageKey, and response
    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: BillRecord,
        distinctAddSize: Int
    ): Int? {
        // This line checks if the records in the response are not empty
        return response.records.lastOrNull()?.id
    }

    override fun getDataListFromResponse(dataKey: Any, response: BillRecord): List<Record> {
        return response.records
    }

    override suspend fun getData(
        reqKey: Any,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int
    ): ApiResponse<BillRecord> {
        return api.getBillList(pageKey)
    }

    override fun getNextPageKey(cState: CState<List<Record>>, dataKey: Any, pageKey: Int?): Int {
        return cState.requireData.last().id
    }
}