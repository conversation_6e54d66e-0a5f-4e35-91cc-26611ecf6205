package com.buque.wakoo.viewmodel

import com.buque.wakoo.bean.NotificationInfo
import com.buque.wakoo.bean.NotificationListResponse
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.service.NotificationApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.requireData

class NotificationInfoViewModel :
    ListPaginateViewModel<Any, Int, NotificationInfo, NotificationListResponse>() {
    private val userApiService
        get() = NotificationApiService.instance

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<NotificationInfo>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int =
        cState.requireData
            .last()
            .id
            .toInt()

    override suspend fun getData(
        reqKey: Any,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int,
    ): ApiResponse<NotificationListResponse> = userApiService.getNotificationList(pageKey)

    override fun getDataListFromResponse(
        dataKey: Any,
        response: NotificationListResponse,
    ): List<NotificationInfo> = response.notifications

    override fun getHasNextPageKeyFromResponse(
        dataKey: Any,
        pageKey: Int,
        response: NotificationListResponse,
        distinctAddSize: Int,
    ): Int? =
        if (response.hasNext) {
            response.notifications.lastOrNull()?.id
        } else {
            null
        }

    override fun getDistinctSelector(): (NotificationInfo) -> String =
        {
            it.id.toString()
        }

    suspend fun getUnreadBadge() =
        executeApiCallExpectingData {
            userApiService.getUnreadBadge()
        }
}
