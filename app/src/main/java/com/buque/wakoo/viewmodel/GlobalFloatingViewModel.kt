package com.buque.wakoo.viewmodel

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.ui.floating.AbsFloatingBanner
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.EventBus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.concurrent.PriorityBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 全局飘屏弹窗ViewModel
 * 实现生产消费模型：任务入队 -> 按优先级排序 -> 自动发送到channel
 *
 *
 */
class GlobalFloatingViewModel : ViewModel() {
    init {
        // 监听EventBus，接收新任务
        viewModelScope.launch {
            EventBus.on<AbsFloatingBanner>().collectLatest { task ->
                enqueueTask(task)
            }
        }
    }

    private var currentTask = MutableStateFlow<AbsFloatingBanner?>(null)

    //region 生产消费模型

    /**
     * 优先级队列，priority越高越在前面
     */
    private val priorityQueue =
        PriorityBlockingQueue<AbsFloatingBanner>(10) { task1, task2 ->
            // 优先级比较：priority高的排在前面
            task2.priority.compareTo(task1.priority)
        }

    // 消费者状态控制
    private val isConsumerRunning = AtomicBoolean(false)

    /**
     * 启动消费者协程
     */
    private fun startConsumer() {
        if (isConsumerRunning.compareAndSet(false, true)) {
            viewModelScope.launch(Dispatchers.Default) {
                try {
                    while (isActive) {
                        // 从优先级队列中取出任务
                        val task = priorityQueue.poll()
                        currentTask.value = task

                        if (task == null) {
                            break
                        }

                        task.await()
                        currentTask.value = null
                        delay(100)
                    }
                } catch (e: Exception) {
                    LogUtils.w(e.stackTraceToString())
                } finally {
                    isConsumerRunning.set(false)
                }
            }
        }
    }

    /**
     * 入队任务并排序
     * @param task 要入队的任务
     */
    private fun enqueueTask(task: AbsFloatingBanner) {
        priorityQueue.offer(task)

        // 如果消费者没有运行，重新启动
        if (!isConsumerRunning.get()) {
            startConsumer()
        }
    }

    /**
     * 获取当前队列大小
     * @return 队列中的任务数量
     */
    fun getQueueSize(): Int = priorityQueue.size

    /**
     * 清空队列
     */
    fun clearQueue() {
        priorityQueue.clear()
    }
    //endregion

    //region 通用飘屏组件, 其他的也都可以哦

    /**
     * 礼物横幅组件
     * 按顺序处理channel中的任务，每个任务显示指定时长后自动处理下一个
     */
    @Composable
    fun FloatingBannerWidget() {
        val scope = rememberCoroutineScope()
        val task by currentTask.collectAsState()
        task?.apply {
            val state = remember(uuid) { MutableTransitionState(false).apply { targetState = true } }

            AnimatedVisibility(
                visibleState = state,
                modifier = Modifier.padding(top = 80.dp),
                enter = slideInHorizontally(initialOffsetX = { -it }),
                exit = fadeOut(),
//                + slideOutHorizontally(targetOffsetX = { -it }
            ) {
                Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.TopCenter) {
                    Render()
                }
            }

            // 控制显示时长和任务完成
            LaunchedEffect(uuid) {
                // 等待显示的市场info.duration秒
                delay(duration * 1000L)

                // 开始退出动画
                state.targetState = false

                // 监听动画完成状态
                snapshotFlow { state.isIdle && !state.currentState }
                    .filter { it }
                    .onEach {
                        // 动画完成后，恢复任务，让消费者继续处理下一个任务
                        resume()
                    }.launchIn(scope)
            }
        }
    }
    //endregion
}
