package com.buque.wakoo.viewmodel.chatgroup

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.repository.ChatGroupRepo
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel
import kotlinx.coroutines.launch

class SearchMemberViewModel(
    val groupId: String,
) : BasicListPaginateViewModel<Int, ChatGroupMember>() {
    var keywords: String = ""
        set(value) {
            field = value
            if (value.isEmpty()) {
                setCState(Unit, CState.Idle)
                return
            }
            viewModelScope.launch {
                refreshList(true)
            }
        }

    override var enableLoadMore: Boolean = false

    private val repo: ChatGroupRepo
        get() = GlobalRepository.chatGroupRepo

    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int,
    ): Result<List<ChatGroupMember>> = repo.searchMember(groupId, keywords)

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<ChatGroupMember>>,
        dataKey: Any,
        pageKey: Int?,
    ): Int = 0

    override fun getDistinctSelector(): (ChatGroupMember) -> String =
        {
            it.memberId.toString()
        }

    suspend fun kickOutMember(member: ChatGroupMember) =
        repo
            .kickOutMember(groupId, member.memberId.toString())
            .onSuccess {
                remove(member)
            }

    fun remove(item: ChatGroupMember) {
        val st = getListState()
        if (st is CState.Success) {
            val l = st.data
            l.remove(item)
        }
    }
}