package com.buque.wakoo.viewmodel

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.hb.HBSettings
import com.buque.wakoo.bean.hb.HongBaoInfo
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.utils.Server
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement

class HongBaoViewModel(val sceneType: Int, val sceneId: String) : ViewModel(), IMCompatListener {

    val repo = GlobalRepository.hongBaoRepo

    private val map = mutableMapOf<String, HongBaoInfo>()

    private val _settingsState: MutableState<CState<HBSettings>> = mutableStateOf(CState.Idle)
    val settingsState: State<CState<HBSettings>> = _settingsState

    private val _hongBaoListState = mutableStateOf(emptyList<HongBaoInfo>())

    val hongBaoListState: State<List<HongBaoInfo>> = _hongBaoListState

    init {
        IMCompatCore.addIMListener(this)
        refreshHongBaoList()
    }


    private fun refreshHongBaoList() {
        viewModelScope.launch {
            repo.getList(sceneType, sceneId)
                .onSuccess { obj ->
                    _hongBaoListState.value = obj.getOrNull("list")?.let { el ->
                        AppJson.decodeFromJsonElement<List<HongBaoInfo>>(el)
                    }.orEmpty().filter { it.status != 2 && it.remainedNumber > 0 }
                }
        }
    }

    fun refreshSettings() {
        viewModelScope.launch {
            repo.getSettings()
                .onSuccess {
                    _settingsState.value = CState.Success(it)
                }
        }
    }

    @Composable
    fun rememberHongBaoState(id: String) = run {
        val state = remember {
            mutableStateOf<HongBaoInfo?>(map[id])
        }
        val scope = rememberCoroutineScope()
        LaunchedEffect(id) {
            scope.launch {
                repo.getDetail(id).onSuccess {
                    Server.setCurrentMillis(it.nowTimestamp * 1000L)
                    state.value = it
                    map[id] = it
                }
            }
        }
        state
    }

    suspend fun grab(id: String) = repo.grab(id)
        .onSuccess {
            repo.getDetail(id).onSuccess {
                map[id] = it
                hideHongBaoPendant(id)
            }
        }

    override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
        super.onRecvNewCustomMessage(message, offline)
        val cmd = message.cmd
        if (cmd == IMEvent.RED_PACKET_START_GRAB) {
            refreshHongBaoList()
        }
    }

    private fun hideHongBaoPendant(id: String) {
        _hongBaoListState.value = _hongBaoListState.value.filter { it.id != id }
    }

    override fun onCleared() {
        super.onCleared()
        IMCompatCore.removeIMListener(this)
    }
}