package com.buque.wakoo.viewmodel.dressup

import com.buque.wakoo.bean.DressUpPropItem
import com.buque.wakoo.bean.DressUpTabsItem
import com.buque.wakoo.bean.DressUpUIItem
import com.buque.wakoo.bean.DressupTab
import com.buque.wakoo.network.api.service.DressupApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.toCState
import com.buque.wakoo.viewmodel.CSViewModel

class DressUpTabViewModel : CSViewModel<List<DressUpUIItem>>() {
    init {
        refreshState()
    }

    private val operationRepo = DressupOperationRepo()

    val diamondState = operationRepo.diamondBalance
    val integralState = operationRepo.integralBalance

    companion object {
        fun convertToUIItems(tabs: List<DressupTab>): List<DressUpUIItem> {
            val retList = mutableListOf<DressUpUIItem>()

            retList.add(DressUpTabsItem(tabs.map { it.toUIItem() }))
            tabs.forEach {
                retList.add(it.toUIItem())
                retList.addAll(it.list)
            }
            return retList
        }
    }

    override suspend fun loadState(): CState<List<DressUpUIItem>> =
        executeApiCallExpectingData {
            DressupApiService.instance.getStoreList()
        }.map {
            operationRepo.updateBalance(diamond = it.diamond, integral = it.banlance)
            convertToUIItems(it.list)
        }.toCState()

    /**
     * @param payway 0钻石 1金币
     */
    suspend fun purchase(
        item: DressUpPropItem,
        days: Int,
        payway: Int,
    ) = operationRepo.purchaseDressUpPropItem(item.propInfo.id.toString(), item.propInfo.propType, days, payway)
}
