package com.buque.wakoo.viewmodel

import androidx.lifecycle.viewModelScope
import com.buque.wakoo.bean.boost.BoostConfig
import com.buque.wakoo.bean.boost.ExcItem
import com.buque.wakoo.bean.boost.MissionInfo
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.manager.DeviceInfoManager
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.BoostApi
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.toCState
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

data class BoostState(val config: BoostConfig, val signInfo: MissionInfo.MissionSeries? = null, val missions: MissionInfo? = null)

class BoostViewModel(val awardType: Int) : CSViewModel<BoostState>() {

    private val api = ApiClient.createuserApiService<BoostApi>()
    val stateFlowCash = MutableStateFlow<List<ExcItem>>(emptyList())
    val stateFlowDiamond = MutableStateFlow<List<ExcItem>>(emptyList())

    init {
        viewModelScope.launch {
            getExchangeList(2)
                .onSuccess { obj ->
                    val list = obj.parseValue<List<ExcItem>>("items").orEmpty()
                    stateFlowDiamond.value = list.filter { it.itemType == 100 }
                    stateFlowCash.value = list.filter { it.itemType == 101 }
                }
        }
    }


    override suspend fun loadState(): CState<BoostState> {
        val configDef = viewModelScope.async {
            executeApiCallExpectingData { api.getBoostConfig() }
        }
        val signDef = viewModelScope.async {
            executeApiCallExpectingData { api.getSignInfo() }
        }

        val missionDef = viewModelScope.async {
            executeApiCallExpectingData { api.getTaskInfo(true, awardType) }
        }

        val results = awaitAll(configDef, signDef, missionDef)
        return results[0].map { config ->
            config as BoostConfig
            val signInfo = results[1].getOrNull() as? MissionInfo.MissionSeries
            val missions = results[2].getOrNull() as? MissionInfo
            BoostState(config, signInfo, missions)
        }.toCState()

    }


    suspend fun makeSign(seriesId: String) = executeApiCallExpectingData {
        api.makeSign(
            mapOf(
                "ism_device_id" to DeviceInfoManager.smBoxId,
                "series_id" to seriesId
            )
        )
    }.onSuccess {
        refreshState()
    }

    suspend fun fillInviteCode(code: String) =
        executeApiCallExpectingData { api.fillInviteCode(mapOf("invite_code" to code)) }
            .onSuccess {
                refreshState()
            }

    suspend fun finishTask(taskId: Int) = executeApiCallExpectingData { api.finishTask(taskId) }

    suspend fun preCheck() = executeApiCallExpectingData {
        api.preCheck()
    }.map {
        it.parseValue<String>("jump_link").orEmpty()
    }

    suspend fun getExchangeList(type: Int = 1) = executeApiCallExpectingData {
        api.getExchangeList(type)
    }

    suspend fun exchange(selectedItem: ExcItem) =executeApiCallExpectingData {
        api.exchangeItem(mapOf("item_id" to selectedItem.id.toString()))
    }
}

