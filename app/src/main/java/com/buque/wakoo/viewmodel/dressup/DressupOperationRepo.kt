package com.buque.wakoo.viewmodel.dressup

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.ext.getIntOrNull
import com.buque.wakoo.network.api.service.DressupApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.json.JsonObject

class DressupOperationRepo {
    private val _diamondBalance = MutableStateFlow(0)
    val diamondBalance = _diamondBalance.asStateFlow()
    private val _integralBalance = MutableStateFlow(0)
    val integralBalance = _integralBalance.asStateFlow()

    fun updateBalance(
        diamond: Int? = null,
        integral: Int? = null,
    ) {
        if (diamond != null) {
            _diamondBalance.tryEmit(diamond)
        }
        if (integral != null) {
            _integralBalance.tryEmit(integral)
        }
    }

    suspend fun purchaseDressUpPropItem(
        id: String,
        type: Int,
        days: Int,
        payWay: Int,
    ): Result<JsonObject> {
        var purchaseType =
            when (payWay) {
                // 钻石
                0 -> 1
                // 金币
                1 -> 2
                else -> throw IllegalArgumentException("暂不支持其他方式")
            }
        if (SelfUser?.isJP == true) { // 日区
            purchaseType += 20
        }
        return executeApiCallExpectingData {
            DressupApiService.instance.buy(
                mapOf("decoration_id" to id, "decoration_type" to type, "days" to days, "purchase_type" to purchaseType),
            )
        }.onSuccess {
            updateBalance(integral = it.getIntOrNull("my_balance"))
        }
    }
}
