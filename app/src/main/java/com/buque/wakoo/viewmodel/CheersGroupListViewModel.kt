package com.buque.wakoo.viewmodel

import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.CheersGroupListResponse
import com.buque.wakoo.network.api.bean.PkUser
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.ui.widget.state.CState

class CheersGroupListViewModel(val pkSide: Int) : ListPaginateViewModel<String, Int, PkUser, CheersGroupListResponse>() {
    var desc: String = ""

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(cState: CState<List<PkUser>>, dataKey: Any, pageKey: Int?): Int = 0

    override suspend fun getData(
        reqKey: String,
        dataKey: Any,
        pageKey: Int,
        pageSize: Int
    ): ApiResponse<CheersGroupListResponse> = VoiceRoomApiService.instance.fetchPkContributionBillboard(pkSide, reqKey)

    override fun getDataListFromResponse(dataKey: Any, response: CheersGroupListResponse): List<PkUser> {
        desc = response.desc
        return response.suuportUserInfo.map { PkUser(BasicUser.fromResponse(it), it.cheerValue ?: 0) }
    }

    override fun getHasNextPageKeyFromResponse(dataKey: Any, pageKey: Int, response: CheersGroupListResponse, distinctAddSize: Int): Int? = null

    override fun getDistinctSelector(): (PkUser) -> String {
        return {
            it.user?.id ?: "0"
        }
    }
}