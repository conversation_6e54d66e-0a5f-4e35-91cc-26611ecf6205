package com.buque.wakoo.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AudioRecordManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.CommonApiService
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadCallbacks
import com.buque.wakoo.utils.upload.UploadUtils
import kotlinx.coroutines.launch

class ProfileVoiceEditViewModel : ViewModel() {
    val voiceSocial = mutableStateOf("")
    val audioRecordManager: AudioRecordManager
        get() = AudioRecordManager.singletonInstance
    private val cosTransferHelper = CosTransferHelper()

    init {
        refresh()
    }

    fun refresh() {
        viewModelScope.launch {
            executeApiCallExpectingData {
                CommonApiService.instance.getHelloWord()
            }.onSuccess {
                it.getStringOrNull("hello_message")?.apply {
                    voiceSocial.value = this
                }
            }
        }
    }

    suspend fun publish(
        filePath: String,
        duration: Int,
    ): Boolean {
        // 定义回调
        val callbacks =
            UploadCallbacks(
                onProgress = { progress, max ->
                    val percentage = if (max > 0) (progress * 100 / max).toInt() else 0
                },
                onStateChanged = { state ->
                },
                onInitUploadId = { uploadId ->
                },
            )

        val path = UploadUtils.generateOSSPath(filePath, UploadUtils.DEFAULT_VOICE_PATH)
        val currentTaskWrapper =
            cosTransferHelper.upload(
                cosPath = path,
                filePath = filePath,
                callbacks = callbacks,
            )

        // 挂起等待上传结果
        return when (val result = currentTaskWrapper.await()) {
            is TransferResult.Success -> {
                executeApiCallExpectingData {
                    UserApiService.instance.updateUserSoundBrand(
                        mapOf(
                            "voice_intro_url" to result.url,
                            "voice_intro_duration" to duration.toString(),
                        ),
                    )
                }.isSuccess
            }

            is TransferResult.Failure -> {
                showToast("上传失败".localized)
                false
            }
        }
    }
}
