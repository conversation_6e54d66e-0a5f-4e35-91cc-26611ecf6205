package com.buque.wakoo.viewmodel

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.widget.pagination.LoadResult
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.errorOrNull
import com.buque.wakoo.ui.widget.state.executeStatefulApiCall
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

data class RefreshResult<PageKey>(
    val success: <PERSON><PERSON><PERSON>,
    val nextPageKey: PageKey?,
)

abstract class AbsListPaginateViewModel<ReqK<PERSON>, PageKey : Any, <PERSON>Key, Data> : BaseViewModel() {
    protected open val defaultPageSize: Int = 20

    protected abstract fun getCState(dataKey: DataKey): CState<SnapshotStateList<Data>>

    protected abstract fun setCState(
        dataKey: DataKey,
        cState: CState<SnapshotStateList<Data>>,
    )

    protected abstract fun getFirstPageKey(dataKey: DataKey): PageKey

    protected abstract fun getNextPageKey(
        cState: CState<List<Data>>,
        dataKey: DataKey,
        pageKey: PageKey?,
    ): PageKey

    protected abstract fun getDistinctSelector(): (Data) -> String

    protected abstract suspend fun loadMoreData(
        reqKey: ReqKey,
        dataKey: DataKey,
        pageKey: PageKey?,
        dataEmptyCheck: Boolean = true,
        pageSize: Int = defaultPageSize,
    ): LoadResult<PageKey>

    protected abstract suspend fun loadFirstPageData(
        reqKey: ReqKey,
        dataKey: DataKey,
        isRefresh: Boolean,
        pageSize: Int = defaultPageSize,
    ): RefreshResult<PageKey>
}

abstract class BasicListPaginateViewModel<PageKey : Any, Data> : AbsListPaginateViewModel<Any, PageKey, Any, Data>() {
    protected val listCState = mutableStateOf<CState<SnapshotStateList<Data>>>(CState.Idle)

    protected open var enableLoadMore = true

    fun getListState() = listCState.value

    override fun setCState(
        dataKey: Any,
        cState: CState<SnapshotStateList<Data>>,
    ) {
        listCState.value = cState
    }

    protected open fun onLoadComplete(hasMore: Boolean) {
    }

    override fun getCState(dataKey: Any): CState<SnapshotStateList<Data>> = listCState.value

    override suspend fun loadFirstPageData(
        reqKey: Any,
        dataKey: Any,
        isRefresh: Boolean,
        pageSize: Int,
    ): RefreshResult<PageKey> {
        val pageKey = getFirstPageKey(dataKey)
        val st = getCState(dataKey)
        val newSt =
            if (isRefresh && st is CState.Success) {
                // 如果已是成功状态，则认为是“刷新”，保持旧数据，更新 isRefreshing 标志
                st.copy(
                    isRefreshing = true,
                    throwable = st.throwable,
                )
            } else {
                // 否则是初次加载
                CState.Loading(isRefreshing = isRefresh, throwable = st.errorOrNull)
            }
        setCState(dataKey, newSt)
        return loadData(pageKey, pageSize)
            .fold(onSuccess = {
                withContext(Dispatchers.Default) {
                    val list = mutableStateListOf<Data>()
                    list.addAll(it.distinctBy(getDistinctSelector()))
                    setCState(dataKey, CState.Success(list))
                }
                val nextPageKey =
                    if (it.size < pageSize) {
                        null
                    } else {
                        getNextPageKey(getCState(dataKey), dataKey, pageKey)
                    }
                onLoadComplete(it.isNotEmpty())
                RefreshResult(true, nextPageKey)
            }) {
                setCState(dataKey, st)
                RefreshResult(false, null)
            }
    }

    suspend fun loadNexPageData() = loadMoreData(Unit, Unit, null, false, defaultPageSize)

    override suspend fun loadMoreData(
        reqKey: Any,
        dataKey: Any,
        pageKey: PageKey?,
        dataEmptyCheck: Boolean,
        pageSize: Int,
    ): LoadResult<PageKey> {
        if (!enableLoadMore) return LoadResult.Page(null)
        val state = getCState(dataKey)
        if (dataEmptyCheck && (state !is CState.Success || state.data.isEmpty())) {
            return LoadResult.Page(null)
        }
        val pk =
            pageKey ?: run {
                getNextPageKey(getCState(dataKey), dataKey, pageKey)
            }
        return loadData(pk, pageSize)
            .fold(onSuccess = {
                val state = getCState(dataKey)
                if (state is CState.Success) {
                    val newList =
                        buildList {
                            addAll(state.data)
                            addAll(it)
                        }.distinctBy(getDistinctSelector())
                    state.data.clear()
                    state.data.addAll(newList)
                }
                val nextPageKey =
                    if (it.size < pageSize) {
                        null
                    } else {
                        getNextPageKey(getCState(dataKey), dataKey, pageKey)
                    }
                onLoadComplete(it.isNotEmpty())
                LoadResult.Page(nextPageKey)
            }) {
                LoadResult.Error(it)
            }
    }

    abstract suspend fun loadData(
        pageKey: PageKey,
        pageSize: Int,
    ): Result<List<Data>>

    suspend fun refreshList(isRefresh: Boolean = true): RefreshResult<PageKey> =
        loadFirstPageData(
            reqKey = Unit,
            dataKey = Unit,
            isRefresh = isRefresh,
        )
}

abstract class BaseListPaginateViewModel<ReqKey, PageKey : Any, DataKey, Data, DataResponse> :
    AbsListPaginateViewModel<ReqKey, PageKey, DataKey, Data>() {
    override val defaultPageSize: Int = 20

    abstract override fun getCState(dataKey: DataKey): CState<SnapshotStateList<Data>>

    abstract override fun setCState(
        dataKey: DataKey,
        cState: CState<SnapshotStateList<Data>>,
    )

    abstract override fun getFirstPageKey(dataKey: DataKey): PageKey

    abstract override fun getNextPageKey(
        cState: CState<List<Data>>,
        dataKey: DataKey,
        pageKey: PageKey?,
    ): PageKey

    protected abstract suspend fun getData(
        reqKey: ReqKey,
        dataKey: DataKey,
        pageKey: PageKey,
        pageSize: Int,
    ): ApiResponse<DataResponse>

    protected abstract fun getDataListFromResponse(
        dataKey: DataKey,
        response: DataResponse,
    ): List<Data>

    protected abstract fun getHasNextPageKeyFromResponse(
        dataKey: DataKey,
        pageKey: PageKey,
        response: DataResponse,
        distinctAddSize: Int,
    ): PageKey?

    abstract override fun getDistinctSelector(): (Data) -> String

    override suspend fun loadMoreData(
        reqKey: ReqKey,
        dataKey: DataKey,
        pageKey: PageKey?,
        dataEmptyCheck: Boolean,
        pageSize: Int,
    ): LoadResult<PageKey> {
        val state = getCState(dataKey)
        if (dataEmptyCheck && (state !is CState.Success || state.data.isEmpty())) {
            return LoadResult.Page(null)
        }
        val nextPageKey =
            pageKey ?: getNextPageKey(cState = state, dataKey = dataKey, pageKey = pageKey)
        return executeApiCallExpectingData {
            getData(
                reqKey = reqKey,
                dataKey = dataKey,
                pageKey = nextPageKey,
                pageSize = pageSize,
            )
        }.fold({ response ->
            val state = getCState(dataKey)
            var distinctAddSize = -1
            if (state is CState.Success) {
                val oldSize = state.data.size
                val newList =
                    withContext(Dispatchers.Default) {
                        buildList {
                            addAll(state.data)
                            addAll(getDataListFromResponse(dataKey, response))
                        }.distinctBy(getDistinctSelector())
                    }
                distinctAddSize = (newList.size - oldSize).coerceAtLeast(0)
                state.data.clear()
                state.data.addAll(newList)
            }
            LoadResult.Page(
                getHasNextPageKeyFromResponse(
                    dataKey,
                    nextPageKey,
                    response,
                    distinctAddSize,
                ),
            )
        }) {
            LoadResult.Error(it)
        }
    }

    override suspend fun loadFirstPageData(
        reqKey: ReqKey,
        dataKey: DataKey,
        isRefresh: Boolean,
        pageSize: Int,
    ): RefreshResult<PageKey> {
        var success = false
        var nextPageKey: PageKey? = null
        executeStatefulApiCall<SnapshotStateList<Data>, DataResponse>(
            updateState = { action ->
                setCState(dataKey, action(getCState(dataKey)))
            },
            isRefresh = isRefresh,
            apiCall = {
                getData(
                    reqKey = reqKey,
                    dataKey = dataKey,
                    pageKey = getFirstPageKey(dataKey),
                    pageSize = pageSize,
                )
            },
            transform = { response ->
                success = true
                nextPageKey =
                    getHasNextPageKeyFromResponse(
                        dataKey,
                        getFirstPageKey(dataKey),
                        response,
                        -1,
                    )
                val state = getCState(dataKey)
                if (state is CState.Success) {
                    state.data.apply {
                        clear()
                        addAll(getDataListFromResponse(dataKey, response))
                    }
                } else {
                    mutableStateListOf<Data>().apply {
                        addAll(getDataListFromResponse(dataKey, response))
                    }
                }
            },
        )
        return RefreshResult(success, nextPageKey)
    }
}

abstract class ListPaginateViewModel<ReqKey, PageKey : Any, Data, DataResponse> :
    BaseListPaginateViewModel<ReqKey, PageKey, Any, Data, DataResponse>() {
    protected val listCState = mutableStateOf<CState<SnapshotStateList<Data>>>(CState.Idle)

    override fun getCState(dataKey: Any): CState<SnapshotStateList<Data>> = listCState.value

    override fun setCState(
        dataKey: Any,
        cState: CState<SnapshotStateList<Data>>,
    ) {
        listCState.value = cState
    }

    fun getListState() = listCState.value

    suspend fun refreshList(
        reqKey: ReqKey,
        isRefresh: Boolean = true,
    ): RefreshResult<PageKey> =
        loadFirstPageData(
            reqKey = reqKey,
            dataKey = Unit,
            isRefresh = isRefresh,
        )

    suspend fun loadMoreTabDataList(
        reqKey: ReqKey,
        pageKey: PageKey?,
        dataEmptyCheck: Boolean = true,
        pageSize: Int = defaultPageSize,
    ): LoadResult<PageKey> = loadMoreData(reqKey, Unit, pageKey, dataEmptyCheck, pageSize)
}

abstract class TabListPaginateViewModel<ReqKey, PageKey : Any, TabKey, Data, DataResponse> :
    BaseListPaginateViewModel<ReqKey, PageKey, TabKey, Data, DataResponse>() {
    protected val tabListMap = mutableStateMapOf<TabKey, CState<SnapshotStateList<Data>>>()

    override fun getCState(tabKey: TabKey): CState<SnapshotStateList<Data>> = tabListMap.getOrPut(tabKey) { CState.Idle }

    override fun setCState(
        tabKey: TabKey,
        cState: CState<SnapshotStateList<Data>>,
    ) {
        tabListMap[tabKey] = cState
    }

    protected fun tryGetState(tabKey: TabKey) = tabListMap[tabKey]

    fun getListState(tabKey: TabKey) = tryGetState(tabKey) ?: CState.Idle

    suspend fun refreshTabList(
        reqKey: ReqKey,
        tabKey: TabKey,
        isRefresh: Boolean = true,
    ): RefreshResult<PageKey> =
        loadFirstPageData(
            reqKey = reqKey,
            dataKey = tabKey,
            isRefresh = isRefresh,
        )

    suspend fun loadMoreTabDataList(
        reqKey: ReqKey,
        tabKey: TabKey,
        pageKey: PageKey?,
        dataEmptyCheck: Boolean = true,
        pageSize: Int = defaultPageSize,
    ): LoadResult<PageKey> = loadMoreData(reqKey, tabKey, pageKey, dataEmptyCheck, pageSize)
}
