package com.buque.wakoo.rtc

import android.content.Context
import android.view.View
import androidx.compose.runtime.FloatState
import androidx.compose.runtime.MutableFloatState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf

fun interface IRtcEngineFactory {
    fun create(
        context: Context,
        handle: IRtcEventHandle,
    ): IRtcEngine
}

interface IRtcEngine {
    fun joinChannel(
        channelId: String,
        token: String,
        upMic: Boolean,
        muted: <PERSON><PERSON><PERSON>,
        speaker: <PERSON><PERSON><PERSON>,
    ): Boolean

    fun levelChannel(): Boolean

    fun upMic(token: String?): Boolean

    fun downMic(token: String?): Boolean

    fun muteLocalAudioStream(muted: Boolean): Boolean

    fun muteAllRemoteAudio(muted: Boolean): Boolean

    fun startPCMCapture()

    fun stopPCMCapture()

    fun setAudioCaptureVolume(volume: Int)

    fun startPlayingStream(
        uid: String,
        view: View?,
    ): Boolean

    fun startPlayingStreamByStreamId(
        streamId: String,
        view: View?,
    ): Boolean = startPlayingStream(streamId, view)

    fun stopPlayingStream(uid: String): Boolean

    fun stopPlayingStreamByStreamId(streamId: String): Boolean = stopPlayingStream(streamId)

    fun startVideoPreview(view: View): Boolean

    fun stopVideoPreview(): Boolean

    fun mutePublishStreamVideo(muted: Boolean): Boolean

    fun enableCamera(enabled: Boolean): Boolean

    fun useFrontCamera(front: Boolean): Boolean

    fun setEnableSpeakerphone(enabled: Boolean): Boolean

    fun muteRemoteAudioStream(
        uid: String,
        muted: Boolean,
    ): Boolean

    fun muteRemoteAudioStreamByStreamId(
        streamId: String,
        muted: Boolean,
    ): Boolean = muteRemoteAudioStream(streamId, muted)

    fun startSubscribingStream(): Boolean

    fun stopSubscribingStream(): Boolean

    fun isSpeakerphoneEnabled(): Boolean

    fun isLocalAudioMuted(): Boolean

    fun renewToken(
        channelId: String,
        token: String,
    ): Boolean

    fun generateStreamId(
        channelId: String,
        uid: String,
    ): String = "$channelId@@@$uid"

    fun parseUidByStreamId(streamId: String): String {
        if (streamId.contains("@@@")) {
            return streamId.split("@@@")[1]
        }
        return streamId
    }
}

interface IRtcEventHandle {
    fun onJoinChannelSuccess()

    fun onJoinChannelFailed()

    fun onClientRoleChanged(
        oldRole: Int,
        newRole: Int,
    )

    fun onClientRoleChangeFailed(
        reason: Int,
        currentRole: Int,
    )

    fun onUserJoined(
        uid: String,
        streamId: String,
    )

    fun onUserOffline(
        uid: String,
        streamId: String,
        reason: Int,
    )

    fun onStreamQualityUpdate(
        uid: String,
        streamId: String,
        videoRecvFPS: Double,
        videoRenderFPS: Double,
        audioRecvFPS: Double,
        audioRenderFPS: Double,
        level: Int,
    )

    fun onRemoteAudioStateChanged(
        uid: String,
        streamId: String,
        muted: Boolean,
    )

    fun onRemoteVideoStateChanged(
        uid: String,
        streamId: String,
        muted: Boolean,
    )

    fun onAudioVolumeIndication(speakers: Map<String, Float>)

    fun onPlayerRenderStreamFirstFrame(
        uid: String,
        streamId: String,
        video: Boolean,
    )

    fun onPlayerRenderCameraFirstFrame(
        uid: String,
        streamId: String,
    )

    fun onPublisherCapturedVideoFirstFrame(value: Int)

    fun onTokenPrivilegeWillExpire()
}

enum class RtcScenario {
    Room,
    OneToOneAudio,
    OneToOneVideo,
}

data class TencentRtcConfig(
    val quality: Int,
    val aecEnabled: Boolean,
    val agcEnabled: Boolean,
    val ansEnabled: Boolean,
)

data class VolumeInfo(
    val userId: String,
    val volume: Float,
)

class MutableMicState(
    val userId: String,
    var isInMic: Boolean,
) {
    internal val muteState: MutableState<Boolean> = mutableStateOf(false)

    internal val volumeState: MutableFloatState = mutableFloatStateOf(0f)

    val isMuted: Boolean
        get() = muteState.value

    val volume: Float
        get() = volumeState.floatValue

    fun getMuteState(): State<Boolean> = muteState

    fun getVolumeState(): FloatState = volumeState
}
