package com.buque.wakoo.app

import com.adjust.sdk.AdjustEvent

object Const {
    object KVKey {
        const val FIRST_ENTER_PUBLISH = "first_enter_publish"

        const val FIRST_ENTER_VOICE_FEED = "first_enter_voice_feed"

        const val APP_STARTUP_COUNT = "app_startup_count"

        const val APP_RATING_COMPLETE = "app_rating_complete"

        const val HAS_REQUESTED_MEDIA_PERMISSION = "has_requested_media_permission"

        const val FROM_MESSAGE_NOTIFICATION = "from_message_notification"

        const val BLOCK_ROOM_GIFT_EFFECT = "block_room_gift_effect"
    }

    object RoomInfoChangeKey {
        const val TITLE = "title"
        const val DESC = "desc"
        const val TAGS = "tags"
        const val ROOM_MODE = "room_mode"
        const val MIC_MODE = "occupy_mic_mode"
        const val ROOM_LOCK = "room_locked"
        const val ROOM_BACKGROUND = "room_background"
        const val GAME_START = "game_start"
        const val NOTICE = "notice"
    }

    object SavedState {
        const val DATA = "SavedState_data"
        const val LIVE_ROOM = "SavedState_LIVE_ROOM"
    }

    const val WEB_DEBUG = false
    const val LOCAL_WEB_URL = "http://192.168.10.41:5173/"

    object BusinessCode {
        val hongbao = "0401000"
    }

    @Suppress("ktlint:standard:property-naming")
    object AdjustEvent {
        //  注册成功
        val 注册成功
            get() = AdjustEvent("3r9k1e")

        // 其余是服务端下发
    }
}
