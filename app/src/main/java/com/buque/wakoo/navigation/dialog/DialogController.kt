package com.buque.wakoo.navigation.dialog

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveableStateHolder
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.util.Predicate
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.createSavedStateHandle
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.delegate
import com.buque.wakoo.manager.AppManager
import com.buque.wakoo.ui.dialog.AnimatedDialog
import com.buque.wakoo.ui.dialog.AnimatedDialogState
import com.buque.wakoo.ui.dialog.AnimatedWidget
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.job
import kotlinx.coroutines.launch

/**
 * 内部用于表示一个当前活跃（正在显示）的Dialog实例。
 */
internal data class ActiveDialog(
    val id: Int,
    val destination: DialogDestination,
    val resultDeferred: CompletableDeferred<Any?>?,
    val showState: MutableState<Boolean> = mutableStateOf(true),
) {
    // 是否有dismiss标记，这种弹窗是真正意义上关闭
    val dismissFlag
        get() = dialogState.dismissFlag

    val dialogState = AnimatedDialogState()
}

/**
 * `DialogHandle`的内部实现。
 * 它持有对`DialogController`的引用，以便将操作委托回去。
 */
private class DialogHandleImpl(
    override val id: Int,
    private val resultDeferred: CompletableDeferred<Any?>,
    override val dialogController: DialogController,
) : DialogHandle {
    override suspend fun await(): Any? = resultDeferred.await()

    override fun dismiss(
        result: Any?,
        tag: String?,
    ) {
        dialogController.dismiss(id, result, tag)
    }

    override fun sendEvent(
        event: DialogEvent,
        tag: String?,
    ) {
        dialogController.sendEvent(id, event, tag)
    }
}

/**
 * Dialog控制器，负责管理所有Dialog的生命周期、状态和结果传递。
 * 通常以ViewModel的形式存在，并通过Hilt/Koin等进行注入。
 */
class DialogController(
    private val singleMode: Boolean,
    private val handle: SavedStateHandle,
) : ViewModel() {
    companion object {
        val preview: DialogHandle
            @SuppressLint("RestrictedApi")
            get() =
                DialogHandleImpl(
                    id = 0,
                    resultDeferred = CompletableDeferred<Any?>(),
                    dialogController =
                        DialogController(
                            singleMode = false,
                            handle = SavedStateHandle.createHandle(null, null),
                        ),
                )
    }

    private val eventChannel = Channel<DialogEventWrapper>(Channel.BUFFERED)
    val events: Flow<DialogEventWrapper> = eventChannel.receiveAsFlow()

    private var nextId by handle.delegate("dialog_controller_id_counter", 0)

    private var dialogUUID: String = AppManager.activeUUID

    @OptIn(SavedStateHandleSaveableApi::class)
    private val activeDialogs: MutableList<ActiveDialog> by handle.saveable(
        saver = createDialogListSaver(singleMode),
    ) {
        mutableStateListOf()
    }

    override fun onCleared() {
        eventChannel.close()
        // 取消所有仍在等待的协程，以防内存泄漏
        activeDialogs.forEach { it.resultDeferred?.cancel() }
        activeDialogs.clear()
        super.onCleared()
    }

    /**
     * 提交显示一个Dialog, 不管返回值
     *
     * @param destination 要显示的Dialog定义。
     *
     */
    fun post(destination: DialogDestination) {
        viewModelScope.launch {
            val id = nextId++
            addActiveDialog(ActiveDialog(id, destination, null))
        }
    }

    /**
     * 大部分弹窗不需要这个特性
     * 显示一个可还原的Dialog(进程重建)，并立即返回一个可用于控制该Dialog的句柄。
     * 可能会返回一个[kotlin.coroutines.cancellation.CancellationException]
     * 注意进程重建的情况下，只能通过event获取事件和返回值, 因为[DialogHandle]不存在了，这个是无法还原的
     *
     * @param destination 要显示的Dialog定义。
     * @return 一个`DialogHandle`实例，可用于等待结果或从外部关闭Dialog。
     */
    suspend fun showRestorable(destination: DialogDestination.Restorable): DialogHandle = show(destination)

    /**
     * 显示一个Dialog，并立即返回一个可用于控制该Dialog的句柄。
     * 可能会返回一个[kotlin.coroutines.cancellation.CancellationException]
     *
     * @param destination 要显示的Dialog定义。
     * @return 一个`DialogHandle`实例，可用于等待结果或从外部关闭Dialog。
     */
    suspend fun show(destination: DialogDestination): DialogHandle =
        viewModelScope
            .async {
                val id = nextId++
                val resultDeferred = CompletableDeferred<Any?>(parent = viewModelScope.coroutineContext.job)
                addActiveDialog(ActiveDialog(id, destination, resultDeferred))
                // 创建Handle实现时，把controller自身(this)传进去，建立连接
                DialogHandleImpl(id, resultDeferred, this@DialogController)
            }.await()

    /**
     * [便利方法] 显示一个Dialog并挂起，直到它关闭并返回结果。
     *
     * @param destination 要显示的Dialog定义。
     * @return Dialog关闭时返回的结果，如果Dialog被取消则可能为null。
     */
    suspend fun showForResult(destination: DialogDestination): Any? = show(destination).await()

    /**
     * 关闭一个指定ID的Dialog。
     * 主要由`DialogHandle`和`DialogScope`内部调用。
     */
    fun dismiss(
        id: Int,
        result: Any?,
        tag: String? = null,
    ) {
        dismissAndSendResult(id, result, tag)
    }

    fun sendEvent(
        id: Int,
        event: DialogEvent,
        tag: String? = null,
    ) {
        viewModelScope.launch {
            eventChannel.send(DialogEventWrapper(id, tag, event))
        }
    }

    /**
     * 一个Composable函数，负责将当前所有活跃的Dialog渲染到UI上。
     * 应在你的UI树的顶层（如Scaffold内）调用一次。
     */
    @Composable
    fun <ParamsScope : Any> RenderDialogs(paramsScope: ParamsScope) {
        if (dialogUUID != AppManager.activeUUID) { // 说明可能是配置发生改变activity重建，viewModel没有重建
            dialogUUID = AppManager.activeUUID
            val newDialogs =
                activeDialogs.run {
                    val lastIndex = lastIndex
                    filterIndexed { index, it ->
                        it.destination.needKeepInConfigChange.also { show ->
                            if (show) {
                                it.dialogState.reset()
                                it.dialogState.disableEnterAnim = !singleMode || index == lastIndex
                                it.showState.value = !singleMode || index == lastIndex
                            }
                        }
                    }
                }

            activeDialogs.clear()
            activeDialogs.addAll(newDialogs)
        }

        val saveableStateHolder = rememberSaveableStateHolder()

        activeDialogs.forEach { activeDialog ->
            // 使用id作为key，确保在列表变化时scope能正确创建和记忆
            if (singleMode && !activeDialog.showState.value) {
                return@forEach
            }
            key(activeDialog.id) {
                val scope =
                    remember(activeDialog.id) {
                        DialogScopeImpl(activeDialog.id)
                    }

                if (!activeDialog.destination.dialogProperties.useSystemDialog) {
                    AnimatedWidget(
                        state = activeDialog.dialogState,
                        onDismissRequest = {
                            viewModelScope.launch(start = CoroutineStart.UNDISPATCHED) {
                                if (!activeDialog.dialogState.shouldRemoved) {
                                    activeDialog.showState.value = false
                                } else {
                                    activeDialog.dialogState.dismissFlag = true
                                    removeActiveDialog(activeDialog)
                                }
                            }
                        },
                        properties = activeDialog.destination.dialogProperties,
                    ) {
                        activeDialog.destination.content.invoke(scope, paramsScope)
                    }
                } else if (!activeDialog.destination.dialogProperties.useCustomAnimation) {
                    Dialog(
                        onDismissRequest = {
                            // 当用户点击外部或按返回键关闭Dialog时
                            dismissAndSendResult(activeDialog.id, null, null)
                        },
                        properties =
                            DialogProperties(
                                dismissOnBackPress = activeDialog.destination.dialogProperties.dismissOnBackPress,
                                dismissOnClickOutside = activeDialog.destination.dialogProperties.dismissOnClickOutside,
                            ),
                    ) {
                        saveableStateHolder.SaveableStateProvider(activeDialog.id) {
                            activeDialog.destination.content.invoke(scope, paramsScope)
                        }
                    }
                } else {
                    AnimatedDialog(
                        state = activeDialog.dialogState,
                        onDismiss = {
                            viewModelScope.launch(start = CoroutineStart.UNDISPATCHED) {
                                if (!activeDialog.dialogState.shouldRemoved) {
                                    activeDialog.showState.value = false
                                } else {
                                    activeDialog.dialogState.dismissFlag = true
                                    removeActiveDialog(activeDialog)
                                }
                            }
                        },
                        properties = activeDialog.destination.dialogProperties,
                    ) {
                        saveableStateHolder.SaveableStateProvider(activeDialog.id) {
                            activeDialog.destination.content.invoke(scope, paramsScope)
                        }
                    }
                }
            }
        }

        DisposableEffect(Unit) {
            onDispose {
                // 页面退出，比如进入一个新页面，当前页面会销毁，再按返回键则恢复弹窗
                activeDialogs.apply {
                    val lastIndex = lastIndex
                    forEachIndexed { index, it ->
                        it.dialogState.disableEnterAnim = !singleMode || index == lastIndex
                    }
                }
            }
        }
    }

    private fun dismissAndSendResult(
        id: Int,
        result: Any?,
        tag: String?,
    ) {
        viewModelScope.launch(start = CoroutineStart.UNDISPATCHED) {
            val item = activeDialogs.find { it.id == id }
            if (item != null) {
                if (!item.destination.dialogProperties.useCustomAnimation) {
                    item.dialogState.dismissFlag = true
                    removeActiveDialog(item)
                } else {
                    if (item.showState.value) {
                        item.dialogState.dismiss()
                        activeDialogs.getOrNull(activeDialogs.lastIndex - 1)?.takeIf { !it.showState.value }?.also {
                            it.dialogState.reset()
                            it.showState.value = true
                        }
                    } else {
                        removeActiveDialog(item)
                    }
                }
                // 通过Deferred返回结果，不一定能收到如进程重建后
                item.resultDeferred?.complete(result)
                // （如进程重建后），也发送事件。
                eventChannel.trySend(DialogEventWrapper(id, tag, DialogResultEvent(result)))
            }
        }
    }

    fun dismiss(predicate: Predicate<DialogDestination>) {
        viewModelScope.launch(start = CoroutineStart.UNDISPATCHED) {
            val items = activeDialogs.filter { predicate.test(it.destination) }
            items.forEach { item ->
                if (!item.destination.dialogProperties.useCustomAnimation) {
                    item.dialogState.dismissFlag = true
                    removeActiveDialog(item)
                } else {
                    if (item.showState.value) {
                        item.dialogState.dismiss()
                        removeActiveDialog(item)
                    } else {
                        removeActiveDialog(item)
                    }
                }
            }
        }
    }

    private fun addActiveDialog(dialog: ActiveDialog) {
        if (singleMode) {
            val item = activeDialogs.lastOrNull()
            if (item != null) {
                if (!item.destination.dialogProperties.useCustomAnimation) {
                    item.showState.value = false
                } else {
                    item.dialogState.apply {
                        shouldRemoved = false
                        isActiveClose = true
                    }
                }
            }
        }
        activeDialogs.add(dialog)
    }

    private fun removeActiveDialog(dialog: ActiveDialog) {
        if (activeDialogs.remove(dialog) && singleMode) {
            activeDialogs.lastOrNull()?.takeIf { !it.showState.value }?.also {
                it.dialogState.reset()
                it.showState.value = true
            }
        }
    }

    private inner class DialogScopeImpl(
        override val id: Int,
    ) : DialogScope {
        override val dialogController: DialogController
            get() = this@DialogController

        override fun dismiss(
            result: Any?,
            tag: String?,
        ) {
            <EMAIL>(id, result, tag)
        }

        override fun sendEvent(
            event: DialogEvent,
            tag: String?,
        ) {
            <EMAIL>(id, event, tag)
        }
    }
}

@SuppressLint("RestrictedApi")
@Composable
fun rememberDialogController(
    render: Boolean = true,
    key: String? = null,
    singleMode: Boolean = false,
): DialogController =
    if (LocalInspectionMode.current) {
        remember {
            DialogController(singleMode, SavedStateHandle.createHandle(null, null))
        }.apply {
            if (render) {
                RenderDialogs(Unit)
            }
        }
    } else {
        viewModel<DialogController>(key = key) {
            DialogController(singleMode, createSavedStateHandle())
        }.apply {
            if (render) {
                RenderDialogs(Unit)
            }
        }
    }

@SuppressLint("RestrictedApi")
@Composable
fun <ParamsScope : Any> rememberDialogControllerWithParamsScope(
    paramsScope: ParamsScope,
    singleMode: Boolean = false,
    key: String? = null,
): DialogController =
    if (LocalInspectionMode.current) {
        remember {
            DialogController(singleMode, SavedStateHandle.createHandle(null, null))
        }.apply {
            RenderDialogs(paramsScope)
        }
    } else {
        viewModel<DialogController>(key = key) {
            DialogController(singleMode, createSavedStateHandle())
        }.apply {
            RenderDialogs(paramsScope)
        }
    }
