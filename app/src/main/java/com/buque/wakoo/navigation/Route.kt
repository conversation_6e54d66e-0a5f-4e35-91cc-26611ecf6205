package com.buque.wakoo.navigation

import android.net.Uri
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.InputEdit
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.im.utils.asInstance
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.screens.liveroom.screen.GuideInfo
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.data.common.SelectorMediaType
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerAlbum
import com.buque.wakoo.utils.LogUtils
import kotlinx.serialization.Serializable

/**
 * app link格式为 wakoo://page/${path}?params
 * path为类名小写加_格式，参数也是这个格式
 * 有沟槽参数的需要添加companion object，及其fromUri方法
 */
object Route {
    @Serializable
    data class Fallback(
        val unknownKey: String,
    ) : AppNavKey

    @Serializable
    data object Login : AppNavKey {
        override val isRequiresLogin: Boolean = false
    }

    @Serializable
    data class Web(
        val url: String,
        val title: String? = null,
        override val isRequiresLogin: Boolean = true,
    ) : AppNavKey

    @Serializable
    data object Home : AppNavKey

    @Serializable
    data object VoicePublish : AppNavKey

    @Serializable
    data class MediaSelector(
        val mediaType: SelectorMediaType = SelectorMediaType.IMAGE,
        val maxSelectCount: Int = 1,
        val selectedItem: List<MediaItem>? = null,
    ) : AppResultNavKey() {
        override val isRequiresLogin: Boolean = false
    }

    /**
     * @param type
     * 1	用户
     * 2	语音房
     * 3	私密小屋
     * 4	瞬间
     * 5	声音日记
     * 6	直播间
     * 7	部落
     */
    @Serializable
    data class Report(
        val type: Int,
        val targetId: String,
    ) : AppNavKey {
        companion object {
            fun fromUri(uri: Uri): Report {
                val type = uri.getQueryParameter("type")?.asInstance<Int>() ?: 0
                val targetId = uri.getQueryParameter("target_id") ?: "0"
                LogUtils.i("report=>type:$type,targetId:$targetId")
                return Report(type, targetId)
            }
        }
    }

    @Serializable
    data object Settings : AppNavKey

    @Serializable
    data object BlackList : AppNavKey

    @Serializable
    data object Feedback : AppNavKey

    @Serializable
    data object AboutApp : AppNavKey

    @Serializable
    data class Chat(
        val user: BasicUser,
    ) : AppNavKey

    // 充值
    @Serializable
    data object Recharge : AppNavKey

    // 充值记录
    @Serializable
    data object RechargeRecord : AppNavKey

    // 会员
    @Serializable
    data object Member : AppNavKey

    @Serializable
    data object Debug : AppNavKey

    @Serializable
    data class LiveRoom(
        val basicInfo: BasicRoomInfo,
    ) : AppNavKey {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as LiveRoom

            return basicInfo.id == other.basicInfo.id
        }

        override fun hashCode(): Int = basicInfo.id.hashCode()
    }

    @Serializable
    data class InviteToPrivateRoom(
        val info: GuideInfo,
        val firstEnter: Boolean,
    ) : AppNavKey

    @Serializable
    data class SelectUser(
        val sceneType: Int,
        val sceneID: String,
        val selectFor: Int = SELECT_FOR_SHARE_ROOM,
        val title: String = "邀请加入房间".localized,
        val buttonText: String = "分享".localized,
    ) : AppNavKey {
        companion object {
            val SELECT_FOR_SHARE_ROOM = 0
            val SELECT_FOR_INVITE_GROUP = 1
        }
    }

    // 群组
    @Serializable
    data class ChatGroup(
        val groupId: String,
    ) : AppNavKey

    @Serializable
    data object ChatGroupSquare : AppNavKey

    // 装扮商城 简单Tab页
    @Serializable
    data object DressUpSample : AppNavKey

    // 装扮商城 - 列表
    @Serializable
    data class DressUpShopList(
        val type: Int = 1,
        val title: String = "装扮商城".localized,
    ) : AppNavKey

    // 我的装扮
    @Serializable
    data class DressUpMine(
        val initialTab: Int = -1,
    ) : AppNavKey

    /**
     * 用户搜索页面
     */
    @Serializable
    data object UserSearch : AppNavKey

    @Serializable
    data object UserAlbum : AppNavKey

    data class ChatGroupDetail(
        val groupId: String,
    ) : AppNavKey

    @Serializable
    data class BoostPage(
        val type: Int,
    ) : AppNavKey

    @Serializable
    object PointsRecord: AppNavKey

    @Serializable
    object JaDiamondRecord: AppNavKey

    //region 用户资料
    @Serializable
    data class UserProfile(
        val user: BasicUser,
    ) : AppNavKey

    @Serializable
    data object EditUserInfo : AppNavKey

    @Serializable
    data class EditTextUserInfo(
        val edit: InputEdit,
    ) : AppNavKey

    @Serializable
    data class UserRelations(
        val initIndex: Int,
        val userId: String,
    ) : AppNavKey

    @Serializable
    data object UserProfileVoiceEdit : AppNavKey
    //endregion

    //region 瞬间
    @Serializable
    data object MomentPublish : AppNavKey

    @Serializable
    data class MomentList(
        val userId: String,
    ) : AppNavKey
    //endregion

    //region 通用
    @Serializable
    class LocationSelector : AppResultNavKey()
    //endregion
}

object LoginRoute {
    @Serializable
    data object Start : AppNavKey

    @Serializable
    data object Phone : AppNavKey

    @Serializable
    data object Fill : AppNavKey
}

sealed interface HomeBottomNavKey : BottomNavKey<HomeNavTab>

object HomeTabRoute {
    @Serializable
    data object Home : HomeBottomNavKey {
        override val tab: HomeNavTab = HomeNavTab.Home
    }

    @Serializable
    data object Task : HomeBottomNavKey {
        override val tab: HomeNavTab = HomeNavTab.Task
    }

    @Serializable
    data object Discover : HomeBottomNavKey {
        override val tab: HomeNavTab = HomeNavTab.Discover
    }

    @Serializable
    data object Message : HomeBottomNavKey {
        override val tab: HomeNavTab = HomeNavTab.Message
    }

    @Serializable
    data object Mine : HomeBottomNavKey {
        override val tab: HomeNavTab = HomeNavTab.Mine
    }
}

object VoicePublishRoute {
    @Serializable
    data object Edit : AppNavKey

    @Serializable
    data class Preview(
        val item: VoiceCardItem,
        val playItem: PlayMediaItem,
    ) : AppNavKey
}

object LiveRoomRoute {
    @Serializable
    data object Home : AppNavKey

    @Serializable
    data object BlackList : AppNavKey

    @Serializable
    data object AdminList : AppNavKey
}

object ChatGroupRoute {
    @Serializable
    data object Home : AppNavKey

    @Serializable
    data class ChatGroupDetail(
        val groupId: String,
    ) : AppNavKey

    /**
     * 加入群组申请列表
     */
    @Serializable
    data class ChatGroupApplyJoinList(
        val chatGroupId: String,
    ) : AppNavKey

    // 群组设置
    @Serializable
    data class GroupSettings(
        val chatGroupId: String,
    ) : AppNavKey

    // 设置管理员
    @Serializable
    data class ChatGroupAdminSetting(
        val groupId: String,
    ) : AppNavKey

    // 添加管理员
    @Serializable
    data class ChatGroupAdminIncrease(
        val groupId: String,
        val maxCount: Int,
    ) : AppNavKey

    // 踢出成员
    @Serializable
    data class KickOutMember(
        val groupId: String,
        val targetRoles: String,
    ) : AppNavKey
}

@Serializable
data class GalleryScreenKey(
    val album: MediaViewerAlbum,
) : AppNavKey
