package com.buque.wakoo.db

import androidx.room.Dao
import androidx.room.Database
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.RoomDatabase
import androidx.room.Transaction
import com.buque.wakoo.bean.user.BasicUser

@Dao
interface UserDao {
    @Query("SELECT * FROM users")
    suspend fun getAll(): List<BasicUser>

    @Query("SELECT * FROM users WHERE userid = (:userId)")
    suspend fun getUserById(userId: String): BasicUser?

    @Transaction
    @Query("SELECT * FROM users WHERE userid IN (:userIds)")
    fun loadAllByIds(userIds: IntArray): List<BasicUser>

    @Transaction
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(vararg users: BasicUser)

    @Delete
    suspend fun delete(user: BasicUser)
}

@Database(entities = [BasicUser::class], version = 1)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
}
