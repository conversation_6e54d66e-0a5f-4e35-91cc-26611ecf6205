package com.buque.wakoo.ext

import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Process
import com.buque.wakoo.WakooApplication

private val mainHandler = Handler(Looper.getMainLooper())

/**
 * 是否是主线程
 */
val isMainThread: Boolean
    get() = Looper.myLooper() == Looper.getMainLooper()

fun runInMainThread(action: () -> Unit) {
    if (isMainThread) {
        action()
    } else {
        mainHandler.post(action)
    }
}

fun isX86Device(): Bo<PERSON>an {
    // Build.SUPPORTED_ABIS 是一个 String 数组，包含了设备支持的所有 ABI，
    // 例如 ["x86_64", "armeabi-v7a", "armeabi"]
    val supportedAbis = Build.SUPPORTED_ABIS
    for (abi in supportedAbis) {
        if (abi.startsWith("x86")) {
            return true
        }
    }
    return false
}

fun restartApp() {
    WakooApplication.instance.apply {
        packageManager
            .getLaunchIntentForPackage(packageName)
            ?.component
            ?.let {
                Intent.makeRestartActivityTask(it)
            }?.also {
                startActivity(it)
            }
        Process.killProcess(Process.myPid())
    }
}
