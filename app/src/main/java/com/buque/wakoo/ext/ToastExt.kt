package com.buque.wakoo.ext

import NotificationDuration
import NotificationType
import createNotification

fun showToast(
    message: String?,
    title: String? = null,
    duration: NotificationDuration = NotificationDuration.SHORT,
) {
    if (!message.isNullOrBlank()) {
        runInMainThread {
            val notification = createNotification(NotificationType.TOAST)
            notification.show(message, title, duration)
        }
    }
}

fun showLongToast(
    message: String,
    title: String? = null,
) {
    showToast(message, title, NotificationDuration.LONG)
}

fun Throwable.toast() {
    this.message?.let {
        showToast(it)
    }
}

fun <T> Result<T>.toastWhenError() = this
