package com.buque.wakoo.ext

import android.content.Context
import android.content.Intent
import android.view.inputmethod.InputMethodManager
import com.buque.wakoo.MainActivity

/**
 * 返回桌面
 */
fun Context.backToHome() {
    if (this is MainActivity) {
        try {
            val homeIntent = Intent(Intent.ACTION_MAIN)
            homeIntent.addCategory(Intent.CATEGORY_HOME)
            homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(homeIntent)
        } catch (e: Exception) {
            this.finish()
        }
    }
}

fun Context.hasInputMethod(): Boolean {
    // 1. 获取 InputMethodManager 服务
    val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager

    // 如果服务为空，则无法判断，通常返回 false
    if (imm == null) {
        return false
    }

    // 2. 获取当前已启用的输入法列表
    // getEnabledInputMethodList() 返回一个 InputMethodInfo 对象的列表
    val enabledInputMethods = imm.enabledInputMethodList

    // 3. 判断列表是否为空
    // 如果列表不为空，则表示至少有一个输入法可用
    return enabledInputMethods.isNotEmpty()
}
