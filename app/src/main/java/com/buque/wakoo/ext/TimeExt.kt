package com.buque.wakoo.ext

import java.util.Calendar
import java.util.TimeZone

fun TimeZone.getTimeZoneNum(): Int =
    runCatching {
        getDisplayName(false, TimeZone.SHORT)
            .replace("GMT", "")
            .split(":")[0]
            .replace("+", "")
            .toInt()
    }.getOrNull() ?: 0

fun Long.getDaysBetweenTimestamps(destionTS: Long): Long {
    val cal1: Calendar = Calendar.getInstance()
    cal1.setTimeInMillis(this)
    setMidnight(cal1) // 归一化到午夜

    val cal2: Calendar = Calendar.getInstance()
    cal2.setTimeInMillis(destionTS)
    setMidnight(cal2) // 归一化到午夜

    val millisDiff: Long = Math.abs(cal2.getTimeInMillis() - cal1.getTimeInMillis())
    return millisDiff / (24 * 60 * 60 * 1000) // 转换为天数
}

private fun setMidnight(cal: Calendar) {
    cal.set(Calendar.HOUR_OF_DAY, 0)
    cal.set(Calendar.MINUTE, 0)
    cal.set(Calendar.SECOND, 0)
    cal.set(Calendar.MILLISECOND, 0)
}
