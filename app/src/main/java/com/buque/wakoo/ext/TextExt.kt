package com.buque.wakoo.ext

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.movableContentOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp


data class SpanClickItem(
    val tag: String,
    val router: String,
)

@Immutable
class TextSpan private constructor(
    val text: AnnotatedString,
    val inlineContentList: List<IInlineTextContent>? = null,
    val clickTags: List<SpanClickItem>? = null,
) {
    class Builder(private val builder: AnnotatedString.Builder) : Appendable by builder {

        private val inlineTextStyles: MutableList<IInlineTextContent> = mutableListOf()

        private val clickTags: MutableList<SpanClickItem> = mutableListOf()

        fun appendInlineContent(inlineTextContent: IInlineTextContent) {
            builder.appendInlineContent(inlineTextContent)
            inlineTextStyles.add(inlineTextContent)
        }

        fun appendInlineContentList(inlineTextContentList: List<IInlineTextContent>) {
            for (item in inlineTextContentList) {
                builder.appendInlineContent(item)
            }
            inlineTextStyles.addAll(inlineTextContentList)
        }

        fun clickable(item: SpanClickItem) {
            clickTags.add(item)
        }

        fun toTextSpan(): TextSpan {
            return TextSpan(
                builder.toAnnotatedString(),
                inlineTextStyles.toList(),
                clickTags.toList(),
            )
        }
    }

}

private fun AnnotatedString.Builder.appendInlineContent(inlineTextContent: IInlineTextContent) {
    val alternateText = inlineTextContent.alternateText
    if (alternateText != null) {
        appendInlineContent(inlineTextContent.key, alternateText)
    } else {
        appendInlineContent(inlineTextContent.key)
    }
}

inline fun AnnotatedString.Builder.color(color: Color, builder: (AnnotatedString.Builder).() -> Unit) {
    withStyle(SpanStyle(color = color)) {
        builder()
    }
}

inline fun buildTextSpan(builder: (AnnotatedString.Builder).(TextSpan.Builder) -> Unit): TextSpan {
    return AnnotatedString.Builder().run {
        TextSpan.Builder(this).also {
            builder(it)
        }.toTextSpan()
    }
}

/**
 * 提前不知道尺寸
 */
fun inlineTextContent(
    key: String,
    alternateText: String? = null,
    paddingValues: PaddingValues = PaddingValues(0.dp),
    content: @Composable (Modifier) -> Unit,
): IInlineTextContent {
    return InlineTextContentPreMeasureImpl(key, alternateText, paddingValues, content)
}

/**
 * 提前知道尺寸
 */
fun inlineTextContent(
    key: String,
    density: Density,
    width: Int,
    height: Int = width,
    alternateText: String? = null,
    paddingValues: PaddingValues = PaddingValues(0.dp),
    content: @Composable (Modifier) -> Unit,
): IInlineTextContent {
    return object : IInlineTextContent {

        override val key = key

        override val alternateText = alternateText

        override val inlineTextContent: InlineTextContent = InlineTextContent(
            with(density) {
                Placeholder(
                    width = paddingValues.let {
                        it.calculateLeftPadding(LayoutDirection.Ltr).plus(it.calculateRightPadding(LayoutDirection.Ltr))
                    }.plus(width.dp).toSp(),
                    height = paddingValues.let {
                        it.calculateTopPadding().plus(it.calculateBottomPadding())
                    }.plus(height.dp).toSp(),
                    placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                )
            }
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                content(Modifier.fillMaxSize())
            }
        }
    }
}

interface IInlineTextContent {

    val key: String

    val alternateText: String?

    val inlineTextContent: InlineTextContent
}

interface IPreMeasure {


    val measured: Boolean

    @Composable
    fun preMeasureContent()

    @Composable
    fun applyDpSize(dpSize: DpSize): InlineTextContent
}

@Stable
private class InlineTextContentPreMeasureImpl(
    override val key: String,
    override val alternateText: String?,
    private val paddingValues: PaddingValues,
    content: @Composable (Modifier) -> Unit,
) : IInlineTextContent, IPreMeasure {

    private val children = movableContentOf(content)

    override var measured = false

    override lateinit var inlineTextContent: InlineTextContent
        private set

    @Composable
    override fun preMeasureContent() {
        if (measured) {
            return
        }
        children(Modifier)
    }

    @Composable
    override fun applyDpSize(dpSize: DpSize): InlineTextContent {
        if (measured) {
            return inlineTextContent
        }
        measured = true
        inlineTextContent = with(LocalDensity.current) {
            val width = paddingValues.let {
                it.calculateLeftPadding(LayoutDirection.Ltr).plus(it.calculateRightPadding(LayoutDirection.Ltr))
            }.plus(dpSize.width).toSp()

            val height = paddingValues.let {
                it.calculateTopPadding().plus(it.calculateBottomPadding())
            }.plus(dpSize.height).toSp()

            InlineTextContent(
                Placeholder(
                    width = width,
                    height = height,
                    placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                )
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    contentAlignment = Alignment.Center
                ) {
                    children(Modifier.fillMaxSize())
                }
            }
        }
        return inlineTextContent
    }
}

