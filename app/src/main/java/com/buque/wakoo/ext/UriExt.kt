package com.buque.wakoo.ext

import android.annotation.SuppressLint
import android.content.ContentResolver
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.ImageDecoder
import android.graphics.drawable.AnimatedImageDrawable
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.provider.OpenableColumns
import com.buque.wakoo.bean.ImageInfo
import java.io.File
import java.io.IOException
import java.io.InputStream

/**
 * 从 Uri 中获取原始文件名。
 *
 * 这个函数能处理 content:// 和 file:// 类型的 Uri。
 * 对于 content URI，它会查询 ContentResolver 来获取显示名称。
 * 对于 file URI，它会直接从路径中提取最后一部分。
 *
 * @param context 用于访问 ContentResolver 的上下文。
 * @return 返回文件名（包含扩展名），如果无法获取则返回 null。
 */
@SuppressLint("Range")
fun Uri.getOriginalFileName(context: Context): String? =
    when (scheme) {
        "content" -> {
            var fileName: String? = null
            // 使用 ContentResolver 查询文件名
            context.contentResolver.query(this, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    // 获取 DISPLAY_NAME 列的值
                    fileName = cursor.getString(cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME))
                }
            }
            fileName
        }

        "file" -> {
            // 直接从路径中获取最后一部分
            this.path?.let { path ->
                File(path).name
            }
        }

        else -> null
    }

fun Uri.getImageInfo(context: Context): ImageInfo? {
    val uri: Uri = this
    val contentResolver: ContentResolver = context.contentResolver
    var inputStream: InputStream? = null
    var pfd: ParcelFileDescriptor? = null

    try {
        // 获取宽高
        inputStream = contentResolver.openInputStream(uri)
        val options = BitmapFactory.Options().apply { inJustDecodeBounds = true }
        BitmapFactory.decodeStream(inputStream, null, options)
        val width = options.outWidth
        val height = options.outHeight

        // 获取大小
        pfd = contentResolver.openFileDescriptor(uri, "r")
        val size = pfd?.statSize ?: -1L

        if (width > 0 && height > 0 && size > -1L) {
            return ImageInfo(width, height, size)
        }
    } catch (e: IOException) {
        e.printStackTrace()
    } finally {
        inputStream?.close()
        pfd?.close()
    }

    return null
}

/**
 * 精准判断一个 Uri 指向的是否为动图。
 * 推荐在IO线程中执行此操作。
 *
 * @param contentResolver ContentResolver 实例
 * @param uri 要检查的图片的 Uri
 * @return 如果是动图（GIF, aPNG, aWEBP, aHEIF）则返回 true，否则返回 false。
 */
fun isAnimated(
    contentResolver: ContentResolver?,
    uri: Uri,
): Boolean {
    contentResolver ?: return false
    // 仅在 Android P (API 28)及以上版本可用
    var source: ImageDecoder.Source? = null
    try {
        source = ImageDecoder.createSource(contentResolver, uri)
        // 解码并获取 Drawable，但我们只关心它的类型，不需要渲染它
        val drawable = ImageDecoder.decodeDrawable(source)
        // AnimatedImageDrawable 是所有动画格式（GIF, aWEBP等）在解码后的通用类型
        return drawable is AnimatedImageDrawable
    } catch (e: Exception) {
        // 处理解码异常
        e.printStackTrace()
        return false
    }
}
