package com.buque.wakoo.utils.upload

import android.content.Context
import android.net.Uri
import android.os.Build
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import com.buque.wakoo.ext.getOriginalFileName
import com.buque.wakoo.ui.widget.media.data.common.SelectorMediaType
import com.buque.wakoo.utils.upload.UploadUtils.legacyMultipleContract
import com.buque.wakoo.utils.upload.UploadUtils.legacySingleContract
import com.buque.wakoo.utils.upload.UploadUtils.multipleContract
import com.buque.wakoo.utils.upload.UploadUtils.singleContract
import kotlinx.datetime.TimeZone
import kotlinx.datetime.number
import kotlinx.datetime.toLocalDateTime
import java.io.File
import kotlin.random.Random
import kotlin.time.Clock
import kotlin.time.ExperimentalTime
import kotlin.time.Instant

object UploadUtils {
    // 根据配置选择 Contract
    val singleContract = ActivityResultContracts.PickVisualMedia()
    val multipleContract = ActivityResultContracts.PickMultipleVisualMedia()
    val legacySingleContract = ActivityResultContracts.GetContent()
    val legacyMultipleContract = ActivityResultContracts.GetMultipleContents()

    const val DEFAULT_AVATAR_PATH = "avatar"
    const val DEFAULT_VOICE_PATH = "voice"
    const val DEFAULT_REPORT_PATH = "report"
    const val DEFAULT_ALBUM_PATH = "album"
    const val DEFAULT_MOMENT_PATH = "album"

    fun generateOSSPath(
        context: Context,
        uri: Uri,
        prefix: String = "",
    ): String {
        val originalFileName = uri.getOriginalFileName(context) ?: "unknown_file"
        return generateFromOriginalName(originalFileName, prefix)
    }

    fun generateOSSPath(
        file: File,
        prefix: String = "",
    ): String = generateFromOriginalName(file.name, prefix)

    fun generateOSSPath(
        filePath: String,
        prefix: String = "",
    ): String = generateFromOriginalName(File(filePath).name, prefix)

    /**
     * 内部核心逻辑，从一个已知的原始文件名生成最终的对象键。
     * 所有 public 方法都最终调用此函数，实现了逻辑复用。
     */
    @OptIn(ExperimentalTime::class)
    private fun generateFromOriginalName(
        originalFileName: String,
        prefix: String = "",
    ): String {
        // 1. 安全地分离文件名和扩展名
        val baseName = originalFileName.substringBeforeLast('.', missingDelimiterValue = originalFileName)
        val extension = originalFileName.substringAfterLast('.', missingDelimiterValue = "")

        // 2. 清理文件名
        val sanitizedBaseName =
            baseName
                .replace(Regex("[^a-zA-Z0-9.\\-_]"), "")
                .take(50)

        // 3. 准备唯一性组件
        val now: Instant = Clock.System.now()
        val timestamp = now.toEpochMilliseconds()
        val randomSuffix = Random.nextInt(100, 1000)

        // 4. 按日期创建目录结构
        val localDateTime = now.toLocalDateTime(TimeZone.currentSystemDefault())
        val datePath =
            buildString {
                append(localDateTime.year)
                append(
                    localDateTime.month.number
                        .toString()
                        .padStart(2, '0'),
                )
                append(localDateTime.day.toString().padStart(2, '0'))
            }

        // 5. 组合最终的文件名
        val uniqueFileName =
            buildString {
                append(timestamp)
                append("_")
                append(randomSuffix)
                if (sanitizedBaseName.isNotEmpty()) {
                    append("_")
                    append(sanitizedBaseName)
                }
                if (extension.isNotEmpty()) {
                    append(".")
                    append(extension)
                }
            }

        // 6. 组合完整路径 (对象键)
        return buildString {
            if (prefix.isNotEmpty() && !prefix.endsWith('/')) {
                append(prefix)
                append('/')
            } else {
                append(prefix)
            }
            append(datePath)
            append('/')
            append(uniqueFileName)
        }
    }
}

interface MediaSelectLauncher {
    fun launch()
}

class Simple(
    private val mediaType: SelectorMediaType,
    private val maxItems: Int,
    private val launcher: ManagedActivityResultLauncher<PickVisualMediaRequest, *>,
) : MediaSelectLauncher {
    override fun launch() {
        launcher.launch(
            when (mediaType) {
                SelectorMediaType.IMAGE ->
                    PickVisualMediaRequest(
                        mediaType = ActivityResultContracts.PickVisualMedia.ImageOnly,
                        maxItems = maxItems,
                        isOrderedSelection = true,
                    )

                SelectorMediaType.VIDEO ->
                    PickVisualMediaRequest(
                        mediaType = ActivityResultContracts.PickVisualMedia.VideoOnly,
                        maxItems = maxItems,
                        isOrderedSelection = true,
                    )

                SelectorMediaType.ALL ->
                    PickVisualMediaRequest(
                        mediaType = ActivityResultContracts.PickVisualMedia.ImageAndVideo,
                        maxItems = maxItems,
                        isOrderedSelection = true,
                    )
            },
        )
    }
}

class Legacy(
    private val mediaType: SelectorMediaType,
    private val launcher: ManagedActivityResultLauncher<String, *>,
) : MediaSelectLauncher {
    override fun launch() {
        try {
            launcher.launch(
                when (mediaType) {
                    SelectorMediaType.IMAGE -> "image/*"
                    SelectorMediaType.VIDEO -> "video/*"
                    SelectorMediaType.ALL -> "image/*,video/*" // 注意这在某些旧设备上可能不完美
                },
            )
        } catch (e: Exception) {
        }
    }
}

@Composable
fun rememberMediaSelectLauncher(
    mediaType: SelectorMediaType = SelectorMediaType.IMAGE,
    maxItems: Int = 1,
    onMediaSelected: (List<Uri>) -> Unit,
): MediaSelectLauncher =
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Simple(
            mediaType,
            maxItems,
            rememberLauncherForActivityResult(
                contract = if (maxItems > 1) multipleContract else singleContract,
                onResult = { result ->
                    val uris =
                        when (result) {
                            is Uri -> listOf(result) // 单选结果
                            is List<*> -> result.filterIsInstance<Uri>() // 多选结果
                            else -> emptyList()
                        }
                    onMediaSelected(uris)
                },
            ),
        )
    } else {
        Legacy(
            mediaType,
            rememberLauncherForActivityResult(
                contract = if (maxItems > 1) legacyMultipleContract else legacySingleContract,
                onResult = { result ->
                    val uris =
                        when (result) {
                            is Uri -> listOf(result)
                            is List<*> -> result.filterIsInstance<Uri>()
                            else -> emptyList()
                        }
                    onMediaSelected(uris)
                },
            ),
        )
    }
