package com.buque.wakoo.utils

import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun rememberCustomGridCells(columns: Int, extraWidth: Dp = 20.dp): GridCells {
    val extraPx = with(LocalDensity.current) { extraWidth.toPx() }.toInt()
    return remember(columns, extraPx) {
        object : GridCells {
            override fun Density.calculateCrossAxisCellSizes(availableSize: Int, spacing: Int): List<Int> {
                val extraIndexs = if (columns % 2 == 0) {//双数
                    listOf(
                        columns / 2 - 1,
                        columns / 2
                    )
                } else {//单数
                    listOf(
                        columns / 2
                    )
                }
                val width = (availableSize - spacing * (columns - 1) - extraPx * extraIndexs.size) / columns

                return List(columns, { index ->
                    width + if (extraIndexs.indexOf(index) != -1) extraPx else 0
                })
            }
        }
    }
}