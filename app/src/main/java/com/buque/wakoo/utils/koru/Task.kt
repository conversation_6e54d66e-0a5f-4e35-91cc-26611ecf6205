package com.buque.wakoo.utils.koru

import java.util.UUID

/**
 * 代表一个可执行的任务单元。
 * 这是一个不可变的数据类，以确保线程安全。
 *
 * @property id 任务的唯一标识符，用于跟踪、撤回和更新。
 * @property priority 任务的优先级，数字越大，优先级越高。
 * @property timeoutMillis 任务执行的超时限制（毫秒）。如果为null，则没有超时限制。
 * @property action 定义任务具体行为的挂起函数。它接收任务的payload作为参数。
 * @property onError 当任务执行过程中发生异常（包括超时）时调用的回调函数。
 */
interface ITask {
    val id: UUID
    val priority: Int
    val timeoutMillis: Long?
    val onError: ((throwable: Throwable) -> Unit)?
    val action: suspend () -> Unit

    fun updatePriority(newPriority: Int): ITask
}

data class SimpleTask(
    override val id: UUID = UUID.randomUUID(),
    override val priority: Int = 0,
    override val timeoutMillis: Long? = null,
    override val onError: ((throwable: Throwable) -> Unit)? = null,
    override val action: suspend () -> Unit,
) : ITask {
    override fun updatePriority(newPriority: Int): ITask = copy(priority = newPriority)
}

abstract class AbsTask(
    override val id: UUID = UUID.randomUUID(),
    override val priority: Int = 0,
    override val timeoutMillis: Long? = null,
) : ITask {
    override val action: suspend () -> Unit = {
        execute()
    }

    override val onError: ((throwable: Throwable) -> Unit)? = {
        error(it)
    }

    abstract fun execute()

    abstract fun error(throwable: Throwable)
}

data class PayloadTask<T>(
    override val id: UUID = UUID.randomUUID(),
    val payload: T,
    override val priority: Int = 0,
    override val timeoutMillis: Long? = null,
    override val onError: ((throwable: Throwable) -> Unit)? = null,
    val onAction: suspend (T) -> Unit,
) : ITask {
    override fun updatePriority(newPriority: Int): ITask = copy(priority = newPriority)

    override val action: suspend () -> Unit = {
        onAction(payload)
    }
}
