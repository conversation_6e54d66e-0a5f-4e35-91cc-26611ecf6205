package com.buque.wakoo.utils.eventBus

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterIsInstance

object EventBus {
    // 私有的可变 SharedFlow，用于内部发送事件
    private val _events = MutableSharedFlow<Any>(extraBufferCapacity = 5)

    // 公开的只读 SharedFlow，用于外部订阅
    val events = _events.asSharedFlow()

    /**
     * 发送一个事件到总线
     * @param event 要发送的事件对象
     */
    suspend fun send(event: Any) {
        _events.emit(event)
    }

    fun trySend(event: Any) {
        _events.tryEmit(event)
    }

    /**
     * 订阅特定类型的事件
     * 使用 reified 泛型和 filterIsInstance 来实现类型安全
     * @return 一个只包含特定类型事件的 Flow
     */
    inline fun <reified T> on(): Flow<T> = events.filterIsInstance<T>()
}

/**
一个用于安全地订阅和处理事件的 Composable Effect。
它会自动处理生命周期，仅在组件处于活跃状态时监听事件。
@param T 要监听的事件类型。
@param onEvent 接收到事件时要执行的回调 lambda。
 */
@Composable
inline fun <reified T> EventBusEffect(crossinline onEvent: (T) -> Unit) {
    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(lifecycleOwner.lifecycle) {
// 使用 repeatOnLifecycle 来确保仅在生命周期处于 STARTED 或更高状态时收集事件
        lifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
            EventBus.on<T>().collectLatest { event ->
                onEvent(event)
            }
        }
    }
}

@Composable
inline fun <reified T> EventBusEffect(
    key1: Any?,
    crossinline onEvent: (T) -> Unit,
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(key1, lifecycleOwner.lifecycle) {
// 使用 repeatOnLifecycle 来确保仅在生命周期处于 STARTED 或更高状态时收集事件
        lifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
            EventBus.on<T>().collectLatest { event ->
                onEvent(event)
            }
        }
    }
}

@Composable
inline fun <reified T> EventBusEffect(
    key1: Any?,
    key2: Any?,
    crossinline onEvent: (T) -> Unit,
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(key1, key2, lifecycleOwner.lifecycle) {
// 使用 repeatOnLifecycle 来确保仅在生命周期处于 STARTED 或更高状态时收集事件
        lifecycleOwner.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
            EventBus.on<T>().collectLatest { event ->
                onEvent(event)
            }
        }
    }
}
