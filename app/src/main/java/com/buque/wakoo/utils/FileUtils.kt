package com.buque.wakoo.utils

import android.content.Context
import android.os.Environment
import java.io.File
import java.io.IOException
import java.net.MalformedURLException
import java.net.URL
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.regex.Pattern

object FileUtils {
    // 定义默认文件名前缀，用于无法从 URL 提取出有效文件名的情况
    private const val DEFAULT_FILENAME_PREFIX = "download_file_"

    // 定义在文件名中通常不合法的字符的正则表达式。
    // 包括：\ / : * ? " < > |
    private val INVALID_FILENAME_CHARS = Pattern.compile("[\\\\/:*?\"<>|]")

    private const val VOICE_CARD_TYPE = "voice_card"

    private const val VOICE_MSG_TYPE = "voice_msg"

    private const val RECORD_TYPE = "recordings"

    private const val GIFT_TYPE = "gift"

    private const val BUBBLE_TYPE = "bubble"

    private const val LANGUAGE_TYPE = "language"

    /**
     * 从给定的 URL 字符串中提取一个合适的文件名，并进行文件系统兼容性清理。
     *
     * 该函数尝试从 URL 的路径段中派生文件名。
     * 如果无法提取出有效文件名（例如，URL 以斜杠结尾，或者只包含非法字符），
     * 或者 URL 本身就是格式错误的，它将生成一个基于主机名和时间戳的通用回退文件名。
     *
     * @param urlString 要从中提取文件名的 URL 字符串。
     * @param defaultExtension 可选参数：如果从 URL 提取的文件名没有扩展名，则附加此默认扩展名。
     * 此参数仅在成功提取到文件名时才应用。
     * @return 一个经过清理的文件名 (String)，适合保存到磁盘。
     */
    fun createFileNameFromUrl(
        urlString: String,
        defaultExtension: String = "",
    ): String {
        try {
            val url = URL(urlString)
            val host = url.host ?: "unknown_host" // 获取主机名，用于回退文件名
            var path = url.path ?: "" // url.path 会自动排除查询参数和片段

            var fileName: String

            // 使用 '/' 分割路径，并过滤掉空段（例如处理 "a//b/c" 中的双斜杠）
            val segments = path.split('/').filter { it.isNotBlank() }

            if (segments.isNotEmpty()) {
                // 如果存在有效的路径段，取最后一个作为基础文件名
                fileName = segments.last()
            } else {
                // 如果没有有意义的路径段（例如："http://example.com/" 或 "http://example.com"）
                // 则将文件名初始化为空，以便进入后面的回退逻辑
                fileName = ""
            }

            // 清理文件名：将非法字符替换为下划线
            fileName = INVALID_FILENAME_CHARS.matcher(fileName).replaceAll("_")

            // 移除可能由替换操作产生的首尾点号或空格
            fileName = fileName.trim().trimStart('.').trimEnd('.')

            // 回退逻辑：如果文件名在清理后仍然为空，或者只包含下划线或点号（这些通常是原始非法字符替换后剩下的）
            if (fileName.isEmpty() || fileName.matches(Regex("[._]+"))) {
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val sanitizedHost = INVALID_FILENAME_CHARS.matcher(host).replaceAll("_") // 清理主机名，用于回退文件名
                return "${DEFAULT_FILENAME_PREFIX}${sanitizedHost}_${timestamp}${if (defaultExtension.isNotBlank()) ".$defaultExtension" else ""}"
            }

            // 如果提供了默认扩展名，并且当前文件名没有扩展名，则添加默认扩展名
            if (defaultExtension.isNotBlank() && !fileName.contains(".")) {
                return "$fileName.$defaultExtension"
            }

            return fileName
        } catch (e: MalformedURLException) {
            // 处理 URL 格式错误的情况
            System.err.println("错误：提供的 URL 格式不正确: $urlString - ${e.message}")
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            return "${DEFAULT_FILENAME_PREFIX}invalid_url_${timestamp}${if (defaultExtension.isNotBlank()) ".$defaultExtension" else ""}"
        }
    }

    fun initialize(context: Context) {
        createDefaultVoiceCardDownloadOutputFile(context)
    }

    fun createOutputFile(
        context: Context,
        type: String,
    ): File {
        val dir =
            File(
                context.filesDir,
                type,
            )

        // 确保目录存在
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                throw IOException("无法创建录音目录: ${dir.absolutePath}")
            }
        }
        return dir
    }

    fun createDefaultRecordOutputFile(context: Context) = createOutputFile(context, RECORD_TYPE)

    fun createDefaultVoiceCardDownloadOutputFile(context: Context) =
        createOutputFile(context, "${Environment.DIRECTORY_DOWNLOADS}/$VOICE_CARD_TYPE")

    fun createDefaultVoiceMessageDownloadOutputFile(context: Context) =
        createOutputFile(context, "${Environment.DIRECTORY_DOWNLOADS}/$VOICE_MSG_TYPE")

    fun createDefaultGiftDownloadOutputFile(context: Context) = createOutputFile(context, "${Environment.DIRECTORY_DOWNLOADS}/$GIFT_TYPE")

    fun createDefaultBubbleDownloadOutputFile(context: Context) =
        createOutputFile(context, "${Environment.DIRECTORY_DOWNLOADS}/$BUBBLE_TYPE")

    fun createDefaultLanguageDownloadOutputFile(context: Context) =
        createOutputFile(context, "${Environment.DIRECTORY_DOWNLOADS}/$LANGUAGE_TYPE")

    fun createDownloadFile(
        context: Context,
        type: String,
        url: String,
    ) = File(createOutputFile(context, "${Environment.DIRECTORY_DOWNLOADS}/$type"), createFileNameFromUrl(url))

    fun createVoiceCardDownloadFile(
        context: Context,
        url: String,
    ) = createDownloadFile(context, VOICE_CARD_TYPE, url)
}
