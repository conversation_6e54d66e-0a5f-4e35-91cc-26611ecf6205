package com.buque.wakoo.utils.ninepatch

import android.graphics.Bitmap
import android.graphics.Rect
import kotlinx.coroutines.CoroutineExceptionHandler

private val exceptionHandler =
    CoroutineExceptionHandler { _, throwable ->
    }

private val SKIN_BUBBLE_PADDING_RECT = Rect(66, 54, 66, 54)

fun createDefaultNinePatchChunk(
    bitmap: Bitmap,
    padding: Rect?,
): NinePatchChunk {
    if (bitmap.width < 3 || bitmap.height < 3) {
        return NinePatchChunk.createEmptyChunk()
    }
    return NinePatchChunk().apply {
        xDivs = arrayListOf(createDiv(bitmap.width))
        yDivs = arrayListOf(createDiv(bitmap.height))
        this.padding.set(padding ?: SKIN_BUBBLE_PADDING_RECT)
        NinePatchChunk.setupColorsByPlain(bitmap, this)
    }
}

private fun createDiv(size: Int) =
    if (size.rem(2) == 0) {
        val value = size.div(2)
        Div(value.minus(1), value.plus(1))
    } else {
        val value = size.minus(1).div(2)
        Div(value, value.plus(1))
    }
