package com.buque.wakoo.utils

object Server {

    private var serverLastBaseTimestamp: Long = 0L
    private var systemLastBaseTimestamp: Long = 0L

    fun setCurrentMillis(millis: Long) {
        serverLastBaseTimestamp = millis
        systemLastBaseTimestamp = System.currentTimeMillis()
    }

    fun currentTimeMillis(): Long =
        if (serverLastBaseTimestamp == 0L || systemLastBaseTimestamp == 0L) {
            System.currentTimeMillis()
        } else {
            (serverLastBaseTimestamp + (System.currentTimeMillis() - systemLastBaseTimestamp))
        }
}