package com.buque.wakoo.utils

import androidx.core.util.Pools

/**
 * <AUTHOR>
 * @Date 2022/2/7 5:47 下午
 * 单个builder的长度在256B - 8KB 之间
 */
class RecycleStringBuilder {
    val sBuilder: StringBuilder = StringBuilder(256)
    val length: Int
        get() = sBuilder.length

    fun recycle() {
        sBuilder.setLength(0)
        if (sBuilder.capacity() > 1024 * 8) {
            // 超出8k的话就不存往池子里存,直接释放掉
            return
        }
        try {
            sPools.release(this)
            poolLength += 1
        } catch (e: Exception) {
            // not do anything
        }
    }

    fun setEmpty() {
        sBuilder.setLength(0)
    }

    fun isEmpty(): Boolean = sBuilder.isEmpty()

    fun indexOf(
        str: Char,
        fromIndex: Int,
    ): Int = sBuilder.indexOf(str, fromIndex)

    fun indexOf(str: String): Int = sBuilder.indexOf(str)

    fun indexOf(
        str: String,
        fromIndex: Int,
    ): Int = sBuilder.indexOf(str, fromIndex)

    fun lastIndexOf(str: String): Int = sBuilder.lastIndexOf(str)

    fun lastIndexOf(
        str: String,
        fromIndex: Int,
    ): Int = sBuilder.lastIndexOf(str, fromIndex)

    fun substring(start: Int): String? = sBuilder.substring(start)

    fun substring(
        start: Int,
        end: Int,
    ): String = sBuilder.substring(start, end) ?: ""

    fun append(content: Any?): RecycleStringBuilder {
        when (content) {
            is Int -> sBuilder.append(content)
            is Float -> sBuilder.append(content)
            is Double -> sBuilder.append(content)
            is CharSequence -> sBuilder.append(content)
            is String -> sBuilder.append(content)
            is Boolean -> sBuilder.append(content)
            is Long -> sBuilder.append(content)
            is Char -> sBuilder.append(content)
            is CharArray -> sBuilder.append(content)
            else -> {
                if (content == null) {
                    sBuilder.append(" null ")
                } else {
                    sBuilder.append(content)
                }
            }
        }
        return this
    }

    override fun toString(): String = sBuilder.toString()

    companion object {
        private val sPools = Pools.SynchronizedPool<RecycleStringBuilder>(100)

        fun obtain(): RecycleStringBuilder {
            val sbuilder = sPools.acquire()
            if (sbuilder != null) {
                poolLength -= 1
            }
            return sbuilder ?: RecycleStringBuilder()
        }

        inline fun string(func: (RecycleStringBuilder) -> Unit): String {
            val obj = obtain()
            func(obj)
            val result = obj.toString()
            obj.recycle()
            return result
        }

        var poolLength: Int = 0
            private set(value) {
                field = value
            }
            get() {
                return field
            }
    }
}

inline fun String.join(
    vararg items: Any?,
    middleText: String = "",
): String =
    RecycleStringBuilder.string { sbuilder ->
        sbuilder.append(this)
        items.forEach {
            sbuilder.append(middleText)
            if (it is CharSequence) {
                sbuilder.append(it)
            } else {
                sbuilder.append(it.toString())
            }
        }
    }
