package com.buque.wakoo.utils

import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.cancellation.CancellationException
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

open class ResultContinuation<T> : (CancellableContinuation<T>) -> Unit {
    @Volatile
    private var continuation: CancellableContinuation<T>? = null

    val isActive: Boolean
        get() = continuation != null && continuation?.isActive == true

    override fun invoke(continuation: CancellableContinuation<T>) {
        this.continuation = continuation
        continuation.invokeOnCancellation {
            this.continuation = null
        }
    }

    fun resume(value: T) {
        if (continuation?.isActive == true) {
            continuation?.resume(value)
            continuation = null
        }
    }

    fun cancel() {
        if (continuation?.isActive == true) {
            continuation?.resumeWithException(CancellationException())
            continuation = null
        }
    }

    suspend fun suspendUntil(): T = suspendCancellableCoroutine(this)

    suspend fun suspendUntilWithTimeout(timeMillis: Long): T? =
        withTimeoutOrNull(timeMillis) {
            suspendUntil()
        }
}

class AwaitContinuation : ResultContinuation<Unit>() {
    var shouldSuspend = false
        set(value) {
            field = value
            if (value.not()) {
                resume()
            }
        }

    fun resume() {
        resume(Unit)
    }
}

fun awaitContinuation() = AwaitContinuation()

class ActiveDelay : LifecycleOwner {
    private val mLifecycleRegistry = LifecycleRegistry(this)

    private var leftTimeMillis = 0L

    suspend fun aDelay(timeMillis: Long) {
        if (timeMillis <= 0) {
            return
        }
        if (mLifecycleRegistry.currentState == Lifecycle.State.INITIALIZED) {
            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
        }
        leftTimeMillis = timeMillis
        repeatOnLifecycle(Lifecycle.State.RESUMED) {
            if (leftTimeMillis > 0) {
                val temp = SystemClock.elapsedRealtime()
                try {
                    delay(leftTimeMillis)
                } catch (e: CancellationException) {
                    leftTimeMillis = leftTimeMillis.minus(SystemClock.elapsedRealtime().minus(temp))
                    throw e
                }
            }
            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        }
    }

    fun pause() {
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    }

    fun resume() {
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
    }

    fun abort() {
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    }

    override val lifecycle: Lifecycle = mLifecycleRegistry
}
