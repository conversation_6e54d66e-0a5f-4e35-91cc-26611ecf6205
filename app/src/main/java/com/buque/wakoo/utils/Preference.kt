package com.buque.wakoo.utils

import android.annotation.SuppressLint
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.app.appCoroutineScope
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.launch
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.net.URLDecoder
import java.net.URLEncoder
import kotlin.reflect.KProperty


class Preference<T>(val name: String, private val default: T) {
    @Volatile
    private var cacheValue: T? = null

    companion object {
        val instance by lazy {
            Preference("none", -1)
        }
    }

    private val mmkv: MMKV by lazy {
        DevicesKV
    }

    operator fun getValue(thisRef: Any?, property: KProperty<*>): T {
        return cacheValue ?: getSharedPreferences(name, default).apply {
            cacheValue = this
        }
    }

    operator fun setValue(thisRef: Any?, property: KProperty<*>, value: T) {
        cacheValue = value
        appCoroutineScope.launch {
            putSharedPreferences(name, value)
        }
    }


    @SuppressLint("CommitPrefEdits")
    private fun putSharedPreferences(name: String, value: T) = with(mmkv) {
        when (value) {
            is Long -> encode(name, value)
            is String -> encode(name, value)
            is Double -> encode(name, value)
            is Int -> encode(name, value)
            is Boolean -> encode(name, value)
            is Float -> encode(name, value)
            else -> encode(name, serialize(value))
        }
    }


    @Suppress("UNCHECKED_CAST")
    private fun getSharedPreferences(name: String, default: T): T = with(mmkv) {
        val res: Any = when (default) {
            is Long -> decodeLong(name, default)
            is String -> decodeString(name, default) ?: default
            is Int -> decodeInt(name, default)
            is Boolean -> decodeBool(name, default)
            is Float -> decodeFloat(name, default)
            else -> {
                val serialize = serialize(default)
                val saveSerizalize = decodeString(name, serialize)!!
                val result = deSerialization<T>(saveSerizalize)
                if (result == null) {
                    remove(name)
                    appCoroutineScope.launch {
                        putSharedPreferences(name, default)
                    }
                    return default
                } else {
                    return result
                }
            }
        }
        return res as T
    }

    fun MMKV.encode(name: String, value: T) {
        putSharedPreferences(name, value)
    }

    fun MMKV.decode(name: String, default: T): T = getSharedPreferences(name, default)

    /**
     * 删除全部数据
     */
    fun clearPreference() {
        mmkv.clearAll()
    }

    /**
     * 根据key删除存储数据
     */
    fun clearPreference(key: String) {
        mmkv.remove(key)
    }

    @Throws(IOException::class)
    private fun <A> serialize(obj: A): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        val objectOutputStream = ObjectOutputStream(
            byteArrayOutputStream
        )
        objectOutputStream.writeObject(obj)
        var serStr = byteArrayOutputStream.toString("ISO-8859-1")
        serStr = URLEncoder.encode(serStr, "UTF-8")
        objectOutputStream.close()
        byteArrayOutputStream.close()
        return serStr
    }

    @Suppress("UNCHECKED_CAST")
    @Throws(IOException::class, ClassNotFoundException::class)
    private fun <A> deSerialization(str: String): A? {
        try {
            val redStr = URLDecoder.decode(str, "UTF-8")
            val byteArrayInputStream = ByteArrayInputStream(
                redStr.toByteArray(charset("ISO-8859-1"))
            )
            val objectInputStream = ObjectInputStream(
                byteArrayInputStream
            )
            val obj = objectInputStream.readObject() as A
            objectInputStream.close()
            byteArrayInputStream.close()
            return obj
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }


    fun contains(key: String): Boolean {
        return mmkv.contains(key)
    }

    fun getAll(): Map<String, *> {
        return mmkv.all
    }

}