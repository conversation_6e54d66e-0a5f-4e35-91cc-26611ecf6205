package com.buque.wakoo.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.Enumeration
import java.util.zip.ZipEntry
import java.util.zip.ZipFile

object ZipUtil {
    /**
     * # 解压文件
     * [zipFile] 需要解压的文件
     * [folderPath] 解压文件目录
     */
    suspend fun upZipFile(
        zipFile: File?,
        folderPath: String,
    ) {
        if (zipFile == null) return
        val destFolderPath = if (folderPath.endsWith("/")) folderPath else "$folderPath/"
        withContext(Dispatchers.IO) {
            val zfile = ZipFile(zipFile)
            val zList: Enumeration<*> = zfile.entries()
            var ze: ZipEntry?
            val buf = ByteArray(10240)
            while (zList.hasMoreElements()) {
                ze =
                    try {
                        zList.nextElement() as? ZipEntry
                    } catch (e: Exception) {
                        e.printStackTrace()
                        null
                    }
                if (ze == null) continue
                if (ze.isDirectory) {
                    var dirstr = destFolderPath + ze.name
                    dirstr = String(dirstr.toByteArray())
                    val f = File(dirstr)
                    f.mkdir()
                } else {
                    val f = File(destFolderPath + ze.name.removeSuffix("/"))
                    val p = f.parentFile
                    if (p != null && p.exists().not()) {
                        p.mkdirs()
                    }
                    if (!f.exists()) {
                        try {
                            f.createNewFile()
                        } catch (e: Exception) {
                        }
                    }
                    var `is`: InputStream? = null
                    var fos: FileOutputStream? = null
                    try {
                        `is` = zfile.getInputStream(ze)
                        fos = FileOutputStream(f)
                        var len: Int
                        while ((`is`.read(buf).also { len = it }) != -1) {
                            fos.write(buf, 0, len)
                        }
                    } catch (e: Exception) {
                    } finally {
                        `is`?.close()
                        fos?.close()
                    }
                }
            }
            zfile.close()
        }
    }
}
