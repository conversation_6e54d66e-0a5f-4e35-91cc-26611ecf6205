package com.buque.wakoo.utils

import androidx.core.util.Pools
import java.util.concurrent.ConcurrentHashMap

/**
 *  @time 2022/4/10
 *  <AUTHOR>
 */
object RecyclePoolUtils {
    val poolSet = mutableMapOf<String, Pools.SynchronizedPool<*>>()
    val newInstanceMap = ConcurrentHashMap<String, InstanceCallback<*>>()

    @Volatile
    private var count = 0

    fun decreaseInstance() {
        count--
        if (count <= 0) {
            count = 0
            poolSet.clear()
            newInstanceMap.clear()
        }
    }

    fun addInstance() {
        count++
    }

    fun <T : Any> clear(thiz: T) {
        val toString = thiz.javaClass.toString()
        newInstanceMap.remove(getKey(thiz))
    }

    fun <T : Any> register(
        className: String,
        newInstanceFunc: InstanceCallback<T>,
    ) {
        val toString = getKey(className)
        if (!newInstanceMap.containsKey(toString)) {
            newInstanceMap.put(toString, newInstanceFunc)
        }
    }

    fun <T : Any> hasgister(thiz: T): Boolean {
        val toString = getKey(thiz)
        return newInstanceMap.containsKey(toString)
    }

    inline fun <reified T : Any> obtain(): T {
        val toString = T::class.java.toString()
        var get = poolSet.get(toString)
        if (get == null) {
            get = Pools.SynchronizedPool<T>(100)
            poolSet.put(toString, get)
        }
        var t = get.acquire() as T?
        if (t == null) {
            val function =
                (newInstanceMap[toString] as InstanceCallback<T>?)
                    ?: throw IllegalArgumentException("you muse set Callback first")
            t = function.newInstance() as T
            return t
        } else {
            return t
        }
    }

    fun <T : Any> recycle(obj: T) {
        val toString = getKey(obj)
        var pool: Pools.SynchronizedPool<T>? = null
        var get = poolSet.get(toString)
        if (get == null) {
            return
        } else {
            try {
                pool = get as Pools.SynchronizedPool<T>
            } catch (e: Exception) {
                return
            }
        }
        val function =
            (newInstanceMap[toString] as InstanceCallback<T>?)
                ?: throw IllegalArgumentException("you muse set Callback first")
        function.reset(obj)
        pool.release(obj)
    }

    private fun getKey(obj: Any): String =
        if (obj is String) {
            obj.toString()
        } else {
            obj.javaClass.toString()
        }

    interface InstanceCallback<T> {
        fun newInstance(): T

        fun reset(data: T)
    }
}
