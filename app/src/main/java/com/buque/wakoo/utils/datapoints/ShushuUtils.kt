package com.buque.wakoo.utils.datapoints

import android.Manifest
import android.content.Context
import android.os.Build
import cn.thinkingdata.analytics.TDAnalytics
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.push.IMNotificationHelper
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.DeviceInfoManager
import com.buque.wakoo.manager.DeviceInfoManager.deviceId
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.LocationManager
import com.buque.wakoo.manager.NetworkManager
import com.buque.wakoo.utils.PermissionUtils
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject


/**
 * # 基础属性
 * # device_id:          设备ID,          字符串,   user_setOnce,   创建账号时记录,                    来源: 客户端
 * # app_version:        客户端版本号,     文本,     user_set,       更新时覆盖,                        来源: 客户端, 示例: 1.6.5
 * # network:            网络类型,         文本,     user_set,       更新时覆盖,                        来源: 客户端, 示例: wifi，移动网络
 * # client_platform:    操作系统,         文本,     user_set,       更新时覆盖, 平台标识：android，IOS，harmonyOS，PC, 来源: 客户端
 * # system_version:     操作系统版本,     字符串,   user_set,       客户端SDK可自动采集, 示例: Android OS 9, 来源: 客户端
 * # public_id:          公开ID,          数值,     user_set,       用户当前公开ID，hifun的public_id, 来源: 客户端
 * # nickname:           昵称,            文本,     user_set,       登出/退出界面/发生变化时上报,      来源: 客户端
 * # gender:             性别,            字符串,   user_set,       填写性别后进行设置，登录时更新,    来源: 客户端
 * # age:                年龄,            数值,     user_set,       登出/退出界面/发生变化时上报,      来源: 客户端
 * # birthday:           生日,            时间,     user_set,       生日发生变化时设置,                来源: 客户端
 * # channel:            注册渠道,        字符串,   user_setOnce,   注册时设置，上传用户的渠道信息，如app store、谷歌商店，官网包，testflight等, 来源: 客户端, 示例: gg=谷歌
 * # location:           地理位置,        字符串,   user_set,       打开定位权限时设置,                来源: 客户端, 备注: 获取到位置之后上报
 * # is_member:          是否会员,        布尔值,   user_set,       登录时上报,                        来源: 客户端
 *
 * # 关键时间点
 * # last_active_time:   最后活跃时间,    时间,     user_set,       退出后台时上报,                    来源: 客户端
 *
 * # 累计数值
 * # total_login_count:          累计登录次数,            数值,     user_add,        每次登录时累加,                    来源: 客户端, 备注: 冷启动+账号登录，不是热启动
 * # manually_send_message_count: 累计主动私聊消息数量,    数值,     user_add,        每次主动私聊消息数累加,            来源: 客户端, 备注: 统计成功数
 *
 * # 资源
 * # balance:           金币,            数值,     user_set,       到首页/弹出金币充值弹窗/弹出礼物面板时候上报, 来源: 客户端
 * # silver_balance:    银币,            数值,     user_set,       弹出礼物面板弹窗时候上报,                    来源: 客户端
 * # follower_count:    关注数,          数值,     user_set,       退出界面时上报,                            来源: 客户端
 * # followee_count:    粉丝数,          数值,     user_set,       退出界面时上报,                            来源: 客户端
 * # friend_count:      好友人数,        数值,     user_set,       退出界面时上报,                            来源: 客户端
 */
object ShushuUtils : IMCompatListener, NetworkManager.Callback {
    private var isInit = false;

    fun init(context: Context) {

        //1. 如果已经注册了,但是当前非UCOO环境,解注册
//        if (isInit &&) {
//            //解注册
//            isInit = false
//            IMCompatCore.removeIMListener(this)
//            NetworkManager.unregister(this)
//            TDAnalytics.logout()
//            return
//        } else if (!isAvailable) {
//            //2. 如果当前没注册,但是非UCOO环境,则直接返回
//            return
//        } else
        if (isInit) {
            //3. 如果当前已经注册了,则直接返回
            return
        }

        TDAnalytics.init(context.applicationContext, EnvironmentManager.current.shushuId, EnvironmentManager.current.shushuReportId)
        TDAnalytics.enableLog(BuildConfig.DEBUG)

        isInit = true;
        appCoroutineScope.launch {
            AccountManager.accountStateFlow.distinctUntilChangedBy { item ->
                item?.id
            }.collectLatest {
                if (it == null) {
                    //退出
                    TDAnalytics.logout()
                } else {
                    //登录
                    TDAnalytics.login(it.userInfo.id.toString())
                    try {
                        val newProperties = JSONObject()
                        newProperties.put("age", it.age)
                        newProperties.put("gender", if (it.gender == 1) "男" else "女")
                        newProperties.put("user_level", it.userInfo.level)
                        newProperties.put("is_high_quality", it.userInfo.type == 1)
                        newProperties.put("public_id", it.userInfo.publishId)
                        newProperties.put("device_id", DeviceInfoManager.deviceId)
                        newProperties.put("is_vpn", NetworkManager.isUsingVPN())

                        TDAnalytics.setSuperProperties(newProperties)

                        userSet(
                            "app_version" to BuildConfig.VERSION_NAME,
                            "client_platform" to "Android",
                            "system_version" to Build.VERSION.SDK_INT.toString(),

                            "public_id" to it.userInfo.publishId,
                            "nickname" to it.userInfo.name,
                            "gender" to if (it.gender == 1) "男" else "女",
                            "age" to it.age,
                            "birthday" to it.birthday,

                            "location_permission" to if (PermissionUtils.hasPermissions(context, arrayOf(Manifest.permission.ACCESS_FINE_LOCATION))) 1 else 0,
                            "notify_permission" to if (IMNotificationHelper.hasNotificationPermission()) 1 else 0,

                            "is_member" to it.isVip,
                        )


                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                }
            }
        }

        appCoroutineScope.launch {
            LocationManager.locationFlow.collectLatest {
                if (it != null) {
                    userSet(
                        "location" to "${it.country}-${it.city}"
                    )
                }
            }
        }

        NetworkManager.register(this)

        IMCompatCore.addIMListener(this)
    }

    private var lastType = ""

    override fun onNetworkChanged() {
        val currentType = NetworkManager.networkType()
        if (lastType != currentType) {
            lastType = currentType
            userSet("network" to currentType)
        }
    }

    override fun onSendNewMessage(message: UCInstanceMessage, onlyLocal: Boolean) {
        if (message.isC2CMsg && !onlyLocal && message is UCTextMessage || message is UCImageMessage || message is UCVoiceMessage) {
            userAdd("manually_send_message_count" to 1)
        }
    }

    private fun userSet(vararg params: Pair<String, Any>, once: Boolean = false) {
        if (!isInit) {
            return
        }
        val prop = JSONObject()
        params.forEach {
            prop.putOpt(it.first, it.second)
        }
        if (once) {
            TDAnalytics.userSetOnce(prop)
        } else {
            TDAnalytics.userSet(prop)
        }
    }

    private fun userAdd(vararg params: Pair<String, Any>) {
        if (!isInit) {
            return
        }
        val prop = JSONObject()
        params.forEach {
            prop.putOpt(it.first, it.second)
        }
        TDAnalytics.userAdd(prop)
    }

    /**
     * @param name 事件名称
     * @param extra 携带参数,这是一个jsonObject,包含有多个键值对
     */
    fun trace(name: String, extra: String?) {
        if (!isInit) {
            return
        }
        try {
            val jobj = JSONObject(extra ?: "{}")
            TDAnalytics.track(name, jobj)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }
}