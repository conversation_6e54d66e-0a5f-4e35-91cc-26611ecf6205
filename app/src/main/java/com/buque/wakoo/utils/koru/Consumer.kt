package com.buque.wakoo.utils.koru

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import kotlin.coroutines.cancellation.CancellationException

/**
 * 任务的执行者。每个消费者实例都是独立的，可以并行处理任务。
 *
 * @param id 消费者的唯一标识符，用于日志和调试。
 * @param broker 消费者将从中获取任务的TaskBroker实例。
 * @param scope 控制消费者生命周期的协程作用域。当此scope被取消时，消费者将停止所有活动。
 */
@Suppress("ktlint:standard:backing-property-naming")
class Consumer(
    val id: String,
    private val broker: TaskBroker,
    private val scope: CoroutineScope,
) {
    // 使用一个独立的、带SupervisorJob的作用域来控制内部的所有协程。
    // 这可以防止一个子协程的失败导致整个Consumer的崩溃。
    private val controlScope = CoroutineScope(scope.coroutineContext + SupervisorJob())

    private val _currentTask = MutableStateFlow<ITask?>(null)
    val currentTask: StateFlow<ITask?> = _currentTask.asStateFlow()

    val isConsuming: StateFlow<Boolean> =
        _currentTask
            .map { it != null }
            .stateIn(controlScope, SharingStarted.Eagerly, false)

    // 这个Job专门负责监听Channel，它的生命周期是可控的。
    private var listenerJob: Job? = null
    private val isRunning get() = listenerJob?.isActive == true

    /** 开始监听并处理来自Broker的任务。*/
    fun start() {
        if (isRunning) return

        // 启动一个可取消的监听协程
        listenerJob =
            controlScope.launch(Dispatchers.Default) {
                while (isActive) { // 循环的存活由listenerJob的取消状态决定
                    try {
                        // 挂起直到从Channel接收到一个任务
                        val task = broker.consumeChannel().receive()

                        // **关键**: 任务的执行在同一个控制域下启动一个新协程。
                        // 这确保了即使listenerJob被取消，这个执行任务的协程也不会受影响。
                        val processingJob =
                            controlScope.launch {
                                processTask(task)
                            }
                        // 但我们必须等待它完成，以保证“一次只处理一个任务”
                        processingJob.join()
                    } catch (e: ClosedReceiveChannelException) {
                        // Channel已关闭，正常退出监听循环
                        break
                    } catch (e: CancellationException) {
                        // listenerJob被主动取消，正常退出监听循环
                        break
                    }
                }
            }
    }

    /** 优雅地停止接收新任务。当前任务会继续执行直到完成。*/
    fun stop() {
        // 取消监听协程。如果它正挂起在 receive()，会立即被中断。
        // 如果它正等待 processingJob.join()，也会被中断。
        // 但这不会影响到已经启动的 processingJob 本身。
        listenerJob?.cancel()
        listenerJob = null
    }

    /** 从停止状态中恢复，继续接收和处理新任务。*/
    fun resume() {
        start()
    }

    /** 强制取消当前正在执行的任务。*/
    fun cancelCurrentTask() {
        // 我们直接取消正在执行的任务的Job
        // processTask内部的finally块会确保状态被清理
        controlScope.launch {
            // 在processTask内部，我们将当前任务的Job赋值给了_currentTaskJob
            _currentTaskJob?.cancel(CancellationException("Task manually cancelled by user."))
        }
    }

    /** 立即停止消费者，并取消当前正在执行的任务。*/
    fun shutdown() {
        // 取消整个控制域会停止所有子协程，包括监听和正在执行的任务
        controlScope.cancel()
    }

    // --- 任务处理逻辑，补充了取消日志 ---
    private var _currentTaskJob: Job? = null

    private suspend fun processTask(task: ITask) {
        coroutineScope {
            // 将当前执行的Job保存起来，以便cancelCurrentTask可以访问
            _currentTaskJob = this.coroutineContext[Job]

            _currentTask.value = task
            broker.emitEvent(BrokerEvent.TaskExecutionStarted(task.id, id))

            try {
                withTimeout(task.timeoutMillis ?: Long.MAX_VALUE) {
                    task.action()
                }
                broker.emitEvent(BrokerEvent.TaskExecutionSuccess(task.id, id))
            } catch (e: Throwable) {
                when (e) {
                    is CancellationException -> {
                        // **新增**: 明确记录任务被取消的事件
                        broker.emitEvent(BrokerEvent.TaskExecutionCancelled(task.id, id, e.message))
                        throw e // 重新抛出以遵循结构化并发
                    }
                    else -> {
                        task.onError?.invoke(e)
                        broker.emitEvent(BrokerEvent.TaskExecutionFailed(task.id, id, e))
                    }
                }
            } finally {
                _currentTask.value = null
                _currentTaskJob = null
            }
        }
    }
}
