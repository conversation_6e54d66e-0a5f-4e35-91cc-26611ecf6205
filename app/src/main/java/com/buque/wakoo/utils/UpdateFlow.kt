package com.buque.wakoo.utils

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.scan

/**
 * 一个用于更新值的函数类型别名。
 * @param T 要更新的值的类型。
 */
typealias UpdateAction<T> = (T) -> T

/**
 * 定义了对值的两种操作：全量设置(Set)和增量更新(Update)。
 */
sealed interface ValueUpdate<T> {
    data class Set<T>(
        val value: T,
    ) : ValueUpdate<T>

    data class Update<T>(
        val action: UpdateAction<T>,
    ) : ValueUpdate<T>
}

/**
 * 创建一个状态演进流。
 *
 * 该函数通过合并一个全量值流 (`valueFlow`) 和一个增量更新流 (`updateFlow`) 来创建一个新的状态流。
 * 新流会从 `initialValue` 开始，并根据两个输入流的事件按顺序演进状态。
 *
 * - 当 `valueFlow` 发射一个新值时，状态会被完全替换。
 * - 当 `updateFlow` 发射一个更新函数时，该函数会应用于当前状态以产生新状态。
 *
 * @param T 状态的类型。
 * @param initialValue 状态的初始值。
 * @param valueFlow 提供完整状态值的流（例如，从数据库或网络重新加载）。
 * @param updateFlow 提供更新函数的流（例如，用户操作导致的局部修改）。
 * @return 一个表示持续演进的状态的 Flow<T>。
 */
fun <T> evolveStateFlow(
    initialValue: T,
    valueFlow: Flow<T>,
    updateFlow: Flow<UpdateAction<T>>,
): Flow<T> {
    // 将两个输入流都映射为统一的 ValueUpdate 动作类型，然后合并它们
    val allActions =
        merge(
            valueFlow.map { ValueUpdate.Set(it) },
            updateFlow.map { ValueUpdate.Update(it) },
        )

    // 使用 scan 从初始值开始，对每一个到来的动作应用规约逻辑
    return allActions.scan(initialValue) { currentState, action ->
        when (action) {
            is ValueUpdate.Set -> action.value
            is ValueUpdate.Update -> action.action(currentState)
        }
    }
}

fun <T> Flow<T>.evolveWith(
    initialValue: T,
    updateFlow: Flow<UpdateAction<T>>,
) = evolveStateFlow(initialValue, this, updateFlow)

fun <T> StateFlow<T>.xx(updateFlow: Flow<UpdateAction<T>>) = evolveStateFlow(value, this, updateFlow)
