package com.buque.wakoo.utils

import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.im.utils.doOnLifecycleEvent
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.NetworkManager
import com.buque.wakoo.network.api.service.CommonApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive
import kotlin.coroutines.cancellation.CancellationException
import kotlin.text.split

object SayHiLogicUtils {
    //region sayhi逻辑

    /**
     * sayhi逻辑
     * 在splash中(已登录的情况)调用
     * 在MainActivity(已登录的情况,至少在cupid时是这样的)调用
     * 登录完成后调用
     * 在进入语音房时调用
     *
     * 7.29 更新, 学习iOS只在IM登录成功时调用
     *
     */
    suspend fun sayHi(
        roomId: String? = null,
        isHangingOut: Boolean = false,
    ) {
        val map = mutableMapOf("through_vpn" to "${NetworkManager.isUsingVPN() || NetworkManager.isUsingProxy()}")
        if (roomId == null) {
            // 搞毛,加这个参数就签名错误
//            map.put("is_hanging_out", "$isHangingOut")
        } else {
            // 搞毛,现在根本就不需要在语音房调用, 一调用就报错,但是老项目都是这么调的
            map.put("audioroom_id", roomId)
        }

        executeApiCallExpectingData {
            CommonApiService.instance.sayHi(map)
        }
    }

    private const val SAY_HI_KEY = "every_day_sayHi"
    private const val SAY_HI_A_KEY = "cupid_every_day_sayHi_a"
    private const val SAY_HI_B_KEY = "cupid_every_day_sayHi_b"

    fun registerEveryDaySayHiTask() {
        appCoroutineScope.launch {
            AccountManager.accountStateFlow
                .distinctUntilChanged { old, new -> old?.id == new?.id }
                .collectLatest {
                    if (it != null) {
                        coroutineScope {
                            launch {
                                val existsTaskA =
                                    currentUserKV.getString(SAY_HI_A_KEY, "")?.takeIf {
                                        it.contains("#")
                                    }
                                if (existsTaskA == null) {
                                    sayHi(true, SAY_HI_A_KEY, 1)
                                } else {
                                    val taskString = existsTaskA.split("#")
                                    startTimerOfSayHi(
                                        taskString[0].toLong(),
                                        taskString[1].toLong(),
                                        SAY_HI_A_KEY,
                                        1,
                                    )
                                }
                            }

                            launch {
                                val existsTaskB =
                                    currentUserKV.getString(SAY_HI_B_KEY, "")?.takeIf {
                                        it.contains("#")
                                    }
                                if (existsTaskB == null) {
                                    sayHi(true, SAY_HI_B_KEY, 2)
                                } else {
                                    val taskString = existsTaskB.split("#")
                                    startTimerOfSayHi(
                                        taskString[0].toLong(),
                                        taskString[1].toLong(),
                                        SAY_HI_B_KEY,
                                        2,
                                    )
                                }
                            }
                        }
                    }
                }
        }
    }

    private suspend fun startTimerOfSayHi(
        totalTime: Long,
        delayTime: Long,
        key: String,
        type: Int? = null,
    ) {
        if (delayTime >= totalTime) {
            sayHi(false, key, type)
            return
        }

        var realTimeTotal = delayTime
        var resumeTime = -1L

        fun recordTime() {
            if (resumeTime != -1L) {
                realTimeTotal = SystemClock.elapsedRealtime().minus(resumeTime).plus(realTimeTotal)
                currentUserKV.putString(key, "$totalTime#$realTimeTotal")
                resumeTime = -1L
            }
        }

        val startWorkEvent = Lifecycle.Event.upTo(Lifecycle.State.STARTED)
        val cancelWorkEvent = Lifecycle.Event.downFrom(Lifecycle.State.STARTED)
        val activeDelay = ActiveDelay()
        val disposable =
            ProcessLifecycleOwner.get().doOnLifecycleEvent {
                if (it == startWorkEvent) {
                    activeDelay.resume()
                    resumeTime = SystemClock.elapsedRealtime()
                } else if (it == cancelWorkEvent) {
                    activeDelay.pause()
                    recordTime()
                }
            }
        try {
            activeDelay.aDelay(totalTime.minus(delayTime))
        } catch (e: CancellationException) {
            recordTime() // 协程取消，一般为退出登录
            throw e
        } finally {
            disposable.dispose()
        }
        sayHi(false, key, type)
    }

    private suspend fun sayHi(
        start: Boolean,
        key: String,
        type: Int? = null,
    ) {
        if (!start) {
            currentUserKV.remove(key)
        }

        val timerMilliSeconds =
            (
                    executeApiCallExpectingData {
                        if (start) {
                            CommonApiService.instance.sayHiV2Start(type)
                        } else {
                            CommonApiService.instance.sayHiV2End(type)
                        }
                    }.getOrNull()?.get("online_duration")?.jsonPrimitive?.intOrNull ?: 0
                    ) * 1000L
        if (timerMilliSeconds > 0) {
            currentUserKV.putString(key, "$timerMilliSeconds#0")
            startTimerOfSayHi(timerMilliSeconds, 0, key, type)
        }
    }
    //endregion
}