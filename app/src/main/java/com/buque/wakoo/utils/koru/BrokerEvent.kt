package com.buque.wakoo.utils.koru

import java.util.UUID

/**
 * 一个密封接口，用于表示任务调度中心（TaskBroker）中发生的所有关键事件。
 * 这对于日志记录、调试和系统监控至关重要。
 */
sealed interface BrokerEvent {
    val taskId: UUID

    data class TaskSubmitted(
        val task: ITask,
    ) : BrokerEvent {
        override val taskId: UUID get() = task.id
    }

    data class TaskRetracted(
        override val taskId: UUID,
    ) : BrokerEvent

    data class TaskPriorityUpdated(
        override val taskId: UUID,
        val newPriority: Int,
    ) : BrokerEvent

    data class TaskDispatched(
        override val taskId: UUID,
        val consumerId: String,
    ) : BrokerEvent

    data class TaskExecutionStarted(
        override val taskId: UUID,
        val consumerId: String,
    ) : BrokerEvent

    data class TaskExecutionSuccess(
        override val taskId: UUID,
        val consumerId: String,
    ) : BrokerEvent

    data class TaskExecutionCancelled(
        override val taskId: UUID,
        val consumerId: String,
        val reason: String?,
    ) : BrokerEvent

    data class TaskExecutionFailed(
        override val taskId: UUID,
        val consumerId: String,
        val error: Throwable,
    ) : BrokerEvent

    // Broker级别的事件，没有taskId
    sealed interface BrokerLevelEvent : BrokerEvent {
        override val taskId: UUID get() = UUID.randomUUID() // Not applicable
    }

    data object QueueCleared : BrokerLevelEvent

    data object BrokerShutdown : BrokerLevelEvent
}
