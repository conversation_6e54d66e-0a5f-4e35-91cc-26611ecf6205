package com.buque.wakoo.utils.ninepatch

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import androidx.annotation.ColorInt
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.user.ChatBubble
import com.buque.wakoo.bean.user.User
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Composable
fun rememberNinePatchPainter(
    bubble: ChatBubble?,
    left: <PERSON><PERSON><PERSON>,
    theme: BubbleTheme = BubbleTheme(),
    type: Int,
): State<NinePatchPainter> {
    val context = LocalContext.current
    val density = LocalDensity.current
    val scope = rememberCoroutineScope()
    return remember(bubble, left) {
        if (bubble != null) {
            NinePathLoader.recordChatBubble(bubble)
            if (left && bubble.leftImg.isNotBlank()) {
                return@remember ninePatchPainter(context, density, scope, bubble.leftImg, bubble.leftPadding, theme, type)
            }

            if (!left && bubble.rightImg.isNotBlank()) {
                return@remember ninePatchPainter(context, density, scope, bubble.rightImg, bubble.rightPadding, theme, type)
            }
        }
        mutableStateOf(defaultNinePatchPainter(density, theme, type))
    }
}

private fun ninePatchPainter(
    context: Context,
    density: Density,
    scope: CoroutineScope,
    url: String,
    padding: Rect,
    theme: BubbleTheme,
    type: Int,
): State<NinePatchPainter> {
    val cache = NinePathLoader.getChatBubbleCache(url)
    if (cache != null) {
        return mutableStateOf(cache.bitmapToNinePatchPainter(context, density))
    }
    val state = mutableStateOf(defaultNinePatchPainter(density, theme, type))
    scope.launch {
        val painter = NinePathLoader.load(url, padding)?.bitmapToNinePatchPainter(context, density)
        if (painter != null) {
            state.value = painter
        }
    }
    return state
}

fun NinePatchBitmap.bitmapToNinePatchPainter(
    context: Context,
    density: Density,
): NinePatchPainter {
    val drawable = NinePatchChunk.create9PatchDrawable(context, this, null)
    val rect = Rect()
    drawable.getPadding(rect)
    return NinePatchPainter(
        drawable,
        with(density) {
            PaddingValues(rect.left.toDp(), rect.top.toDp(), rect.right.toDp(), rect.bottom.toDp())
        },
        true,
    )
}

private fun defaultNinePatchPainter(
    density: Density,
    theme: BubbleTheme,
    type: Int,
): NinePatchPainter {
    val dp8 =
        with(density) {
            theme.radius.toPx()
        }
    return NinePatchPainter(
        allRadiusDrawable(theme.color(type).toArgb(), dp8),
        theme.paddingValues,
    )
}

private fun allRadiusDrawable(
    @ColorInt color: Int,
    radius: Float,
): Drawable = radiusDrawable(color, radius, radius, radius, radius, radius, radius, radius, radius)

private fun radiusDrawable(
    @ColorInt color: Int,
    vararg radii: Float,
): Drawable =
    GradientDrawable().also {
        it.setColor(color)
        it.cornerRadii = radii
    }

data class BubbleTheme constructor(
    private val color1: Color = Color(0x33000000),
    private val color2: Color = color1,
    val translateColor: Color = Color(0x33000000),
    val radius: Dp = 8.dp,
    val paddingValues: PaddingValues = PaddingValues(horizontal = 8.dp, vertical = 13.dp),
) {
    /**
     * @param type == 0 对方
     * @param type == 1 自己
     */
    fun color(type: Int) =
        if (type == 0) {
            color1
        } else {
            color2
        }

    fun color(user: User) =
        if (!user.sIsSelf) {
            color1
        } else {
            color2
        }
}

data class NinePatchPainter constructor(
    val drawable: Drawable,
    val padding: PaddingValues,
    val isSkin: Boolean = false,
)
