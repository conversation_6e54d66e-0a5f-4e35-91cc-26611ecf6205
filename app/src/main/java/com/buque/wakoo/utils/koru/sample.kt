package com.buque.wakoo.utils.koru

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID

suspend fun main(): Unit =
    coroutineScope {
        println("--- 启动任务调度系统 ---")
        val appScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
        val broker = TaskBroker(appScope)

        // 1. 启动一个日志观察者，打印所有Broker事件
        val loggerJob =
            launch {
                broker.events.collect { event ->
                    val logMessage =
                        when (event) {
                            is BrokerEvent.TaskSubmitted -> "✅ [Broker] 任务已提交: ID=${event.taskId}, 优先级=${event.task.priority}"
                            is BrokerEvent.TaskRetracted -> "↩️ [Broker] 任务已撤回: ID=${event.taskId}"
                            is BrokerEvent.TaskPriorityUpdated -> "🔼 [Broker] 优先级更新: ID=${event.taskId}, 新优先级=${event.newPriority}"
                            is BrokerEvent.TaskDispatched -> "🚚 [Broker] 任务已分发: ID=${event.taskId} -> Consumer [${event.consumerId}]"
                            is BrokerEvent.TaskExecutionStarted -> "▶️ [Consumer] [${event.consumerId}] 开始执行任务: ID=${event.taskId}"
                            is BrokerEvent.TaskExecutionSuccess -> "✔️ [Consumer] [${event.consumerId}] 成功完成任务: ID=${event.taskId}"
                            is BrokerEvent.TaskExecutionFailed -> "❌ [Consumer] [${event.consumerId}] 任务失败: ID=${event.taskId}, 原因: ${event.error.message}"
                            is BrokerEvent.TaskExecutionCancelled -> "❌ [Consumer] [${event.consumerId}] 任务取消: ID=${event.taskId}"
                            is BrokerEvent.QueueCleared -> "🗑️ [Broker] 任务队列已清空"
                            is BrokerEvent.BrokerShutdown -> "🛑 [Broker] 调度中心已关闭"
                        }
                    println(logMessage)
                }
            }

        // 2. 创建并启动两个消费者
        val consumerA = Consumer("A", broker, CoroutineScope(appScope.coroutineContext + SupervisorJob()))
        val consumerB = Consumer("B", broker, CoroutineScope(appScope.coroutineContext + SupervisorJob()))
        consumerA.start()
        consumerB.start()
        println("\n--- 消费者 A 和 B 已启动 ---\n")
        delay(500)

        // 3. 生产者开始提交任务
        println("--- 场景1: 提交一组任务，观察优先级调度 ---")
        broker.submit(createTask("普通邮件", 5, 1000))
        broker.submit(createTask("报告生成", 2, 2000))
        broker.submit(createTask("!!!紧急修复!!!", 10, 1500))
        delay(5000) // 等待任务被消费

        println("\n--- 场景2: 运行时更新优先级 ---")
        val lowPriorityTaskId = UUID.randomUUID()
        broker.submit(
            PayloadTask(id = lowPriorityTaskId, "低优先级任务", 1, onAction = { payload ->
                println("  -> 执行: $payload")
                delay(5000)
            }),
        )
        broker.submit(createTask("另一个普通任务", 5, 1000))
        delay(500) // 等待普通任务被分发
        println("  -> 提升'低优先级任务'的优先级到最高(20)...")
        broker.updatePriority(lowPriorityTaskId, 20)
        delay(7000)

        println("\n--- 场景3: 撤回一个尚未执行的任务 ---")
        val taskToRetractId = UUID.randomUUID()
        broker.submit(createTask("会被消费的任务", 5, 1000))
        broker.submit(
            PayloadTask(
                id = taskToRetractId,
                "这个任务将被撤回",
                1,
                onAction = { payload -> println("  -> 如果你看到这条消息，说明撤回失败了！") },
            ),
        )
        delay(100) // 在它被调度前快速撤回
        broker.retract(taskToRetractId)
        delay(2000)

        println("\n--- 场景4: 停止消费者，提交任务，然后恢复 ---")
        println("  -> 停止 Consumer B...")
        consumerB.stop()
        broker.submit(createTask("任务A-1 (给A)", 7, 1000))
        broker.submit(createTask("任务C-1 (给B)", 8, 1000))
        delay(500) // A会处理任务A-1，但B不会处理C-1
        println("  -> 恢复 Consumer B...")
        consumerB.resume()
        delay(2000)

        println("\n--- 场景5: 演示任务失败和手动取消 ---")
        val failingTask =
            PayloadTask<String>(payload = "会失败的任务", priority = 9, onError = { e ->
                println("  -> 自定义错误回调被触发: ${e.message}")
            }) { throw IllegalStateException("数据库连接失败") }
        broker.submit(failingTask)

        val longRunningTask = createTask("一个很长的任务", 8, 10000)
        broker.submit(longRunningTask)
        delay(1500) // 等待长任务开始
        println("  -> 手动取消长任务...")
        // 假设我们知道是A在处理它
        if (consumerA.currentTask.value?.id == longRunningTask.id) consumerA.cancelCurrentTask()
        if (consumerB.currentTask.value?.id == longRunningTask.id) consumerB.cancelCurrentTask()
        delay(1000)

        println("\n--- 系统关闭 ---")
        broker.shutdown(hardStop = false)
        consumerA.shutdown()
        consumerB.shutdown()
        appScope.cancel()
        loggerJob.cancel()
    }

fun createTask(
    payload: String,
    priority: Int,
    delayMillis: Long,
): PayloadTask<String> =
    PayloadTask(
        payload = payload,
        priority = priority,
    ) {
        println("  -> 开始执行: '$it' (预计耗时: ${delayMillis}ms)")
        delay(delayMillis)
        println("  -> 完成执行: '$it'")
    }
