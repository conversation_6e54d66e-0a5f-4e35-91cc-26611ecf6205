package com.buque.wakoo.utils.koru

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

/**
 * 任务调度中心，负责管理任务的优先级队列和向消费者分发任务。
 * 它遵循结构化并发，其生命周期由传入的CoroutineScope控制。
 *
 * @param scope 控制Broker生命周期的协程作用域。当此scope被取消时，Broker将停止所有活动。
 */
@Suppress("ktlint:standard:backing-property-naming")
class TaskBroker(
    private val scope: CoroutineScope,
) {
    // 使用ConcurrentHashMap来存储任务，以实现高效的查找和更新。
    private val taskMap = ConcurrentHashMap<UUID, ITask>()

    // 使用StateFlow来维护一个按优先级排序的任务ID列表。这使得UI或其他观察者可以响应式地看到队列状态。
    private val _sortedTaskIds = MutableStateFlow<List<UUID>>(emptyList())

    // 分发通道，确保每个任务只被一个消费者接收。
    // Channel.UNLIMITED确保生产者（Broker的调度循环）在提交任务时不会被挂起。
    private val dispatchChannel = Channel<ITask>(Channel.UNLIMITED)

    // 事件总线，用于广播Broker内部的关键事件。
    private val _events = MutableSharedFlow<BrokerEvent>(extraBufferCapacity = 64)
    val events: SharedFlow<BrokerEvent> = _events.asSharedFlow()

    init {
        // 启动一个主调度协程。当scope被取消时，这个协程会自动停止。
        scope
            .launch(Dispatchers.Default) {
                // 只要Broker的scope是活跃的，就持续运行。
                while (isActive) {
                    // 等待队列中有任务出现
                    val nextTaskId = _sortedTaskIds.first { it.isNotEmpty() }.first()
                    val taskToDispatch = taskMap[nextTaskId]

                    if (taskToDispatch != null) {
                        // 从队列中移除并尝试发送到通道
                        removeTaskFromQueue(nextTaskId)
                        dispatchChannel.send(taskToDispatch)
                    }
                }
            }.invokeOnCompletion {
                // 当调度协程结束时（例如scope被取消），关闭通道。
                dispatchChannel.close()
            }
    }

    /** 提交一个新任务 */
    suspend fun submit(task: ITask) {
        taskMap[task.id] = task
        _sortedTaskIds.update { currentIds ->
            (currentIds + task.id).sortedByDescending { taskMap[it]?.priority ?: -1 }
        }
        _events.emit(BrokerEvent.TaskSubmitted(task))
    }

    /** 根据ID从待处理队列中撤回一个任务。如果任务已被分发，则无效。*/
    suspend fun retract(taskId: UUID) {
        if (taskMap.containsKey(taskId)) {
            removeTaskFromQueue(taskId)
            _events.emit(BrokerEvent.TaskRetracted(taskId))
        }
    }

    /** 更新一个已在队列中的任务的优先级。*/
    suspend fun updatePriority(
        taskId: UUID,
        newPriority: Int,
    ) {
        taskMap[taskId]?.let {
            taskMap[taskId] = it.updatePriority(newPriority = newPriority)
            _sortedTaskIds.update { currentIds ->
                currentIds.sortedByDescending { taskMap[it]?.priority ?: -1 }
            }
            _events.emit(BrokerEvent.TaskPriorityUpdated(taskId, newPriority))
        }
    }

    /**
     * 关闭Broker并清理资源。
     * @param hardStop 如果为true，将清空所有待处理的任务。如果为false，则允许队列中的任务被处理。
     */
    suspend fun shutdown(hardStop: Boolean) {
        if (hardStop) {
            taskMap.clear()
            _sortedTaskIds.value = emptyList()
            _events.emit(BrokerEvent.QueueCleared)
        }
        _events.emit(BrokerEvent.BrokerShutdown)
        // 取消主scope会停止调度循环并关闭通道，实现优雅关闭。
        scope.cancel()
    }

    // 将分发通道以只读形式暴露给内部的消费者
    internal fun consumeChannel() = dispatchChannel

    // 将事件发射器暴露给内部的消费者
    internal suspend fun emitEvent(event: BrokerEvent) = _events.emit(event)

    private fun removeTaskFromQueue(taskId: UUID) {
        taskMap.remove(taskId)
        _sortedTaskIds.update { currentIds ->
            currentIds.filterNot { it == taskId }
        }
    }
}
