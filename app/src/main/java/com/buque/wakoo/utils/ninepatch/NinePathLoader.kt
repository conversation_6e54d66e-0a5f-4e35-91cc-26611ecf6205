package com.buque.wakoo.utils.ninepatch

import android.graphics.BitmapFactory
import android.graphics.Rect
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.user.ChatBubble
import com.buque.wakoo.manager.DownloadManager
import com.buque.wakoo.utils.FileUtils
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.SoftReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArraySet
import kotlin.coroutines.cancellation.CancellationException

object NinePathLoader {
    private const val CACHE_BUBBLE_KEY = "cache_bubble_key"

    const val PIC_DENSITY_DPI = 480 // 设计图气泡素材设备像素密度

    private val cacheSet by lazy {
        CopyOnWriteArraySet<ChatBubble>()
    }

    private val softNinePatchBitmaps: ConcurrentHashMap<Any, SoftReference<NinePatchBitmap>> by lazy {
        ConcurrentHashMap()
    }

    fun preloadBubbleBitmap() {
        appCoroutineScope.launch(Dispatchers.Default) {
            val cache = DevicesKV.getString(CACHE_BUBBLE_KEY, "").orEmpty()
            val list =
                try {
                    AppJson.decodeFromString<List<ChatBubble>>(cache)
                } catch (e: Exception) {
                    DevicesKV.remove(CACHE_BUBBLE_KEY)
                    return@launch
                }
            cacheSet.addAll(list)
            LogUtils.i("DotNinePng", "提前预加载点九bitmap: ${cacheSet.size}")
            for (bubble in list) {
                coroutineScope {
                    launch {
                        preloadChatBubble(bubble.leftImg, bubble.leftPadding)
                    }
                    launch {
                        preloadChatBubble(bubble.rightImg, bubble.rightPadding)
                    }
                }
            }
        }
    }

    fun tryPreload(bubble: ChatBubble?) {
        bubble ?: return
        recordChatBubble(bubble)
        appCoroutineScope.launch {
            coroutineScope {
                launch {
                    preloadChatBubble(bubble.leftImg, bubble.leftPadding)
                }
                launch {
                    preloadChatBubble(bubble.rightImg, bubble.rightPadding)
                }
            }
        }
    }

    fun getChatBubbleCache(url: String): NinePatchBitmap? =
        softNinePatchBitmaps[url]?.let {
            val ret = it.get()
            if (ret == null) {
                softNinePatchBitmaps.remove(url)
            }
            ret
        }

    fun recordChatBubble(bubble: ChatBubble) {
        if (cacheSet.contains(bubble)) {
            return
        }
        appCoroutineScope.launch(Dispatchers.Default) {
            val contains =
                synchronized(cacheSet) {
                    val contains = cacheSet.contains(bubble)
                    if (!contains) {
                        cacheSet.add(bubble)
                    }
                    contains
                }
            if (!contains) {
                DevicesKV.putString(CACHE_BUBBLE_KEY, AppJson.encodeToString(cacheSet.toList()))
            }
        }
    }

    suspend fun load(
        url: String,
        padding: Rect,
    ): NinePatchBitmap? =
        getChatBubbleCache(url) ?: run {
            appCoroutineScope
                .launch {
                    downloadChatBubble(url, padding).also {
                        softNinePatchBitmaps[url] = SoftReference(it)
                    }
                }.join()
//            ninePatchShare.joinPreviousOrRun(url) {
            getChatBubbleCache(url)
//            }
        }

    private suspend fun preloadChatBubble(
        url: String,
        padding: Rect,
    ): NinePatchBitmap? =
        getChatBubbleCache(url) ?: try {
            downloadChatBubble(url, padding)?.also {
                softNinePatchBitmaps[url] = SoftReference(it)
            }
        } catch (e: Exception) {
            null
        }

    private val downloadPath: String by lazy {
        FileUtils.createDefaultBubbleDownloadOutputFile(WakooApplication.instance).absolutePath
    }

    private suspend fun downloadChatBubble(
        url: String,
        padding: Rect,
    ): NinePatchBitmap? =
//        ninePatchShare.joinPreviousOrRun(url, keepTime = 1000) {
        withContext(Dispatchers.IO) {
            try {
                url
                DownloadManager
                    .download(
                        url,
                        downloadPath,
                        url.split("/").last(),
                    ).getOrNull()
                    ?.let {
                        NinePatchChunk.create9PatchBitmap(WakooApplication.instance, it.path.decodeBitmapFromPath(), padding)
                    }
            } catch (e: CancellationException) {
                throw e
            } catch (_: Throwable) {
                null
            }
        }
//        }

    private fun String.decodeBitmapFromPath() =
        BitmapFactory.decodeFile(this)?.also {
            it.density = PIC_DENSITY_DPI
        }
}
