package com.buque.wakoo.utils.datapoints

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.buque.wakoo.app.AppJson

object Analytics {

    object Event {
        //region 首充礼包
        const val NEWBIE_PACKAGE_PENDANT_SHOW = "newbie_package_pendant_show"//首充礼包挂件展示
        const val NEWBIE_PACKAGE_PENDANT_CLICK = "newbie_package_pendant_click"//首充礼包挂件点击
        const val NEWBIE_PACKAGE_POPUP_BUY_BTN_CLICK = "newbie_package_popup_buy_btn_click"//首充礼包购买按钮点击
        //endregion

        //region 私密小屋
        const val NEWBIE_PR_POPUP_ACCEPT = "NEWBIE_PR_POPUP_ACCEPT"//点击"接受邀请"按钮
        const val NEWBIE_PR_GUIDE_MIC_GRANT = "newbie_pr_guide_mic_grant"//点击"授权麦克风"
        const val NEWBIE_PR_GUIDE_MIC_REFUSE = "newbie_pr_guide_mic_refuse"//点击"拒绝授权麦克风"
        //endregion
    }

    fun trace(eventName: String, properties: Map<String, Any>) {
        ShushuUtils.trace(eventName, AppJson.encodeToString(properties))
    }
}


/**
 * 页面进入时上报
 *
 * @param exposureName 上报事件(页面)
 * @param reExposureMode 是否重复曝光,false为只执行一次
 * @param onDispose 被释放时
 * @param content 内容
 */
@Composable
inline fun PageEnterReport(
    exposureName: String,
    crossinline properties: () -> Map<String, Any> = { mapOf() },
    reExposureMode: Boolean = true,
    noinline onDispose: () -> Unit = {},
) {
    ReportExposureCompose(
        reExposureMode = reExposureMode,
        onExposureStart = {
            Analytics.trace(exposureName, (properties()))
        },
        onDispose = onDispose,
    )
}

/**
 * 页面退出时上报
 *
 * @param exposureName 上报事件(页面)
 * @param reExposureMode 是否重复曝光,false为只执行一次
 * @param onDispose 被释放时
 * @param content 内容
 */
@Composable
inline fun PageExitReport(
    exposureName: String,
    crossinline properties: () -> Map<String, Any> = { mapOf() },
    reExposureMode: Boolean = true,
    noinline onDispose: () -> Unit = {},
) {
    ReportExposureCompose(
        reExposureMode = reExposureMode,
        onExposureEnd = {
            Analytics.trace(exposureName, (properties()))
        },
        onDispose = onDispose,
    )
}

@Composable
inline fun PageExposureReport(
    noinline buildExposureStartProp: () -> Pair<String, Map<String, Any>> = { "" to emptyMap() },
    noinline buildExposureEndProp: () -> Pair<String, Map<String, Any>> = { "" to emptyMap() },
    reExposureMode: Boolean = true,
    noinline onDispose: () -> Unit = {},
) {
    ReportExposureCompose(
        reExposureMode = reExposureMode,
        onExposureStart = {
            val prop = buildExposureStartProp()
            Analytics.trace(prop.first, (prop.second))
        },
        onExposureEnd = {
            val prop = buildExposureEndProp()
            Analytics.trace(prop.first, (prop.second))
        },
        onDispose = onDispose,
    )
}


/**
 * @param exposureName 上报事件(页面)
 * @param reExposureMode 是否重复曝光,false为只执行一次
 * @param onExposureStart 进入页面时
 * @param onExposureEnd 退出页面时
 * @param onDispose 被释放时
 * @param content 内容
 */
@Composable
inline fun ReportExposureCompose(
    reExposureMode: Boolean = true,
    noinline onExposureStart: () -> Unit = {},
    noinline onExposureEnd: () -> Unit = {},
    noinline onDispose: () -> Unit = {},
) {

    val currentOnExposureStart by rememberUpdatedState(onExposureStart)

    val currentOnExposureEnd by rememberUpdatedState(onExposureEnd)

    val currentOnDispose by rememberUpdatedState(onDispose)

    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                //曝光
                currentOnExposureStart()
            } else if (event == Lifecycle.Event.ON_PAUSE) {
                //反曝光
                currentOnExposureEnd()
            }
        }
        if (reExposureMode) {
            lifecycleOwner.lifecycle.addObserver(observer)
        } else {
            currentOnExposureStart()
        }

        onDispose {
            //反曝光
            currentOnDispose()
            if (reExposureMode) {
                lifecycleOwner.lifecycle.removeObserver(observer)
            } else {
                currentOnExposureEnd()
            }
        }
    }
}