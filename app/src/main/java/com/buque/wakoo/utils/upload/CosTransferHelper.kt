package com.buque.wakoo.utils.upload

import android.net.Uri
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.bean.OssToken
import com.buque.wakoo.manager.localized
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.transfer.COSXMLUploadTask
import com.tencent.cos.xml.transfer.TransferManager
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import kotlin.coroutines.resume

/**
 * 一个包装了 [COSXMLUploadTask] 的协程友好类。
 * 它允许你挂起等待上传结果，同时也可以控制任务（暂停、恢复、取消）。
 *
 * @property task 底层的 COS 上传任务实例。
 */
class UploadTaskWrapper(
    private val domain: String,
    private val cosPath: String,
    val task: COSXMLUploadTask,
) {
    /**
     * 挂起当前协程，直到上传任务完成（成功或失败）。
     * @return 返回一个 [TransferResult] 对象。
     */
    suspend fun await(): TransferResult =
        suspendCancellableCoroutine { continuation ->
            task.setCosXmlResultListener(
                object : CosXmlResultListener {
                    override fun onSuccess(
                        request: CosXmlRequest?,
                        result: CosXmlResult,
                    ) {
                        if (continuation.isActive) {
                            continuation.resume(
                                TransferResult.Success(
                                    result as COSXMLUploadTask.COSXMLUploadTaskResult,
                                    if (domain.isBlank()) result.accessUrl else "$domain/$cosPath",
                                ),
                            )
                        }
                    }

                    override fun onFail(
                        request: CosXmlRequest,
                        clientException: CosXmlClientException?,
                        serviceException: CosXmlServiceException?,
                    ) {
                        if (continuation.isActive) {
                            val exception = clientException ?: serviceException ?: Exception("未知上传错误".localized)
                            continuation.resume(TransferResult.Failure(exception))
                        }
                    }
                },
            )

            // 当协程被取消时，也取消上传任务
            continuation.invokeOnCancellation {
                if (task.taskState != com.tencent.cos.xml.transfer.TransferState.COMPLETED) {
                    task.cancel()
                }
            }
        }

    /**
     * 暂停上传任务。
     * 如果上传已经进入最后完成阶段，则暂停可能会失败。
     * @return 如果暂停请求被成功接受，则返回 true。
     */
    fun pause(): Boolean = task.pauseSafely()

    /**
     * 恢复已暂停的上传任务。
     */
    fun resume() = task.resume()

    /**
     * 取消上传任务。
     */
    fun cancel() = task.cancel()
}

/**
 * 一个基于腾讯云COS高级接口 [TransferManager] 的协程上传帮助类。
 *
 * @param transferManager 一个已初始化的 [TransferManager] 实例。
 */
class CosTransferHelper {
    private val transferManager: TransferManager
        get() = CosTransferProvider.transferManager

    /**
     * 上传一个本地文件。
     *
     * @param cosPath 对象在COS上的路径（对象键）。
     * @param file 要上传的 [File] 对象。
     * @param uploadId 可选的 uploadId，用于断点续传。
     * @param callbacks 用于接收进度、状态和 uploadId 的回调。
     * @return 返回一个 [UploadTaskWrapper] 对象，用于控制任务和等待结果。
     */
    suspend fun upload(
        cosPath: String,
        file: File,
        uploadId: String? = null,
        callbacks: UploadCallbacks = UploadCallbacks(),
    ): UploadTaskWrapper {
        val token = CosTransferProvider.getOssToken()
        val bucket = token.bucket
        val cosPath = "${token.pathPrefix}$cosPath"
        val task = transferManager.upload(bucket, cosPath, file.absolutePath, uploadId)
        return setupCallbacks(token, cosPath, task, callbacks)
    }

    /**
     * 通过文件路径上传一个本地文件。
     *
     * @param cosPath 对象在COS上的路径（对象键）。
     * @param filePath 要上传文件的绝对路径。
     * @param uploadId 可选的 uploadId，用于断点续传。
     * @param callbacks 用于接收进度、状态和 uploadId 的回调。
     * @return 返回一个 [UploadTaskWrapper] 对象，用于控制任务和等待结果。
     */
    suspend fun upload(
        cosPath: String,
        filePath: String,
        uploadId: String? = null,
        callbacks: UploadCallbacks = UploadCallbacks(),
    ): UploadTaskWrapper {
        val token = CosTransferProvider.getOssToken()
        val bucket = token.bucket
        val cosPath = "${token.pathPrefix}$cosPath"
        val task = transferManager.upload(bucket, cosPath, filePath, uploadId)
        return setupCallbacks(token, cosPath, task, callbacks)
    }

    /**
     * 通过 [Uri] 上传一个文件，通常用于处理来自相册或文件选择器的文件。
     *
     * @param cosPath 对象在COS上的路径（对象键）。
     * @param uri 要上传文件的 [Uri]。
     * @param uploadId 可选的 uploadId，用于断点续传。
     * @param callbacks 用于接收进度、状态和 uploadId 的回调。
     * @return 返回一个 [UploadTaskWrapper] 对象，用于控制任务和等待结果。
     */
    suspend fun upload(
        cosPath: String,
        uri: Uri,
        uploadId: String? = null,
        callbacks: UploadCallbacks = UploadCallbacks(),
    ): UploadTaskWrapper {
        val token = CosTransferProvider.getOssToken()
        val bucket = token.bucket
        val cosPath = "${token.pathPrefix}$cosPath"
        val task = transferManager.upload(bucket, cosPath, uri, uploadId)
        return setupCallbacks(token, cosPath, task, callbacks)
    }

    suspend fun upload(
        uris: List<Uri>,
        cosPrefix: String,
        callbacks: UploadCallbacks = UploadCallbacks(),
    ): List<UploadTaskWrapper> {
        val token = CosTransferProvider.getOssToken()
        val bucket = token.bucket
        return uris.map { uri ->
            val path = UploadUtils.generateOSSPath(WakooApplication.instance, uri, cosPrefix)
            val cosPath = "${token.pathPrefix}$path"
            val task = transferManager.upload(bucket, cosPath, uri, null)
            // 这里不能用串行, 最好并行上传,这样可以保证token不过期
            setupCallbacks(token, cosPath, task, callbacks)
        }
    }

    /**
     * 辅助函数，为 [COSXMLUploadTask] 设置各种回调。
     */
    private fun setupCallbacks(
        token: OssToken,
        cosPath: String,
        task: COSXMLUploadTask,
        callbacks: UploadCallbacks,
    ): UploadTaskWrapper {
        task.setCosXmlProgressListener { complete, target ->
            callbacks.onProgress(complete, target)
        }
        task.setTransferStateListener { state ->
            callbacks.onStateChanged(state)
        }
        task.setInitMultipleUploadListener { initiateMultipartUpload ->
            callbacks.onInitUploadId(initiateMultipartUpload.uploadId)
        }
        return UploadTaskWrapper(token.domain, cosPath, task)
    }
}
