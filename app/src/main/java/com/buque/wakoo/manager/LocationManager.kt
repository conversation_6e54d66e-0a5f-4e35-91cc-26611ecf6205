package com.buque.wakoo.manager

import android.annotation.SuppressLint
import android.content.Context
import android.location.Address
import android.location.Geocoder
import android.location.Location
import android.location.LocationManager
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.location.LocationListenerCompat
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.ext.resumeIfActive
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resumeWithException

@SuppressLint("MissingPermission")
object LocationManager {
    data class LocationData(
        val lat: Double,
        val lon: Double,
        val country: String,
        val city: String,
    ) {
        fun toStringPair() = country to city

        fun getFullAddress() = "$country · $city"

        val isEmpty: Boolean
            get() = lat == 0.0 && lon == 0.0
    }

    private val providers = listOf(LocationManager.GPS_PROVIDER, LocationManager.NETWORK_PROVIDER)
    val locationFlow = MutableStateFlow<LocationData?>(null)

    private val locationManager: LocationManager by lazy {
        WakooApplication.instance.getSystemService(LocationManager::class.java)
    }

    @Volatile
    private var isRequesting = false

    fun requestLocationOnce() {
        if (isRequesting) {
            return
        }
        if (isLocationEnable(WakooApplication.instance).not()) {
            showToast("定位服务未开启".localized)
            return
        }
        appCoroutineScope.launch(Dispatchers.Main) {
            isRequesting = true
            LogUtils.d("loc :requesting")
            try {
                var location: Location? = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)
                LogUtils.d("loc :getLastKnownLocation ->$location")
                if (location == null) {
                    for (provider in providers) {
                        val providerEnabled = locationManager.isProviderEnabled(provider)
                        LogUtils.d("provider enable:$provider->$providerEnabled")
                        if (providerEnabled) {
                            location = getLocation(provider)
                            if (location != null) {
                                break
                            }
                        }
                    }
                }
                if (location == null) {
                    return@launch
                }
                val address: Address? =
                    location.let {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            getAddress33(it)
                        } else {
                            getAddress(it)
                        }
                    }
                LogUtils.d("loc:  address:$address")
                LocationData(
                    location.latitude,
                    location.longitude,
                    address?.countryName ?: "",
                    address?.locality ?: "",
                ).let {
                    locationFlow.emit(it)
                }
            } catch (e: Exception) {
                LogUtils.d("loc :${e.message}")
                locationFlow.emit(LocationData(0.0, 0.0, "", ""))
            } finally {
                isRequesting = false
            }
            LogUtils.d("loc : finally")
        }
    }

    private fun isLocationEnable(context: Context): Boolean {
        val locationManager = context.getSystemService(LocationManager::class.java)
        val gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        val network = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        return gps || network
    }

    private suspend fun getLocation(provider: String = LocationManager.GPS_PROVIDER): Location? =
        withTimeoutOrNull(15_000) {
            suspendCancellableCoroutine { continuation ->
                try {
                    val listener =
                        object : LocationListenerCompat {
                            override fun onLocationChanged(location: Location) {
                                continuation.resumeIfActive(location)
                                LogUtils.d("loc: changed $location")
                                locationManager.removeUpdates(this)
                            }
                        }
                    LogUtils.d("loc: req update")
                    locationManager.requestLocationUpdates(
                        provider,
                        5L,
                        1f,
                        listener,
                    )
                    continuation.invokeOnCancellation {
                        locationManager.removeUpdates(listener)
                    }
                } catch (e: Exception) {
                    LogUtils.w("GetLocationService", "loc: error %s", e)
                    continuation.resumeIfActive(null)
                }
            }
        }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private suspend fun getAddress33(location: Location) =
        suspendCancellableCoroutine { cancellableContinuation ->
            try {
                val geo = Geocoder(WakooApplication.instance)
                geo.getFromLocation(location.latitude, location.longitude, 5) {
                    cancellableContinuation.resumeIfActive(it.firstOrNull())
                }
            } catch (e: Exception) {
                if (cancellableContinuation.isActive) {
                    cancellableContinuation.resumeWithException(e)
                }
            }
        }

    private suspend fun getAddress(location: Location): Address? =
        withContext(Dispatchers.Default) {
            try {
                val geo = Geocoder(WakooApplication.instance)
                geo.getFromLocation(location.latitude, location.longitude, 5)?.firstOrNull()
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
}
