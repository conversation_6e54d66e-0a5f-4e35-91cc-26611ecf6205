package com.buque.wakoo.manager

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import com.buque.wakoo.MainActivity
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.im.utils.doOnLifecycleEvent
import com.buque.wakoo.im_business.GlobalNotification
import com.buque.wakoo.im_business.rtm.IRtmMsgHandler
import com.buque.wakoo.im_business.rtm.RtmManager
import com.buque.wakoo.im_business.rtm.RtmMsgHandler
import com.buque.wakoo.ui.dialog.AppRatingContent
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.easyPostDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.util.UUID

object AppManager {
    var activeUUID: String = ""
        private set

    private val rtmMsgHandler: IRtmMsgHandler = RtmMsgHandler()

    private val appActivityState: MutableState<MainActivity?> = mutableStateOf(null)

    private val appIsForegroundState: MutableState<Boolean> = mutableStateOf(false)

    val appActivity
        get() = appActivityState.value

    val appActivityFlow = snapshotFlow { appActivityState.value }

    val appIsForeground
        get() = appIsForegroundState.value

    val appIsForegroundFlow = snapshotFlow { appIsForeground }

    fun initialize(activity: MainActivity) {
        activeUUID = UUID.randomUUID().toString()
        checkShowAppRatingDialog(activity)

        activity.lifecycle.doOnLifecycleEvent { event ->
            when (event) {
                Lifecycle.Event.ON_CREATE -> {
                    appActivityState.value = activity
                    AppSavedStateManager.bind(activity.savedStateRegistry)
                }

                Lifecycle.Event.ON_START -> {
                    appIsForegroundState.value = true
                    GlobalNotification.register()
                }

                Lifecycle.Event.ON_STOP -> {
                    appIsForegroundState.value = false
                    GlobalNotification.unregister()
                }

                Lifecycle.Event.ON_DESTROY -> {
                    appIsForegroundState.value = false
                    appActivityState.value = null
                }

                else -> Unit
            }
        }

        RtmManager.message
            .flowWithLifecycle(activity.lifecycle)
            .onEach {
                rtmMsgHandler.handleMessage(it)
            }.launchIn(activity.lifecycleScope)
    }

    private fun checkShowAppRatingDialog(activity: MainActivity) {
        if (EnvironmentManager.isGoogleChannel && EnvironmentManager.isProdRelease &&
            !DevicesKV.getBoolean(Const.KVKey.APP_RATING_COMPLETE, false)
        ) {
            val count = DevicesKV.getInt(Const.KVKey.APP_STARTUP_COUNT, 0)
            if (count == 2 || count == 4 || count == 8 || count == 10 || count == 15) {
                activity.lifecycleScope.launch {
                    activity.withResumed {}
                    delay(150)
                    EventBus.easyPostDialog {
                        AppRatingContent {
                            DevicesKV.putBoolean(Const.KVKey.APP_RATING_COMPLETE, true)
                        }
                    }
                }
            }
        }
    }
}
