package com.buque.wakoo.manager

import com.buque.wakoo.BuildConfig
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.AppEnvConfig
import com.buque.wakoo.bean.Environment
import com.buque.wakoo.ext.restartApp
import com.tencent.mmkv.MMKV

object EnvironmentManager {
    private const val CONFIG_ENV_KEY = "config_env_key"
    private const val CONFIG_ENV_LOG_KEY = "config_env_log_key"

    val isReleaseBuild = BuildConfig.BUILD_TYPE == "release"

    val isProfileBuild = BuildConfig.BUILD_TYPE == "profile"

    val isDebugBuild = BuildConfig.BUILD_TYPE == "debug"

    val isDevelopmentEnvironment: Boolean
        get() {
            check(isInitialized)
            return currentEnvironmentKey == "development"
        }

    val isProductionEnvironment: Boolean
        get() {
            check(isInitialized)
            return currentEnvironmentKey == "production"
        }

    val isGoogleChannel = BuildConfig.FLAVOR_channel == "play"

    val isProdRelease
        get() = isReleaseBuild && !isProductionEnvironment

    private var isInitialized = false

    private val mmkv by lazy {
        MMKV.mmkvWithID("env_config_prefs")
    }

    // 从 BuildConfig 解析的完整配置
    private val fullAppEnvConfig: AppEnvConfig by lazy {
        AppJson.decodeFromString<AppEnvConfig>(BuildConfig.ENV_CONFIG_JSON)
    }

    val prodApiUrl by lazy {
        (fullAppEnvConfig.environments["production"] ?: fullAppEnvConfig.environments.values.firstOrNull())?.apiUrl
            ?: "https://fastapi.wakooclub.com/"
    }

    private var currentEnvironmentKey: String = ""

    /**
     * 获取当前激活的环境配置
     * 优先使用运行时环境，如果没有则使用编译时环境
     */
    val current: Environment by lazy {
        val environment =
            fullAppEnvConfig.environments[currentEnvironmentKey]
                ?: if (fullAppEnvConfig.environments.size == 1) {
                    fullAppEnvConfig.environments.values.single()
                } else {
                    throw IllegalStateException(
                        "Current flavor '$currentEnvironmentKey' does not have a matching environment in the config map.",
                    )
                }
        if (isProdRelease) {
            environment
        } else {
            environment.copy(enableLog = mmkv.getBoolean(CONFIG_ENV_LOG_KEY, environment.enableLog))
        }
    }

    /**
     * 初始化环境管理器
     * @param context 应用上下文
     * @param initialEnvKey 初始环境键（可选）
     */
    fun initialize() {
        if (isInitialized) {
            return
        }
        isInitialized = true
        currentEnvironmentKey =
            if (isProdRelease) {
                "production"
            } else {
                mmkv.getString(CONFIG_ENV_KEY, null)?.takeIf { it.isNotBlank() }
                    ?: fullAppEnvConfig.defaultEnvironment?.takeIf { it.isNotBlank() } ?: BuildConfig.FLAVOR_environment
            }
    }

    /**
     * 获取所有可用的环境列表（仅在 Debug 模式下可用）
     */
    fun getAvailableEnvironments(): Map<String, Environment>? =
        if (!isProdRelease) {
            fullAppEnvConfig.environments
        } else {
            null
        }

    /**
     * 获取当前运行时环境的键
     */
    fun getCurrentEnvironmentKey(): String = currentEnvironmentKey

    /**
     * 切换到指定环境（仅在 Debug 模式下可用）
     * @param context 上下文
     * @param environmentKey 环境键
     * @param autoRestart 是否自动重启应用，默认为 true
     * @return 切换是否成功
     */
    fun switchEnvironment(environmentKey: String): Boolean {
        if (isProdRelease) {
            return false
        }
        if (fullAppEnvConfig.environments.containsKey(environmentKey)) {
            mmkv.putString(CONFIG_ENV_KEY, environmentKey)
            restartApp()
            return true
        }
        return false
    }

    fun switchLogEnable(enable: Boolean): Boolean {
        if (isProdRelease) {
            return false
        }
        mmkv.putBoolean(CONFIG_ENV_LOG_KEY, enable)
        restartApp()
        return true
    }

    fun reset() {
        if (!isProdRelease) {
            mmkv.clear()
            restartApp()
        }
    }
}
