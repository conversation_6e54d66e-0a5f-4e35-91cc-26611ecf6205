package com.buque.wakoo.manager

import android.content.Context
import androidx.credentials.CredentialManager
import androidx.credentials.CustomCredential
import androidx.credentials.GetCredentialRequest
import androidx.credentials.GetCredentialResponse
import androidx.credentials.exceptions.GetCredentialCancellationException
import androidx.credentials.exceptions.GetCredentialException
import androidx.credentials.exceptions.NoCredentialException
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GetSignInWithGoogleOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.android.libraries.identity.googleid.GoogleIdTokenParsingException
import com.buque.wakoo.utils.EncryptionUtils

object GoogleManager {
    suspend fun signIn(
        context: Context,
        signIn: Boolean = true,
    ): Result<String> {
        val googleClientId = EnvironmentManager.current.googleClientId
        // 注册
        val signUpOption =
            GetGoogleIdOption
                .Builder()
                .setFilterByAuthorizedAccounts(false)
                .setServerClientId(googleClientId)
                .build()
        // 登录
        val signInOption =
            GetSignInWithGoogleOption
                .Builder(googleClientId)
                .setNonce(EncryptionUtils.generateRandomString())
                .build()
        val request: GetCredentialRequest =
            GetCredentialRequest
                .Builder()
                .addCredentialOption(if (signIn) signInOption else signUpOption)
                .build()
        return try {
            val result =
                CredentialManager.create(context).getCredential(
                    request = request,
                    context = context,
                )
            handleResult(result)
        } catch (e: GetCredentialException) {
            if (e is GetCredentialCancellationException) {
                Result.failure(IllegalStateException("登录已取消".localized, e))
            } else if (signIn && e is NoCredentialException) {
                signIn(context, false)
            } else {
                val msg = "google signIn error(${e.javaClass.simpleName}): ${e.message}"
                Result.failure(IllegalStateException(msg))
            }
        } catch (e: Throwable) {
            val msg = "google signIn error(${e.javaClass.simpleName}): ${e.message}"
            Result.failure(IllegalStateException(msg))
        }
    }

    private fun handleResult(result: GetCredentialResponse): Result<String> {
        // Handle the successfully returned credential.
        return when (val credential = result.credential) {
            is CustomCredential -> {
                if (credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
                    try {
                        val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
                        val idToken = googleIdTokenCredential.idToken
                        if (idToken.isEmpty()) {
                            Result.failure(IllegalStateException("出错了，请重新登录！".localized))
                        } else {
                            Result.success(idToken)
                        }
                    } catch (e: GoogleIdTokenParsingException) {
                        val msg = "google signIn error: ${e.message}"
                        Result.failure(IllegalStateException(msg, e))
                    }
                } else {
                    val msg = "google signIn error, unrecognized custom credential type: ${credential.type}"
                    Result.failure(IllegalStateException(msg))
                }
            }

            else -> {
                val msg =
                    "google signIn error, unrecognized credential type(${credential.javaClass.simpleName}): ${credential.type}"
                Result.failure(IllegalStateException(msg))
            }
        }
    }
}
