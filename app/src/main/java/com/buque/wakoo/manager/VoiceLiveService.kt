package com.buque.wakoo.manager

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.core.app.ServiceCompat
import com.buque.wakoo.R

class VoiceLiveService : Service() {
    companion object {
        private const val CHANNEL_ID = "live_room"

        private const val ONGOING_NOTIFICATION_ID = 19811120
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(
        intent: Intent?,
        flags: Int,
        startId: Int,
    ): Int {
        // 确保你的服务在接收到启动命令后立即进入前台
        val notification = createNotification()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            ServiceCompat.startForeground(
                this,
                ONGOING_NOTIFICATION_ID,
                notification,
                // 声明服务类型，这在 Android 10 (API 29) 及以上是必需的
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE or ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK,
            )
        } else {
            startForeground(ONGOING_NOTIFICATION_ID, notification) // ID 不能为 0
        }
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        stopForeground(STOP_FOREGROUND_REMOVE)
    }

    private fun createNotification(): Notification {
        // 创建 Intent 和 PendingIntent
        val launchIntent: Intent? = packageManager.getLaunchIntentForPackage(packageName)

        val pendingIntent =
            launchIntent?.let { intent ->
                PendingIntent.getActivity(
                    this,
                    0,
                    intent,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    } else {
                        PendingIntent.FLAG_UPDATE_CURRENT
                    },
                )
            }

        // 创建一个前台服务通知
        return NotificationCompat
            .Builder(this, CHANNEL_ID)
            .setContentIntent(pendingIntent)
            .setContentTitle("Wakoo聊天交友".localized)
            .setContentText("Wakoo正在后台运行，可以接收语音频道声音".localized)
            .setSmallIcon(R.drawable.ic_app_logo)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // 设置通知公开可见
            .setOngoing(true) // 设置持续(不消失的常驻通知)
            .setCategory(Notification.CATEGORY_SERVICE) // 设置类别
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .build()
    }

    private fun createNotificationChannel() {
        val serviceChannel =
            NotificationChannel(
                CHANNEL_ID,
                "语音直播服务".localized,
                NotificationManager.IMPORTANCE_LOW,
            )
        val manager = getSystemService(NotificationManager::class.java)
        manager.createNotificationChannel(serviceChannel)
    }
}
