package com.buque.wakoo.manager

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.media.MediaRecorder
import android.os.Build
import androidx.core.content.ContextCompat
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.ext.formatMillisDuration
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.FileUtils
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileNotFoundException
import java.io.IOException

/**
 * 录制配置类
 *
 * 默认配置说明：
 * - 采样率 22050Hz：在保证语音清晰度的同时减小文件大小，适合语音录制
 * - 比特率 64kbps：平衡音质和文件大小，适合大部分使用场景
 * - 单声道：语音录制通常不需要立体声，可以减半文件大小
 * - AAC编码：兼容性好，压缩效率高
 * - 最大时长 60000毫秒：适合大部分录音场景
 * - 最短时长 5000毫秒：避免误触产生无效录音
 */
data class RecordingConfig(
    val maxDurationMillis: Long = 60000L, // 最大录制时长（毫秒），默认1分钟
    val minDurationMillis: Long = 5000L, // 最短录制时长（毫秒），默认5秒
    val outputDirectory: File? = null, // 输出目录，null则使用应用私有目录
    val audioSource: Int = MediaRecorder.AudioSource.VOICE_COMMUNICATION,
    val outputFormat: Int = MediaRecorder.OutputFormat.MPEG_4,
    val audioEncoder: Int = MediaRecorder.AudioEncoder.AAC,
    val audioSamplingRate: Int = 22050, // 采样率：22.05kHz，平衡音质和文件大小
    val audioEncodingBitRate: Int = 64000, // 比特率：64kbps，适合语音录制
    val audioChannels: Int = 1, // 声道数：单声道，减小文件大小
    val fileExtension: String = "m4a", // 文件扩展名
    val enableAutoStop: Boolean = true, // 是否启用自动停止
) {
    companion object {
        /**
         * 获取预估的文件大小（字节）
         * 公式：(比特率 * 时长毫秒数) / 8 / 1000
         */
        fun getEstimatedFileSize(
            config: RecordingConfig,
            durationMillis: Long,
        ): Long = (config.audioEncodingBitRate * durationMillis / 8 / 1000).toLong()

        /**
         * 格式化文件大小为可读字符串
         */
        @SuppressLint("DefaultLocale")
        fun formatFileSize(sizeBytes: Long): String =
            when {
                sizeBytes < 1024 -> "${sizeBytes}B"
                sizeBytes < 1024 * 1024 -> "${sizeBytes / 1024}KB"
                else ->
                    String.format(
                        "%.1fMB",
                        sizeBytes / (1024.0 * 1024.0),
                    )
            }

        /**
         * 获取配置描述信息
         */
        fun getConfigDescription(config: RecordingConfig): String {
            val channelDesc = if (config.audioChannels == 1) "单声道" else "立体声"
            val qualityDesc =
                when {
                    config.audioEncodingBitRate >= 128000 -> "高音质"
                    config.audioEncodingBitRate >= 64000 -> "标准音质"
                    else -> "节省空间"
                }

            return "$qualityDesc, ${config.audioSamplingRate / 1000}kHz, ${config.audioEncodingBitRate / 1000}kbps, $channelDesc"
        }
    }
}

/**
 * 录制结束原因枚举
 */
enum class RecordingEndReason {
    USER_STOPPED, // 用户主动停止
    AUTO_STOPPED, // 达到最大时长自动停止
    SYSTEM_INTERRUPTED, // 系统中断（如来电）
    ERROR_OCCURRED, // 发生错误
}

/**
 * 录制失败原因枚举
 */
enum class RecordingFailureReason {
    PERMISSION_DENIED, // 权限被拒绝
    DEVICE_BUSY, // 设备被占用
    STORAGE_INSUFFICIENT, // 存储空间不足
    DURATION_TOO_SHORT, // 录制时长太短
    MEDIA_RECORDER_ERROR, // MediaRecorder错误
    FILE_NOT_EXIST, // 文件不存在
    IO_ERROR, // 文件IO错误
    UNKNOWN_ERROR, // 未知错误
}

/**
 * 录制状态密封类 - 统一管理所有状态和数据信息
 */
sealed class RecordingState(
    val duration: Int = 0,
    val outputFile: File? = null,
    val fileSize: Long = 0L,
) {
    // 空闲状态
    object Idle : RecordingState()

    // 录制中
    data class Recording(
        val currentDuration: Int = 0,
        val currentOutputFile: File,
        val config: RecordingConfig,
        val preparingCancel: Boolean = false,
    ) : RecordingState(
            duration = currentDuration,
            outputFile = currentOutputFile,
        )

    // 暂停中
    data class Paused(
        val currentDuration: Int = 0,
        val currentOutputFile: File,
        val config: RecordingConfig,
    ) : RecordingState(
            duration = currentDuration,
            outputFile = currentOutputFile,
        )

    // 录制完成
    data class Completed(
        val totalDuration: Int,
        val finalOutputFile: File,
        val finalFileSize: Long,
        val endReason: RecordingEndReason,
        val config: RecordingConfig,
    ) : RecordingState(
            duration = totalDuration,
            outputFile = finalOutputFile,
            fileSize = finalFileSize,
        )

    // 错误状态
    data class Error(
        val message: String,
        val failureReason: RecordingFailureReason,
        val cause: Throwable? = null,
        val config: RecordingConfig,
    ) : RecordingState()

    // 便捷的状态判断属性
    val isRecording: Boolean get() = this is Recording
    val isPaused: Boolean get() = this is Paused
    val isIdle: Boolean get() = this is Idle
    val isCompleted: Boolean get() = this is Completed
    val hasError: Boolean get() = this is Error
    val isPermissionDenied: Boolean get() = this is Error && failureReason == RecordingFailureReason.PERMISSION_DENIED

    // 便捷的文件路径访问
    val filePath: String? get() = outputFile?.absolutePath

    // 格式化时长
    val formattedDuration: String
        @SuppressLint("DefaultLocale")
        get() = duration.formatMillisDuration
}

/**
 * 音频录制管理器
 * 负责处理音频录制的开始、暂停、停止等操作
 *
 * 功能特性：
 * - 权限检查
 * - 录制时长计时
 * - 最大录制时长限制
 * - 可配置的录制参数
 * - 完善的状态管理
 * - 错误处理和回调
 */
class AudioRecordManager private constructor(
    private val context: Context,
    private val globalConfig: RecordingConfig = RecordingConfig(),
) {
    companion object {
        val singletonInstance by lazy {
            AudioRecordManager(WakooApplication.instance)
        }

        fun createInstance(
            context: Context,
            globalConfig: RecordingConfig = RecordingConfig(),
        ) = AudioRecordManager(context.applicationContext, globalConfig)
    }

    private var config = globalConfig

    private var mediaRecorder: MediaRecorder? = null
    private var recordingStartTime = 0L
    private var pausedDuration = 0L // 暂停的总时长
    private var timerJob: Job? = null
    private var actualRecordingStarted = false // 标记是否真正开始录音

    // 统一的状态管理
    private val _recordingState = MutableStateFlow<RecordingState>(RecordingState.Idle)
    val recordingState: StateFlow<RecordingState> = _recordingState.asStateFlow()

    // 便捷的状态访问属性
    val currentState: RecordingState get() = _recordingState.value
    val currentDuration: Int get() = _recordingState.value.duration
    val isRecording: Boolean get() = _recordingState.value.isRecording
    val isPaused: Boolean get() = _recordingState.value.isPaused

    /**
     * 检查录音权限
     */
    fun checkPermissions(): Boolean =
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO,
        ) == PackageManager.PERMISSION_GRANTED

    /**
     * 开始录制
     */
    fun startRecording(
        outputDirectory: File? = null, // 输出文件夹
        maxDurationMillis: Long = 60000L, // 最大录制时长（毫秒），默认1分钟
        minDurationMillis: Long = 5000L, // 最短录制时长（毫秒），默认5秒
    ): Boolean {
        // 配置录制参数
        config =
            globalConfig.copy(
                maxDurationMillis = maxDurationMillis,
                minDurationMillis = minDurationMillis,
                outputDirectory = outputDirectory,
            )
        val state = _recordingState.value
        if (state.isRecording) {
            LogUtils.w("录音已在进行中，无法重复开始")
            return false
        }
        // 检查权限
        if (!checkPermissions()) {
            handleRecordingError(
                "录音权限未授予".localized,
                RecordingFailureReason.PERMISSION_DENIED,
                null,
                null,
            )
            return false
        }

        if (state.isPaused) {
            // 如果是暂停状态，恢复录音
            LogUtils.d("检测到暂停状态，自动恢复录音")
            return resumeRecording()
        }

        // 在后台线程执行录音初始化，避免阻塞主线程
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 创建输出文件
                val outputFile = createOutputFile()

                // 初始化MediaRecorder
                val recorder =
                    createMediaRecorder().apply {
                        configureRecorder(config)
                        setOutputFile(outputFile.absolutePath)
                        prepare()
                    }

                // 切换到主线程更新状态和开始录音
                withContext(Dispatchers.Main) {
                    try {
                        recorder.start()
                        mediaRecorder = recorder
                        actualRecordingStarted = true

                        // 重置计时相关变量
                        recordingStartTime = DateTimeUtils.currentTimeMillis()
                        pausedDuration = 0L

                        // 更新状态
                        _recordingState.value =
                            RecordingState.Recording(
                                currentDuration = 0,
                                currentOutputFile = outputFile,
                                config = config,
                                preparingCancel = (
                                    (_recordingState.value as? RecordingState.Recording)?.preparingCancel
                                        ?: false
                                ),
                            )

                        // 开始计时（确保真正开始录音后才计时）
                        startTimer()

                        LogUtils.d("录音开始: ${outputFile.absolutePath}\n配置: $config")
                    } catch (e: Exception) {
                        val failureReason = determineFailureReason(e)
                        handleRecordingError(
                            "录音启动失败".localized,
                            failureReason,
                            e,
                            outputFile,
                        )
                        recorder.release()
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    val failureReason = determineFailureReason(e)
                    handleRecordingError(
                        "录音初始化失败".localized,
                        failureReason,
                        e,
                        null,
                    )
                }
            }
        }

        return true
    }

    /**
     * 暂停录制
     */
    fun pauseRecording(): Boolean {
        if (!isRecording || isPaused) {
            LogUtils.w("当前状态无法暂停录制")
            return false
        }

        return try {
            mediaRecorder?.pause()

            // 停止计时器
            stopTimer()

            // 更新状态
            _recordingState.value =
                RecordingState.Paused(
                    currentDuration = currentDuration,
                    currentOutputFile = _recordingState.value.outputFile!!,
                    config = config,
                )

            LogUtils.d("录音暂停")
            true
        } catch (e: Exception) {
            val errorMsg = "录音暂停失败: %s".localizedFormat(e.message.orEmpty())
            val failureReason = determineFailureReason(e)
            handleRecordingError(
                errorMsg,
                failureReason,
                e,
                _recordingState.value.outputFile,
            )
            false
        }
    }

    /**
     * 恢复录制
     */
    fun resumeRecording(): Boolean {
        if (!isPaused) {
            LogUtils.w("当前状态无法恢复录制")
            return false
        }

        return try {
            if (!_recordingState.value.outputFile!!.exists()) {
                val e = FileNotFoundException("文件不存在".localized)
                handleRecordingError(
                    "文件不存在".localized,
                    RecordingFailureReason.FILE_NOT_EXIST,
                    e,
                    _recordingState.value.outputFile,
                )
                return false
            }
            mediaRecorder?.resume()

            // 重新开始计时
            startTimer()

            // 更新状态
            _recordingState.value =
                RecordingState.Recording(
                    currentDuration = currentDuration,
                    currentOutputFile = _recordingState.value.outputFile!!,
                    config = config,
                    preparingCancel = ((_recordingState.value as? RecordingState.Recording)?.preparingCancel ?: false),
                )

            LogUtils.d("录音恢复")
            true
        } catch (e: Exception) {
            val errorMsg = "录音恢复失败: %s".localizedFormat(e.message.orEmpty())
            val failureReason = determineFailureReason(e)
            handleRecordingError(
                errorMsg,
                failureReason,
                e,
                _recordingState.value.outputFile,
            )
            false
        }
    }

    /**
     * 停止录制
     * @param endReason 结束原因，默认为用户主动停止
     */
    fun stopRecording(endReason: RecordingEndReason = RecordingEndReason.USER_STOPPED): String? {
        if (!isRecording && !isPaused) {
            LogUtils.w("当前没有进行录制")
            return null
        }

        try {
            releaseRecorder()

            val currentOutputFile = _recordingState.value.outputFile
            val duration = currentDuration

            // 检查最短录制时长
            if (duration < config.minDurationMillis) {
                val errorMsg = "录制时长太短，最少需要%s秒，实际录制%s秒".localizedFormat(config.minDurationMillis / 1000, duration / 1000)
                handleRecordingError(
                    errorMsg,
                    RecordingFailureReason.DURATION_TOO_SHORT,
                    null,
                    currentOutputFile,
                )
                return null
            }

            val fileSize = currentOutputFile?.length() ?: 0L

            // 更新状态为完成
            if (currentOutputFile != null && currentOutputFile.exists()) {
                _recordingState.value =
                    RecordingState.Completed(
                        totalDuration = duration,
                        finalOutputFile = currentOutputFile,
                        finalFileSize = fileSize,
                        endReason = endReason,
                        config = config,
                    )

                val reasonText =
                    when (endReason) {
                        RecordingEndReason.USER_STOPPED -> "用户主动停止".localized
                        RecordingEndReason.AUTO_STOPPED -> "达到最大时长自动停止".localized
                        RecordingEndReason.SYSTEM_INTERRUPTED -> "系统中断".localized
                        RecordingEndReason.ERROR_OCCURRED -> "发生错误".localized
                    }

                LogUtils.d("录音完成: ${currentOutputFile.absolutePath}, 总时长: ${duration}毫秒, 文件大小: ${fileSize}字节, 结束原因: $reasonText")
                return currentOutputFile.absolutePath
            } else {
                handleRecordingError(
                    "输出文件为空".localized,
                    RecordingFailureReason.IO_ERROR,
                    null,
                    null,
                )
                return null
            }
        } catch (e: Exception) {
            val errorMsg = "录音停止失败: %s".localizedFormat(e.message.orEmpty())
            val failureReason = determineFailureReason(e)
            handleRecordingError(
                errorMsg,
                failureReason,
                e,
                _recordingState.value.outputFile,
            )
            return null
        }
    }

    /**
     * 取消录制
     */
    fun cancelRecording(): Boolean {
        if (!isRecording && !isPaused) {
            LogUtils.w("当前没有进行录制")
            return false
        }
        try {
            val currentOutputFile = _recordingState.value.outputFile

            releaseRecorder()

            // 删除临时文件
            currentOutputFile?.delete()

            // 重置状态
            _recordingState.value = RecordingState.Idle

            recordingStartTime = 0L
            pausedDuration = 0L
            actualRecordingStarted = false
            LogUtils.d("录音取消，临时文件已删除")
            return true
        } catch (e: Exception) {
            val errorMsg = "录音取消失败: %s".localizedFormat(e.message.orEmpty())
            handleRecordingError(
                errorMsg,
                RecordingFailureReason.UNKNOWN_ERROR,
                e,
                _recordingState.value.outputFile,
            )
        }
        return false
    }

    /**
     * 根据异常判断失败原因
     */
    private fun determineFailureReason(exception: Throwable): RecordingFailureReason {
        val message = exception.message?.lowercase() ?: ""
        LogUtils.e(
            exception,
            "determineFailureReason",
        )
        return when {
            // 权限相关错误
            exception is SecurityException -> RecordingFailureReason.PERMISSION_DENIED
            message.contains("permission") -> RecordingFailureReason.PERMISSION_DENIED

            // 设备忙碌相关错误
            message.contains("busy") -> RecordingFailureReason.DEVICE_BUSY
            message.contains("in use") -> RecordingFailureReason.DEVICE_BUSY
            message.contains("occupied") -> RecordingFailureReason.DEVICE_BUSY

            // 存储空间相关错误
            message.contains("no space") -> RecordingFailureReason.STORAGE_INSUFFICIENT
            message.contains("disk full") -> RecordingFailureReason.STORAGE_INSUFFICIENT
            message.contains("insufficient") -> RecordingFailureReason.STORAGE_INSUFFICIENT
            exception.message?.contains("存储空间不足") == true -> RecordingFailureReason.STORAGE_INSUFFICIENT

            // IO相关错误
            exception is IOException -> {
                when {
                    message.contains("read-only") -> RecordingFailureReason.IO_ERROR
                    message.contains("directory") -> RecordingFailureReason.IO_ERROR
                    message.contains("file") -> RecordingFailureReason.IO_ERROR
                    else -> RecordingFailureReason.IO_ERROR
                }
            }

            // MediaRecorder相关错误
            exception is IllegalStateException -> RecordingFailureReason.MEDIA_RECORDER_ERROR
            exception is RuntimeException && message.contains("start failed") -> RecordingFailureReason.MEDIA_RECORDER_ERROR

            // 默认为MediaRecorder错误
            else -> RecordingFailureReason.MEDIA_RECORDER_ERROR
        }
    }

    /**
     * 处理录音错误的统一方法
     */
    private fun handleRecordingError(
        message: String,
        failureReason: RecordingFailureReason,
        cause: Throwable?,
        tempFile: File?,
    ) {
        // 清理资源
        releaseRecorder()

        // 删除临时文件
        tempFile?.delete()

        // 更新错误状态
        _recordingState.value =
            RecordingState.Error(
                message = message,
                failureReason = failureReason,
                cause = cause,
                config = config,
            )

        LogUtils.e(
            "录音错误: $message, 原因: $failureReason",
            cause,
        )
    }

    /**
     * 重置录制状态
     */
    fun reset() {
        if (!cancelRecording()) {
            // 重置状态
            _recordingState.value = RecordingState.Idle

            recordingStartTime = 0L
            pausedDuration = 0L
            actualRecordingStarted = false
        }
    }

    /**
     * 创建MediaRecorder实例
     */
    private fun createMediaRecorder(): MediaRecorder =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            MediaRecorder(context)
        } else {
            @Suppress("DEPRECATION")
            MediaRecorder()
        }

    /**
     * 配置MediaRecorder
     */
    private fun MediaRecorder.configureRecorder(config: RecordingConfig) {
        setAudioSource(config.audioSource)
        setOutputFormat(config.outputFormat)
        setAudioEncoder(config.audioEncoder)
        setAudioSamplingRate(config.audioSamplingRate)
        setAudioEncodingBitRate(config.audioEncodingBitRate)
        setAudioChannels(config.audioChannels)
    }

    /**
     * 开始计时器
     */
    private fun startTimer() {
        timerJob?.cancel()
        timerJob =
            CoroutineScope(Dispatchers.Main).launch {
                while (isRecording && !isPaused && actualRecordingStarted) {
                    delay(250) // 更新频率改为250毫秒，提供更精确的时间显示
                    if (isRecording && !isPaused && actualRecordingStarted) {
                        val newDuration =
                            DateTimeUtils.currentTimeMillis() - recordingStartTime - pausedDuration

                        // 检查MediaRecorder是否还在正常工作（检测系统中断）
                        try {
                            // 尝试获取最大振幅来检测录音器状态
                            mediaRecorder?.maxAmplitude
                        } catch (e: Exception) {
                            // MediaRecorder出现异常，可能是系统中断
                            LogUtils.w("检测到录音器异常，可能是系统中断: ${e.message}")
                            stopRecording(RecordingEndReason.SYSTEM_INTERRUPTED)
                            break
                        }

                        // 更新状态中的时长
                        _recordingState.value =
                            RecordingState.Recording(
                                currentDuration = newDuration.toInt(),
                                currentOutputFile = _recordingState.value.outputFile!!,
                                config = config,
                                preparingCancel = (
                                    (_recordingState.value as? RecordingState.Recording)?.preparingCancel
                                        ?: false
                                ),
                            )

                        // 检查是否达到最大录制时长
                        if (config.enableAutoStop && newDuration >= config.maxDurationMillis) {
                            LogUtils.d("达到最大录制时长，自动停止录制")
                            stopRecording(RecordingEndReason.AUTO_STOPPED)
                            break
                        }
                    }
                }
            }
    }

    /**
     * 停止计时器
     */
    private fun stopTimer() {
        timerJob?.cancel()
        timerJob = null

        // 如果是暂停状态，记录暂停时的时长
        if (isPaused) {
            pausedDuration += DateTimeUtils.currentTimeMillis() - recordingStartTime
        }
    }

    /**
     * 释放录制器资源
     */
    private fun releaseRecorder() {
        try {
            stopTimer()
            mediaRecorder?.apply {
                if (actualRecordingStarted) {
                    stop()
                }
                release()
            }
        } catch (e: Exception) {
            LogUtils.e(
                e,
                "释放录制器失败",
            )
        } finally {
            mediaRecorder = null
            actualRecordingStarted = false
        }
    }

    /**
     * 创建输出文件
     */
    private fun createOutputFile(): File {
        val recordingsDir =
            config.outputDirectory ?: FileUtils.createDefaultRecordOutputFile(context)

        // 确保目录存在
        if (!recordingsDir.exists()) {
            if (!recordingsDir.mkdirs()) {
                throw IOException("无法创建录音目录: %s".localizedFormat(recordingsDir.absolutePath))
            }
        }

        // 检查目录是否可写
        if (!recordingsDir.canWrite()) {
            throw IOException("录音目录不可写: %s".localizedFormat(recordingsDir.absolutePath))
        }

        // 检查存储空间
        val freeSpace = recordingsDir.freeSpace
        val estimatedSize =
            RecordingConfig.getEstimatedFileSize(
                config,
                config.maxDurationMillis,
            )
        if (freeSpace < estimatedSize * 2) { // 预留双倍空间
            throw IOException(
                "存储空间不足，需要${RecordingConfig.formatFileSize(estimatedSize)}，可用${
                    RecordingConfig.formatFileSize(
                        freeSpace,
                    )
                }",
            )
        }

        return File.createTempFile("recording_", ".${config.fileExtension}", recordingsDir).also {
            it.deleteOnExit()
        }
    }

    /**
     * 获取当前录制文件路径
     */
    fun getCurrentRecordingPath(): String? = _recordingState.value.outputFile?.absolutePath

    /**
     * 获取当前录制配置
     */
    fun getCurrentConfig(): RecordingConfig = config

    /**
     * 获取录制文件大小（字节）
     */
    fun getRecordingFileSize(): Long = _recordingState.value.fileSize

    /**
     * 格式化录制时长为可读字符串
     */
    fun getFormattedDuration(): String = _recordingState.value.formattedDuration

    /**
     * 清理资源
     */
    fun cleanup() {
        reset()
        releaseRecorder()
    }

    fun changeRecordingStatus(preparingCancel: Boolean) {
        (_recordingState.value as? RecordingState.Recording)?.let { state ->
            if (state.preparingCancel == preparingCancel) {
                return
            }
            _recordingState.value =
                RecordingState.Recording(
                    currentDuration = state.currentDuration.toInt(),
                    currentOutputFile = state.currentOutputFile,
                    config = config,
                    preparingCancel = preparingCancel,
                )
        }
    }
}
