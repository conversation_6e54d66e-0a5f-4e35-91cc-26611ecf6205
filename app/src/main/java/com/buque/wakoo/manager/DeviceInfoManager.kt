package com.buque.wakoo.manager

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.AdjustEvent
import com.adjust.sdk.LogLevel
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.utils.LogUtils
import com.ishumei.smantifraud.SmAntiFraud
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

object DeviceInfoManager {
    private const val KEY_ANDROID_ID = "key_android_id"

    const val OS_PLATFORM = "android"

    private var _deviceId: String? = null

    @Suppress("ktlint:standard:backing-property-naming")
    private var _adid: String? = null

    val deviceId: String
        get() = _deviceId.orEmpty()

    val adId: String
        get() = _adid.orEmpty()

    val deviceName: String by lazy {
        "${Build.BRAND};${Build.MODEL};${Build.DEVICE}"
    }

    val deviceOs: String by lazy {
        "android ${Build.VERSION.RELEASE}"
    }

    var smBoxId: String = ""
        private set

    @SuppressLint("HardwareIds")
    fun initialize(context: Context) {
        DevicesKV.putInt(Const.KVKey.APP_STARTUP_COUNT, DevicesKV.getInt(Const.KVKey.APP_STARTUP_COUNT, 0) + 1)
        _deviceId = DevicesKV.getString(KEY_ANDROID_ID, "")?.takeIf {
            it.isNotBlank()
        } ?: try {
            Settings.Secure
                .getString(
                    context.contentResolver,
                    Settings.Secure.ANDROID_ID,
                ).also {
                    DevicesKV.putString(KEY_ANDROID_ID, it)
                }
        } catch (_: Exception) {
            ""
        }.orEmpty()
        LogUtils.i("devices id: $_deviceId")

        val option: SmAntiFraud.SmOption = SmAntiFraud.SmOption()
        with(EnvironmentManager.current) {
            option.setOrganization(smOrganization)
            option.setAppId(smAppId)
            option.setPublicKey(smPubKey)
        }
        option.setArea(SmAntiFraud.AREA_XJP)
        // 此方法必须在 create 方法之前调用，否则可能会出现不触发回调问题
        SmAntiFraud.registerServerIdCallback(
            object : SmAntiFraud.IServerSmidCallback {
                override fun onSuccess(boxId: String) {
                    // 服务器下发成功或缓存中有可用 boxId
                    // 如果缓存中存在 boxId，此方法会触发 2 次，第 2 次会更新缓存中的 boxId
                    LogUtils.i("SmAntiFraud onSuccess $boxId")
                    smBoxId = boxId
                }

                override fun onError(errCode: Int) {
                    appCoroutineScope.launch(Dispatchers.IO) {
                        smBoxId = SmAntiFraud.getDeviceId()
                    }
                    // -1：无网络，常见原因：设备无网络
                    // -2：网络异常，网络连接异常（conn.getResponseCode() 抛出异常）或者 http 状态非 200，常见原因：代理或私有化服务器配置错误
                    // -3：业务异常，下发业务状态码非 1100，服务器未返回 deviceId，常见原因：参数配置错误、qps 超限、服务器异常
                    LogUtils.e("SmAntiFraud onError $errCode")
                }
            },
        )
        // 初始化
        val isOk =
            SmAntiFraud.create(
                context,
                option,
            )

        LogUtils.i("SmAntiFraud sm init $isOk")

        // 初始化adjust
        initializeAdjust(context)
    }

    private fun initializeAdjust(context: Context) {
        val environment =
            if (EnvironmentManager.isDevelopmentEnvironment) {
                AdjustConfig.ENVIRONMENT_SANDBOX
            } else {
                AdjustConfig.ENVIRONMENT_PRODUCTION
            }
        val config = AdjustConfig(context, EnvironmentManager.current.adjustToken, environment)
        config.setLogLevel(
            when {
                EnvironmentManager.isDebugBuild -> LogLevel.VERBOSE
                EnvironmentManager.isDevelopmentEnvironment -> LogLevel.WARN
                else -> LogLevel.SUPPRESS
            },
        )
        Adjust.initSdk(config)

        Adjust.getAdid {
            _adid = it
        }

        IMCompatCore.addIMListener(
            object : IMCompatListener {
                override fun onRecvNewCustomMessage(
                    message: UCCustomMessage,
                    offline: Boolean,
                ) {
                    if (message.cmd == IMEvent.REPORT_ADJUST_EVENTS) {
                        val dataPack = message.parseDataJson<AdjustDataPack>() ?: return
                        AdjustEvent(dataPack.token)
                            .apply {
                                setRevenue(dataPack.revenue?.toDoubleOrNull() ?: 0.0, dataPack.currency)
                                dataPack.callbackParams?.forEach { key, value ->
                                    addCallbackParameter(key, value)
                                }
                                dataPack.partnerParams?.forEach { key, value ->
                                    addPartnerParameter(key, value)
                                }
                            }.track()
                    }
                }
            },
        )
    }
}

fun AdjustEvent.track() {
    if (!EnvironmentManager.isProductionEnvironment || EnvironmentManager.isProdRelease) {
        Adjust.trackEvent(this)
    }
    if (!EnvironmentManager.isProdRelease) {
        LogUtils.iTag("adjust_event", "$this")
    }
}

@Serializable
private data class AdjustDataPack(
    @SerialName("event_token") val token: String,
    @SerialName("revenue") val revenue: String?,
    @SerialName("currency") val currency: String?,
    @SerialName("callback_params") val callbackParams: MutableMap<String, String>?,
    @SerialName("partner_params") val partnerParams: MutableMap<String, String>?,
)
