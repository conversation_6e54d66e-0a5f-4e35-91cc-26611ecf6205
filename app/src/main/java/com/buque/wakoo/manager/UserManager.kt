package com.buque.wakoo.manager

import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.room.Room
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.bean.user.TempUserInfo
import com.buque.wakoo.db.AppDatabase
import com.buque.wakoo.im.utils.doOnLifecycleEvent
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.service.CommonApiService
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.utils.ActiveDelay
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.coroutines.cancellation.CancellationException

fun interface UserInfoUpdateListener {
    fun onUserInfoUpdated(temp: TempUserInfo)
}

object UserManager {
    private val DB_NAME = if (BuildConfig.DEBUG) "ucoo-user-debug" else "ucoo-user"

    private val db by lazy {
        Room
            .databaseBuilder(WakooApplication.instance, AppDatabase::class.java, DB_NAME)
            .fallbackToDestructiveMigration(false)
            .build()
    }

    private val userDao by lazy {
        db.userDao()
    }

    private val userInfoUpdateListeners = CopyOnWriteArrayList<UserInfoUpdateListener>()

    fun registerUserInfoUpdateListener(listener: UserInfoUpdateListener) {
        if (!userInfoUpdateListeners.contains(listener)) {
            userInfoUpdateListeners.add(listener)
        }
    }

    fun unregisterUserInfoUpdateListener(listener: UserInfoUpdateListener) {
        userInfoUpdateListeners.remove(listener)
    }

    fun notifyUserInfoUpdateListeners(response: UserResponse) {
        userInfoUpdateListeners.forEach { it.onUserInfoUpdated(TempUserInfo(response)) }
    }

    fun notifyUserInfoUpdateListeners(tempUserInfo: TempUserInfo) {
        userInfoUpdateListeners.forEach { it.onUserInfoUpdated(tempUserInfo) }
    }

    fun refreshUserInfo(userId: String): Job =
        appCoroutineScope.launch {
            getRemoteUserInfo(userId)
        }

    fun refreshSelfUserInfo() = refreshUserInfo(SelfUser?.id.orEmpty())

    suspend fun getRemoteSelfUserInfo(): Result<SelfUserInfo> =
        getRemoteUserInfo(SelfUser?.id.orEmpty()).map {
            AccountManager.selfUser ?: SelfUserInfo.fromResponse(it)
        }

    suspend fun getRemoteUserInfo(userId: String): Result<UserResponse> {
        if (!AccountManager.isLoggedIn) {
            return Result.failure(IllegalStateException("请先登录".localized))
        }
        return executeApiCallExpectingData { UserApiService.instance.getUserInfo(userId) }
            .onSuccess { response ->
                if (SelfUser?.id == response.id) {
                    AccountManager.updateSelfUserInfo {
                        SelfUserInfo.fromResponse(response)
                    }
                } else {
                    userDao.insertAll(BasicUser.fromResponse(response))
                }
                notifyUserInfoUpdateListeners(response)
            }
    }

    suspend fun getLocalUserInfo(userId: String): Result<BasicUser?> =
        runCatching {
            userDao.getUserById(userId)
        }

    fun getUserInfoFlow(
        userId: String,
        fromLocal: Boolean = true,
    ): Flow<TempUserInfo> =
        callbackFlow {
            if (fromLocal && SelfUser?.id != userId) { // 自己的数据没必要查，数据库里面没有
                getLocalUserInfo(userId).getOrNull()?.also {
                    send(TempUserInfo(null, it))
                }
            }
            val listener =
                UserInfoUpdateListener {
                    if (it.id == userId) {
                        launch {
                            send(it)
                        }
                    }
                }
            registerUserInfoUpdateListener(listener)
            refreshUserInfo(userId)
            awaitClose {
                unregisterUserInfoUpdateListener(listener)
            }
        }

}
