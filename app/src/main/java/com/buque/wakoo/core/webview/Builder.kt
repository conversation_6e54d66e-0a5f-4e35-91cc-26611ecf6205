package com.buque.wakoo.core.webview

import android.Manifest
import android.content.Context
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SingleActionDialog
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryEasyPostDialog
import com.buque.wakoo.utils.eventBus.tryToLink
import com.buque.webview.handler.BaseBridgeHandler
import com.buque.webview.handler.HandlerName
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive

fun buildJSBEventHandlerList(delegate: JsBridgeEventDelegate): List<Pair<String, BridgeHandler>> {
    return listOf(
        HandlerName.FINISH to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    delegate.onFinish()
                }
            },
        HandlerName.TITLE to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    val title = data.getString("title")
                    delegate.onSetTitle(title)
                }
            },
        HandlerName.IMMERSIVE to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    delegate.onImmersive(data.getBoolean("statusBar") == true, data.getBoolean("navigationBar") == true)
                }
            },
        HandlerName.STATUS_BAR to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    callback.sendSuccess(context, delegate.onGetStatusBarHeight(context))
                }
            },
        HandlerName.NAVIGATION_BAR to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    callback.sendSuccess(context, delegate.onGetNavigationBarHeight(context))
                }
            },
        HandlerName.REQUEST_ANDROID_PERMISSION to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    val permissions =
                        data.getString("scope")?.split(",")?.toTypedArray()
                    delegate.onRequestAndroidPermission(this, permissions, callback)
                }
            },
        HandlerName.REQUEST_PERMISSION to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    val permissions =
                        data
                            .getString("scope")
                            ?.split(",")
                            ?.flatMap { name ->
                                when (name) {
                                    "CAMERA" -> listOf(Manifest.permission.CAMERA)
                                    "ALBUM" -> {
                                        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.TIRAMISU) {
                                            listOf(
                                                Manifest.permission.READ_EXTERNAL_STORAGE,
                                                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                            )
                                        } else {
                                            listOf(
                                                Manifest.permission.READ_MEDIA_IMAGES,
                                                Manifest.permission.READ_MEDIA_VIDEO,
                                            )
                                        }
                                    }

                                    else -> emptyList()
                                }
                            }?.toTypedArray()
                    if (permissions.isNullOrEmpty()) {
                        callback.sendSuccess(context, 0)
                        return
                    }
                    delegate.onRequestAndroidPermission(this, permissions, callback)
                }
            },
        HandlerName.UCOO_PAY to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    val productId =
                        data.getString("product_id")?.takeIf { it.isNotEmpty() }
                            ?: kotlin.run {
                                callback.sendFailure(context, -1)
                                return
                            }
                    val fkLink = data.getString("fk_link").orEmpty()
                    val fkType =
                        data.getInt("fk_type") ?: kotlin.run {
                            callback.sendFailure(context, -2)
                            return
                        }
                    val orderType =
                        data.getInt("order_type") ?: kotlin.run {
                            callback.sendFailure(context, -3)
                            return
                        }
                    delegate.onPay(context, productId, fkLink, fkType, orderType)
                }
            },
        HandlerName.WEB_VIDEO_EFFECT to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    val effectUrl =
                        data.getString("effect_url")?.takeIf { it.isNotEmpty() }
                            ?: return
                    delegate.onPlayVideoEffect(context, effectUrl)
                }
            },
        HandlerName.APP_H5_JUMP_APP to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    delegate.onJumpAppPage(context, data)
                }
            },
        HandlerName.API_ERROR to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    val msg = data.getString("msg") ?: ""
                    val code = data.getInt("code") ?: -1
                    delegate.onApiError(code, msg)
                }
            },
        HandlerName.WARM_TIP to
            object : BaseBridgeHandler() {
                override fun handlerV2(
                    context: Context,
                    data: JsonObject,
                    callback: CallBackFunction,
                ) {
                    data.getString("jump_link")?.takeIsNotEmpty()?.also {
                        it.tryToLink()
                        UserManager.refreshSelfUserInfo()
                    } ?: data.getString("content")?.also { content ->
                        EventBus.tryEasyPostDialog {
                            SingleActionDialog(
                                title = "温馨提示".localized,
                                content = content,
                                buttonConfig = DialogButtonStyles.Primary.copy("我知道了".localized),
                            ) {
                                dismiss()
                            }
                        }
                        UserManager.refreshSelfUserInfo()
                    }
                }
            },
    )
}
