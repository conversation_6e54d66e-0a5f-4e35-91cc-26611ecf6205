package com.buque.wakoo.core.pay

import android.app.Activity
import android.content.Context
import android.util.ArrayMap
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.SkuDetails
import com.android.billingclient.api.queryProductDetails
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.manager.localized
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.utils.AwaitContinuation
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.awaitContinuation
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.json.JsonObject

interface GoogleProduct {
    val googleProductId: String
    val googleProductType: String
    val googleProductPrice: String
}

data class BillingProduct constructor(
    override val googleProductId: String,
    override val googleProductType: String,
    override val googleProductPrice: String,
) : GoogleProduct

private data class GoogleGood(
    val details: Any,
    val product: BillingProduct?,
)

interface GoodsOrderRepository {
    suspend fun onOrderCancel(orderNo: String): Result<JsonObject>

    suspend fun markOrderComplete(
        orderNo: String,
        productId: String,
        token: String,
    ): Result<JsonObject>

    suspend fun reportError(errorMsg: String)
}

interface IPurchasesUpdatedListener {
    fun onPurchasesUpdated(completed: Boolean = false)

    fun onPurchasesMessage(message: String)
}

object GoogleBillingManager {
    private const val TAG = "GoogleBillingManager"
    private val context: Context
        get() = WakooApplication.instance

    private lateinit var goodsOrderRepository: GoodsOrderRepository

    private val billingClient by lazy {
        BillingClient
            .newBuilder(context)
            .setListener(purchasesUpdatedListener)
            .enablePendingPurchases(
                PendingPurchasesParams
                    .newBuilder()
                    .enablePrepaidPlans()
                    .enableOneTimeProducts()
                    .build(),
            ).enableAutoServiceReconnection() // Add this line to enable reconnection
            .build()
    }

    private val purchasesUpdatedListener =
        PurchasesUpdatedListener { billingResult, purchases ->
            currentOrder?.apply {
                val (orderNo, productId) = this
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK && purchases != null) {
                    for (purchase in purchases) {
                        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED && purchase.products.firstOrNull() == productId &&
                            purchase.accountIdentifiers?.obfuscatedAccountId == orderNo
                        ) {
                            orderCompleted(orderNo, productId, purchase.purchaseToken)
                        }
                    }
                } else if (billingResult.responseCode == BillingClient.BillingResponseCode.USER_CANCELED) {
                    // Handle an error caused by a user cancelling the purchase flow.
                    listener?.onPurchasesMessage(
                        "取消支付".localized,
                    )
                    appCoroutineScope.launch {
                        goodsOrderRepository.onOrderCancel(orderNo)
                    }
                } else {
                    if (BuildConfig.DEBUG) {
                        listener?.onPurchasesMessage(billingResult.debugMessage)
                    }
                    // Handle any other error codes.
                    appCoroutineScope.launch {
                        goodsOrderRepository.onOrderCancel(orderNo)
                    }
                }
            }
        }

    private var readyContinuation: AwaitContinuation? = null

    private val productDetailsMap = ArrayMap<String, GoogleGood>()

    private var currentOrder: Pair<String, String>? = null

    private var listener: IPurchasesUpdatedListener? = null

    private var isUnavailable = false

    fun bindOrder(
        orderNo: String,
        productId: String,
        listener: IPurchasesUpdatedListener?,
    ) {
        currentOrder = orderNo to productId
        GoogleBillingManager.listener = listener
    }

    fun clearListener() {
        listener = null
    }

    @JvmStatic
    fun initGoogleBilling(goodsOrderRepository: GoodsOrderRepository) {
        this.goodsOrderRepository = goodsOrderRepository
        connection()
    }

    fun isReady() = billingClient.isReady

    private fun connection() {
        billingClient.startConnection(
            object : BillingClientStateListener {
                override fun onBillingSetupFinished(billingResult: BillingResult) {
                    when (billingResult.responseCode) {
                        BillingClient.BillingResponseCode.OK -> {
                            logI("google支付连接成功")
                            readyContinuation?.resume()
                        }

                        BillingClient.BillingResponseCode.BILLING_UNAVAILABLE -> {
                            reportBillingError("google支付服务不可用，code: ${billingResult.responseCode}")
                            isUnavailable = true
                        }

                        else -> {
                            reportBillingError("google支付服务连接出错，code: ${billingResult.responseCode}")
                        }
                    }
                }

                override fun onBillingServiceDisconnected() {
                    val listener = this
                    appCoroutineScope.launch {
                        delay(1000)
                        billingClient.startConnection(listener)
                    }
                }
            },
        )
    }

    private suspend fun awaitReady() {
        if (!isReady()) {
            if (!isUnavailable) { // 结算服务不可用
                readyContinuation = awaitContinuation()
                readyContinuation?.suspendUntilWithTimeout(5000)
            }
        }
    }

    suspend fun fetchProductDetails(products: List<GoogleProduct>) {
        try {
            awaitReady()
            withTimeout(5000L) {
                processPurchases(products)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun processPurchases(products: List<GoogleProduct>) {
        if (products.isEmpty()) {
            return
        }
        if (!isReady()) {
            return
        }
        val cached =
            products.all {
                productDetailsMap.contains(it.googleProductId)
            }
        if (cached) {
            return
        }
        if (billingClient.isFeatureSupported(BillingClient.FeatureType.PRODUCT_DETAILS).responseCode ==
            BillingClient.BillingResponseCode.OK
        ) {
            val productList =
                products.map {
                    QueryProductDetailsParams.Product
                        .newBuilder()
                        .setProductId(it.googleProductId)
                        .setProductType(it.googleProductType)
                        .build()
                }
            val params = QueryProductDetailsParams.newBuilder()
            params.setProductList(productList)

            // leverage queryProductDetails Kotlin extension function
            val productDetailsResult =
                withContext(Dispatchers.IO) {
                    billingClient.queryProductDetails(params.build())
                }
            logI("productDetailsResult：$productDetailsResult")
            // Process the result.
            if (productDetailsResult.billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                productDetailsResult.productDetailsList?.forEach { item ->
                    val product =
                        if (item.productType == BillingClient.ProductType.INAPP) {
                            item.oneTimePurchaseOfferDetails?.let {
                                logI(
                                    "goodItem1, id:${item.productId}, type:${item.productType}, priceCurrencyCode:${it.priceCurrencyCode}, formattedPrice:${it.formattedPrice}",
                                )
                                BillingProduct(item.productId, item.productType, it.formattedPrice)
                            }
                        } else {
                            item.subscriptionOfferDetails
                                ?.firstOrNull()
                                ?.pricingPhases
                                ?.pricingPhaseList
                                ?.firstOrNull()
                                ?.let {
                                    logI(
                                        "goodItem2, id:${item.productId}, type:${item.productType}, priceCurrencyCode:${it.priceCurrencyCode}, formattedPrice:${it.formattedPrice}",
                                    )
                                    BillingProduct(item.productId, item.productType, it.formattedPrice)
                                }
                        }
                    logI("cache goodItem 1, id: ${item.productId}, product: $product")
                    productDetailsMap[item.productId] = GoogleGood(item, product)
                }
            }
        } else {
            LogUtils.e("ERR:不支持PRODUCT_DETAILS")
        }
    }

    fun checkProduct(productId: String): Boolean {
        if (!isReady()) {
            return false
        }
        productDetailsMap[productId] ?: kotlin.run {
            return false
        }
        return true
    }

    fun getProductPrice(productId: String): String? = productDetailsMap[productId]?.product?.googleProductPrice

    fun buy(
        activity: Activity,
        productId: String,
        orderNo: String,
    ): Int {
        if (!isReady()) {
            return -100
        }
        val good =
            productDetailsMap[productId] ?: kotlin.run {
                return -101
            }

        val productDetails = good.details

        val billingFlowParams =
            if (productDetails is ProductDetails) {
                // An activity reference from which the billing flow will be launched.
                val productDetailsParamsList =
                    listOf(
                        BillingFlowParams.ProductDetailsParams
                            .newBuilder()
                            // retrieve a value for "productDetails" by calling queryProductDetailsAsync()
                            .setProductDetails(productDetails)
                            .apply {
                                // to get an offer token, call ProductDetails.subscriptionOfferDetails()
                                // for a list of offers that are available to the user
                                if (productDetails.productType == BillingClient.ProductType.SUBS) {
                                    productDetails.subscriptionOfferDetails?.firstOrNull()?.offerToken?.also {
                                        setOfferToken(it)
                                    }
                                }
                            }.build(),
                    )

                BillingFlowParams
                    .newBuilder()
                    .setProductDetailsParamsList(productDetailsParamsList)
                    .setObfuscatedAccountId(orderNo)
                    .build()
            } else {
                BillingFlowParams
                    .newBuilder()
                    .setSkuDetails(productDetails as SkuDetails)
                    .setObfuscatedAccountId(orderNo)
                    .build()
            }

        // Launch the billing flow
        return billingClient.launchBillingFlow(activity, billingFlowParams).responseCode
    }

    private fun orderCompleted(
        orderNo: String,
        productId: String,
        token: String,
    ) {
        appCoroutineScope.launch {
            listener?.onPurchasesUpdated(false)
            try {
                goodsOrderRepository.let { repo ->
                    repo
                        .markOrderComplete(orderNo, productId, token)
                        .onFailure {
                            listener?.onPurchasesMessage(it.message.orEmpty())
                        }.onSuccess {
                            currentOrder = null
                            listener?.onPurchasesMessage(
                                "支付成功".localized,
                            )
                            // 刷新用户信息，mineTab自动刷新过了
                        }
                }
            } finally {
                listener?.onPurchasesUpdated(true)
            }
        }
    }

    private fun reportBillingError(errorMsg: String) {
        logE(errorMsg)
        appCoroutineScope.launch {
            goodsOrderRepository.reportError(errorMsg)
        }
    }

    suspend fun fetchFirstRecharge() =
        GlobalRepository.walletRepo.fetchFirstRecharge()

    private fun inAppProduct(
        id: String,
        price: String,
    ): BillingProduct = BillingProduct(id, BillingClient.ProductType.INAPP, price)

    private fun logE(errorMsg: String) {
        LogUtils.e("billing:$errorMsg")
//        Log.e("LogUtils", errorMsg)
    }

    fun logI(s: String) {
        LogUtils.i("billing:$s")
//        Log.i("LogUtils", s)
    }
}
