package com.buque.wakoo.core.webview.offline

import android.content.Context
import android.net.Uri
import android.util.Log
import android.webkit.WebResourceResponse
import android.webkit.WebView
import androidx.webkit.WebViewAssetLoader
import androidx.webkit.WebViewAssetLoader.InternalStoragePathHandler
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.manager.DownloadManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.L10nManager
import com.buque.wakoo.utils.ZipUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.util.concurrent.TimeUnit

object OfflinePkgManager {
    private data class LocalGame(
        val gameName: String,
        val exist: <PERSON><PERSON><PERSON>,
        val webViewAssetLoader: WebViewAssetLoader?,
    )

    private const val TAG = "OfflinePkgManager"
    private const val DEV_CONFIG_URL =
        "https://qyqy-china-big-file2-1319886479.cos.ap-shanghai.myqcloud.com/games/develop/game_conf_wakoo.json"
    private const val REL_CONFIG_URL =
        "https://qyqy-china-big-file-1319886479.cos.ap-singapore.myqcloud.com/games/release/game_conf_wakoo.json"
    private const val STAGING_CONFIG_URL =
        "https://qyqy-china-big-file2-1319886479.cos.ap-shanghai.myqcloud.com/games/staging/game_conf_wakoo.json"

    private val configFileName = "game_conf.json"

    private const val DOMAIN_UCOO_GAME = "wakoo_game.com"

    val hostOfflinePages: String
        get() = "https://${DOMAIN_UCOO_GAME}"

    private var offlinePkgConfig: OfflinePkgConfig? = null
    private val env: String
        get() = when {
            EnvironmentManager.isDebugBuild -> "develop"
            EnvironmentManager.isReleaseBuild -> "release"
            else -> "staging"
        }

    private val mapGame = mutableMapOf<String, LocalGame>()

    fun interceptRequest(view: WebView?, uri: Uri?): WebResourceResponse? {
        val pkgConfig = offlinePkgConfig
        if (uri != null && view != null && pkgConfig != null) {
            try {
                if (DOMAIN_UCOO_GAME == uri.host) {
                    val gameName = uri.path?.let { path ->
                        val str = path.removePrefix("/")
                        val index = str.indexOfFirst { it == '/' }
                        if (index > -1) {
                            str.substring(0, index)
                        } else {
                            null
                        }
                    }
                    logD(TAG, "intercept == url:$uri=>gameName:$gameName")
                    logD(
                        TAG,
                        "intercept == host:${uri.host},path:${uri.path},authority:${uri.authority}=>$gameName"
                    )
                    return mapGame[gameName]?.webViewAssetLoader?.shouldInterceptRequest(uri)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return null
    }

    private fun isGameExist(gameName: String) = mapGame[gameName]?.exist == true

    fun isLocalUrl(url: String) = url.startsWith("https://$DOMAIN_UCOO_GAME")

    fun getLocalUrl(uri: Uri?): String? {
        uri ?: return null
        val pkgConfig = offlinePkgConfig ?: return null
        val inter = pkgConfig.interceptors.find { it.remotePath == uri?.path } ?: return null
        val gameName = inter.gameName
        //验证文件完整
        return if (isGameExist(gameName)) {
            val localUrl = "https://$DOMAIN_UCOO_GAME/${inter.gameName}/index.html"
            //拼接参数
            val builder = Uri.parse(localUrl).buildUpon()
            if (inter.params.isNotEmpty()) {
                inter.params.forEach {
                    builder.appendQueryParameter(it.key, it.value)
                }
                val newUrl = builder.build().toString()
                logD(TAG, "url changed:[$uri]=>[$newUrl]")
                newUrl
            }
            builder.appendQueryParameter(
                "app-language",
                L10nManager.languageTag
            )
            builder.build().toString()
        } else {
            logD(TAG, "$gameName NOT EXIST")
            null
        }
    }

    private val client: OkHttpClient by lazy {
        // 使用 lazy 延迟初始化
        val builder =
            OkHttpClient
                .Builder()
                .connectTimeout(30, TimeUnit.SECONDS) // 连接超时时间
                .readTimeout(30, TimeUnit.SECONDS) // 读取超时时间
                .writeTimeout(30, TimeUnit.SECONDS) // 写入超时时间
        builder.build()
    }

    fun startCheck(context: Context) {
        val confUrl = when {
            EnvironmentManager.isDebugBuild -> DEV_CONFIG_URL
            EnvironmentManager.isReleaseBuild -> REL_CONFIG_URL
            else -> STAGING_CONFIG_URL
        }
        appCoroutineScope.launch(Dispatchers.IO) {
            val request = Request.Builder()
                .url(confUrl)
                .build()
            val resp = try {
                client.newCall(request).execute()
            } catch (e: Exception) {
                return@launch
            }
            if (resp.isSuccessful) {
                val json = resp.body.string()
                val config = AppJson.decodeFromString<OfflinePkgConfig>(json)
                val dirGame = File(context.filesDir, "games/$env")
                if (!dirGame.exists()) {
                    dirGame.mkdirs()
                }
                val gamesNeedUpdate = mutableListOf<OfflinePkgConfig.Game>()
                try {
                    var f: File
                    config.games.forEach { game ->
                        //游戏各自的配置
                        f = File(dirGame, "${game.name}/config.json")
                        if (f.exists()) {
                            //对比版本，版本不一致则下载
                            try {
                                val g =
                                    AppJson.decodeFromString<OfflinePkgConfig.Game>(f.readText())
                                logD(
                                    TAG,
                                    "[${game.name}] remote version=>${game.version},local version=>${g.version}"
                                )
                                if (g.version != game.version) {
                                    gamesNeedUpdate.add(game)
                                }
                            } catch (e: Exception) {
                                logE(TAG, e.message.orEmpty())
                                gamesNeedUpdate.add(game)
                            }
                        } else {
                            logD(TAG, "config file not exist, ${f.absolutePath}")
                            gamesNeedUpdate.add(game)
                        }
                    }
                    //下载游戏
                    logD(TAG, "gamesNeedUpdate: ${gamesNeedUpdate.map { it.name }}")
                    gamesNeedUpdate.forEach { g ->
                        downloadGame(g, dirGame)
                    }
                    //更新配置
                    writeConfig(dirGame, json)
                    offlinePkgConfig = config
                    checkGames(context, config.games)
                } catch (e: Exception) {
                    e.printStackTrace()
                    logE(TAG, e.message.orEmpty())
                }
            } else {
                logE(TAG, "request config error")
            }
        }
    }

    private suspend fun checkGames(context: Context, games: List<OfflinePkgConfig.Game>) =
        withContext(Dispatchers.IO) {
            mapGame.clear()
            games.forEach { game ->
                val gameName = game.name
                val fConfig = File(context.filesDir, "games/$env/$gameName/config.json")
                val fEntry = File(context.filesDir, "games/$env/$gameName/dist/index.html")
                val exist = fConfig.exists() && fEntry.exists()
                val publicDir = File(context.filesDir, "games/$env/$gameName/dist")
                val wsl = if (exist) WebViewAssetLoader.Builder()
                    .setDomain(DOMAIN_UCOO_GAME)
                    .addPathHandler("/$gameName/", InternalStoragePathHandler(context, publicDir))
                    .build()
                else
                    null
                mapGame[gameName] = LocalGame(gameName, exist, wsl)
            }
        }

    private suspend fun downloadGame(game: OfflinePkgConfig.Game, dirGame: File) {
        val gameFolder = File(dirGame, game.name)
        if (gameFolder.exists()) {
            gameFolder.deleteRecursively()
        }
        gameFolder.mkdirs()
        try {
            val uri = Uri.parse(game.downloadUrl)
            val destGameZip =
                File(gameFolder, uri?.lastPathSegment ?: "${game.name}-${game.version}.zip")
            if (destGameZip.exists() && destGameZip.length() == 0L) {
                destGameZip.delete()
            }
            //下载游戏包
            logI(TAG, "download :${game.downloadUrl}")
            val result = DownloadManager.download(game.downloadUrl, gameFolder.absolutePath, destGameZip.name)
            if (result.isSuccess) {
                logI(TAG, "下载游戏成功[${game.name}]")
                //解压游戏包
                logI(TAG, "开始解压游戏[${game.name}]")
                destGameZip.parentFile?.let {
                    ZipUtil.upZipFile(
                        result.getOrThrow(),
                        it.absolutePath
                    )
                }
                logI(
                    TAG,
                    "解压游戏成功[${game.name}]=>文件夹${destGameZip.parentFile?.absolutePath}"
                )
                //写入游戏配置
                val confFile = File(dirGame, "${game.name}/config.json")
                if (!confFile.exists()) {
                    withContext(Dispatchers.IO) {
                        confFile.createNewFile()
                    }
                }
                confFile.writeText(AppJson.encodeToString(game))
                logI(TAG, "游戏配置写入成功[${game.name}]：$game")
            } else {
                logE(
                    TAG,
                    "下载游戏失败[${game.name}]：" + result.exceptionOrNull()?.message.orEmpty()
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            logE(TAG, "下载游戏失败：" + e.message.orEmpty())
        }
    }

//    suspend fun downloadFile(url: String,
//                             filePath: String,
//                             fileName: String,):Result<File>{
//        val request = Request.Builder().url(url).build()
//        val resp = client.newCall(request).execute()
//        return if (resp.isSuccessful){
//
//        }else{
//            Result.failure()
//        }
//    }

    private fun writeConfig(dirGame: File, json: String) {
        val configFile = File(dirGame, configFileName)
        if (!configFile.exists()) {
            configFile.createNewFile()
        }
        configFile.writeText(json)
    }

    private fun logE(tag: String, message: String) {
        if (EnvironmentManager.current.enableLog) {
            Log.e(tag, message)
        }
    }

    private fun logD(tag: String, message: String) {
        if (EnvironmentManager.current.enableLog) {
            Log.d(tag, message)
        }
    }

    private fun logI(tag: String, message: String) {
        if (EnvironmentManager.current.enableLog) {
            Log.i(tag, message)
        }
    }

}

