package com.buque.wakoo.im.inter

import android.content.Context
import android.text.Spanned
import android.text.SpannedString
import com.buque.wakoo.im.bean.ConversationAtInfo
import com.buque.wakoo.im.utils.takeIsNotEmpty

interface IUCConversation {
    open val timestamp: Long
        get() = 0

    /**
     * 国际化
     */
    fun getDisplayName(
        context: Context,
        default: String = "",
    ): CharSequence = name.takeIsNotEmpty() ?: default

    /**
     * 国际化
     */
    fun getDisplaySummary(
        context: Context,
        default: String = "",
    ): Spanned = SpannedString(desc.takeIsNotEmpty() ?: draftText.takeIsNotEmpty() ?: default)

    /**
     * 会话id，和[IUCMessage.targetId]含义相同
     */
    val id: String

    /**
     * 业务id, 不能用于im
     */
    val stableId: String
        get() = id

    /**
     * 会话展示名称
     */
    val name: String
        get() = ""

    /**
     * 会话最新摘要
     */
    val desc: String?
        get() = null

    /**
     * 会话展示图标
     */
    val iconUrl: String
        get() = ""

    /**
     * 未读消息数量
     */
    val unreadCount: Int
        get() = 0

    /**
     * 会话草稿文本
     */
    val draftText: String?
        get() = null

    /**
     * 会话是否置顶
     */
    val isPinned: Boolean
        get() = false

    /**
     * 会话排序字段
     */
    val orderKey: Long
        get() = 0

    /**
     * 第一条未读消息的Sequence
     */
    val firstUnreadMsgSequence: Long

    /**
     * 群组会话中的at信息
     */
    val atInfoList: ConversationAtInfo?
}
