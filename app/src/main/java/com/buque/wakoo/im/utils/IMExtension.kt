package com.buque.wakoo.im.utils

import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.buque.wakoo.ext.isMainThread
import com.buque.wakoo.manager.EnvironmentManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import java.lang.reflect.InvocationTargetException
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.EmptyCoroutineContext

fun <T : CharSequence> T?.takeIsNotEmpty(): T? = this?.takeIf { it.isNotEmpty() }

fun <T : CharSequence> T?.takeIsNotBlank(): T? = this?.takeIf { it.isNotBlank() }

inline fun <reified T> Any.requireInstance(): T = this as T

inline fun <reified T> Any.asInstance(): T? = this as? T

inline fun <reified T> Any?.asInstance(default: T): T = ((this as? T)) ?: default

val _isAppRelease = EnvironmentManager.isProdRelease

val InValidCoroutineScope =
    CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate + EmptyCoroutineContext).also {
        it.cancel()
    }

internal interface Disposable {
    fun dispose()

    fun isDisposed(): Boolean
}

internal class DisposableImpl(
    var callback: () -> Unit = {},
) : Disposable {
    private val disposable = AtomicBoolean(false)

    override fun dispose() {
        if (disposable.compareAndSet(false, true)) {
            callback()
        }
    }

    override fun isDisposed(): Boolean = disposable.get()
}

private val mMainHandler by lazy {
    val looper = Looper.getMainLooper()
    var h: Handler? = null
    if (Build.VERSION.SDK_INT >= 28) {
        h = Handler.createAsync(looper)
    } else {
        if (Build.VERSION.SDK_INT >= 16) {
            try {
                h =
                    Handler::class.java
                        .getDeclaredConstructor(
                            Looper::class.java,
                            Handler.Callback::class.java,
                            Boolean::class.javaPrimitiveType,
                        ).newInstance(looper, null, true)
            } catch (ignored: IllegalAccessException) {
            } catch (ignored: InstantiationException) {
            } catch (ignored: NoSuchMethodException) {
            } catch (e: InvocationTargetException) {
                h = Handler(looper)
            }
        }
    }
    h ?: Handler(looper)
}

internal fun imPostToMainThread(
    delay: Long = 0,
    runnable: Runnable,
) {
    if (isMainThread && delay <= 0) {
        runnable.run()
    } else {
        mMainHandler.postDelayed(runnable, delay)
    }
}

//region lifecycle相关

internal fun Lifecycle.doOnDestroy(block: () -> Unit): Disposable =
    doOnLifecycleEvent { event ->
        if (event == Lifecycle.Event.ON_DESTROY) {
            block()
        }
    }

internal fun Lifecycle.doOnCreate(block: () -> Unit): Disposable =
    doOnLifecycleEvent { event ->
        if (event == Lifecycle.Event.ON_CREATE) {
            block()
        }
    }

internal fun LifecycleOwner.doOnLifecycleEvent(block: (Lifecycle.Event) -> Unit): Disposable = lifecycle.doOnLifecycleEvent(block)

internal fun LifecycleOwner.doOnDestroy(block: () -> Unit): Disposable = lifecycle.doOnDestroy(block)

internal fun Lifecycle.doOnLifecycleEvent(block: (Lifecycle.Event) -> Unit): Disposable {
    val disposable = DisposableImpl()
    val callback =
        object : LifecycleEventObserver {
            override fun onStateChanged(
                source: LifecycleOwner,
                event: Lifecycle.Event,
            ) {
                if (event == Lifecycle.Event.ON_DESTROY) {
                    removeObserver(this)
                }
                block(event)
            }
        }
    disposable.callback = {
        removeObserver(callback)
    }
    imPostToMainThread {
        if (disposable.isDisposed()) {
            return@imPostToMainThread
        }
        addObserver(callback)
    }
    return disposable
}
//endregion
