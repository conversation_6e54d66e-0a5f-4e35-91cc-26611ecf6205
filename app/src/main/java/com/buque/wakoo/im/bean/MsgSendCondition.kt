package com.buque.wakoo.im.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class MsgSendCondition(
    @SerialName("can_chat")
    val canChat: Boolean = false,
    @SerialName("more_refuse_hint")
    val toastMsg: String = "",
    @SerialName("toast")
    val toastMsg2: String = "",
    @SerialName("title")
    val title: String = "",
    @SerialName("hint")
    val hint: String = "",
    @SerialName("is_cp")
    val isCp: Boolean = false,
    @SerialName("is_friend")
    val isFriend: Boolean = true,
    @SerialName("need_friend_relation")
    val needFriendRelation: Boolean = true,
    @SerialName("is_member")
    val isMember: Boolean? = false,
    @SerialName("charge_guide_plan")
    val abValue: Int = 0,
    @SerialName("can_be_cp")
    val canMakeCp: Boolean = false,
    @SerialName("msg_extra")
    val msgExtra: JsonObject? = null,
    @SerialName("ever_been_member")
    val everMember: Boolean = false, // abValue == 23,
    @SerialName("currency_mark")
    val currencyMark: String = "", // everMember == false, abValue == 23,
    @SerialName("currency_number")
    val currencyNumber: String = "", // everMember == false, abValue == 23,
    @SerialName("biz_type")
    val bizType: String = "",
    @SerialName("block_card_btn_txt")
    val blockCardBtnTxt: String = "",
    @SerialName("hint_type")
    val hintType: Int = 0,
    @SerialName("trigger_block_card")
    val triggerBlockCard: Boolean = false,
)
