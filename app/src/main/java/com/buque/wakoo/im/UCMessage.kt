package com.buque.wakoo.im

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im.bean.MsgSendStatus
import com.buque.wakoo.im.inter.IUCMessage
import kotlinx.serialization.json.JsonObject

/**
 * [rawMessage] 因为原始消息是第三方定义的，在消息生命周期内，内部数据很可能发生变化，所有实现该接口的
 * 实现类一般情况下都不是稳定，不可变的类。由[rawMessage]衍生获取到到属性很可能发生变化，在不同时期获取
 * 到值可能不一样。
 */

interface UCMessage : IUCMessage {
    companion object {
        val NoSet = Any()
    }

    /**
     * 放置在cloudCustomData里的消息
     * 这个只有在私聊时用户自己携带,代发的消息是不会带上的
     */
    val senderUser: IMUser?

    val sequence: Long

    val isNeedReadReceipt: Boolean

    val isExcludedFromLastMessage: Boolean
    // 扩展需要在消息发送成功之后才能更新
}

fun IUCMessage.getExtraString(key: String): String = getExtraValue(key).orEmpty()

fun IUCMessage.getExtraInt(key: String): Int? = getExtraValue(key)?.toIntOrNull()

fun IUCMessage.getExtraInt(
    key: String,
    default: Int,
): Int = getExtraInt(key) ?: default

fun IUCMessage.getExtraLong(key: String): Long? = getExtraValue(key)?.toLongOrNull()

fun IUCMessage.getExtraLong(
    key: String,
    default: Long,
): Long = getExtraLong(key) ?: default

fun IUCMessage.getExtraFloat(key: String): Float? = getExtraValue(key)?.toFloatOrNull()

fun IUCMessage.getExtraFloat(
    key: String,
    default: Float,
): Float = getExtraFloat(key) ?: default

fun IUCMessage.getExtraBoolean(key: String): Boolean? = getExtraValue(key)?.lowercase()?.toBooleanStrictOrNull()

fun IUCMessage.getExtraBoolean(
    key: String,
    default: Boolean,
): Boolean = getExtraBoolean(key) ?: default

inline fun <reified T> IUCMessage.getExtraTypeValue(key: String): T? =
    try {
        getExtraValue(key)?.let { AppJson.decodeFromString<T>(it) }
    } catch (e: Exception) {
        null
    }

inline fun <reified T> IUCMessage.getExtraTypeValue(
    key: String,
    default: T,
): T = getExtraTypeValue(key) ?: default

fun IUCMessage.getExtraJson(key: String): JsonObject? = getExtraTypeValue<JsonObject>(key)

fun IUCMessage.getExtraJson(
    key: String,
    default: JsonObject,
): JsonObject = getExtraJson(key) ?: default

val IUCMessage.isSending: Boolean
    get() = sendStatus == MsgSendStatus.Sending

val IUCMessage.isSent: Boolean
    get() = sendStatus == MsgSendStatus.Sent

val IUCMessage.isFailure: Boolean
    get() = sendStatus == MsgSendStatus.Failure

val IUCMessage.isRevoked: Boolean
    get() = sendStatus == MsgSendStatus.Revoked

/**
 * 当前设备的语言的翻译文本
 */
// val IUCMessage.currentTranslateText: String?
//    get() = getTranslateTextByCode(MultiLanguages.getAppLanguage().lanCode)
