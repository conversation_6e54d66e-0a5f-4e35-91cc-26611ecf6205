package com.buque.wakoo.im

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im.bean.PushInfo
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.inter.IUCCustomMessage
import com.buque.wakoo.im.inter.IUCMessage
import com.buque.wakoo.im_business.message.types.UCEmojiMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.manager.localized

interface UCInstanceMessage : IUCMessage {
    val base: UCMessage

    /**
     * 有些消息需要在解析的结构体里面获取user
     */
    val user: IMUser?
        get() = base.senderUser

    fun getSummaryString(): String

    fun toMsgString(): String {
        if (base.isRevoked) {
            return "(已撤回)${getSummaryString()}"
        }
        return getSummaryString()
    }

    fun requireUser() = user ?: IMUser.fromUid(uid = sender)
}

interface UCFakerMessage : UCInstanceMessage {
    val contentType: String

    fun equalsSame(other: UCFakerMessage?): Boolean
}

val UCInstanceMessage.sequence
    get() = base.sequence

val UCInstanceMessage.isNeedReadReceipt
    get() = base.isNeedReadReceipt

fun UCInstanceMessage.toPushInfo(params: SendParams): PushInfo? {
    val desc =
        when (this) {
            is UCTextMessage -> {
                text.take(40)
            }

            is UCImageMessage -> {
                "对方发来一张图片".localized
            }

            is UCVoiceMessage -> {
                "对方发来一段语音".localized
            }

            is UCEmojiMessage -> {
                "对方发来一个互动表情".localized
            }

            is IUCCustomMessage -> {
                summary ?: "[系统通知]".localized
            }

            else -> {
                return null
            }
        }

    return if (params.type == ConversationType.C2C) {
        PushInfo(title = SelfUser?.name.orEmpty(), desc = desc, avatar = SelfUser?.avatar.orEmpty())
    } else {
        PushInfo(
            title = "群聊消息".localized,
            desc = desc,
            avatar = "",
        )
    }
}
