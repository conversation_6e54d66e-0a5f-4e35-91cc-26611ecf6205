package com.buque.wakoo.im.compat

import android.content.Context
import android.os.SystemClock
import androidx.annotation.MainThread
import androidx.collection.LruCache
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.im.IMConversationController
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.IMConversationResult
import com.buque.wakoo.im.bean.MessageAtInfo
import com.buque.wakoo.im.bean.MsgSendStatus
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.getExtraBoolean
import com.buque.wakoo.im.inter.EventDispatcher
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.UCConversation
import com.buque.wakoo.im.utils.IMLogUtils
import com.buque.wakoo.im.utils.IMUtils
import com.buque.wakoo.im.utils.InValidCoroutineScope
import com.buque.wakoo.im.utils.TaskThrottle
import com.buque.wakoo.im.utils._isAppRelease
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.tim.TIMConverter
import com.tencent.imsdk.v2.V2TIMManager
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.EmptyCoroutineContext

object IMCompatCore {
    private data class CacheMessage(
        val message: UCInstanceMessage,
        val cacheTime: Long = SystemClock.elapsedRealtime(),
    )

    // 是否被标记为垃圾消息
    const val CONTENT_ILLEGAL = "content_illegal"

    private const val TAG = "IMCompatCore"

    val currentUser
        get() = SelfUser

    internal lateinit var applicationContext: Context

    /**
     * 一秒最多发五条消息
     */
    private val msgSendRateLimit by lazy { TaskThrottle(5, 1050) }

    //region 事件分发

    private val messageCache by lazy {
        LruCache<String, CacheMessage>(20)
    }

    private var _imCoroutineScope: CoroutineScope? = null

    fun obtainLoginCoroutineScope(): CoroutineScope = _imCoroutineScope ?: InValidCoroutineScope

    @Synchronized
    fun addIMListener(listener: IMCompatListener) {
        mDispatcher.add(listener)
    }

    @Synchronized
    fun removeIMListener(listener: IMCompatListener) {
        mDispatcher.remove(listener)
    }

    fun putMsg(ucMessage: UCInstanceMessage) {
        messageCache.put(ucMessage.id, CacheMessage(ucMessage))
    }

    val dispatcher: EventDispatcher
        get() = mDispatcher

    private val mDispatcher =
        object : EventDispatcher {
            private val imListenerList: MutableList<IMCompatListener> by lazy(LazyThreadSafetyMode.NONE) {
                mutableListOf()
            }

            fun add(listener: IMCompatListener) {
                if (!imListenerList.contains(listener)) {
                    imListenerList.add(listener)
                }
            }

            fun remove(listener: IMCompatListener) {
                imListenerList.remove(listener)
            }

            override suspend fun dispatchListener(
                preRun: suspend () -> Unit,
                action: suspend (IMCompatListener) -> Unit,
            ) {
                withContext(Dispatchers.Main.immediate) {
                    preRun()
                    imListenerList.toList().forEach {
                        action(it)
                    }
                }
            }

            override suspend fun dispatchRecvNewMessage(
                message: UCInstanceMessage,
                offline: Boolean,
            ) {
                IMCompatCore.dispatchRecvNewMessage(message, offline)
            }

            override fun synchronizedDispatchListener(action: suspend (IMCompatListener) -> Unit) {
                IMUtils.launchOnMain {
                    imListenerList.toList().forEach {
                        action(it)
                    }
                }
            }
        }

    //endregion

    private lateinit var engineImpl: IIMEngine

    // 是否已登录
    private var isLogin = false

    fun initIMSDK(
        context: Context,
        engine: IIMEngine,
    ) {
        this.applicationContext = context
        engineImpl = engine
        engineImpl.initSDK(context)
    }

    //region im登录登出

    /**
     * @param imChannel 设置IM的发送和接收通道
     * 在一次登录周期内，不能重复调用此方法，否则可能出现数据错乱
     */
    @MainThread
    fun login(
        userId: String,
        token: String,
    ) {
        if (isLogin) {
            return
        }
        isLogin = true

        val environmentContext =
            if (_isAppRelease) {
                CoroutineExceptionHandler { _, throwable ->
                    IMLogUtils.i("im", "登录失败")
                }
            } else {
                EmptyCoroutineContext
            }
        _imCoroutineScope?.cancel()
        _imCoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate + environmentContext)

        engineImpl.attach(mDispatcher)

        mDispatcher.synchronizedDispatchListener {
            it.onCallUserLogin()
        }
        var loaded = false
        IMUtils.launchOnMain {
            innerLogin(
                userId = userId,
                signText = token,
                executeLogin = { userId, token ->
                    engineImpl.login(userId, token) {
                        if (!loaded) {
                            loaded = true
                            IMConversationController.loadConversations()
                        }
                    }
                },
            )
        }
    }

    private suspend inline fun CoroutineScope.innerLogin(
        userId: String,
        signText: String?,
        executeLogin: (String, String?) -> Boolean,
    ) {
        var token: String? = signText
        while (isActive) {
            try {
                if (executeLogin(userId, token.takeIsNotEmpty())) { //  登录成功
                    break
                } else {
                    delay(3000)
                }
            } catch (e: Exception) {
                token = null
            }
        }
    }

    /**
     * 退出登录，退出登录后，需要重新登录
     */
    @MainThread
    fun logout() {
        if (!isLogin) { // 已退出登录
            return
        }
        isLogin = false

        engineImpl.logout()

        // 这里要使用全局的, 使用imCoroutineScope会在下面被取消任务
        appCoroutineScope.launch {
            mDispatcher.dispatchListener {
                it.onUserLogout()
            }
        }

        IMConversationController.clearConversations()

        _imCoroutineScope?.cancel()
        _imCoroutineScope = null
    }
    //endregion

    //region 发送消息(全部都是兼容消息发送方法)

    /**
     * 发送C2C消息
     * @param bundle 消息内容主体
     */
    fun sendC2CMessage(
        id: String,
        bundle: MessageBundle,
        atInfo: MessageAtInfo? = null,
    ) {
        sendMessage(SendParams(id, ConversationType.C2C, atInfo), bundle)
    }

    /**
     * 发送群组消息
     * @param bundle 消息内容主体
     */
    fun sendGroupMessage(
        id: String,
        bundle: MessageBundle,
        atInfo: MessageAtInfo? = null,
    ) {
        sendMessage(SendParams(id, ConversationType.GROUP, atInfo), bundle)
    }

    /**
     * 发送聊天室消息
     * @param bundle 消息内容主体
     */
    fun sendChatRoomMessage(
        id: String,
        bundle: MessageBundle,
        atInfo: MessageAtInfo? = null,
    ) {
        sendMessage(SendParams(id, ConversationType.CHATROOM, atInfo), bundle)
    }

    /**
     * 发送消息
     * @param bundle 消息内容主体
     */
    fun sendMessage(
        id: String,
        type: ConversationType,
        bundle: MessageBundle,
        atInfo: MessageAtInfo? = null,
    ) {
        sendMessage(SendParams(id, type, atInfo), bundle)
    }

    /**
     * 发送消息
     * @param params 消息发送参数
     * @param bundle 消息内容主体
     */
    fun sendMessage(
        params: SendParams,
        bundle: MessageBundle,
    ) {
        sendMessages(params, listOf(bundle))
    }

    /**
     * 发送多条消息
     * @param params 消息发送参数
     * @param bundles 消息内容主体
     */
    fun sendMessages(
        params: SendParams,
        bundles: List<MessageBundle>,
    ) {
        if (bundles.isEmpty()) {
            log("sendMessages: bundles was empty. return !!!")
            return
        }
        IMUtils.launchCoroutine(IMUtils.sendMsgCoroutineContext) {
            bundles.forEach { bundle ->
                if (bundle.isEmpty) { // 消息体为null不发送消息
                    return@forEach
                }
                try {
                    val mainMessage = messageBundle2UCMessage(params, bundle)
                    sendUCMessage(
                        params = params,
                        bundle = bundle,
                        mainMessage = mainMessage,
                    )
                } catch (e: Exception) {
                    // tim 有时候创建消息会出现异常
                    log("sendMessage warning ,${e.stackTraceToString()}")
                    return@forEach
                }
            }
        }
    }

    fun insertLocalMessage(
        id: String,
        type: ConversationType,
        bundle: MessageBundle,
    ) {
        sendMessage(SendParams(id, type, onlyLocal = true), bundle)
    }

    /**
     * 插入本地消息
     * @param params 消息发送参数
     * @param bundle 消息内容主体
     */
    fun insertLocalMessage(
        params: SendParams,
        bundle: MessageBundle,
    ) {
        sendMessage(params.copy(onlyLocal = true), bundle)
    }

    /**
     * 插入多条本地消息
     * @param params 消息发送参数
     * @param bundle 消息内容主体
     */
    fun insertLocalMessages(
        params: SendParams,
        bundles: List<MessageBundle>,
    ) {
        sendMessages(params.copy(onlyLocal = true), bundles)
    }

    /**
     * 一般用于重发消息，该消息只发主通道
     */
    fun sendMessage(
        params: SendParams,
        ucMessage: UCMessage,
    ) {
        if (params.receiver == currentUser?.id || ucMessage.sendStatus != MsgSendStatus.Failure) {
            return
        }
        if (ucMessage.getExtraBoolean(CONTENT_ILLEGAL, false)) {
            return
        }
        IMUtils.launchCoroutine(IMUtils.sendMsgCoroutineContext) {
            sendUCMessage(
                params = params,
                bundle = null,
                mainMessage = ucMessage,
            )
        }
    }
    //endregion

    //region 会话和消息操作

    /**
     * 撤回消息
     */
    fun recallMessage(ucMessage: UCMessage) {
        engineImpl.recallMessage(ucMessage)
//        when (ucMessage) {
// //            is UCMessage.RCIM -> RCIMCore.recallMessage(ucMessage.rawMessage)
//            is UCMessage.TIM -> TIMCore.recallMessage(ucMessage.rawMessage)
//            else -> {
//                throw IllegalArgumentException("unknown message type")
//            }
//        }
    }

    /**
     * 删除消息
     */
    fun deleteMessage(ucMessage: UCMessage) {
        engineImpl.deleteMessage(ucMessage)
//        when (ucMessage) {
// //            is UCMessage.RCIM -> RCIMCore.deleteMessage(ucMessage.rawMessage)
//            is UCMessage.TIM -> TIMCore.deleteMessage(ucMessage.rawMessage)
//            else -> {
//                throw IllegalArgumentException("unknown message type")
//            }
//        }
    }

    /**
     * 标记消息已读
     */
    fun markMessageReadReceipts(message: UCInstanceMessage) {
//        if (imChannel != IMChannel.TIM) {
//            message.base.apply {
//                rcMessage?.also {
//                    RCIMCore.markMessageReadReceipts(it)
//                } ?: run {
//                    RCIMCore.markMessageReadReceipts(targetId, ConversationType.C2C.toRcType(), timestamp)
//                }
//            }
//        }
//
//        if (imChannel != IMChannel.RCIM) {
//            val conversationId =
//                message.targetId.id2ConvId(if (message.isC2CMsg) ConversationType.C2C else ConversationType.GROUP)
//            TIMCore.cleanConversationUnreadCount(
//                isMainChannel = imChannel != IMChannel.MAIN_RCIM_SUB_TIM,
//                conversationId = conversationId,
//                cleanTimestamp = message.timestamp.plus(500).div(1000)
//            )
//        }

        engineImpl.markMessageReadReceipts(message)

//        val conversationId =
//            message.targetId.id2ConvId(if (message.isC2CMsg) ConversationType.C2C else ConversationType.GROUP)
//        TIMCore.cleanConversationUnreadCount(
//            isMainChannel = true,
//            conversationId = conversationId,
//            cleanTimestamp = message.timestamp.plus(500).div(1000)
//        )
    }

    /**
     * 标记消息已读
     */
    @Deprecated("腾讯设计的太垃圾，使用markMessageReadReceipts(timestamp: Long")
    fun markMessageReadReceipts(list: List<UCInstanceMessage>) {
//        if (imChannel != IMChannel.TIM) {
//            list.firstOrNull()?.base?.apply {
//                rcMessage?.also {
//                    RCIMCore.markMessageReadReceipts(it)
//                } ?: run {
//                    RCIMCore.markMessageReadReceipts(targetId, ConversationType.C2C.toRcType(), timestamp)
//                }
//            }
//        }
//
//        if (imChannel != IMChannel.RCIM) {
//            // 腾讯需要message才能标记，这里没有message则忽略，cleanConversationUnreadCount会标记为全部已读
//            list.mapNotNull {
//                it.base.timMessage
//            }.takeIf { it.isNotEmpty() }?.also {
//                TIMCore.markMessageReadReceipts(it)
//            }
//        }

        engineImpl.markMessageReadReceipts(list)

        // 腾讯需要message才能标记，这里没有message则忽略，cleanConversationUnreadCount会标记为全部已读
//        list.mapNotNull {
//            it.base.timMessage
//        }.takeIf { it.isNotEmpty() }?.also {
//            TIMCore.markMessageReadReceipts(it)
//        }
    }

    /**
     * 会话置顶设置
     */
    fun deleteConversation(
        id: String,
        type: ConversationType,
    ) {
//        val mainIMChannel = getMainIMChannel()

//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.deleteConversation(
//                isMainChannel = mainIMChannel == IMChannel.RCIM,
//                id = id,
//                type = type.toRcType()
//            )
//        }
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.deleteConversation(
//                isMainChannel = mainIMChannel == IMChannel.TIM,
//                conversationId = id.id2ConvId(type)
//            )
//        }

        engineImpl.deleteConversation(id, type)

//        TIMCore.deleteConversation(
//            isMainChannel = true,
//            conversationId = id.id2ConvId(type)
//        )
    }

    /**
     * 删除会话
     */
    fun deleteConversation(ucConversation: UCConversation) {
//        val mainIMChannel = getMainIMChannel()
//
//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.deleteConversation(
//                isMainChannel = mainIMChannel == IMChannel.RCIM,
//                id = ucConversation.id,
//                type = ucConversation.type.toRcType()
//            )
//        }
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.deleteConversation(
//                isMainChannel = mainIMChannel == IMChannel.TIM,
//                conversationId = ucConversation.stableId
//            )
//        }

        engineImpl.deleteConversation(ucConversation)

//        TIMCore.deleteConversation(
//            isMainChannel = true,
//            conversationId = ucConversation.stableId
//        )
    }

    /**
     * 会话置顶设置
     */
    fun setPinnedConversation(
        id: String,
        type: ConversationType,
        isPinned: Boolean,
    ) {
//        val mainIMChannel = getMainIMChannel()
//
//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.setPinnedConversation(
//                isMainChannel = mainIMChannel == IMChannel.RCIM,
//                id = id,
//                type = type.toRcType(),
//                isPinned = isPinned
//            )
//        }
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.setPinnedConversation(
//                isMainChannel = mainIMChannel == IMChannel.TIM,
//                conversationId = id.id2ConvId(type),
//                isPinned = isPinned
//            )
//        }

        engineImpl.setPinnedConversation(id, type, isPinned)

//        TIMCore.setPinnedConversation(
//            isMainChannel = true,
//            conversationId = id.id2ConvId(type),
//            isPinned = isPinned
//        )
    }

    /**
     * 会话置顶设置
     */
    fun setPinnedConversation(
        ucConversation: UCConversation,
        isPinned: Boolean,
    ) {
//        val mainIMChannel = getMainIMChannel()
//
//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.setPinnedConversation(
//                isMainChannel = mainIMChannel == IMChannel.RCIM,
//                id = ucConversation.id,
//                type = ucConversation.type.toRcType(),
//                isPinned = isPinned
//            )
//        }
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.setPinnedConversation(
//                isMainChannel = mainIMChannel == IMChannel.TIM,
//                conversationId = ucConversation.stableId,
//                isPinned = isPinned
//            )
//        }

        engineImpl.setPinnedConversation(ucConversation, isPinned)

//        TIMCore.setPinnedConversation(
//            isMainChannel = true,
//            conversationId = ucConversation.stableId,
//            isPinned = isPinned
//        )
    }

    /**
     * 清空会话未读数
     */
    fun cleanConversationUnreadCount(
        id: String,
        type: ConversationType,
    ) {
//        val mainIMChannel = getMainIMChannel()
//
//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.cleanConversationUnreadCount(
//                isMainChannel = mainIMChannel == IMChannel.RCIM,
//                id = id,
//                type = type.toRcType()
//            )
//        }
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.cleanConversationUnreadCount(
//                isMainChannel = mainIMChannel == IMChannel.TIM,
//                conversationId = id.id2ConvId(type)
//            )
//        }

        engineImpl.cleanConversationUnreadCount(id, type)

//        TIMCore.cleanConversationUnreadCount(
//            isMainChannel = true,
//            conversationId = id.id2ConvId(type)
//        )
    }

    /**
     * 清空会话未读数
     */
    fun cleanConversationUnreadCount(ucConversation: UCConversation) {
//        val mainIMChannel = getMainIMChannel()
//
//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.cleanConversationUnreadCount(
//                isMainChannel = mainIMChannel == IMChannel.RCIM,
//                id = ucConversation.id,
//                type = ucConversation.type.toRcType()
//            )
//        }
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.cleanConversationUnreadCount(
//                isMainChannel = mainIMChannel == IMChannel.TIM,
//                conversationId = ucConversation.stableId
//            )
//        }

        engineImpl.cleanConversationUnreadCount(ucConversation)
//        TIMCore.cleanConversationUnreadCount(
//            isMainChannel = true,
//            conversationId = ucConversation.stableId
//        )
    }

    /**
     * 清除所有会话未读数
     */
    fun cleanAllConversationUnreadCount() {
//        val mainIMChannel = getMainIMChannel()
//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.cleanAllConversationUnreadCount(mainIMChannel == IMChannel.RCIM)
//        }
//
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.cleanAllConversationUnreadCount(mainIMChannel == IMChannel.TIM)
//        }

        engineImpl.cleanAllConversationUnreadCount()

//        TIMCore.cleanAllConversationUnreadCount(true)
    }

    /**
     * 设置会话消息免打扰
     */
    fun setConversationReceiveMessageOpt(
        id: String,
        type: ConversationType,
        notDisturbEnable: Boolean,
    ) {
//        if (imChannel != IMChannel.TIM) {
//            RCIMCore.setConversationReceiveMessageOpt(id, type.toRcType(), notDisturbEnable)
//        }
//
//        if (imChannel != IMChannel.RCIM) {
//            TIMCore.setConversationReceiveMessageOpt(id, type, notDisturbEnable)
//        }
        engineImpl.setConversationReceiveMessageOpt(id, type, notDisturbEnable)
//        TIMCore.setConversationReceiveMessageOpt(id, type, notDisturbEnable)
    }

    suspend fun joinConversation(
        id: String,
        type: ConversationType,
        clearHistory: Boolean = false,
    ): Boolean =
        coroutineScope {
//            when (imChannel) {
//                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
//                    if (imChannel != IMChannel.RCIM) {
//                        launch {
//                            TIMCore.joinGroup(id, clearHistory)
//                        }
//                    }
//                    RCIMCore.joinConversation(id, type.toRcType())
//                }
//
//                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
//                    if (imChannel != IMChannel.TIM) {
//                        launch {
//                            RCIMCore.joinConversation(id, type.toRcType())
//                        }
//                    }
//                    TIMCore.joinGroup(id, clearHistory)
//                }
//            }
            engineImpl.joinConversation(id, type, clearHistory)
//            TIMCore.joinGroup(id, clearHistory)
        }

    suspend fun quitConversation(
        id: String,
        type: ConversationType,
        clearHistory: Boolean = false,
    ) = coroutineScope {
//            when (imChannel) {
//                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
//                    if (imChannel != IMChannel.RCIM) {
//                        launch {
//                            TIMCore.quitGroup(id, clearHistory)
//                        }
//                    }
//                    RCIMCore.quitConversation(id, type.toRcType())
//                }
//
//                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
//                    if (imChannel != IMChannel.TIM) {
//                        launch {
//                            RCIMCore.quitConversation(id, type.toRcType())
//                        }
//                    }
//                    TIMCore.quitGroup(id, clearHistory)
//                }
//            }
        engineImpl.quitConversation(id, type, clearHistory)
//            TIMCore.quitGroup(id, clearHistory)
    }

    //endregion

    //region 获取会话/列表

    /**
     * 获取历史消息
     * @param descendPullOrder 是否降序拉取
     * @param onlyLocal 是否拉取本地消息
     * @param enableNotify 为true表示请求成功后，同时会回调onFetchHistoryMessage
     * @return 返回值为null表示失败，不为null表示成功
     */
    suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        count: Int = 20,
        lastMsg: UCMessage? = null,
        descendPullOrder: Boolean = true,
        onlyLocal: Boolean = false,
        enableNotify: (List<UCInstanceMessage>) -> Boolean = { true },
    ): List<UCInstanceMessage>? {
        val list =
            withContext(IMUtils.imHandleCoroutineContext) {
                engineImpl.getHistoryMessages(id, type, count, lastMsg, descendPullOrder, onlyLocal)
            }

        if (!list.isNullOrEmpty() && enableNotify(list)) {
            mDispatcher.dispatchListener {
                if (it.match(id)) {
                    it.onFetchHistoryMessagesSuccess(list, descendPullOrder)
                }
            }
        }

        return list
    }

    suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        sequence: Long,
        before: Int,
        after: Int,
    ): List<UCInstanceMessage>? =
        withContext(IMUtils.imHandleCoroutineContext) {
            engineImpl.getHistoryMessages(id, type, sequence, before, after)
        }

    /**
     * 获取会话列表
     */
    suspend fun getConversations(
        nextSeq: Long,
        count: Int,
    ): IMConversationResult? =
        withContext(IMUtils.imHandleCoroutineContext) {
//            when (imChannel) {
//                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
//                    RCIMCore.getConversations(nextSeq, count)?.let { list ->
//                        IMConversationResult(
//                            isFinished = list.size < count,
//                            nextSeq = list.lastOrNull()?.sentTime ?: 0,
//                            list = list.map { UCConversation.RCIM(it) })
//                    }
//                }
//
//                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
//                    TIMCore.getConversations(nextSeq, count)?.let {
//                        IMConversationResult(
//                            isFinished = it.isFinished,
//                            nextSeq = it.nextSeq,
//                            list = it.conversationList.orEmpty().toUCConversationListByTIM()
//                        )
//                    }
//                }
//            }
            engineImpl.getConversations(nextSeq, count)
//            TIMCore.getConversations(nextSeq, count)?.let {
//                IMConversationResult(
//                    isFinished = it.isFinished,
//                    nextSeq = it.nextSeq,
//                    list = it.conversationList.orEmpty().toUCConversationListByTIM()
//                )
//            }
        }

    /**
     * 获取IM会话
     */
    suspend fun getIMConversation(
        id: String,
        type: ConversationType,
        excludeCache: Boolean = false,
    ): UCConversation? {
        if (!excludeCache) {
            val cache = IMConversationController.getIMConversations(id, type)
            if (cache != null) {
                return cache
            }
        }
        return withContext(IMUtils.imHandleCoroutineContext) {
//            when (imChannel) {
//                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
//                    RCIMCore.getConversation(id, type.toRcType())?.let {
//                        UCConversation.RCIM(it)
//                    }
//                }
//
//                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
//                    TIMCore.getConversation(id, type)?.let {
//                        UCConversation.TIM(it)
//                    }
//                }
//            }
            engineImpl.getIMConversation(id, type)
//            TIMCore.getConversation(id, type)?.let {
//                UCConversation.TIM(it)
//            }
        }
    }
    //endregion

    /**
     * 分发消息
     *
     * @param message
     * @param offline
     */
    private suspend fun dispatchRecvNewMessage(
        message: UCInstanceMessage,
        offline: Boolean = false,
    ) {
        val receiverTime = SystemClock.elapsedRealtime()
//        val pendingTranslateText: String? = if (userPartition.isUCOO) {
//            if (message is UCTextMessage) {
//                message.text
//            } else if (UIConfig.userConf.micAsrEnabled && message is UCCustomMessage && message.cmd == MsgEventCmd.MIC_SPEECH_TEXT) {
//                message.getContentText()
//            } else {
//                null
//            }
//        } else {
//            null
//        }
        val pendingTranslateText: String? = null

        val dispatchMsg =
            if (!pendingTranslateText.isNullOrEmpty()) {
//            val code = MultiLanguages.getAppLanguage().lanCode
//            val translatedText = translateText(code, pendingTranslateText)
//            (messageCache.remove(message.id)?.takeIf { it.cacheTime > receiverTime }?.message ?: message).also {
//                if (!translatedText.isNullOrEmpty() && translatedText != pendingTranslateText) { // 成功，原文和译文不同保存翻译
//                    it.saveTranslateText(code, translatedText)
//                }
//            }
                error("")
            } else {
                messageCache.remove(message.id)?.takeIf { it.cacheTime > receiverTime }?.message ?: message
            }

        mDispatcher.dispatchListener {
            if (it.match(dispatchMsg)) {
                it.onRecvNewMessage(dispatchMsg, offline)
                when (dispatchMsg) {
//                    is UCVoiceCallMessage -> {
//                        it.onVoiceCallMessage(dispatchMsg, offline)
//                    }
//
//                    is UCVideoCallMessage -> {
//                        it.onVideoCallMessage(dispatchMsg, offline)
//                    }

                    is UCCustomMessage -> {
                        it.onRecvNewCustomMessage(dispatchMsg, offline)
                    }
                }
            }
        }
    }

    //region 消息转换

    /**
     * 根据参数和消息内容创建UCMessage对象
     */
    private fun messageBundle2UCMessage(
        params: SendParams,
        bundle: MessageBundle,
    ): UCMessage = engineImpl.createMessage(params, bundle)

    /**
     * 发送UCMessage对象
     */
    private suspend fun sendUCMessage(
        params: SendParams,
        bundle: MessageBundle?,
        mainMessage: UCMessage,
    ) {
        if (!engineImpl.checkMessageCanSend(params, bundle, mainMessage)) {
            return
        }
        msgSendRateLimit.post {
            engineImpl.sendMessage(params, mainMessage)
        }
    }

    fun IMCompatListener.match(message: UCInstanceMessage): Boolean = match(message.targetId)

    fun IMCompatListener.match(id: String): Boolean = filter.isEmpty || filter.id.isNullOrEmpty() || filter.id == id

    /**
     * 此方法只用来测试,拿到后台相对于的cmd详细json, 调用此方法
     * ps: 需要自己处理IMListener的filter参数, 因为消息都是不带sender和target的
     */
    fun mockSendCustomJson(str: String) {
        val v2 =
            V2TIMManager
                .getMessageManager()
                .createCustomMessage(str.toByteArray(Charsets.UTF_8))

        val msg = TIMConverter.parseMsgFromOrigin(v2)
        IMUtils.launchOnMain {
            val message = UCCustomMessage(msg, str)
            dispatchRecvNewMessage(message, false)
        }
    }

    private fun log(message: String) {
        log({ message })
    }

    private fun log(block: () -> String) {
        IMLogUtils.i(TAG, block())
    }
}
