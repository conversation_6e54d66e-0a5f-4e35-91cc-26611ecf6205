package com.buque.wakoo.im.compat

import android.content.Context
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.IMConversationResult
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.inter.EventDispatcher
import com.buque.wakoo.im.inter.UCConversation
import kotlinx.serialization.json.JsonObject

interface IIMEngine {
    //region 初始化方法

    suspend fun login(
        userId: String,
        token: String?,
        loadConversation: (() -> Unit)? = null,
    ): Boolean

    fun logout()

    fun initSDK(context: Context)

    // 挂载回调
    fun attach(dispatcher: EventDispatcher)
    //endregion

    //region 消息操作

    // 创建消息
    fun createMessage(
        params: SendParams,
        bundle: MessageBundle,
    ): UCMessage

    // 发送消息
    suspend fun sendMessage(
        params: SendParams,
        mainMessage: UCMessage,
    )

    // 撤回消息
    fun recallMessage(ucMessage: UCMessage)

    // 删除消息
    fun deleteMessage(ucMessage: UCMessage)

    // 标记消息已读
    fun markMessageReadReceipts(message: UCInstanceMessage)

    // 标记消息已读
    fun markMessageReadReceipts(list: List<UCInstanceMessage>)
    //endregion

    //region 会话操作

    // 删除会话
    fun deleteConversation(
        id: String,
        type: ConversationType,
    )

    // 删除会话
    fun deleteConversation(ucConversation: UCConversation)

    /**
     * 置顶/取消置顶
     *
     * @param id 会话id
     * @param type 会话type
     * @param isPinned 是否置顶
     */
    fun setPinnedConversation(
        id: String,
        type: ConversationType,
        isPinned: Boolean,
    )

    /**
     * 置顶/取消置顶
     *
     * @param ucConversation 会话实体
     * @param isPinned 是否置顶
     */
    fun setPinnedConversation(
        ucConversation: UCConversation,
        isPinned: Boolean,
    )

    // 清除会话未读
    fun cleanConversationUnreadCount(
        id: String,
        type: ConversationType,
    )

    // 清除会话未读
    fun cleanConversationUnreadCount(ucConversation: UCConversation)

    // 清除所有未读
    fun cleanAllConversationUnreadCount()

    // 设置会话免打扰
    fun setConversationReceiveMessageOpt(
        id: String,
        type: ConversationType,
        notDisturbEnable: Boolean,
    )

    // 加入会话(群组/部落)
    suspend fun joinConversation(
        id: String,
        type: ConversationType,
        clearHistory: Boolean = false,
    ): Boolean

    // 退出会话(群组/部落)
    suspend fun quitConversation(
        id: String,
        type: ConversationType,
        clearHistory: Boolean = false,
    ): Boolean

    // 获取会话列表
    suspend fun getConversations(
        nextSeq: Long,
        count: Int,
    ): IMConversationResult?

    // 获取指定会话实体
    suspend fun getIMConversation(
        id: String,
        type: ConversationType,
        excludeCache: Boolean = false,
    ): UCConversation?
    //endregion

    //region 历史消息
    suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        count: Int = 20,
        lastMsg: UCMessage? = null,
        descendPullOrder: Boolean = true,
        onlyLocal: Boolean = false,
    ): List<UCInstanceMessage>?

    suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        sequence: Long,
        before: Int,
        after: Int,
    ): List<UCInstanceMessage>?
    //endregion

    fun setMsgExtraForSendMessage(
        message: UCMessage,
        extra: JsonObject,
    )

    suspend fun checkMessageCanSend(
        params: SendParams,
        bundle: MessageBundle?,
        message: UCMessage,
    ): Boolean
}
