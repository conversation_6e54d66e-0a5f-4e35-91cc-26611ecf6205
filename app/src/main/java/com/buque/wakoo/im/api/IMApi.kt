package com.buque.wakoo.im.api

import com.buque.wakoo.im.bean.MsgSendCondition
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

internal interface IMApi {
    @GET("api/ucuser/v1/tim/token")
    suspend fun getTimToken(): ApiResponse<JsonObject>

    /**
     * 消息发送前检测
     *
     * {
     *   "msg_extra": {"is_free": True},  # 跟 UCOO 一致
     *   "toast": "陌生人之间最多只能发送15条消息哦",  # toast 提示，为空字符串则不 toast
     *   "trigger_block_card": True,  # 是否触发拦截卡片，无此字段默认为 False，有此字段需判断是否为 True
     *   "hint": "陌生人之间最多发送15条消息\n互相关注或者添加对方为好友后聊天不限制",  # trigger_block_card 为 True 时为拦截卡片文案
     *   "block_card_btn_txt": "添加对方为好友"  # trigger_block_card 为 True 时为拦截卡片按钮文案
     * }
     * ## 对于日区 增加字段
     * {
     *   "kindly_remind_type": 1,  # 如果为1，则为“即将使用金币”的拦截卡片
     *   "hint": "您的免费消息条数已用完，接下来发消息{}钻石/条\n和对方互相关注成为好友后，可无限制聊天"
     * }
     */
    @POST("api/xya/c2c/v1/msg/grant")
    suspend fun checkMsgCanSend(
        @Body fields: Map<String, String>,
    ): ApiResponse<MsgSendCondition>

    @POST("api/xya/livehouse/v1/message/audit")
    suspend fun checkPublicScreenMessage(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<IMApi>()
        }
    }
}
