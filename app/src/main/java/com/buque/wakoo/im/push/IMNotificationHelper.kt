package com.buque.wakoo.im.push

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.Person
import androidx.core.app.RemoteInput
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import com.buque.wakoo.MainActivity
import com.buque.wakoo.R
import com.buque.wakoo.app.Const
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.isRevoked
import com.buque.wakoo.im.utils.doOnLifecycleEvent
import com.buque.wakoo.manager.localized

object IMNotificationHelper {
    private const val CHANNEL_ID = "ucoo-tim-fcm"
    private const val KEY_ID = "receive_id"
    private const val KEY_TYPE = "receive_type"
    private const val KEY_TEXT_REPLY = "key_text_reply"
    private const val ACTION = "com.im.NOTIFICATION_DISMISSED"

    private var notificationId: Int = 1000

    private val notificationCache by lazy {
        mutableMapOf<String, NotificationInfo>()
    }

    init {
        ProcessLifecycleOwner.get().doOnLifecycleEvent {
            if (it == Lifecycle.Event.ON_START) {
                clearAllCache()
                (IMCompatCore.applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as? NotificationManager)?.cancelAll()
            }
        }
    }

    fun hasNotificationPermission(): Boolean =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                IMCompatCore.applicationContext,
                Manifest.permission.POST_NOTIFICATIONS,
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(IMCompatCore.applicationContext).areNotificationsEnabled()
        }

    fun showIMMessageNotification(
        message: UCInstanceMessage,
        title: String,
        desc: String,
    ) {
        if (message.targetId.isEmpty()) {
            return
        }
        val imUser = message.user ?: return
        val context = IMCompatCore.applicationContext
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as? NotificationManager ?: return

        val cache = notificationCache[message.targetId]
        if (message.isRevoked) {
            if (cache != null) {
                val index = cache.messages.indexOfLast { it.msgId == message.id }
                if (index >= 0) {
                    val item = cache.messages[index]
                    cache.messages[index] =
                        item.copy(
                            message =
                                NotificationCompat.MessagingStyle.Message(
                                    desc,
                                    item.message.timestamp,
                                    item.message.person,
                                ),
                        )
                }
            }
        }

        val name: String

        val exists =
            if (message.isRevoked) {
                if (cache == null || cache.messages.isEmpty()) return
                val last = cache.messages.last()
                name = last.name
                last
            } else {
                name = if (message.isSelf) "你".localized else imUser.name
                null
            }

        val channel =
            NotificationChannel(
                CHANNEL_ID,
                "聊天通知".localized,
                NotificationManager.IMPORTANCE_HIGH,
            ).apply {
                description = "用于显示聊天消息通知".localized
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
        notificationManager.createNotificationChannel(channel)

        // 定义消息的发送者
        val user =
            Person
                .Builder()
                .setName(name) // 发送者名字
                .build()

        val styleMessage =
            exists ?: MessageInfo(
                message.id,
                name,
                NotificationCompat.MessagingStyle.Message(desc, message.timestamp, user),
            )

        val conversationTitle =
            if (!message.isC2CMsg) {
                title
            } else {
                null
            }
        // 构建 MessagingStyle 消息
        val messagingStyle =
            NotificationCompat
                .MessagingStyle(user)
                .setConversationTitle(conversationTitle)
                .setGroupConversation(conversationTitle != null)

        cache?.messages?.forEach {
            if (styleMessage.msgId != it.msgId) {
                messagingStyle.addHistoricMessage(it.message)
            }
        }

        messagingStyle.addMessage(styleMessage.message)

        // 点击通知跳转到聊天界面
        val intent = Intent(context, MainActivity::class.java)
        intent.putExtra(Const.KVKey.FROM_MESSAGE_NOTIFICATION, true)
        val pendingIntent =
            PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )

        val notificationId = cache?.id ?: getNotificationId()

        if (exists == null) {
            if (cache != null) {
                cache.messages.add(styleMessage)
            } else {
                notificationCache[message.targetId] = NotificationInfo(notificationId, mutableListOf(styleMessage))
            }
        }

        // 监听通知消失
        val dismissIntent =
            Intent(context, NotificationDismissReceiver::class.java).apply {
                putExtra(KEY_ID, message.targetId)
                action = ACTION
            }

        val dismissPendingIntent =
            PendingIntent.getBroadcast(
                context,
                0,
                dismissIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                },
            )

        // **创建 RemoteInput（输入框）**
        val remoteInput =
            RemoteInput
                .Builder(KEY_TEXT_REPLY)
                .setLabel("设置回复".localized) // 提示文字
                .build()

        // **创建 PendingIntent 处理回复**
        val replyIntent =
            Intent(context, ReplyReceiver::class.java).apply {
                putExtra(KEY_ID, message.targetId)
                putExtra(KEY_TYPE, message.isC2CMsg)
            }
        val replyPendingIntent =
            PendingIntent.getBroadcast(
                context,
                notificationId,
                replyIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                },
            )

        // **创建回复 Action 按钮**
        val replyAction =
            NotificationCompat.Action
                .Builder(
                    0,
                    "回复".localized,
                    replyPendingIntent,
                ).addRemoteInput(remoteInput) // 添加输入框
                .build()

        // 构建通知
        val notification =
            NotificationCompat
                .Builder(context, CHANNEL_ID)
                .setSmallIcon(
                    R.mipmap.ic_launcher,
                ) // 通知栏小图标
                .setContentTitle("你有一条新消息".localized) // 标题（兼容低版本）
                .setContentText("点击立即查看".localized) // 内容（兼容低版本）
                .setStyle(messagingStyle) // 设置 MessagingStyle
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setDefaults(Notification.DEFAULT_ALL)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setDeleteIntent(dismissPendingIntent)
                .addAction(replyAction)

        // 发送通知
        notificationManager.notify(notificationId, notification.build())
    }

    private fun clearAllCache() {
        notificationCache.clear()
    }

    @Synchronized
    private fun getNotificationId(): Int = notificationId++

    class ReplyReceiver : BroadcastReceiver() {
        override fun onReceive(
            context: Context,
            intent: Intent,
        ) {
            val remoteInput = RemoteInput.getResultsFromIntent(intent) ?: return

            val id = intent.getStringExtra(KEY_ID)
            val isC2CMsg = intent.getBooleanExtra(KEY_TYPE, true)
            if (id.isNullOrEmpty()) return
            val replyText = remoteInput.getCharSequence(KEY_TEXT_REPLY).toString()

            IMCompatCore.sendMessage(
                id,
                if (isC2CMsg) ConversationType.C2C else ConversationType.GROUP,
                MessageBundle.Text.create(replyText),
            )
        }
    }

    class NotificationDismissReceiver : BroadcastReceiver() {
        override fun onReceive(
            context: Context,
            intent: Intent,
        ) {
            if (intent.action == ACTION) {
                val key = intent.getStringExtra(KEY_ID)
                if (!key.isNullOrEmpty()) {
                    notificationCache.remove(key)
                }
            }
        }
    }

    private data class NotificationInfo(
        val id: Int,
        val messages: MutableList<MessageInfo>,
    )

    private data class MessageInfo(
        val msgId: String,
        val name: String,
        val message: NotificationCompat.MessagingStyle.Message,
    )
}
