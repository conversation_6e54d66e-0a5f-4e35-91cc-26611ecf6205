package com.buque.wakoo.network

import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.utils.LogUtils
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.io.IOException
import java.net.ConnectException
import java.net.HttpURLConnection
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLHandshakeException
import kotlin.coroutines.cancellation.CancellationException

class ErrorHandlingInterceptor(
    private val jsonParser: Json,
) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        try {
            val response = chain.proceed(request) // 执行请求

            // 如果HTTP状态码不是成功 (非 2xx)
            if (!response.isSuccessful) {
                val httpStatusCode = response.code
                val errorBodyString = response.body.string() // 读取一次，后续会关闭
                var errorMessageFromServer = response.message // HTTP状态消息
                if (errorBodyString.isNotBlank()) {
                    if (errorBodyString.length < 300) { // 避免过长的html页面作为错误信息
                        errorMessageFromServer = errorBodyString
                    }
                }

                // 根据HTTP状态码抛出特定的HttpException
                val exception =
                    when (httpStatusCode) {
                        HttpURLConnection.HTTP_UNAUTHORIZED -> { // 401
                            val accessTokenExists = AccountManager.getAccessToken() != null
                            val refreshTokenExists = AccountManager.getRefreshToken() != null
                            val isTokenTrulyInvalid =
                                !accessTokenExists && !refreshTokenExists // 如果Token被Authenticator清除了
                            UnauthorizedException(
                                errorMessageFromServer,
                                errorBodyString,
                                isTokenTrulyInvalid,
                            )
                        }

                        HttpURLConnection.HTTP_FORBIDDEN ->
                            ForbiddenException(
                                errorMessageFromServer,
                                errorBodyString,
                            )

                        HttpURLConnection.HTTP_NOT_FOUND ->
                            NotFoundException(
                                errorMessageFromServer,
                                errorBodyString,
                            )

                        in 400..499 ->
                            HttpException(
                                httpStatusCode,
                                errorMessageFromServer,
                                errorBodyString,
                            ) // 其他4xx客户端错误
                        in 500..599 ->
                            ServerException(
                                httpStatusCode,
                                errorMessageFromServer,
                                errorBodyString,
                            ) // 5xx服务器错误
                        else ->
                            HttpException(
                                httpStatusCode,
                                errorMessageFromServer,
                                errorBodyString,
                            ) // 其他未覆盖的HTTP错误
                    }

                // 重新包装响应体，以便上层（如果需要）仍然可以访问它
                // 但由于我们已经读取了string()，原始的流已关闭。
                // 如果上层绝对需要原始流，这里的处理会更复杂，通常抛异常后上层不关心原始response了。
                response.close() // 确保关闭原始响应
                throw exception // 抛出处理后的HTTP异常
            } else {
                val responseBody = response.body

                // 3. 读取响应体（这一步会消耗掉原始的响应流）
                // responseBody.string() 只能调用一次！
                val bodyString = responseBody.string()

                if (bodyString.isEmpty()) {
                    return response
                }

                // 4. 解析JSON并检查 "toast" 字段
                try {
                    val baseResponse = jsonParser.decodeFromString<ApiResponse<JsonObject>>(bodyString)

                    // 检查data字段是否是一个JsonObject
                    if (baseResponse.data is JsonObject) {
                        val dataObject = baseResponse.data

                        // 从JsonObject中获取 "toast" 字段的值
                        val toastMessage = dataObject["toast"]?.jsonPrimitive?.contentOrNull

                        if (!toastMessage.isNullOrBlank()) {
                            showToast(toastMessage)
                        }
                    }
                } catch (e: CancellationException) {
                    throw e
                } catch (e: Exception) {
                    // 解析失败，可能是响应格式不匹配，直接忽略，不影响正常流程
                    // Log.e("ToastInterceptor", "JSON parsing failed", e)
                }

                // 6. 关键：创建一个新的响应体并放回响应中，以便后续的拦截器或转换器可以继续使用
                val newBody = bodyString.toResponseBody(responseBody.contentType())
                return response.newBuilder().body(newBody).build()
            }
        } catch (e: Throwable) {
            // 处理在 chain.proceed(request) 过程中或上述逻辑中抛出的异常
            when (e) {
                is CancellationException, is ApiException -> throw e // 如果已经是我们自定义的AppException，直接重抛

                is UnknownHostException,
                is ConnectException,
                is SocketTimeoutException,
                is SSLHandshakeException,
                -> {
                    // 这些是典型的网络连接问题
                    LogUtils.e("ErrorHandlingInterceptor: 网络连接异常 - ${e.javaClass.simpleName}: ${e.message}")
                    throw NetworkException(originalException = e)
                }

                is IOException -> {
                    // 其他类型的IO异常 (可能是网络问题，也可能是读写流问题)
                    // 如果能更细致判断，可以转为NetworkException，否则作为通用IOException处理或包装
                    LogUtils.e("ErrorHandlingInterceptor: 通用IO异常 - ${e.javaClass.simpleName}: ${e.message} $request")
                    if (e.message?.contains("Canceled") == true) {
                        throw e
                    } else {
                        throw NetworkException("发生IO错误: %s".localizedFormat(e.localizedMessage.orEmpty()), e) // 粗略归为网络异常
                    }
                }
                // kotlinx.serialization.SerializationException 等解析异常会在Converter中抛出，
                // 它们通常不会在这里被直接捕获，除非Converter的实现在这个拦截器之前或内部。
                // 更好的做法是在Repository层捕获SerializationException。

                else -> {
                    // 未知或未处理的异常
                    LogUtils.e("ErrorHandlingInterceptor: 未知错误 - ${e.javaClass.simpleName}: ${e.message}")

                    throw UnknownException("发生未知错误: %s".localizedFormat(e.localizedMessage.orEmpty()), e)
                }
            }
        }
    }
}
