package com.buque.wakoo.network

import com.buque.wakoo.BuildConfig
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.DeviceInfoManager
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.L10nManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import java.util.TimeZone

/**
 * 一个健壮的Token刷新和请求重试拦截器。
 *
 * 功能：
 * 1. 自动检测Token过期（通过API返回的特定status code）。
 * 2. 使用协程互斥锁（Mutex）确保在任何时候只有一个刷新Token的请求在执行。
 * 3. 当一个请求触发Token刷新时，其他需要认证的并发请求会安全地等待刷新结果。
 * 4. 如果Token刷新成功，自动用新Token重试失败的请求以及所有等待中的请求。
 * 5. 明确处理刷新失败的两种情况：
 *    a. RefreshToken过期：登出用户，并对所有相关请求抛出 [SessionExpiredException]。
 *    b. 其他刷新失败（如网络错误）：自动重试一次刷新，如果仍然失败，则对相关请求抛出 [TokenRefreshFailedException]。
 * 6. 通过请求头（`X-Requires-Auth`）来区分哪些请求需要Token认证，避免不必要的拦截。
 */
class AuthenticationInterceptor : Interceptor {
    companion object {
        // API响应中代表Token过期的业务状态码
        private const val TOKEN_EXPIRED_STATUS = -3

        // API响应中代表RefreshToken过期的业务状态码
        private const val REFRESH_TOKEN_EXPIRED_STATUS = -4

        // 用于标记请求是否需要Token认证的请求头
        // 在Retrofit的ApiService接口中为需要认证的方法添加 @Headers("X-Auth-Free: true")
        private const val HEADER_AUTH_FREE = "X-Auth-Free"

        private const val HEADER_PLATFORM_KEY = "Client-Platform"
        private const val HEADER_SYSTEM_VERSION_KEY = "System-Version"
        private const val HEADER_DEVICE_MODEL_KEY = "Device-Model"
        private const val HEADER_PACKAGE_NAME_KEY = "Package-Name"
        private const val HEADER_APP_VERSION_KEY = "App-Version"
        private const val HEADER_DEVICE_ID_KEY = "Device-ID"
        private const val HEADER_SYSTEM_LANGUAGE = "System-Language"
        private const val HEADER_APP_CHANNEL = "App-Channel"
        private const val HEADER_FK_CHANNEL = "Fk-Channel"
        private const val HEADER_TIMEZONE = "timezone"
        private const val HEADER_AdJUST_ID = "Ad-ID"

        // 表示token无效了
        @Volatile
        private var tokenInvalidFlag: Boolean = false
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val requestBuilder =
            chain.request().newBuilder().apply {
                addHeader(HEADER_PLATFORM_KEY, DeviceInfoManager.OS_PLATFORM)
                addHeader(HEADER_SYSTEM_VERSION_KEY, DeviceInfoManager.deviceOs)
                addHeader(HEADER_DEVICE_ID_KEY, DeviceInfoManager.deviceId)
                addHeader(HEADER_DEVICE_MODEL_KEY, DeviceInfoManager.deviceName)
                addHeader(HEADER_PACKAGE_NAME_KEY, BuildConfig.APPLICATION_ID)
                addHeader(HEADER_APP_VERSION_KEY, BuildConfig.VERSION_NAME)
                addHeader(HEADER_SYSTEM_LANGUAGE, L10nManager.languageTag)
                addHeader(HEADER_APP_CHANNEL, BuildConfig.FLAVOR_channel)
                addHeader(HEADER_FK_CHANNEL, if (EnvironmentManager.isGoogleChannel) "googlepay" else BuildConfig.FLAVOR_channel)
                addHeader(HEADER_TIMEZONE, TimeZone.getDefault().id)
                addHeader(HEADER_AdJUST_ID, DeviceInfoManager.adId)
            }

        // 1. 如果请求明确标记不需要认证，则直接放行
        if (chain.request().header(HEADER_AUTH_FREE)?.isNotBlank() == true) {
            return chain.proceed(requestBuilder.build())
        }

        val originalRequest =
            if (tokenInvalidFlag) { // token无效的标记
                synchronized(this) {
                    // 如果有 token，则添加到请求头
                    AccountManager.getAccessToken()?.also { token ->
                        requestBuilder.addHeader("Access-Token", token)
                    }
                    requestBuilder.build()
                }
            } else {
                // 如果有 token，则添加到请求头
                AccountManager.getAccessToken()?.also { token ->
                    requestBuilder.addHeader("Access-Token", token)
                }
                requestBuilder.build()
            }

        // 2. 正常执行请求
        val response = chain.proceed(originalRequest)

        // 3. 检查响应，判断Token是否过期
        // 只处理HTTP 200，因为业务错误码被包装在JSON响应体中
        if (response.code == 200) {
            // 使用 peekBody() 来读取响应体而不断开流，以便后续可以再次读取
            val responseBodyString = response.peekBody(Long.MAX_VALUE).string()
            val jsonObject = runCatching { JSONObject(responseBodyString) }.getOrNull()

            if (jsonObject?.optInt("status") == TOKEN_EXPIRED_STATUS) {
                LogUtils.d("TokenRefreshInterceptor: 检测到Token过期, 准备刷新. Request: ${originalRequest.url}")
                // 关闭旧的响应，因为它已经没用了
                response.close()

                // 4. Token过期，进入同步的刷新和重试流程
                // 这里使用 runBlocking 是因为 Interceptor.intercept 是一个同步方法。
                // 我们必须在这里阻塞当前线程，等待异步的Token刷新操作完成。
                // Mutex 将确保即使有多个请求同时检测到过期，也只有一个会真正执行刷新操作。
                return synchronized(this) {
                    runBlocking {
                        handleTokenRefreshAndRetry(chain, originalRequest)
                    }
                }
            }
        }

        return response
    }

    /**
     * 协程函数，负责处理Token刷新和请求重试的核心逻辑。
     * 使用互斥锁来保证线程安全。
     */
    private suspend fun handleTokenRefreshAndRetry(
        chain: Interceptor.Chain,
        originalRequest: Request,
    ): Response {
        val currentToken = AccountManager.getAccessToken()
        val tokenInFailedRequest = originalRequest.header("Access-Token")

        // 双重检查锁定（Double-Check Locking）模式：
        // 在获取锁之后，再次检查Token是否已经被其他线程/协程刷新了。
        // 如果当前管理器中的Token和我们这个失败请求的Token不一样了，
        // 说明在我们等待锁的期间，已经有别的请求完成了刷新操作。
        if (currentToken != null && currentToken != tokenInFailedRequest) {
            LogUtils.d("TokenRefreshInterceptor: Token已被刷新, 直接使用新Token重试. Request: ${originalRequest.url}")
            // 直接使用新的Token重试当前请求
            return proceedWithNewToken(chain, originalRequest, currentToken)
        }
        tokenInvalidFlag = true

        // 执行刷新Token的操作，包含重试逻辑
        LogUtils.d("TokenRefreshInterceptor: 开始执行Token刷新操作.")
        val newAccessToken = performTokenRefresh() // 这个函数会处理所有刷新逻辑

        // 使用新的Token重试原始请求
        return proceedWithNewToken(chain, originalRequest, newAccessToken)
    }

    /**
     * 执行实际的Token刷新API调用，并包含一次失败重试逻辑。
     * @return 返回新的 AccessToken。
     * @throws SessionExpiredException 如果RefreshToken过期。
     * @throws TokenRefreshFailedException 如果刷新尝试最终失败。
     */
    private suspend fun performTokenRefresh(): String {
        var refreshToken =
            AccountManager.getRefreshToken()
                ?: run {
                    LogUtils.e("TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.")
                    AccountManager.logout("会话过期，请重新登录".localized)
                    throw SessionExpiredException
                }

        // 尝试第一次刷新
        var refreshResult = ApiClient.refreshTokenApiCall(refreshToken)

        if (!refreshResult.isSuccess) {
            // 检查是否是 RefreshToken 过期
            refreshResult.onBusinessFailure {
                if (it.businessCode == REFRESH_TOKEN_EXPIRED_STATUS) {
                    LogUtils.w("TokenRefreshInterceptor: RefreshToken已过期, 执行登出.")
                    AccountManager.logout("会话过期，请重新登录".localized)
                    throw SessionExpiredException
                }
            }

            refreshToken = AccountManager.getRefreshToken()
                ?: run {
                    LogUtils.e("TokenRefreshInterceptor: RefreshToken为空，无法刷新, 执行登出.")
                    AccountManager.logout("会话过期，请重新登录".localized)
                    throw SessionExpiredException
                }

            // 如果是其他错误（如网络问题），进行第二次尝试
            LogUtils.w("TokenRefreshInterceptor: 首次Token刷新失败, 准备重试一次. Error: ${refreshResult.exceptionOrNull()?.message}")
            kotlinx.coroutines.delay(1000) // 延迟1秒后重试
            refreshResult = ApiClient.refreshTokenApiCall(refreshToken)
        }

        // 处理最终的刷新结果
        if (refreshResult.isSuccess) {
            val newTokens = refreshResult.getOrThrow()
            AccountManager.setApiTokens(newTokens.accessToken, newTokens.refreshToken)
            tokenInvalidFlag = false
            LogUtils.d("TokenRefreshInterceptor: Token刷新成功!")
            return newTokens.accessToken
        } else {
            // 经过重试后仍然失败
            LogUtils.e("TokenRefreshInterceptor: Token刷新彻底失败. Error: ${refreshResult.exceptionOrNull()?.message}")
            // 此时不登出，因为可能是临时网络问题。抛出异常，让调用方决定如何处理。
            throw TokenRefreshFailedException
        }
    }

    /**
     * 使用新的AccessToken构建并执行请求。
     */
    private fun proceedWithNewToken(
        chain: Interceptor.Chain,
        request: Request,
        accessToken: String,
    ): Response {
        val newRequest =
            request
                .newBuilder()
                .header("Access-Token", accessToken)
                .build()

        LogUtils.d("TokenRefreshInterceptor: 使用新Token继续请求. New Token: $accessToken, Request: ${request.url}")
        return chain.proceed(newRequest)
    }
}
