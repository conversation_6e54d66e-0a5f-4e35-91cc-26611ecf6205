package com.buque.wakoo.network.api.service

import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface RelationApi {

    @GET("api/xya/connection/v1/social/couples/check")
    suspend fun checkBuddyPossible(@Query("target_user_id") targetUid: String): ApiResponse<JsonObject>

    @POST("api/xya/connection/v1/social/friends/add")
    suspend fun addFriend(@Body body:Map<String, String>): ApiResponse<JsonObject>

    @POST("api/xya/connection/v1/social/couples/break")
    suspend fun breakUpBuddy(): ApiResponse<JsonObject>
}