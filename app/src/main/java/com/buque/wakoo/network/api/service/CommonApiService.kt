package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.PendantConfig
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface CommonApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<CommonApiService>()
        }
    }

    /**
     * 举报接口
     * ```
     * target_type	枚举
     * 1	用户
     * 2	语音房
     * 3	私密小屋
     * 4	瞬间
     * True
     * target_id	整数	True
     * accusation_type	枚举
     * 1	政治相关
     * 2	色情低俗
     * 3	血腥暴力
     * 4	广告营销
     * 5	恶意诈骗
     * 6	不文明语言
     * 0	其他
     * True
     * note	字符串，最长500个字符	False
     * media_list	图片信息列表
     * 基础格式：[{image_info}, {image_info}..]
     * 其中image_info字段信息如下：
     * url	字符串，URL地址
     * width	整数px值
     * height	整数px值
     * size	整数byte值
     * ```
     * https://api.test.ucoofun.com/doc/index#a2u0
     * @return
     */
    @POST("api/xya/general/v1/report")
    suspend fun report(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    @POST("api/xya/user/v1/user/settings")
    suspend fun userSettings(): ApiResponse<JsonObject>

    @GET("api/xya/general/v1/search ")
    suspend fun searchAllById(
        @Query("public_id") publicId: String,
    ): ApiResponse<JsonObject>

    @GET("api/xya/general/v1/navigation/entry/settings")
    suspend fun getNavigationEntries(): ApiResponse<PendantConfig>

    @GET("api/xya/connection/v1/social/script/hello")
    suspend fun getHelloWord(): ApiResponse<JsonObject>

    @POST("api/xya/c2c/v1/msg/hi")
    suspend fun sayHi(
        @Body body: Map<String, String>,
    ): ApiResponse<JsonObject>

    @GET("api/xya/c2c/v1/online/msg/task/check")
    suspend fun sayHiV2Start(
        @Query("task_type") type: Int? = null,
    ): ApiResponse<JsonObject>

    @GET("api/xya/c2c/v1/online/msg/task/submit")
    suspend fun sayHiV2End(
        @Query("task_type") type: Int? = null,
    ): ApiResponse<JsonObject>

    /**
     * @param is_using 是否使用
     * @param is_cold_start 是否冷启动
     */
    @POST("api/xya/user/v1/ui/switch/report")
    suspend fun appStatusSync(@Body body: Map<String, String>): ApiResponse<JsonObject>
}
