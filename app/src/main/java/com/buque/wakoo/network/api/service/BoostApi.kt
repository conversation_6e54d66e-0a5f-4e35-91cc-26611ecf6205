package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.boost.BoostConfig
import com.buque.wakoo.bean.boost.MissionInfo
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface BoostApi {

    @GET("api/xya/mission/v1/missions/conf")
    suspend fun getBoostConfig(): ApiResponse<BoostConfig>

    /**
     *  hide_finished_mission
     * award_type
     */
    @GET("api/xya/mission/v1/missions/info")
    suspend fun getTaskInfo(
        @Query("hide_finished_mission") hideFinishedMission: Boolean = true,
        @Query("award_type") awardType: Int
    ): ApiResponse<MissionInfo>

    @GET("api/xya/mission/v1/missions/sign_in/info")
    suspend fun getSignInfo(): ApiResponse<MissionInfo.MissionSeries>

    @POST("api/xya/mission/v1/missions/sign_in")
    suspend fun makeSign(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/xya/share/v1/japan/invite_code/fill")
    suspend fun fillInviteCode(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @GET("api/xya/mission/v1/missions/to_finish/info")
    suspend fun finishTask(@Query("task_id") taskId: Int): ApiResponse<JsonObject>

    @POST("api/xya/moneybag/v2/japan/extract/precheck")
    suspend fun preCheck(): ApiResponse<JsonObject>

    @GET("api/xya/moneybag/v1/mall/exchange/list")
    suspend fun getExchangeList(@Query("exchange_type") type: Int = 1): ApiResponse<JsonObject>

    @POST("api/xya/moneybag/v1/mall/exchange/item")
    suspend fun exchangeItem(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @GET("api/xya/moneybag/v1/diamond/change/history")
    suspend fun getPRecords(@Query("account_type") accountType: Int, @Query("last_id") lastId: Int?): ApiResponse<JsonObject>
}