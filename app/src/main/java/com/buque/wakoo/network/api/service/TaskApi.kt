package com.buque.wakoo.network.api.service

import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.api.bean.DoTaskResult
import kotlinx.serialization.json.JsonObject
import retrofit2.http.GET
import retrofit2.http.Query

interface TaskApi {

    @GET("api/xya/connection/v1/social/couples/task/action")
    suspend fun doBuddyTask(@Query("task_id") id: String): ApiResponse<DoTaskResult>
}