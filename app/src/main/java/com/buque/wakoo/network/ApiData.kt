package com.buque.wakoo.network

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

/**
{
"access_token": "0ffda5a48c304f23bbdb21b5eb6224b4",
"refresh_token": "90bd512dbe56432aa4b03f8889c58d36",
"access_token_expire_seconds": 7200,
"refresh_token_expire_seconds": 14400
}
 */
@Serializable
data class TokenRefreshResponse(
    @SerialName("access_token")
    val accessToken: String = "", // 0ffda5a48c304f23bbdb21b5eb6224b4
    @SerialName("access_token_expire_seconds")
    val accessTokenExpireSeconds: Int = 0, // 7200
    @SerialName("refresh_token")
    val refreshToken: String = "", // 90bd512dbe56432aa4b03f8889c58d36
    @SerialName("refresh_token_expire_seconds")
    val refreshTokenExpireSeconds: Int = 0,
)

/**
 * 通用API响应体结构
 * @param T 实际业务数据的类型
 * @property code 业务状态码，例如 0 表示成功，其他表示各种业务错误
 * @property message 业务消息，成功时可能为空，失败时为错误描述
 * @property data 实际的业务数据，成功时有值，失败时可能为空或包含错误详情
 */
@Serializable
data class ApiResponse<out T>(
    @SerialName("status") val code: Int, // 业务状态码
    @SerialName("msg") val message: String?, // 业务消息
    val data: T?,
    val extra: JsonObject? = null,
) {
    val requireData: T
        get() {
            if (data == null) {
                throw MissingApiResponseDataException
            }
            return data
        }

    /**
     * 判断业务是否成功
     */
    fun isSuccessful(): Boolean = code == 0 // 假设0代表业务成功
}
