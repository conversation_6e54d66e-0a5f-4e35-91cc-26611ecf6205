package com.buque.wakoo.network.api.bean


import com.android.billingclient.api.BillingClient
import com.buque.wakoo.core.pay.GoogleProduct
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChargeItemContainer(
    @SerialName("fk_channel")
    val fkChannel:Int,
    @SerialName("charge_items")
    val chargeItems:List<ChargeItemEntity>)

@Serializable
data class ChargeItemEntity(
    @SerialName("currency_mark")
    val currencyMark: String = "",
    @SerialName("currency_number")
    val currencyNumber: String = "",
    @SerialName("currency_unit")
    val currencyUnit: String = "",
    @SerialName("dollar")
    val dollar: String = "",
    @SerialName("product_id")
    val productId: String = "",
    @SerialName("ucoin")
    val ucoin: Int = 0
) : GoogleProduct {
    override val googleProductId: String = productId
    override val googleProductType: String = BillingClient.ProductType.INAPP
    override val googleProductPrice: String = dollar

}

@Serializable
data class ChargeDataEntity(
    val balance: Int = 0,
    @SerialName("charge_items")
    val chargeItems: List<ChargeItemEntity>
)