package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.ConversationConfig
import com.buque.wakoo.bean.CoupleInfo
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import retrofit2.http.GET
import retrofit2.http.Query

interface C2CChatApi {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<C2CChatApi>()
        }
    }

    @GET("api/xya/c2c/v1/online/msg/preference")
    suspend fun getUserConfig(
        @Query("target_userid") uid: String,
    ): ApiResponse<ConversationConfig>

    @GET("api/xya/connection/v1/social/couples/info")
    suspend fun getCpInfoForJP(
        @Query("userid") uid: String,
    ): ApiResponse<CoupleInfo>
}
