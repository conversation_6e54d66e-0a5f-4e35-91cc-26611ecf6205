package com.buque.wakoo.network.api.service

import com.buque.wakoo.BuildConfig
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.GET
import retrofit2.http.Query
import retrofit2.http.Url

interface TranslateApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<TranslateApiService>()
        }
    }

    @GET
    suspend fun queryLanguagePackage(
        @Url fullUrl: String = "${EnvironmentManager.prodApiUrl}api/xya/config/v1/translate/latest",
        @Query("language_code") code: String,
    ): ApiResponse<JsonObject>
}
