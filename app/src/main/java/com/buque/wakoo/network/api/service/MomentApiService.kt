package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.MomentListResponse
import com.buque.wakoo.ext.getTimeZoneNum
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import java.util.TimeZone

interface MomentApiService {
    companion object {
        val instance by lazy {
            ApiClient.createuserApiService<MomentApiService>()
        }
    }

    @POST("api/xya/record/v1/record/add")
    suspend fun publishMoment(
        @Body fields: Map<String, String>,
    ): ApiResponse<JsonObject>

    @GET("api/xya/record/v1/record/show/others")
    suspend fun getMomentList(
        @Query("userid") userid: String,
        @Query("timezone_num") timezoneNum: Int = TimeZone.getDefault().getTimeZoneNum(),
        @Query("last_id") lastId: Int = 0,
    ): ApiResponse<MomentListResponse>

    @GET("api/xya/record/v1/record/show/my")
    suspend fun getMyMoments(
        @Query("timezone_num") timezoneNum: Int = TimeZone.getDefault().getTimeZoneNum(),
        @Query("last_id") lastId: Int = 0,
    ): ApiResponse<MomentListResponse>

    /**
     * 删除moment
     * @param moment_id
     */
    @POST("api/xya/record/v1/record/remove")
    suspend fun deleteMoment(
        @Body map: Map<String, String>,
    ): ApiResponse<JsonObject>

    /**
     * "trend_id" to event.momentId.toString(),
     * "favorite" to event.isLike.toString()
     * 点赞瞬间
     */
    @POST("api/xya/record/v1/record/favorite")
    suspend fun likeMoment(
        @Body map: Map<String, @JvmSuppressWildcards Any>,
    ): ApiResponse<JsonObject>
}
