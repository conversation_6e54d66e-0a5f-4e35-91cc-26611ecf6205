package com.buque.wakoo.network.api.service

import com.buque.wakoo.bean.hb.GrabHongBaoResult
import com.buque.wakoo.bean.hb.HBSettings
import com.buque.wakoo.bean.hb.HongBaoInfo
import com.buque.wakoo.bean.hb.PostHongBaoParams
import com.buque.wakoo.network.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface HongBaoApi {

    @GET("api/xya/luckypacket/v1/luckypacket/settings")
    suspend fun getHongBaoSettings(): ApiResponse<HBSettings>

    @POST("api/xya/luckypacket/v1/luckypacket/create")
     suspend fun postRedPackage(@Body params: PostHongBaoParams): ApiResponse<JsonObject>

    @POST("api/xya/luckypacket/v1/luckypacket/grab")
     suspend fun grabRedPacket(@Body body: Map<String, String>): ApiResponse<GrabHongBaoResult>

    @GET("api/xya/luckypacket/v1/luckypacket/detail")
     suspend fun getRedPacketDetail(@Query("packet_id") redPacketId: String): ApiResponse<HongBaoInfo>

    @GET("api/xya/luckypacket/v1/luckypacket/list")
     suspend fun getRedPacketList(
        @Query("scene_id") sceneId: String,
        @Query("scene_type") sceneType: Int
    ): ApiResponse<JsonObject>
}

interface JaHongBaoApi{
    @GET("api/xya/japanluckypacket/v1/luckypacket/settings")
    suspend fun getHongBaoSettings(): ApiResponse<HBSettings>

    @POST("api/xya/japanluckypacket/v1/luckypacket/create")
    suspend fun postRedPackage(@Body params: PostHongBaoParams): ApiResponse<JsonObject>

    @POST("api/xya/japanluckypacket/v1/luckypacket/grab")
    suspend fun grabRedPacket(@Body body: Map<String, String>): ApiResponse<GrabHongBaoResult>

    @GET("api/xya/japanluckypacket/v1/luckypacket/detail")
    suspend fun getRedPacketDetail(@Query("packet_id") redPacketId: String): ApiResponse<HongBaoInfo>

    @GET("api/xya/japanluckypacket/v1/luckypacket/list")
    suspend fun getRedPacketList(
        @Query("scene_id") sceneId: String,
        @Query("scene_type") sceneType: Int
    ): ApiResponse<JsonObject>
}