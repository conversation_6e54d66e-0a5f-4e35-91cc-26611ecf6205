package com.buque.wakoo.network.api.bean

import com.buque.wakoo.bean.FeedItemData
import com.buque.wakoo.bean.LiveRoomCardItem
import com.buque.wakoo.bean.VoiceCardItem
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator
import kotlinx.serialization.json.JsonNames

@OptIn(ExperimentalSerializationApi::class)
@Serializable
@JsonClassDiscriminator("t") // <-- 仅对 Message 体系生效
sealed interface FeedItemWrapperResponse : IFeedItemResponseConvert

@Serializable
@SerialName("1")
data class VoiceItemWrapperResponse(
    @SerialName("sound") val data: VoiceItemResponse, // 2
) : FeedItemWrapperResponse {
    override val id: Int
        get() = data.id

    override fun convert(): FeedItemData = VoiceCardItem.fromResponse(data)
}

@Serializable
@SerialName("2")
data class LiveRoomItemWrapperResponse(
    @SerialName("audioroom") val data: LiveRoomItemResponse, // 2
) : FeedItemWrapperResponse {
    override val id: Int
        get() = data.id

    override fun convert(): FeedItemData = LiveRoomCardItem.fromResponse(data)
}

@Serializable
sealed interface IFeedItemResponseConvert {
    val id: Int

    fun convert(): FeedItemData
}

/**
 * { "id": 10, "title": "Ssssddsdsfsds", "resource":
 * "https://media.ucoofun.com/mobileclient/ios/2025-05-22/audio_2571857145.wav", "duration": 8,
 * "like_count": 3, "collect_count": 2, "visibility": 1, "created_at": 1747880345, "user": {
 * "userid": 3768, "public_id": "104845", "nickname": "Aaa", "avatar_url":
 * "https://s.test.ucoofun.com/aaceCM?x-oss-process=image/format,webp", "gender": 1, "age": 25,
 * "avatar_frame": "", "is_high_quality": false, "level": 0, "exp_level_info": { "charm_level": 1,
 * "wealth_level": 1 }, "is_follow": false }, "is_like": false, "is_collect": false, "tags": [ {
 * "id": 1, "name": "测试多语言问题" }, { "id": 2, "name": "美好的日子" }, { "id": 5, "name": "青春" } ],
 * "background": "https://media.ucoofun.com/xya/sound/background/%E5%8D%A1%E7%89%87032x.png" }
 */
@Serializable
data class VoiceItemResponse(
    @SerialName("background")
    val background: String =
        "", // https://media.ucoofun.com/xya/sound/background/%E5%8D%A1%E7%89%87032x.png
    @SerialName("collect_count") val collectCount: Int = 0, // 2
    @SerialName("created_at") val createdAt: Int = 0, // 1747880345
    @SerialName("duration") val duration: Int = 0, // 8
    @SerialName("id") override val id: Int = 0, // 10
    @SerialName("is_collect") val isCollect: Boolean = false, // false
    @SerialName("is_like") val isLike: Boolean = false, // false
    @SerialName("like_count") val likeCount: Int = 0, // 3
    @SerialName("resource")
    val resource: String =
        "", // https://media.ucoofun.com/mobileclient/ios/2025-05-22/audio_2571857145.wav
    @SerialName("tags") val tags: List<VoiceTag> = listOf(),
    @SerialName("title") val title: String = "", // Ssssddsdsfsds
    @SerialName("user") val user: UserResponse = UserResponse(),
    @SerialName("visibility") val visibility: Int = 0,
    var pageId: Int = id,
) : IFeedItemResponseConvert {
    override fun convert(): FeedItemData = VoiceCardItem.fromResponse(this)
}

@Serializable
data class LiveRoomItemResponse(
    @SerialName("id") val id: Int = 0, // 10
    @SerialName("owner") val user: UserResponse = UserResponse(),
    @SerialName("title") val title: String = "", // Ssssddsdsfsds
    @SerialName("desc") val desc: String = "", // Ssssddsdsfsds
    @SerialName("tags") val tags: List<VoiceTag> = listOf(),
    @SerialName("background")
    val background: String =
        "", // https://media.ucoofun.com/xya/sound/background/%E5%8D%A1%E7%89%87032x.png
    @SerialName("rule_type")
    val ruleType: Int = 0,
    @SerialName("mics")
    val mics: List<Seat> = listOf(),
    @SerialName("visibility") val visibility: Int = 0,
    @SerialName("audio_live_play_url") val roomAudioStream: String = "",
    @SerialName("in_room_user_cnt") val inRoomUserCnt: Int = 0,
    @SerialName("last_users") val last_users: List<UserResponse> = listOf(),
    var pageId: Int = id,
)

@Serializable
data class VoiceTag(
    @SerialName("id") val id: Int = 0, // 1
    @SerialName("name") val name: String = "", // 测试多语言问题
)

@Serializable
sealed interface IIFeedItemResponseConvertListOwner {
    val hasNext: Boolean
    val list: List<IFeedItemResponseConvert>
}

@Serializable
data class VoiceFeedResponse(
    @SerialName("have_next_page") override val hasNext: Boolean = true,
    @SerialName("sounds")
    override val list: List<VoiceItemResponse> = listOf(),
) : IIFeedItemResponseConvertListOwner

@Serializable
data class VoiceFeedResponseV2(
    @SerialName("have_next_page") override val hasNext: Boolean = true,
    @SerialName("sound_items")
    override val list: List<FeedItemWrapperResponse> = listOf(),
) : IIFeedItemResponseConvertListOwner

@OptIn(ExperimentalSerializationApi::class)
@Serializable
data class VoiceListResponse(
    @SerialName("have_next_page") val hasNext: Boolean = true,
    @JsonNames("liked", "collections")
    val list: List<VoicePageResponse> = listOf(),
)

@Serializable
data class VoicePageResponse(
    @SerialName("id") val id: Int = 0,
    @SerialName("sound")
    val sound: VoiceItemResponse = VoiceItemResponse(),
) {
    init {
        sound.pageId = id
    }
}

@OptIn(ExperimentalSerializationApi::class)
@Serializable
data class VoiceTagListResponse(
    @SerialName("tags") @JsonNames("tags") val list: List<VoiceTag> = listOf(),
    @SerialName("have_next_page") val hasNext: Boolean = false,
)

@Serializable
data class PublishVoiceRequest(
    @SerialName("resource") val resource: String = "",
    @SerialName("duration") val duration: Int = 0,
    @SerialName("title") val title: String = "",
    @SerialName("tags") val tags: String = "",
    @SerialName("visibility") val visibility: Int = 0,
    @SerialName("background_id") val backgroundId: Int = 0,
)

/**
{
"sound_max_duration": 111,
"sound_min_duration": 5,
"background": [
{
"id": 1,
"resource": "xxxxxxx",
"icon": "xxxx"
}
]
}
 */
@Serializable
data class VoicePublishConfig(
    @SerialName("background")
    val background: List<VoiceBackground> = listOf(),
    @SerialName("sound_max_duration")
    val soundMaxDuration: Int = 60,
    @SerialName("sound_min_duration")
    val soundMinDuration: Int = 5,
    val tags: List<VoiceTag> = emptyList(),
)

@Serializable
data class VoiceBackground(
    @SerialName("icon")
    val icon: String = "",
    @SerialName("id")
    val id: Int = 0, // 1
    @SerialName("resource")
    val resource: String = "",
    @SerialName("cost")
    val cost: Int = 0,
    @SerialName("cost_type")
    val costType: Int = 0,
    @SerialName("role_type")
    val roleType: Int = 0,
)
