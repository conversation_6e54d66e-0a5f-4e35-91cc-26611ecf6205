package com.buque.wakoo.repository

import com.buque.wakoo.bean.hb.PostHongBaoParams
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.HongBaoApi
import com.buque.wakoo.network.api.service.JaHongBaoApi
import com.buque.wakoo.network.executeApiCallExpectingData

class HongBaoRepo {

    private val api = ApiClient.createuserApiService<HongBaoApi>()
    private val apiJa = ApiClient.createuserApiService<JaHongBaoApi>()

    private val isCN: Boolean
        get() = AccountManager.selfUser?.isCN == true

    suspend fun getSettings() = executeApiCallExpectingData {
        if (isCN) {
            api.getHongBaoSettings()
        } else {
            apiJa.getHongBaoSettings()
        }
    }

    suspend fun postHongBao(params: PostHongBaoParams) = executeApiCallExpectingData {
        if (isCN) {
            api.postRedPackage(params)
        } else {
            apiJa.postRedPackage(params)
        }
    }

    suspend fun getDetail(id: String) = executeApiCallExpectingData {
        if (isCN) {
            api.getRedPacketDetail(id)
        } else {
            apiJa.getRedPacketDetail(id)
        }
    }

    suspend fun grab(id: String) = executeApiCallExpectingData {
        if (isCN) {
            api.grabRedPacket(mapOf("packet_id" to id))
        } else {
            apiJa.grabRedPacket(mapOf("packet_id" to id))
        }
    }

    suspend fun getList(sceneType: Int, sceneId: String) =
        executeApiCallExpectingData {
            if (isCN) {
                api.getRedPacketList(sceneId, sceneType)
            } else {
                apiJa.getRedPacketList(sceneId, sceneType)
            }
        }
}