package com.buque.wakoo.repository

import com.buque.wakoo.bean.AccountInfo
import com.buque.wakoo.utils.LogUtils
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json

internal interface IAccountPreferencesRepository {
    // 对外暴露的只读Flow
    val accountInfoFlow: StateFlow<AccountInfo?>

    suspend fun updateInfo(action: (accountInfo: AccountInfo) -> AccountInfo)

    /**
     * 保存用户信息（登录时调用）
     */
    suspend fun saveData(accountInfo: AccountInfo)

    /**
     * 清空用户信息（退出登录时调用）
     */
    suspend fun clearData(): Boolean
}

/**
 * 账户数据存储仓库 - 使用MMKV实现
 */
class AccountPreferencesRepository : IAccountPreferencesRepository {
    // 定义存储用的key常量
    private object Keys {
        const val ACCOUNT_INFO = "account_info"
        const val AUTH_TOKEN = "auth_token" // 单独存储token方便快速检查登录状态
    }

    // 初始化MMKV
    private val mmkv: MMKV = MMKV.mmkvWithID("account_prefs")

    // Json序列化工具
    private val json =
        Json {
            ignoreUnknownKeys = true
            coerceInputValues = true
        }

    // 用户数据StateFlow
    private val _accountInfoFlow = MutableStateFlow(getCurrentAccountInfo())

    // 对外暴露的只读Flow
    override val accountInfoFlow: StateFlow<AccountInfo?> = _accountInfoFlow.asStateFlow()

    /**
     * 获取当前保存的账户信息
     */
    private fun getCurrentAccountInfo(): AccountInfo? {
        val authToken = mmkv.decodeString(Keys.AUTH_TOKEN, null)
        return if (authToken.isNullOrEmpty()) {
            LogUtils.d("没有找到登录信息")
            null
        } else {
            try {
                // 尝试读取整个账户信息对象
                val accountInfoJson = mmkv.decodeString(Keys.ACCOUNT_INFO, null)
                if (!accountInfoJson.isNullOrEmpty()) {
                    // 从JSON反序列化
                    json.decodeFromString<AccountInfo>(accountInfoJson).also {
                        LogUtils.d("从JSON读取到用户信息: id=${it.id}, name=${it.name}")
                    }
                } else {
                    null
                }
            } catch (e: Exception) {
                LogUtils.e(e, "读取用户信息失败")
                null
            }
        }
    }

    override suspend fun updateInfo(action: (accountInfo: AccountInfo) -> AccountInfo) {
        val value = accountInfoFlow.value
        if (value != null) {
            val newValue = action(value)
            if (newValue != value) {
                saveData(newValue)
            }
        }
    }

    /**
     * 保存用户信息（登录时调用）
     */
    override suspend fun saveData(accountInfo: AccountInfo) =
        withContext(Dispatchers.IO) {
            try {
                // 序列化整个对象到JSON
                val accountInfoJson = json.encodeToString(accountInfo)
                mmkv.encode(Keys.ACCOUNT_INFO, accountInfoJson)

                // 单独存储token方便快速检查登录状态
                mmkv.encode(Keys.AUTH_TOKEN, accountInfo.apiAuthToken)

                LogUtils.d("保存用户信息成功: id=${accountInfo.id}, name=${accountInfo.name}")

                // 更新StateFlow
                _accountInfoFlow.value = accountInfo
            } catch (e: Exception) {
                LogUtils.e(e, "保存用户信息失败: ${e.message}")
            }
        }

    /**
     * 清空用户信息（退出登录时调用）
     */
    override suspend fun clearData(): Boolean {
        return if (_accountInfoFlow.value != null) {
            withContext(Dispatchers.IO) {
                mmkv.clearAll()
                LogUtils.d("清除用户信息成功")
                // 更新StateFlow
                _accountInfoFlow.value = null
            }
            true
        } else {
            false
        }
    }

}

/**
 * 账户数据存储仓库 - 使用MMKV实现
 */
class PreviewAccountPreferencesRepository : IAccountPreferencesRepository {
    // 对外暴露的只读Flow
    override val accountInfoFlow: StateFlow<AccountInfo?> = MutableStateFlow(null)

    override suspend fun updateInfo(action: (accountInfo: AccountInfo) -> AccountInfo) = Unit

    /**
     * 保存用户信息（登录时调用）
     */
    override suspend fun saveData(accountInfo: AccountInfo) = Unit

    /**
     * 清空用户信息（退出登录时调用）
     */
    override suspend fun clearData(): Boolean = false
}
