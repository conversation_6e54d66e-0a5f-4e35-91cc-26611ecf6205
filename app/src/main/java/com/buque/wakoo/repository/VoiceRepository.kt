package com.buque.wakoo.repository

import com.buque.wakoo.bean.LiveRoomCardItem
import com.buque.wakoo.network.api.bean.PublishVoiceRequest
import com.buque.wakoo.network.api.bean.VoicePublishConfig
import com.buque.wakoo.network.api.bean.VoiceTagListResponse
import com.buque.wakoo.network.api.service.VoiceApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import kotlinx.serialization.json.JsonObject

class VoiceRepository {
    private val voiceApiService
        get() = VoiceApiService.instance

    /** 点赞 */
    suspend fun likeVoice(
        voiceId: String,
        isLike: Boolean,
    ): Result<JsonObject> =
        executeApiCallExpectingData {
            voiceApiService.likeVoice(fields = mapOf("sound_id" to voiceId, "like" to isLike.toString()))
        }

    /** 收藏 */
    suspend fun favoriteVoice(
        voiceId: String,
        isFavorite: <PERSON>olean,
    ): Result<JsonObject> =
        executeApiCallExpectingData {
            voiceApiService.favoriteVoice(fields = mapOf("sound_id" to voiceId, "collect" to isFavorite.toString()))
        }

    /** 不感兴趣 */
    suspend fun markAsBored(id: String): Result<JsonObject> =
        executeApiCallExpectingData {
            voiceApiService.markAsBored(fields = mapOf("sound_id" to id))
        }

    suspend fun markAsBoredRoom(id: String): Result<JsonObject> =
        executeApiCallExpectingData {
            voiceApiService.markAsBoredRoom(fields = mapOf("live_house_id" to id))
        }

    /** 删除声音 */
    suspend fun deleteVoice(voiceId: String): Result<JsonObject> =
        executeApiCallExpectingData {
            voiceApiService.deleteVoice(fields = mapOf("sound_id" to voiceId))
        }

    /** 获取官方标签 */
    suspend fun getOfficialTags(): Result<VoiceTagListResponse> =
        executeApiCallExpectingData {
            voiceApiService.getOfficialTags(tagType = 1)
        }

    /** 获取声音配置 */
    suspend fun getVoiceConf(): Result<VoicePublishConfig> =
        executeApiCallExpectingData {
            voiceApiService.getVoiceConf()
        }

    /** 发布声音 */
    suspend fun publishVoice(request: PublishVoiceRequest): Result<JsonObject> =
        executeApiCallExpectingData {
            voiceApiService.publishVoice(request = request)
        }

    suspend fun getLiveRoomPreviewInfo(liveHouseId: String): Result<LiveRoomCardItem> =
        executeApiCallExpectingData {
            voiceApiService.getLiveRoomPreviewInfo(liveHouseId)
        }.map {
            LiveRoomCardItem.fromResponse(it)
        }
}
