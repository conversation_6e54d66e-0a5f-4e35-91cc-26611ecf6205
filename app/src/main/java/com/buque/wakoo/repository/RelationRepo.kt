package com.buque.wakoo.repository

import com.buque.wakoo.consts.SceneType
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.RelationApi
import com.buque.wakoo.network.executeApiCallExpectingData

class RelationRepo(val api: RelationApi = ApiClient.createuserApiService<RelationApi>()) {


    suspend fun checkBuddyPossible(targetUid: String) = executeApiCallExpectingData {
        api.checkBuddyPossible(targetUid)
    }


    suspend fun addFriend(targetUid: String, sceneType: Int = SceneType.NONE, sceneId: String? = null) = executeApiCallExpectingData {
        api.addFriend(buildMap {
            put("target_user_id", targetUid)
            if (sceneType != SceneType.NONE) {
                put("scene_type", sceneType.toString())
            }
            if (sceneId != null) {
                put("scene_id", sceneId)
            }
        })
    }

    suspend fun breakUpBuddy() = executeApiCallExpectingData { api.breakUpBuddy() }
}