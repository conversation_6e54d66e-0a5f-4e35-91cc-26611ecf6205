package com.buque.wakoo.bean.user

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.ext.keepLastNonNullState
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class SelfUserInfo(
    val user: BasicUser,
    val decorations: UserDecorations = UserDecorations.empty, // 装扮
    val socialInfo: SocialInfo = SocialInfo.empty,
    val currencyInfo: CurrencyInfo = CurrencyInfo.empty,
    val otherInfo: SelfOtherInfo = SelfOtherInfo.empty,
    @Transient val cpRelationInfo: CpRelationInfo? = null,
) : User by user,
    IUserDecorations by decorations,
    ISocialInfo by socialInfo,
    ICurrencyInfo by currencyInfo,
    ISelfOtherInfo by otherInfo {
    companion object {
        val previewBoy
            get() =
                SelfUserInfo(user = BasicUser.sampleBoy)

        fun fromResponse(response: UserResponse): SelfUserInfo =
            SelfUserInfo(
                user = BasicUser.fromResponse(response),
                decorations = UserDecorations.fromResponse(response),
                socialInfo = SocialInfo.fromResponse(response),
                currencyInfo = CurrencyInfo.fromResponse(response),
                otherInfo = SelfOtherInfo.fromResponse(response),
                cpRelationInfo = CpRelationInfo.fromResponse(response),
            )
    }
}

object LocalSelfUserProvider {
    private val LocalSelfUser =
        compositionLocalOf<SelfUserInfo> { error("CompositionLocal LocalSelfUser not present") }

    val current: SelfUserInfo
        @Composable get() = LocalSelfUser.current

    val currentId: String
        @Composable get() = current.id

    val isJP: Boolean
        @Composable get() = current.isJP

    @Composable
    fun Provider(content: @Composable () -> Unit) {
        val selfUser =
            if (LocalInspectionMode.current) {
                SelfUserInfo.previewBoy
            } else {
                val userState = AccountManager.userStateFlow.collectAsStateWithLifecycle()
                keepLastNonNullState(userState.value)
            } ?: return
        CompositionLocalProvider(LocalSelfUser provides selfUser, content = content)
    }
}

val String.isSelf
    @Composable get() = this == LocalSelfUserProvider.currentId

val Int.isSelf
    @Composable get() = this.toString() == LocalSelfUserProvider.currentId
