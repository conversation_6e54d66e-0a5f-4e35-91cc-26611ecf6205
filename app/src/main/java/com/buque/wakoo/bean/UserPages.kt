package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.Serializable

@Serializable
data class UserRelations(
    val user: BasicUser,
    val hasRelations: Boolean,
    val pageKey: Int,
    // 非接口返回数据，是否正在改变关系
    var relationRequesting: Boolean = false,
) : User by user {
    companion object {
        fun fromResponse(
            response: UserResponse,
            hasRelations: Boolean,
            pageKey: Int = response.relationId,
        ): UserRelations =
            UserRelations(
                user = BasicUser.fromResponse(response),
                hasRelations = hasRelations,
                pageKey = pageKey,
            )
    }
}

@Serializable
data class UserPages(
    val user: BasicUser,
    val pageKey: Int,
) : User by user {
    companion object {
        fun fromResponse(
            response: UserResponse,
            pageKey: Int = response.relationId,
        ): UserPages =
            UserPages(
                user = BasicUser.fromResponse(response),
                pageKey = pageKey,
            )
    }
}
