package com.buque.wakoo.bean.user

import com.buque.wakoo.bean.BuddyZone
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

interface IWithRelationInfo {
    val isBlacked: Boolean // false
    val isFollowed: Boolean // false
    val isCp: Boolean
    val isFriend: Boolean
    val needFriendRelation: Boolean
}

/**
 * 自己与他人的关系
 */
@Serializable
data class WithRelationInfo(
    override val isBlacked: Boolean = false, // false
    override val isFollowed: Boolean = false, // false
    override val isFriend: Boolean = false,
    override val isCp: Boolean = false,
    override val needFriendRelation: Boolean = false,
) : IWithRelationInfo {
    companion object {
        val empty = WithRelationInfo()

        fun fromResponse(response: UserResponse): WithRelationInfo =
            WithRelationInfo(
                isBlacked = response.iHaveBlacked,
                isFollowed = response.iHaveFollowed,
                isCp = response.isCp,
                isFriend = response.isFriend,
                needFriendRelation = response.needFriendRelation,
            )
    }

    val isEmpty
        get() = this === empty
}

@Serializable
data class CpRelationInfo(
    val canBeCp: Boolean = false,
    val confRelationEnable: Boolean = false,
    val cpCardBackground: String = "",
    val cpCardEffect: CpCardEffect = CpCardEffect(),
    val cpExtraInfo: CpExtraInfo = CpExtraInfo(),
    val cpPublicBillboardUrl: String = "",
    val defaultCpRuleUrl: String = "",
    val hasRelationship: Boolean = false,
    val hideCpPublicBillboard: Boolean = false,
    val isCp: Boolean = false,
    val isFriend: Boolean = false,
    val needFriendRelation: Boolean = false,
    val publicCp: BasicUser? = null,
    val cp: BasicUser? = null,
    val relationshipAvailable: Boolean = false,
    val relationshipAvailableType: String = "",
    val cpZone: BuddyZone? = null,
    val userHasCp: Boolean = false,
) {
    val publicCpMedalSmallUrl: String = cpExtraInfo.levelInfo.wakooSmallImgUrl

    val publicCpMedalNormalUrl: String = cpExtraInfo.levelInfo.wakooNormalImgUrl

    companion object {
        fun fromResponse(response: UserResponse): CpRelationInfo =
            CpRelationInfo(
                canBeCp = response.canBeCp,
                confRelationEnable = response.confRelationEnable,
                cpCardBackground = response.cpCardBackground,
                cpCardEffect = response.cpCardEffect,
                cpExtraInfo = response.cpExtraInfo,
                cpPublicBillboardUrl = response.cpPublicBillboardUrl,
                defaultCpRuleUrl = response.defaultCpRuleUrl,
                hasRelationship = response.hasRelationship,
                hideCpPublicBillboard = response.hideCpPublicBillboard,
                isCp = response.isCp,
                cp = response.cp,
                isFriend = response.isFriend,
                needFriendRelation = response.needFriendRelation,
                publicCp = response.publicCp,
                relationshipAvailable = response.relationshipAvailable,
                relationshipAvailableType = response.relationshipAvailableType,
                userHasCp = response.userHasCp,
                cpZone = response.cpZone,
            )
    }

    @Serializable
    data class CpCardEffect(
        @SerialName("file_url")
        val fileUrl: String = "",
        @SerialName("link")
        val link: String = "",
    )

    @Serializable
    data class CpExtraInfo(
        @SerialName("cp_value")
        val cpValue: Int = 0,
        @SerialName("level_info")
        val levelInfo: LevelInfo = LevelInfo(),
        @SerialName("togather_days")
        val togatherDays: String = "",
    ) {
        @Serializable
        data class LevelInfo(
            @SerialName("footer_img_url")
            val footerImgUrl: String = "",
            @SerialName("has_next_level")
            val hasNextLevel: Boolean = false,
            @SerialName("header_img_url")
            val headerImgUrl: String = "",
            @SerialName("level")
            val level: Int = 0,
            @SerialName("title")
            val title: String = "",
            @SerialName("wakoo_footer_img_url")
            val wakooFooterImgUrl: String = "",
            @SerialName("wakoo_header_img_url")
            val wakooHeaderImgUrl: String = "",
            @SerialName("wakoo_normal_img_url")
            val wakooNormalImgUrl: String = "",
            @SerialName("wakoo_small_img_url")
            val wakooSmallImgUrl: String = "",
        )
    }
}
