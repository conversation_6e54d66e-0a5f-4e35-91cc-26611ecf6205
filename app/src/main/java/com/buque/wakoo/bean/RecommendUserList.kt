package com.buque.wakoo.bean

import androidx.annotation.Keep
import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RecommendUserList(
    @SerialName("key_area") val keyArea: Int = 1,
    @SerialName("has_next_page") val hasNext: Boolean = false,
    @SerialName("online_user_cnt") val onlineCount: Int = 0,
    @SerialName("items") val userList: List<AreaRecommendUser> = emptyList(),
    @SerialName("is_newbie_list") val isNewbieList: Boolean = false,
)

/**
 * 首页优秀创作者bean类
 */
@Keep
@Serializable
data class AreaRecommendUser(
    @SerialName("age")
    val age: Int = 0,
//    @SerialName("attractive_tags")
//    val attractiveTags: List<Any?> = listOf(),
    @SerialName("audioroom")
    val audioroom: MyLiveRoomInfo? = null,
    @SerialName("avatar_url")
    val avatarUrl: String = "",
    @SerialName("city_name")
    val cityName: String = "",
    @SerialName("follower_cnt")
    val followerCnt: Int = 0,
    @SerialName("gender")
    val gender: Int = 0,
    @SerialName("height")
    val height: Int = 0,
    @SerialName("is_followed")
    val isFollowed: Boolean = false,
    @SerialName("is_member")
    val isMember: Boolean = false,
    @SerialName("level")
    val level: Int = 0,
    @SerialName("nickname")
    val nickname: String = "",
    @SerialName("online_status")
    val onlineStatus: Int = 0,
    @SerialName("online_time")
    val onlineTime: String = "",
    @SerialName("region")
    val region: String = "",
    @SerialName("region_label")
    val regionLabel: String = "",
    @SerialName("region_reason")
    val regionReason: String = "",
    @SerialName("region_reason_label")
    val regionReasonLabel: String = "",
    @SerialName("short_intro")
    val shortIntro: String = "",
    @SerialName("userid")
    val userid: Int = 0,
    @SerialName("constellation_label")
    val constellation: String = "",
    @SerialName("album")
    val albums: List<MediaInfo> = listOf(),
    val relationRequesting: Boolean = false,
) {
    val user: BasicUser by lazy {
        BasicUser(
            id = userid.toString(),
            publishId = "",
            name = nickname,
            avatar = avatarUrl,
            gender = gender,
            age = age,
            birthday = "",
            isVip = isMember,
            height = height,
            intro = shortIntro,
            type = 0,
        )
    }
}
