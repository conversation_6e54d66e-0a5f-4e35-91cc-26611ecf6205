package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.bean.user.User
import kotlinx.serialization.Serializable

@Serializable
data class AccountInfo(
    val tokenInfo: TokenInfo,
    val userInfo: SelfUserInfo,
) : Token by tokenInfo,
    User by userInfo.user {
    companion object {
        val preview
            get() =
                AccountInfo(
                    tokenInfo =
                        TokenInfo(
                            apiAuthToken = "123",
                            apiRefreshToken = "456",
                            imToken = "DDSD",
                        ),
                    userInfo = SelfUserInfo.previewBoy,
                )
    }
}
