package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CoupleInfo(
    @SerialName("is_partner")
    val isCp: Boolean = false, // false
    @SerialName("private_room_id")
    val privateRoomId: Int? = null, // null
    @SerialName("self_has_partner")
    val selfHasCp: Boolean = false, // false
    @SerialName("self_user_info")
    val selfUserInfo: BasicUser, // 456
    @SerialName("show_dress")
    val showDress: Boolean = false, // true
    @SerialName("target_user_has_partner")
    val targetHasCp: Boolean = false, // false
    @SerialName("target_user_info")
    val targetUserInfo: BasicUser, // 133
    @SerialName("show_pendant")
    val showPendant: <PERSON><PERSON><PERSON>, // 133
) {
    val visible: Boolean
        get() = showPendant
}
