package com.buque.wakoo.bean.boost


import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PointsRecordItem(
    @SerialName("change_reason")
    val reason: String = "",
    @SerialName("change_amount")
    val amount: Int = 0,
    @SerialName("id")
    val id: Int = 0,
    @SerialName("create_timestamp")
    val timestamp: Long = 0,
    @SerialName("change_type")
    val type: Int = 0,
    @SerialName("remain_balance")
    val remainBalance: Int = 0,
) {
    val formatTimeStr by lazy {
        DateTimeUtils.formatDate(timestamp, "MM-dd HH:mm")
    }
    val changeValue: String by lazy {
        amount.toString()
    }
}