package com.buque.wakoo.bean.hb


import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * ```
 * {
 *      "id": 253,
 *      "coin_value": 50,
 *      "number": 2,
 *      "remained_number": 2,
 *      "delay_time": 180,
 *      "open_timestamp": 1754280064,
 *      "grab_type": 1,
 *      "status": 1,
 *      "now_timestamp": 1754296368
 *  }
 * ```
 */
@Serializable
data class HongBaoInfo(
    val sender: BasicUser? = null,
    @SerialName("coin_type")
    val coinType: Int = 0,
    @SerialName("coin_value")
    val coinValue: Int = 0,
    @SerialName("delay_time")
    val delayTime: Int = 0,
    @SerialName("fee")
    val fee: Int = 0,
    @SerialName("grab_type")
    val grabType: Int = 0,
    @SerialName("greets")
    val greets: String = "",
    @SerialName("id")
    val id: String = "",
    @SerialName("now_timestamp")
    val nowTimestamp: Int = 0,
    @SerialName("number")
    val number: Int = 0,
    @SerialName("open_timestamp")
    val openTimestamp: Int = 0,
    @SerialName("remained_number")
    val remainedNumber: Int = 0,
    @SerialName("status")
    val status: Int = 0,
    @SerialName("has_grab")
    val hasGrab: Boolean = false,
    @SerialName("grab_coin")
    val grabCoin: Int = 0
) {
    val openMillSecond = openTimestamp * 1000L

    val needCountDown: Boolean
        get() = delayTime > 0
}