package com.buque.wakoo.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonNames

@Serializable
data class ConversationConfig(
    @SerialName("title")
    @JsonNames("title", "heading")
    val title: List<RichItem> = listOf(),
    @SerialName("sub_title")
    @JsonNames("sub_title", "sub_heading")
    val subTitle: List<RichItem> = emptyList(),
    @SerialName("show_read_mark")
    @JsonNames("show_read_mark", "read_icon")
    val showReadMark: Boolean = false,
    @SerialName("give_assets_entries")
    @JsonNames("give_assets_entries", "assets_list")
    val giveAssetsEntries: List<Int> = emptyList(),
    @SerialName("show_cheer_btn")
    @JsonNames("show_cheer_btn", "gift_btn")
    val showGiftButton: Boolean = false,
    @SerialName("private_room_id")
    @JsonNames("private_room_id", "private_space_id")
    val privateRoomID: String = "",
    @SerialName("private_space")
    val privateSpace: PrivateSpace? = null,
)

@Serializable
data class PrivateSpace(
    @SerialName("id")
    val id: String,
    @SerialName("pendant_t")
    val type: Int,
)
