package com.buque.wakoo.bean.boost


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ExcItem(
    @SerialName("desc")
    val desc: String = "",
    @SerialName("exchange_condition")
    val exchangeCondition: Int = 0,
    @SerialName("icon")
    val icon: String = "",
    @SerialName("id")
    val id: Int = 0,
    @SerialName("item_type")
    val itemType: Int = 0,
    @SerialName("name")
    val name: String = "",
    @SerialName("number")
    val number: Int = 0,
    @SerialName("price")
    val price: Int = 0,
    @SerialName("price_type")
    val priceType: Int = 0
) {
    val amount: String = number.toString()
}