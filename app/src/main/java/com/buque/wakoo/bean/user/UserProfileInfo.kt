package com.buque.wakoo.bean.user

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.chatgroup.UserChatGroup
import com.buque.wakoo.manager.localized

data class UserProfileInfo(
    val user: User,
    val showCpZone: Boolean = false,
    val decorations: UserDecorations = UserDecorations.empty, // 装扮
    val socialInfo: SocialInfo = SocialInfo.empty,
    val withRelationInfo: WithRelationInfo = WithRelationInfo.empty,
    val cpRelationInfo: CpRelationInfo? = null,
    val group: UserChatGroup? = null,
) : User by user,
    IUserDecorations by decorations,
    ISocialInfo by socialInfo,
    IWithRelationInfo by withRelationInfo {
    fun getNativeProfileList(): List<Pair<String, String>> =
        listOf(
            "基本情报".localized to nativeProfile.cityCode.name,
            "出生地".localized to nativeProfile.birthCityCode.name,
            "学历".localized to nativeProfile.educationalHistory.name,
            "职业".localized to nativeProfile.job.name,
            "身高".localized to user.formatHeight,
            "体型".localized to nativeProfile.bodyType.name,
            //            "婚姻史" to maritalHistory.name,
            //            "吸烟史" to tobacco.name,
            //            "结婚意愿" to marriageIntention.name,
            //            "期望" to datingHope.name,
        )

    companion object {
        fun fromResponse(tempUserInfo: TempUserInfo): UserProfileInfo =
            if (tempUserInfo.response != null) {
                UserProfileInfo(
                    user = BasicUser.fromResponse(tempUserInfo.response),
                    showCpZone = if (tempUserInfo.response.id == SelfUser?.id) tempUserInfo.response.showCpZone else false,
                    decorations = UserDecorations.fromResponse(tempUserInfo.response),
                    socialInfo = SocialInfo.fromResponse(tempUserInfo.response),
                    withRelationInfo = WithRelationInfo.fromResponse(tempUserInfo.response),
                    cpRelationInfo = CpRelationInfo.fromResponse(tempUserInfo.response),
                    group = tempUserInfo.response.group,
                )
            } else {
                UserProfileInfo(user = tempUserInfo.user)
            }

        fun fromSelfUserInfo(info: SelfUserInfo): UserProfileInfo =
            UserProfileInfo(
                user = info.user,
                showCpZone = info.showCpZone,
                decorations = info.decorations,
                socialInfo = info.socialInfo,
                cpRelationInfo = info.cpRelationInfo,
                group = info.otherInfo.myGroup,
            )
    }
}
