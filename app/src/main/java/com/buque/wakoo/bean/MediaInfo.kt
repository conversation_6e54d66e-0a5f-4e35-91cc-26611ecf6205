package com.buque.wakoo.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import retrofit2.http.Query

@Serializable
data class MediaInfo(
    @SerialName("url") @Query("url")
    val mediaUrl: String? = "",
    @Query("width") val width: Int = 0,
    @Query("height") val height: Int = 0,
    @Query("size") val size: Long? = 0L,
    @SerialName("is_video")
    val isVideo: Boolean = false,
) {
    val aspectRatio: Float
        get() {
            if (width != 0 && height != 0) {
                return Math.clamp(width / height.toFloat(), 0.3f, 1.2f)
            } else {
                return 1f
            }
        }

    @Transient
    val url = mediaUrl.orEmpty()

    var parentId: String = ""
}

data class ImageInfo(
    val width: Int,
    val height: Int,
    val sizeInBytes: Long,
)
