package com.buque.wakoo.bean

import androidx.compose.runtime.Stable
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonNames

typealias ProfileEnum = List<EnumEntity>

@Stable
@Serializable
data class ProfileEnums(
    @SerialName("body_type")
    @JsonNames("body_type", "shape")
    val bodyType: ProfileEnum = emptyList(),
    @SerialName("dating_hope")
    val datingHope: ProfileEnum = emptyList(),
    @SerialName("educational_history")
    @JsonNames("educational_history", "study_years")
    val educationalHistory: ProfileEnum = emptyList(),
    @SerialName("job")
    @JsonNames("job", "career")
    val job: ProfileEnum = emptyList(),
    @SerialName("marital_history")
    val maritalHistory: ProfileEnum = emptyList(),
    @SerialName("marriage_intention")
    val marriageIntention: ProfileEnum = emptyList(),
    @SerialName("tobacco")
    val tobacco: ProfileEnum = emptyList(),
)

@Serializable
data class EnumEntity(
    val code: String = "",
    val name: String = "",
) {
    companion object {
        val EMPTY = EnumEntity()
    }
}

val EnumEntity.isEdit: Boolean
    get() = code != "0"

@Serializable
data class NativeProfile(
    @SerialName("city_code")
    val cityCode: EnumEntity = EnumEntity.EMPTY,
    @SerialName("birth_city_code")
    val birthCityCode: EnumEntity = EnumEntity.EMPTY,
    @SerialName("body_type")
    val bodyType: EnumEntity = EnumEntity.EMPTY,
    @SerialName("dating_hope")
    val datingHope: EnumEntity = EnumEntity.EMPTY,
    @SerialName("educational_history")
    val educationalHistory: EnumEntity = EnumEntity.EMPTY,
    @SerialName("job")
    val job: EnumEntity = EnumEntity.EMPTY,
    @SerialName("marital_history")
    val maritalHistory: EnumEntity = EnumEntity.EMPTY,
    @SerialName("marriage_intention")
    val marriageIntention: EnumEntity = EnumEntity.EMPTY,
    @SerialName("tobacco")
    val tobacco: EnumEntity = EnumEntity.EMPTY,
) {
    companion object {
        val EMPTY = NativeProfile()
    }
}
