package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.DateTimeUtils.DateTimeOutputFormat
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class NotificationListResponse(
    @SerialName("have_next_page")
    val hasNext: Boolean = false, // false
    @SerialName("notifications")
    val notifications: List<NotificationInfo> = listOf(),
)

@Serializable
data class NotificationInfo(
    @SerialName("content")
    val content: String = "", // 点赞了你的声音[xxxxx]
    @SerialName("created_at")
    val createdAt: Int = 0, // 1111
    @SerialName("id")
    val id: Int = 0, // 1
    @SerialName("operator")
    val user: BasicUser,
) {
    val formatCreateTime = DateTimeUtils.secondsToFormattedString(createdAt, DateTimeOutputFormat.DATE_TIME)
}
