package com.buque.wakoo.bean

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class MomentItem(
    @SerialName("create_time")
    val createTime: Long = 0,
    @SerialName("i_have_liked")
    val iHaveLiked: Boolean = false,
    @SerialName("id")
    val id: Int = 0,
    @SerialName("images")
    val images: List<MediaInfo> = listOf(),
    @SerialName("like_cnt")
    val likeCnt: Int = 0,
    @SerialName("location_label")
    val locationLabel: String = "",
    @SerialName("text")
    val text: String = "",
    // 暂时不用,因为还没做到这个功能
//    @SerialName("topics")
//    val topics: List<Any?> = listOf(),
    @SerialName("type")
    val type: String = "",
    @SerialName("video")
    val video: MediaInfo? = null,
    @SerialName("video_audite_status")
    val videoAuditeStatus: Int = 0,
    val isRequesting: Boolean = false,
) {
    @Transient
    var lastLikeStatus: Boolean = iHaveLiked
}

@Serializable
data class MomentListResponse(
    @SerialName("trends")
    val list: List<MomentItem>,
)
