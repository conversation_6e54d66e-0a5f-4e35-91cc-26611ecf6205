package com.buque.wakoo.bean


import androidx.annotation.Keep
import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/*
{
    "cmd": "member_privileged_gift_remind_popup",
    "data": {
    "target_user": {
    "userid": 1391,
    "public_id": "101843",
    "nickname": "诗书塞外",
    "avatar_url": "https://s.test.ucoofun.com/aacehV?x-oss-process=image/format,webp",
    "gender": 1,
    "age": 25,
    "height": 0,
    "avatar_frame": "https://s.ucoofun.com/aabLdz",
    "medal": null,
    "medal_list": [],
    "level": 41,
    "country_flag": "https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FTW.png",
    "exp_level_info": {
    "charm_level": 1,
    "wealth_level": 1
},
    "have_certified": false
},
    "desc": "你还有背包礼物道具未使用\n送个礼物给她吧，她会很开心哦",
    "gift": {
    "id": 2,
    "name": "情人礼盒",
    "icon": "https://media.ucoofun.com/opsite/gift/icon/%E7%88%B1%E5%BF%83%E7%A4%BC%E7%9B%92.png"
},
    "btn_txt": "免费赠送"
}
}
*/
@Keep
@Serializable
data class PrivilegedGiftRemindBean(
    @SerialName("target_user")
    val target_user: BasicUser,
    @SerialName("btn_txt")
    val btnTxt: String = "",
    @SerialName("desc")
    val desc: String = "",
    @SerialName("gift")
    val gift: Gift = Gift()
) {
    @Keep
    @Serializable
    data class Gift(
        @SerialName("icon")
        val icon: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("name")
        val name: String = ""
    )
}