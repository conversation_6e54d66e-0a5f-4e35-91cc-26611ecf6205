package com.buque.wakoo.bean

import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 这里吧diamond和coin解析的地方对调了一下
 * 因为后台说钻石其实是UCOO的积分 blablabla
 *
 * 为了保证后续的逻辑通畅,直接在这个地方就把字段进行对调,免得看走了眼
 *
 * 第一个进行对调的地方
 */
@Serializable
data class DressUpListResponse(
    @SerialName("decoration_list")
    val list: List<DressUpPropItem> = listOf(),
    @SerialName("my_diamond")
    val banlance: Int,
    @SerialName("my_balance")
    val diamond: Int,
)

/**
 * 第二个进行对调的地方
 */
@Serializable
data class DressUpTabsResponse(
    @SerialName("tabs")
    val list: List<DressupTab> = listOf(),
    @SerialName("my_diamond")
    val banlance: Int,
    @SerialName("my_balance")
    val diamond: Int,
)

@Serializable
data class DressUpMineTabResponse(
    @SerialName("tab_list")
    val list: List<DressUpMineTab> = listOf(),
)

@Serializable
data class DressUpMinePropResponse(
    @SerialName("props")
    val list: List<DressUpMineProp> = listOf(),
)

//region 装扮商城

/**
 * 装扮商城简单示例的tab
 */
@Serializable
data class DressupTab(
    @SerialName("tab_name")
    val tabName: String = "",
    @SerialName("prop_type")
    val propType: Int,
    @SerialName("prop_background")
    val propBackground: String = "",
    @SerialName("list")
    val list: List<DressUpPropItem> = listOf(),
) {
    fun toUIItem() = DressUpCategory(tabName, propType, propBackground)
}

/**
 * 装扮商城分类 title 实体类
 */
@Serializable
data class DressUpCategory(
    val tabName: String,
    val propType: Int,
    val propBackground: String,
) : DressUpUIItem {
    override val span: Int = 3
    override val type: Int = 1
}

/**
 * 装扮商城顶部 分类集合 实体类
 */
@Serializable
data class DressUpTabsItem(
    val tabs: List<DressUpCategory>,
) : DressUpUIItem {
    override val span: Int = 3
    override val type: Int = 0
}
//endregion

/**
 * 装扮单个道具item
 */
@Serializable
data class DressUpPropItem(
    @SerialName("id")
    val id: Int,
    @SerialName("native_region")
    val nativeRegion: Int,
    @SerialName("preview_url")
    val previewUrl: String? = null,
    @SerialName("price_conf")
    val priceConf: List<PriceConf> = listOf(),
    @SerialName("prop_info")
    val propInfo: PropInfo,
) : DressUpUIItem {
    /**
     * 这里吧diamond和coin解析的地方对调了一下
     * 因为后台说钻石其实是UCOO的积分 blablabla
     *
     * 为了保证后续的逻辑通畅,直接在这个地方就把字段进行对调,免得看走了眼
     *
     * 第三个对调的地方
     */
    @Serializable
    data class PriceConf(
        @SerialName("diamond")
        val coin: Int,
        @SerialName("day")
        val day: Int,
        @SerialName("coin")
        val diamond: Int,
    )

    @Serializable
    data class PropInfo(
        @SerialName("gain_type")
        val gainType: Int,
        @SerialName("icon")
        val icon: String,
        @SerialName("icon_height")
        val iconHeight: Int?,
        @SerialName("icon_width")
        val iconWidth: Int?,
        @SerialName("id")
        val id: Int,
        @SerialName("name")
        val name: String,
        @SerialName("prop_desc")
        val propDesc: String?,
        @SerialName("prop_type")
        val propType: Int,
        @SerialName("effect_file")
        val effectFile: String? = null,
    )

    override val span: Int = 1

    override val type: Int = 2
}

/**
 * 用来给商城做多布局展示
 */
interface DressUpUIItem {
    val span: Int

    val type: Int
}

//region 我的装扮

@Serializable
data class DressUpMineTab(
    @SerialName("t")
    val type: Int,
    @SerialName("name")
    val name: String,
    @SerialName("order")
    val order: Int,
)

@Serializable
data class DressUpMineProp(
    val id: Int,
    val prop: Prop,
    @SerialName("in_use")
    val isUsing: Boolean,
    @SerialName("expire_timestamp")
    private val expireStamp: Long? = null,
    @SerialName("expire_timestamp_v2")
    private val expireStampV2: Long? = null,
    // 非接口返回数据，是否正在改变关系
    var useInterfaceRequesting: Boolean = false,
) {
    val expireTimestamp
        get() = expireStampV2

    val expireTimeStr =
        if (expireTimestamp != null) "${DateTimeUtils.formatDate(expireTimestamp!!, "yyyy-MM-dd")}到期" else "永久期限"

    @Serializable
    data class Prop(
        @SerialName("id")
        val id: Int,
        @SerialName("name")
        val name: String,
        @SerialName("icon")
        val icon: String,
        @SerialName("t")
        val t: Int,
        @SerialName("days")
        val days: Int,
        @SerialName("price")
        val price: Int,
        @SerialName("order")
        val order: Int,
        @SerialName("gain_type")
        val gainType: Int,
        @SerialName("prop_desc")
        val propDesc: String? = null,
    )
}

//endregion
