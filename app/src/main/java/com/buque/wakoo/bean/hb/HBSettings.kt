package com.buque.wakoo.bean.hb


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class HBSettings(
    @SerialName("delay_types")
    val delayTypes: List<DelayType> = listOf(),
    @SerialName("grab_types")
    val grabTypes: List<GrabType> = listOf(),
    @SerialName("has_relationship")
    val hasRelationship: Boolean = false,
    @SerialName("has_tribe")
    val hasTribe: Boolean = false,
    @SerialName("max_number")
    val maxNumber: Int = 0,
    @SerialName("min_coin")
    val minCoin: Int = 0,
    @SerialName("min_number")
    val minNumber: Int = 0,
    @SerialName("notify_tooltips")
    val notifyTooltips: String = "",
    @SerialName("tips")
    val tips: List<String> = listOf()
) {
    @Serializable
    data class DelayType(
        @SerialName("name")
        val name: String = "",
        @SerialName("value")
        val value: Int = 0
    )

    @Serializable
    data class GrabType(
        @SerialName("name")
        val name: String = "",
        @SerialName("value")
        val value: Int = 0
    ){
        companion object{
            const val ALL = 1

            //仅群组成员
            const val ONLY_TRIBE_MEMBER = 2

            //仅亲友团
            const val ONLY_FAMILY_AND_FRIEND = 3
        }
    }
}