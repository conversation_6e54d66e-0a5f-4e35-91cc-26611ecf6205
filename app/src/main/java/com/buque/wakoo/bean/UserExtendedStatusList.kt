package com.buque.wakoo.bean

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class UserExtendedStatusList(
    @SerialName("users")
    val users: List<UserExtendedStatus> = listOf(),
)

@Keep
@Serializable
data class UserExtendedStatus(
    @SerialName("city_name")
    val cityName: String = "",
    @SerialName("in_same_city")
    val isSameCity: Boolean = false,
    @SerialName("intimate_score")
    val intimateScore: Int = 0,
    @SerialName("is_coin_trade")
    val isCoinTrade: Boolean = false,
    @SerialName("is_member")
    val isVip: Boolean = false,
    @SerialName("level")
    val level: Int = 0,
    @SerialName("online_status")
    val status: Int = 0,
    @SerialName("room_id")
    val roomId: Int = 0,
    @SerialName("userid")
    val id: Int = 0,
)
