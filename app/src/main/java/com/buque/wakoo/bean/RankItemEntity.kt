package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.navigation.RankTimely
import com.buque.wakoo.navigation.RoomRankTab
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

data class RankParams(
    val category: RoomRankTab,
    val rankTimely: RankTimely,
)

@Serializable
data class RankListContainer(
    @SerialName("heart_day_billboard_list")
    val dailyCharmRankList: List<HeartRankItem>,
    @SerialName("rich_day_billboard_list")
    val dailyRichRankList: List<RichRankItem>,
    @SerialName("heart_week_billboard_list")
    val weeklyCharmRankList: List<HeartRankItem>,
    @SerialName("rich_week_billboard_list")
    val weeklyRichRankList: List<RichRankItem>,
) {
    fun getList(
        tab: RoomRankTab,
        timely: RankTimely,
    ): List<RankItem> =
        when (timely) {
            RankTimely.Daily -> if (tab == RoomRankTab.Contribution) dailyRichRankList else dailyCharmRankList
            RankTimely.Weekly -> if (tab == RoomRankTab.Contribution) weeklyRichRankList else weeklyCharmRankList
        }
}

interface RankItem {
    val userInfo: BasicUser
    val value: Int
    val displayValue: String
}

val RankItem.avatar: String
    get() = userInfo.avatar

val RankItem.nickName: String
    get() = userInfo.name

@Serializable
data class RichRankItem(
    @SerialName("user_info") override val userInfo: BasicUser,
    @SerialName("rich_value") override val value: Int,
) : RankItem {
    override val displayValue: String = value.toString()
}

@Serializable
data class HeartRankItem(
    @SerialName("user_info") override val userInfo: BasicUser,
    @SerialName("heart_value") override val value: Int,
) : RankItem {
    override val displayValue: String = value.toString()
}
