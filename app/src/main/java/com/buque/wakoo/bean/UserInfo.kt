// @file:JvmName("UserPagesKt")
//
// package com.buque.wakoo.bean
//
// import androidx.compose.runtime.Composable
// import androidx.compose.runtime.CompositionLocalProvider
// import androidx.compose.runtime.compositionLocalOf
// import androidx.compose.ui.platform.LocalInspectionMode
// import androidx.lifecycle.compose.collectAsStateWithLifecycle
// import androidx.room.ColumnInfo
// import androidx.room.Entity
// import androidx.room.PrimaryKey
// import com.buque.wakoo.app.SelfUser
// import com.buque.wakoo.bean.chatgroup.UserChatGroup
// import com.buque.wakoo.ext.keepLastNonNullState
// import com.buque.wakoo.manager.AccountManager
// import com.buque.wakoo.network.api.bean.SoundBrand
// import com.buque.wakoo.network.api.bean.UserResponse
// import kotlinx.serialization.SerialName
// import kotlinx.serialization.Serializable
//
// @Serializable
// sealed interface User {
//    val id: String
//    val publishId: String
//    val name: String
//    val age: Int
//    val gender: Int
//    val avatar: String
//    val birthday: String
//    val isVip: Boolean
//    val height: Int // 身高
//    val intro: String // 个人简介
//    val type: Int // 0 非主播、1 普通主播、2 台湾素人主播
//
//    val isSelf
//        @Composable get() = id == LocalSelfUserProvider.current.id
//
//    val sIsSelf
//        get() = id == SelfUser?.id
//
//    val isBoy
//        get() = gender == 1
//
//    val isGirl
//        get() = gender == 2
//
//    val genderIsSet
//        get() = isBoy || isGirl
//
//    val ageIsSet
//        get() = age > 0
//
//    val displayGender: String
//        @Composable get() =
//            if (genderIsSet) {
//                if (isBoy) {
//                    "男"
//                } else {
//                    "女"
//                }
//            } else {
//                "未选择"
//            }
// }
//
// fun User.toBasic(): User =
//    when (this) {
//        is User -> this
//        is UserOwner -> this.basic
//    }
//
// sealed interface UserOwner {
//    val basic: User
// }
//
// @Entity(tableName = "users")
// @Serializable
// data class User(
//    @PrimaryKey
//    @ColumnInfo(name = "userid")
//    @SerialName("userid")
//    override val id: String,
//    @ColumnInfo(name = "public_id")
//    @SerialName("public_id")
//    override val publishId: String = "0",
//    @ColumnInfo(name = "nickname")
//    @SerialName("nickname")
//    override val name: String = "",
//    @ColumnInfo(name = "avatar_url")
//    @SerialName("avatar_url")
//    override val avatar: String = "",
//    @ColumnInfo(name = "gender")
//    @SerialName("gender")
//    override val gender: Int = 0,
//    @ColumnInfo(name = "age")
//    @SerialName("age")
//    override val age: Int = -1,
//    @ColumnInfo(name = "birthday")
//    @SerialName("birthday")
//    override val birthday: String = "",
//    @ColumnInfo(name = "is_member")
//    @SerialName("is_member")
//    override val isVip: Boolean = false,
//    @ColumnInfo(name = "height")
//    @SerialName("height")
//    override val height: Int = -1,
//    @ColumnInfo(name = "short_intro")
//    @SerialName("short_intro")
//    override val intro: String = "",
//    @ColumnInfo(name = "type")
//    @SerialName("type")
//    override val type: Int = 0, // 0 非主播、1 普通主播、2 台湾素人主播
// ) : User {
//    companion object {
//        val sampleBoy = User("1", "10001", "小帅", "https://s.test.wakooclub.com/aaceIm", 1, 22, isVip = true)
//        val sampleGirl =
//            User(
//                "2",
//                "10002",
//                "幼儿园班花",
//                "https://s.test.wakooclub.com/aaceHw?x-oss-process=image/format,webp",
//                2,
//                20,
//                isVip = true,
//            )
//
//        fun fromUid(uid: String) = User(uid)
//
//        fun fromResponse(response: UserResponse): User =
//            User(
//                id = response.id,
//                publishId = response.publicId,
//                name = response.nickname,
//                gender = response.gender,
//                avatar = response.avatarUrl,
//                age = response.age,
//                birthday = response.birthday,
//                isVip = response.isMember,
//                height = response.height,
//                intro = response.shortIntro,
//                type = if (response.isHighQuality) 1 else 0,
//            )
//    }
// }
//
// @Serializable
// data class UserExtraInfo(
//    val avatarFrame: String? = null,
//    val medalList: List<Medal>? = null,
//    val isBlacked: Boolean = false, // false
//    val isFollowed: Boolean = false, // false
//    val isMember: Boolean = false,
//    val expireAt: Int = 0,
//    val balance: Int = 0,
//    val group: UserChatGroup? = null,
//    val cash: String = "0", // 现金
//    val pt: String = "0", // 积分
//    val isHQU: Boolean = false,
//    val nativeRegion: Int = 0,
//    val onlineStatus: Int = 0,
//    val canCreateAudioroom: Boolean = false, // 是否能创建语音房
// ) {
//    companion object {
//        val empty = UserExtraInfo()
//    }
//
//    val isCN: Boolean
//        get() = nativeRegion == 0
//
//    val isJP: Boolean
//        get() = nativeRegion == 1
// }
//
// @Serializable
// data class UserDetailProfile(
//    val moments: List<MediaInfo> = listOf(),
//    val momentTotalCount: Int = 0,
//    val soundLikeCount: Int = 0, // 点赞数
//    val soundCount: Int = 0, // 发布作品数
//    val soundBrand: SoundBrand = SoundBrand.EMPTY, // 名片语言
//    val regionLabel: String = "", // 中国大陆
//    val regionReason: Int = 0, // 1 不知道干嘛的
//    val regionReasonLabel: String = "", // 现居地
//    val height: Int = 0,
//    val albums: List<MediaInfo> = listOf(),
//    val shortIntro: String = "", // 简介
//    val nativeProfile: NativeProfile = NativeProfile.EMPTY,
// ) {
//    companion object {
//        val empty = UserDetailProfile()
//    }
//
//    fun getNativeProfileList(): List<Pair<String, String>> =
//        listOf(
//            "基本情报" to nativeProfile.cityCode.name,
//            "出生地" to nativeProfile.birthCityCode.name,
//            "学历" to nativeProfile.educationalHistory.name,
//            "职业" to nativeProfile.job.name,
//            "身高" to getHeightStr(),
//            "体型" to nativeProfile.bodyType.name,
// //            "婚姻史" to maritalHistory.name,
// //            "吸烟史" to tobacco.name,
// //            "结婚意愿" to marriageIntention.name,
// //            "期望" to datingHope.name,
//        )
//
//    fun getHeightStr() = if (height != 0) "${height}cm" else ""
// }
//
// @Serializable
// data class LiveRoomInUserExtraInfo(
//    val myLiveRoom: MyLiveRoomInfo? = null,
// )
//
// @Serializable
// data class Medal(
//    val icon: String = "",
//    val width: Int = 0,
//    val height: Int = 0,
// ) {
//    companion object {
//        val EMPTY = Medal()
//    }
// }
//
// @Serializable
// data class UserInfo(
//    @SerialName("basic") override val basic: User,
//    @SerialName("extra") val extra: UserExtraInfo = UserExtraInfo.empty,
//    @SerialName("liveroom") val roomExtra: LiveRoomInUserExtraInfo? = null,
//    val profile: UserDetailProfile = UserDetailProfile.empty,
//    val relationInfo: RelationInfo = RelationInfo.empty,
// ) : UserOwner,
//    User by basic {
//    companion object {
//        val previewBoy
//            get() =
//                UserInfo(
//                    basic =
//                        User(
//                            id = "001",
//                            publishId = "123",
//                            name = "涂鸦冒险家",
//                            age = 18,
//                            gender = 1,
//                            avatar = "https://picsum.photos/200",
//                            birthday = "2025-01-01",
//                        ),
//                )
//
//        val previewGirl
//            get() =
//                previewBoy.copy(
//                    basic =
//                        previewBoy.basic.copy(
//                            id = "002",
//                            publishId = "456",
//                            name = "涂鸦少女",
//                            age = 16,
//                            gender = 2,
//                        ),
//                )
//
//        fun fromResponse(response: UserResponse): UserInfo =
//            UserInfo(
//                basic = User.fromResponse(response),
//                extra =
//                    UserExtraInfo(
//                        avatarFrame = response.avatarFrame,
//                        medalList = response.medalList,
//                        isBlacked = response.iHaveBlacked,
//                        isFollowed = response.iHaveFollowed,
//                        balance = response.balance,
//                        expireAt = response.member.expireAt,
//                        group = response.group,
//                        pt = response.diamond,
//                        cash = response.cash,
//                        onlineStatus = response.onlineStatus,
//                        isHQU = response.isHighQuality,
//                        nativeRegion = response.nativeRegion,
//                        canCreateAudioroom = response.canCreateAudioroom,
//                    ),
//                profile =
//                    UserDetailProfile(
//                        regionLabel = response.regionLabel,
//                        regionReason = response.regionReason,
//                        regionReasonLabel = response.regionReasonLabel,
//                        height = response.height,
//                        albums = response.albums,
//                        shortIntro = response.shortIntro,
//                        moments = response.moments,
//                        momentTotalCount = response.momentTotalCount,
//                        soundCount = response.soundCount,
//                        soundLikeCount = response.soundLikeCount,
//                        soundBrand = response.soundBrand,
//                        nativeProfile = response.nativeProfile,
//                    ),
//                roomExtra =
//                    LiveRoomInUserExtraInfo(
//                        myLiveRoom = response.room,
//                    ),
//                relationInfo =
//                    RelationInfo(
//                        canBeCp = response.canBeCp,
//                        confRelationEnable = response.confRelationEnable,
//                        cpCardBackground = response.cpCardBackground,
//                        cpCardEffect = response.cpCardEffect,
//                        cpExtraInfo = response.cpExtraInfo,
//                        cpPublicBillboardUrl = response.cpPublicBillboardUrl,
//                        defaultCpRuleUrl = response.defaultCpRuleUrl,
//                        hasRelationship = response.hasRelationship,
//                        hideCpPublicBillboard = response.hideCpPublicBillboard,
//                        iHaveBlacked = response.iHaveBlacked,
//                        iHaveFollowed = response.iHaveFollowed,
//                        isCp = response.isCp,
//                        cp = response.cp,
//                        isFriend = response.isFriend,
//                        needFriendRelation = response.needFriendRelation,
//                        publicCp = response.publicCp,
//                        relationshipAvailable = response.relationshipAvailable,
//                        relationshipAvailableType = response.relationshipAvailableType,
//                        showCpZone = response.showCpZone,
//                        userHasCp = response.userHasCp,
//                        cpZone = response.cpZone,
//                    ),
//            )
//    }
//
//    fun copyUser(
//        id: String = basic.id,
//        publishId: String = basic.publishId,
//        name: String = basic.name,
//        age: Int = basic.age,
//        gender: Int = basic.gender,
//        avatar: String = basic.avatar,
//        birthday: String = basic.birthday,
//        isVip: Boolean = basic.isVip,
//        level: Int = basic.level,
//        followingCount: Int = basic.followingCount,
//        followersCount: Int = basic.followersCount,
//        friendCount: Int = basic.friendCount,
//        type: Int = basic.type,
//    ): UserInfo =
//        copy(
//            basic =
//                basic.copy(
//                    id = id,
//                    publishId = publishId,
//                    name = name,
//                    age = age,
//                    gender = gender,
//                    avatar = avatar,
//                    birthday = birthday,
//                    isVip = isVip,
//                    level = level,
//                    followingCount = followingCount,
//                    followersCount = followersCount,
//                    friendCount = friendCount,
//                    type = type,
//                ),
//        )
//
//    fun isZhubo() = basic.type != 0
//
//    val isHQU: Boolean
//        get() = extra.isHQU
//
//    val group = extra.group
//
//    val isCN: Boolean
//        get() = extra.isCN
//
//    val isJP: Boolean
//        get() = extra.isJP
// }
