package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BuddyZone(
    @SerialName("cp_rule_desc")
    val cpRuleDesc: String = "",
    @SerialName("cp_rule_url")
    val cpRuleUrl: String = "",
    @SerialName("daily_task_info")
    val dailyTaskInfo: DailyTaskInfo = DailyTaskInfo(),
    @SerialName("hot_degree")
    val hotDegree: Int = 0,
    @SerialName("hot_degree_goal")
    val hotDegreeGoal: Int = 0,
    @SerialName("hot_degree_hint")
    val hotDegreeHint: String = "",
    @SerialName("hot_degree_settle_timestamp")
    val hotDegreeSettleTimestamp: Int = 0,
    @SerialName("last_day_cnt")
    val lastDayCnt: Int = 0,
    @SerialName("user1")
    val user1: BasicUser,
    @SerialName("user2")
    val user2: BasicUser,
) {
    @Serializable
    data class DailyTaskInfo(
        @SerialName("audioroom_hot_degree")
        val audioroomHotDegree: Int = 0,
        @SerialName("audioroom_target_duration")
        val audioroomTargetDuration: Int = 0,
        @SerialName("audioroom_together_duration")
        val audioroomTogetherDuration: Int = 0,
        @SerialName("checked_hot_degree")
        val checkedHotDegree: Int = 0,
        @SerialName("cp_checked_status")
        val cpCheckedStatus: Boolean = false,
        @SerialName("cp_coin_hot_degree")
        val cpCoinHotDegree: Int = 0,
        @SerialName("cp_coin_target")
        val cpCoinTarget: Int = 0,
        @SerialName("daily_task_finished")
        val dailyTaskFinished: Boolean = false,
        @SerialName("daily_task_note")
        val dailyTaskNote: String = "",
        @SerialName("daily_task_title")
        val dailyTaskTitle: String = "",
        @SerialName("gift_coin_cnt")
        val giftCoinCnt: Int = 0,
        @SerialName("my_checked_status")
        val myCheckedStatus: Boolean = false,
        @SerialName("ongoing_activity")
        val ongoingActivity: OngoingActivity? = null,
        @SerialName("private_room_hot_degree")
        val privateRoomHotDegree: Int = 0,
        @SerialName("private_room_target_duration")
        val privateRoomTargetDuration: Int = 0,
        @SerialName("private_room_together_duration")
        val privateRoomTogetherDuration: Int = 0,
        @SerialName("show_finish_status_stamp")
        val showFinishStatusStamp: Boolean = false,
        @SerialName("task_end_timestamp")
        val taskEndTimestamp: Int = 0,
        @SerialName("task_infos")
        val taskInfos: List<TaskInfo> = listOf(),
        @SerialName("version")
        val version: Int = 0,
    ) {
        @Serializable
        data class OngoingActivity(
            @SerialName("activity_bonus_list")
            val activityBonusList: List<ActivityBonus> = listOf(),
            @SerialName("activity_day_index")
            val activityDayIndex: Int = 0,
            @SerialName("activity_desc")
            val activityDesc: String = "",
            @SerialName("activity_name")
            val activityName: String = "",
        ) {
            @Serializable
            data class ActivityBonus(
                @SerialName("icon")
                val icon: String = "",
                @SerialName("text")
                val text: String = "",
            )
        }

        @Serializable
        data class TaskInfo(
            @SerialName("task_bonus")
            val taskBonus: String = "",
            @SerialName("task_btn_label")
            val taskBtnLabel: String = "",
            @SerialName("task_id")
            val taskId: String = "",
            @SerialName("task_is_finished")
            val taskIsFinished: Boolean = false,
            @SerialName("task_name")
            val taskName: String = "",
            @SerialName("task_progress")
            val taskProgress: String = "",
            @SerialName("user1_greeting")
            val user1Greeting: String = "",
        )
    }
}
