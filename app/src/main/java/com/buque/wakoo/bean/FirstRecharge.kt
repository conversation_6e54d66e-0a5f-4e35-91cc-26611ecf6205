package com.buque.wakoo.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlin.text.Typography.dollar

@Serializable
data class FirstRechargeInfo(
    @SerialName("items")
    val items: List<ReChargeItem>,
    // 2.40.0 华语区首充新增字段
    @SerialName("cost_num")
    val payPriceLabel: String = "",
    @SerialName("value_num")
    val bonusPriceLabel: String = "",
    @SerialName("unit")
    val unit: String = "",
)

@Serializable
data class FirstRechargeGood(
    @SerialName("fk_channel") // # 1 Google IAP、2 APPLE IAP、3 WXPAY、4 ALIPAY
    val fkChannel: Int,
    @SerialName("charge_items")
    val items: List<ReChargeItem>,
    // 2.40.0 华语区首充新增字段
    @SerialName("name")
    val name: String = "",
    @SerialName("desc")
    val desc: String = "",
    @SerialName("icon")
    val icon: String = "",
    @SerialName("call_type")
    val callType: Int = -1,
)

@Serializable
data class ReChargeItem(
    @SerialName("product_id")
    val productId: String = "",
    @SerialName("currency_mark")
    val currencyMark: String = "",
    @SerialName("currency_number")
    val currencyNumber: String = "",
    @SerialName("fk_link")
    val fkLink: String = "",
    @SerialName("top_rights")
    val topItem: SubItem? = null,
    @SerialName("sub_rights")
    val subItems: List<SubItem> = emptyList(),
) {
    @Transient
    val transformPrice: String by lazy {
        buildString {
            if (currencyMark.isNotEmpty() && currencyNumber.isNotEmpty()) {
                append("$currencyMark ")
                append(currencyNumber)
            } else {
                append("$ ")
                append(dollar)
            }
        }
    }
}

@Serializable
data class SubItem(
    @SerialName("icon")
    val icon: String = "",
    @SerialName("text")
    val text: String = "",
    @SerialName("value_coin_cnt")
    val value: Int = -1,
    @SerialName("bonus_label")
    val bonus_label: String = "",
    @SerialName("duration_label")
    val duration_label: String = "",
)
