package com.buque.wakoo.bean.user

import android.graphics.Rect
import androidx.core.graphics.times
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.MediaInfo
import com.buque.wakoo.bean.MyLiveRoomInfo
import com.buque.wakoo.bean.NativeProfile
import com.buque.wakoo.bean.chatgroup.UserChatGroup
import com.buque.wakoo.network.api.bean.SoundBrand
import com.buque.wakoo.network.api.bean.UserResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// /////////////////////////////////////////////////////////////////////////
// 这个文件中定义都是用户个人相关的信息，不是与他人之间的关系
// /////////////////////////////////////////////////////////////////////////

@Serializable
data class Medal(
    val icon: String = "",
    val width: Int = 0,
    val height: Int = 0,
    val type: Int = 0,
)

@Serializable
data class ChatBubble(
    @SerialName("font_color") val fontColor: String = "",
    @SerialName("left_img") val leftImg: String = "",
    @SerialName("padding_bottom") val paddingBottom: Int = 18,
    @SerialName("padding_left") val paddingLeft: Int = 22,
    @SerialName("padding_right") val paddingRight: Int = 22,
    @SerialName("padding_top") val paddingTop: Int = 18,
    @SerialName("right_img") val rightImg: String = "",
    @SerialName("left_corner_imgs") val startImages: List<String>? = null,
    @SerialName("right_corner_imgs") val endImages: List<String>? = null,
) {
    companion object {
        private const val PIC_SCALE_DENSITY = 3 // 设计图气泡素材设备像素密度
    }

    val leftPadding by lazy { Rect(paddingLeft, paddingTop, paddingRight, paddingBottom).times(PIC_SCALE_DENSITY) }

    val rightPadding by lazy { Rect(paddingRight, paddingTop, paddingLeft, paddingBottom).times(PIC_SCALE_DENSITY) }
}

interface IUserDecorations {
    val avatarFrame: String? // 头像框
    val medalList: List<Medal>? // 勋章
    val haveVip: Boolean // 是否是vip
    val vipExpireAt: Long // vip有效期
    val chatBubble: ChatBubble? // 聊天气泡
    val entranceEffect: String? // 进场特效
    val entranceBanner: String? // 进场横幅
    val cpUrl: String? // 官宣勋章
    val cpAvatar: String? // 官宣勋章
    val level: Int // 等级
    val charmLevel: Int // 财富等级
    val wealthLevel: Int // 魅力等级
}

/**
 * 用户装饰信息
 */
@Serializable
data class UserDecorations(
    override val avatarFrame: String? = null, // 头像框
    override val medalList: List<Medal>? = null, // 勋章
    override val haveVip: Boolean = false, // 是否是vip
    override val vipExpireAt: Long = -1, // vip有效期
    override val chatBubble: ChatBubble? = null, // 聊天气泡
    override val entranceEffect: String? = null, // 进场特效
    override val entranceBanner: String? = null, // 进场横幅
    override val cpUrl: String? = null, // 官宣勋章
    override val cpAvatar: String? = null, // 官宣头像
    override val level: Int = 0, // 等级
    override val charmLevel: Int = 0, // 财富等级
    override val wealthLevel: Int = 0, // 魅力等级
) : IUserDecorations {
    companion object {
        val empty = UserDecorations()

        fun fromResponse(response: UserResponse): UserDecorations =
            UserDecorations(
                avatarFrame = response.avatarFrame,
                medalList =
                    if (response.id == SelfUser?.id) {
                        response.medalList
                    } else {
                        response.medalList
                            ?.filter {
                                it.type == 0 || it.type == 3
                            }?.takeIf { it.isNotEmpty() }
                    },
                haveVip = response.isMember,
                vipExpireAt = response.member.expireAt,
                chatBubble = response.chatBubble,
                entranceEffect = response.entryEffect,
                entranceBanner = response.entranceBanner,
                cpUrl = response.cpExtraInfo.levelInfo.wakooSmallImgUrl,
                cpAvatar = response.publicCp?.avatar,
                level = response.level,
                charmLevel = response.expLevelInfo.charmLevel,
                wealthLevel = response.expLevelInfo.wealthLevel,
            )
    }

    val isEmpty: Boolean
        get() = this === empty
}

interface ISocialInfo {
    val nativeRegion: Int // 账号分区
    val onlineStatus: Int // 在线状态
    val followingCount: Int // 关注数
    val followersCount: Int // 粉丝数
    val friendCount: Int // 好友数
    val momentTotalCount: Int // 瞬间数
    val soundLikeCount: Int // 点赞数
    val soundCount: Int // 发布作品数
    val soundBrand: SoundBrand? // 名片语言
    val regionLabel: String // 中国大陆
    val regionReason: Int // 1 不知道干嘛的
    val regionReasonLabel: String // 现居地
    val nativeProfile: NativeProfile // 本地化信息
    val albums: List<MediaInfo>?
    val moments: List<MediaInfo>?
    val roomId: Int?
    val isInRoom: Boolean

    val cityName: String
    val isSameCity: Boolean
    val intimateScore: Int
    val isCoinTrade: Boolean

    val isCN: Boolean
        get() = nativeRegion == 0

    val isJP: Boolean
        get() = nativeRegion == 1
}

/**
 * 社交相关的更多信息
 */
@Serializable
data class SocialInfo(
    override val nativeRegion: Int = -1, // 账号分区
    override val onlineStatus: Int = 0, // 在线状态
    override val followingCount: Int = 0, // 关注数
    override val followersCount: Int = 0, // 粉丝数
    override val friendCount: Int = 0, // 好友数
    override val momentTotalCount: Int = 0, // 瞬间数
    override val soundLikeCount: Int = 0, // 点赞数
    override val soundCount: Int = 0, // 发布作品数
    override val soundBrand: SoundBrand? = null, // 名片语言
    override val regionLabel: String = "", // 中国大陆
    override val regionReason: Int = 0, // 1 不知道干嘛的
    override val regionReasonLabel: String = "", // 现居地
    override val nativeProfile: NativeProfile = NativeProfile.EMPTY, // 本地化信息
    override val albums: List<MediaInfo>? = null, // 相册
    override val moments: List<MediaInfo>? = null, // 动态
    override val roomId: Int? = null, // 当前的语音房id
    override val isInRoom: Boolean = false, // 是否在语音房内
    override val cityName: String = "", // 城市名称
    override val isSameCity: Boolean = false, // 是否同一城市
    override val intimateScore: Int = 0, // 亲密值
    override val isCoinTrade: Boolean = false, // 是否币商
) : ISocialInfo {
//    fun getNativeProfileList(): List<Pair<String, String>> =
//        listOf(
//            "基本情报" to nativeProfile.cityCode.name,
//            "出生地" to nativeProfile.birthCityCode.name,
//            "学历" to nativeProfile.educationalHistory.name,
//            "职业" to nativeProfile.job.name,
//            "体型" to nativeProfile.bodyType.name,
// //            "婚姻史" to maritalHistory.name,
// //            "吸烟史" to tobacco.name,
// //            "结婚意愿" to marriageIntention.name,
// //            "期望" to datingHope.name,
//        )

    companion object {
        val empty = SocialInfo()

        fun fromResponse(response: UserResponse): SocialInfo =
            SocialInfo(
                nativeRegion = response.nativeRegion,
                onlineStatus = response.onlineStatus,
                followingCount = response.followCnt,
                followersCount = response.fansCnt,
                friendCount = response.friendCnt,
                momentTotalCount = response.momentTotalCount,
                soundLikeCount = response.soundLikeCount,
                soundCount = response.soundCount,
                soundBrand = response.soundBrand,
                regionLabel = response.regionLabel,
                regionReason = response.regionReason,
                regionReasonLabel = response.regionLabel,
                nativeProfile = response.nativeProfile,
                albums = response.albums,
                moments = response.moments,
                roomId = response.roomId,
                isInRoom = response.isInAudioRoom,
            )
    }

    val isEmpty
        get() = this === empty
}

interface ICurrencyInfo {
    val balance: Int // 主流货币，金币或者钻石
    val cash: String // 现金
    val pt: String // 积分
}

@Serializable
data class CurrencyInfo(
    override val balance: Int = 0, // 主流货币，金币或者钻石
    override val cash: String = "0", // 现金
    override val pt: String = "0", // 积分
) : ICurrencyInfo {
    companion object {
        val empty = CurrencyInfo()

        fun fromResponse(response: UserResponse): CurrencyInfo =
            CurrencyInfo(
                balance = response.balance,
                cash = response.cash,
                pt = response.diamond,
            )
    }

    val isEmpty
        get() = this === empty
}

interface ISelfOtherInfo {
    val myGroup: UserChatGroup? // 我的群组
    val canCreateLiveRoom: Boolean // 是否能创建语音房
    val myLiveRoom: MyLiveRoomInfo? // 我的语音房
    val myCpUserId: String? // 我的cp的用户id
    val showCpZone: Boolean // 是否显示cp空间
    val registerTimestamp: Int
}

@Serializable
data class SelfOtherInfo(
    override val myGroup: UserChatGroup? = null, // 我的群组
    override val canCreateLiveRoom: Boolean = false, // 是否能创建语音房
    override val myLiveRoom: MyLiveRoomInfo? = null, // 我的语音房
    override val myCpUserId: String? = null, // 我的cp的用户id
    override val showCpZone: Boolean = false, // 我的cp的用户id
    override val registerTimestamp: Int = 0,
) : ISelfOtherInfo {
    companion object {
        val empty = SelfOtherInfo()

        fun fromResponse(response: UserResponse): SelfOtherInfo =
            SelfOtherInfo(
                myGroup = response.group,
                canCreateLiveRoom = response.canCreateAudioroom,
                myLiveRoom = response.room,
                myCpUserId = response.cp?.id,
                showCpZone = response.showCpZone,
                registerTimestamp = response.registerTimestamp
            )
    }

    val isEmpty
        get() = this === empty
}
