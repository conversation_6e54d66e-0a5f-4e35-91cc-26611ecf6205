package com.buque.wakoo.bean

import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User


sealed class PKState {

    /**
     * 未开始状态
     */
    object Idea : PKState()


    /**
     * pk中
     */
    class PKing(val data: PkData) : PKState()

    /**
     * PK结算
     */
    class Settled(val data: PkData, val result: PKResult) : PKState()
}


data class PkData(
    val blueTeam: Team, // 蓝队
    val redTeam: Team, // 红队
    val deadline: Long, // 截止时间
    val pkPenalty: String, // pk的惩罚
    val pkDurationMinute: Int, // pk时间
)

data class Team(
    val score: Int, // 总分数
    val contributeCount: Int, // 贡献人数
    val contributeUser: List<BasicUser>, // 贡献前三名
)

data class PKResult(
    val win: Int, // 获胜方
    val pkMvp: BasicUser?, // pk最佳选手
    val mvpScore: Int, // 最佳选手分数
    val bestContributor: User?, // pk最佳贡献者
    val contributionScore: Int,// 贡献分
)