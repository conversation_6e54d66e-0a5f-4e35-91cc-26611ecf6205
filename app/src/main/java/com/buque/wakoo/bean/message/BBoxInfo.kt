package com.buque.wakoo.bean.message

import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BBoxInfo(
    @SerialName("room_id")
    val roomId: Int = 0,
    var status: Int = 2,
    @SerialName("expire_timestamp")
    val expireTimestamp: Long = 0L,
    @SerialName("remain_seconds")
    var remainSeconds: Long = 0,
    @SerialName("blind_box_cnt")
    val count: Int = 0,
    val link: String = "",
    @SerialName("blind_box_filled_note")
    val completeDesc: String = "",//完成文案
    @SerialName("blind_box_filled")
    val filled: Boolean = false,
    @SerialName("user_blindbox")
    val userExtra: BoxExtra = BoxExtra.EMPTY,
    @SerialName("cp_user_blindbox")
    val cpExtra: BoxExtra = BoxExtra.EMPTY,
    @SerialName("user_info")
    val userInfo: BasicUser? = null,
    @SerialName("cp_user_info")
    val cpUserInfo: BasicUser? = null,
    @SerialName("gift_id")
    val giftId: Int = -1,
    val price: Int = 0,
    @SerialName("show_blind_box_task")
    val showBlindboxTask: Boolean = false,
) {
    //盲盒任务交互状态 1进行中 2暂停 3完成
    val isProgress: Boolean
        get() = status == 1
    val isPaused: Boolean
        get() = status == 2
    val isComplete: Boolean
        get() = status == 3

    val showPopup: Boolean
        get() {
            val myUid = SelfUser?.id
            return if (!isMine) {
                false
            } else {
                if (userInfo?.id == myUid) {
                    userExtra.showDialog
                } else {
                    cpExtra.showDialog
                }
            }
        }

    val isMine: Boolean
        get() {
            val myUid = SelfUser?.id
            return userInfo?.id == myUid || cpUserInfo?.id == myUid
        }

    val isFilled: Boolean
        get() = if (userInfo?.id == SelfUser?.id) {
            userExtra.filled
        } else if (cpUserInfo?.id == SelfUser?.id) {
            cpExtra.filled
        } else {
            false
        }
}

@kotlinx.serialization.Serializable
data class BoxExtra(
    @SerialName("blind_box_cnt")
    val cnt: Int = 0,
    @SerialName("blind_box_filled")
    val filled: Boolean = false,
    @SerialName("show_popup")
    val showDialog: Boolean = false,
) {
    companion object {
        val EMPTY = BoxExtra()
    }
}