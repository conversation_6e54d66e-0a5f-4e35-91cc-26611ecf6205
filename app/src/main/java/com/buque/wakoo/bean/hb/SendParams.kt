package com.buque.wakoo.bean.hb

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * [sceneType]
 * 1	部落
 * 2	语音房
 * 3	私密小屋
 * 4	私聊
 * 5	动态
 * 6	个人主页
 * [sceneId] 若为房间场景，则为房间id
 */

object CoinType {
    const val GOLD = 1
    const val SILVER = 2
    const val JA_GOLD = 3
}
@Serializable
data class PostHongBaoParams(
    @SerialName("coin_type")
    val coinType: Int,
    @SerialName("coin_value")
    val coinValue: Int,
    @SerialName("grab_type")
    val grabType: Int,
    val greets: String,
    val number: Int,
    @SerialName("scene_id")
    val sceneId: String,
    @SerialName("scene_type")
    val sceneType: Int,
    @SerialName("delay_duration")
    val delayDuration: Int,
)