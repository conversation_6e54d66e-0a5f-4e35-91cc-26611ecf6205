package com.buque.wakoo.bean

import androidx.compose.runtime.mutableStateListOf
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.network.api.bean.LiveRoomItemResponse
import com.buque.wakoo.network.api.bean.Seat
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.network.api.bean.VoiceItemResponse
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.MicSeatsInfo
import com.buque.wakoo.ui.screens.liveroom.RoomUser
import com.buque.wakoo.utils.DateTimeUtils
import kotlinx.serialization.Serializable

@Serializable
sealed interface FeedItemData {
    val type: Int
    val id: String

    val identityId get() = "${type}_$id"
}

@Serializable
data class VoiceCardItem(
    val background: String = "", // https://media.ucoofun.com/xya/sound/background/%E5%8D%A1%E7%89%87032x.png
    val favoriteCount: Int = 0, // 2
    val duration: Int = 0, // 8
    override val type: Int = 1, // 10
    override val id: String = "0", // 10
    val isFavorite: Boolean = false, // false
    val isLike: Boolean = false, // false
    val likeCount: Int = 0, // 3
    val resource: String = "", // https://media.ucoofun.com/mobileclient/ios/2025-05-22/audio_2571857145.wav
    val tags: List<VoiceTag> = listOf(),
    val createdAt: Int = 0, // 1747880345
    val visibility: Int = 1, // 1
    val title: String = "", // Ssssddsdsfsds
    val user: BasicUser, // 1
    val isFollow: Boolean = false,
    // 非接口返回数据，文件名
    var fileName: String = "",
    var filePath: String = "",
    // 非接口返回数据，是否正在点赞请求
    var likeRequesting: Boolean = false,
    // 非接口返回数据，是否正在收藏请求
    var favoriteRequesting: Boolean = false,
    // 非接口返回数据，是否正在关注请求
    var followRequesting: Boolean = false,
) : FeedItemData {
    companion object {
        val preview
            get() =
                VoiceCardItem(
                    title = "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話",
                    user = BasicUser.sampleBoy,
                    isFollow = false,
                    tags =
                        listOf(
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈a"),
                            VoiceTag(1, "哈哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈哈"),
                        ),
                )

        fun fromResponse(response: VoiceItemResponse): VoiceCardItem =
            VoiceCardItem(
                background = response.background,
                favoriteCount = response.collectCount,
                duration = response.duration,
                id = response.id.toString(),
                isFavorite = response.isCollect,
                isLike = response.isLike,
                likeCount = response.likeCount,
                createdAt = response.createdAt,
                visibility = response.visibility,
                title = response.title,
                tags = response.tags,
                resource = response.resource,
                user = BasicUser.fromResponse(response.user),
                isFollow = response.user.iHaveFollowed,
            )
    }

    val formatCreateTime = DateTimeUtils.secondsToFormattedString(createdAt)

    var uploadFileUrl: Pair<String, String>? = null
}

@Serializable
data class LiveRoomCardItem(
    val background: String = "",
    override val type: Int = 2, // 10
    override val id: String = "0", // 10
    val tags: List<VoiceTag> = listOf(),
    val title: String = "", // Ssssddsdsfsds
    val desc: String = "", // Ssssddsdsfsds
    val user: BasicUser,
    val visibility: Int = 0,
    val roomMode: Int = 0,
    val roomAudioStream: String = "",
    val mics: List<Seat> = listOf(),
    val inRoomUserCnt: Int = 0,
    val lastUsers: List<UserResponse> = listOf(),
    val refreshTs: Long = System.currentTimeMillis(),
) : FeedItemData {
    val liveRoomMode = LiveRoomMode.valueOf(roomMode)

    val micList: List<MicSeatsInfo>
        get() = micListState

    private val micListState: MutableList<MicSeatsInfo> =
        mutableStateListOf<MicSeatsInfo>().apply {
            addAll(micsToMicSeatsInfo())
        }

    private fun micsToMicSeatsInfo(): List<MicSeatsInfo> {
        val micSeatCount = liveRoomMode.micSeatCount
        return buildList {
            mics.forEachIndexed { index, it ->
                add(
                    if (it.hasUser && it.user != null) {
                        val roomUser = RoomUser.fromResponse(it.user)
                        MicSeatsInfo.User(index, roomUser, it.heartValue ?: 0)
                    } else {
                        MicSeatsInfo.Empty(index)
                    },
                )
                if (size >= micSeatCount) {
                    return@buildList
                }
            }

            val count = size
            repeat(micSeatCount - count) {
                add(MicSeatsInfo.Empty(count + it))
            }
        }
    }

    companion object {
        val preview
            get() =
                LiveRoomCardItem(
                    title = "有時話不多",
                    desc = "有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話不多，是因為想得太多。喜歡安靜的對話，也期待有人能讀懂沉默裡的内心有時話",
                    user = BasicUser.sampleBoy,
                    tags =
                        listOf(
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈a"),
                            VoiceTag(1, "哈哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈"),
                            VoiceTag(1, "哈哈哈"),
                        ),
                )

        fun fromResponse(response: LiveRoomItemResponse): LiveRoomCardItem =
            LiveRoomCardItem(
                background = response.background,
                id = response.id.toString(),
                visibility = response.visibility,
                roomMode = response.ruleType,
                title = response.title,
                tags = response.tags,
                desc = response.desc,
                roomAudioStream = response.roomAudioStream,
                user = BasicUser.fromResponse(response.user),
                mics = response.mics,
                inRoomUserCnt = response.inRoomUserCnt,
                lastUsers = response.last_users,
            )
    }
}
