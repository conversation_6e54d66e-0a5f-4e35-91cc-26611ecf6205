package com.buque.wakoo.bean

import androidx.compose.ui.text.input.KeyboardType
import com.buque.wakoo.manager.localized
import kotlinx.serialization.Serializable

sealed interface Edit

enum class InputAction {
    NICKNAME,
    SHORTITNRO,
}

@Serializable
data class InputEdit(
    val originText: String = "",
    val title: String = "",
    val inputTip: String? = null,
    val maxLength: Int = Int.MAX_VALUE,
    val canEmpty: <PERSON>ole<PERSON> = false,
    val keyboardType: Int = 0, // KeyboardType.Unspecified
    val action: InputAction,
) : Edit {
    companion object {
        fun createNickNameEdit(content: String) =
            InputEdit(
                originText = content,
                title = "修改昵称".localized,
                inputTip = "最多可输入20个字，支持中英文、数字、表情符号".localized,
                maxLength = 20,
                keyboardType = 1,
                action = InputAction.NICKNAME,
            )

        fun createShortIntroEdit(content: String) =
            InputEdit(
                originText = content,
                title = "修改简介".localized,
                inputTip = "最多可输入200个字，支持中英文、数字、表情符号".localized,
                maxLength = 200,
                canEmpty = true,
                keyboardType = 1,
                action = InputAction.SHORTITNRO,
            )

        fun toKeyboardType(edit: InputEdit): KeyboardType =
            when (edit.keyboardType) {
                1 -> KeyboardType.Text
                2 -> KeyboardType.Ascii
                3 -> KeyboardType.Number
                4 -> KeyboardType.Phone
                5 -> KeyboardType.Uri
                6 -> KeyboardType.Email
                7 -> KeyboardType.Password
                8 -> KeyboardType.NumberPassword
                9 -> KeyboardType.Decimal
                else -> KeyboardType.Unspecified // Fallback for unknown values
            }
    }
}
