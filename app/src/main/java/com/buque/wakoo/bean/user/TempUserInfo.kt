package com.buque.wakoo.bean.user

import com.buque.wakoo.network.api.bean.UserResponse

/**
 * @param response  这里最终选择[UserResponse]，而不是[kotlinx.serialization.json.JsonObject]有两个原因
 *        1. 方便转换，其实就是自己取字段组合，不需要手动解析（虽然可以写解析的扩展方法，但很容易因为不知道有这个方法而重复写很多类似的解析方法）
 *        2. 效率问题，json序列化与序列化，大多时候大家喜欢在当前线程解析，[UserResponse]数据量比较大，多少有影响
 * @param fallbackUser 从缓存读取不到[UserResponse]，只能是一个[BasicUser]
 * 我希望大家在使用[TempUserInfo]是把他当成中间产物，自己转成自己的业务代码，不要直接使用。
 */
class TempUserInfo(
    val response: UserResponse?,
    val fallbackUser: BasicUser? = null,
) {
    init {
        require(response != null || fallbackUser != null)
    }

    val id: String
        get() = response?.id ?: fallbackUser!!.id

    val user by lazy {
        response?.let { BasicUser.fromResponse(it) } ?: fallbackUser!!
    }
}
