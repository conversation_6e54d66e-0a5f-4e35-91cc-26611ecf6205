package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.navigation.dialog.NamedDialogDestination
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogControllerWithParamsScope
import com.buque.wakoo.ui.screens.messages.chat.BlindBoxPendant
import com.buque.wakoo.ui.widget.CommonPendantBanner
import com.buque.wakoo.ui.widget.ComposeSwiperData
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.ui.widget.gift.ChatroomGiftPanel
import com.buque.wakoo.ui.widget.gift.GiftScene
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.viewmodel.HongBaoViewModel
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

@Composable
fun LiveRoomOverlayLayout(
    roomInfoState: LiveRoomInfoState,
    viewModel: LiveRoomViewModel,
    giftViewModel: GiftViewModel,
    hongBaoViewModel: HongBaoViewModel
) {

    val blindBox by roomInfoState.blindBoxState
    val visible by AppConfigManager.bboxVisibleState
    val composePendants by remember {
        derivedStateOf {
            if (visible && blindBox != null && blindBox!!.showBlindboxTask) {
                listOf(ComposeSwiperData {
                    BlindBoxPendant(blindBox!!)
                })
            } else {
                emptyList()
            }
        }
    }
    FloatingLayoutManager {
        CommonPendantBanner(position = 307, initialOffsetDp = DpOffset(x = 0.dp, y = (-84).dp), composePendants = composePendants)
    }

    val config by AppConfigManager.uiConfigFlow.collectAsState()
    if (config.packetEnabled) {
        val hbList by hongBaoViewModel.hongBaoListState
        Box(modifier = Modifier.fillMaxSize()) {
            HongBaoPendantContainer(
                hbList, modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .width(50.dp)
                    .navigationBarsPadding()
                    .padding(bottom = 80.dp, end = 8.dp)
            ) {
                roomInfoState.sendEvent(RoomEvent.ShowHongBao(it.id))
            }
        }
    }


    val dialogController = rememberDialogControllerWithParamsScope(roomInfoState)

    LaunchedEffect(dialogController, roomInfoState) {
        roomInfoState.events
            .onEach { event ->
                when (event) {
                    is RoomEvent.Dialog -> {
                        dialogController.easyPost(
                            name = event.name,
                            dialogProperties = event.dialogProperties,
                            content = {
                                event.content(this, it as LiveRoomInfoState)
                            },
                        )
                    }

                    is RoomEvent.RestorableDialog -> {
                        dialogController.post(event.destination)
                    }

                    is RoomEvent.At -> {
                        viewModel.setInputTextState(InputTextState.Visible("@${event.user.name} "))
                    }

                    is RoomEvent.OpenGiftPanel -> {
                        giftViewModel.fetchGiftData()
                        dialogController.easyPostBottomPanel(useSystemDialog = false) {
                            ChatroomGiftPanel(
                                position = null,
                                onlyUser = event.onlyUser,
                                giftListModelState = giftViewModel.giftListModelState,
                                roomInfoState = roomInfoState,
                            ) { gift, param, users ->
                                giftViewModel.sendGiftAt(
                                    giftScene = if (roomInfoState.isPrivateRoom) GiftScene.Private else GiftScene.ROOM,
                                    targets = users.map { it.id },
                                    gift = gift,
                                    params = param,
                                )
                            }
                        }
                    }

                    is RoomEvent.SendGift -> {
                        giftViewModel.sendGiftAt(
                            giftScene = if (roomInfoState.isPrivateRoom) GiftScene.Private else GiftScene.ROOM,
                            targets = event.targets,
                            gift = event.gift,
                            params = event.params,
                        )
                    }

                    is RoomEvent.EditHongBao -> {
                        viewModel.sendEvent(RoomEvent.PanelDialog(name = "edit_hb") { roomInfoState ->
                            HongBaoEditPanel(hbVm = hongBaoViewModel, onDismiss = {
                                dismiss()
                            })
                        })
                    }

                    is RoomEvent.ShowHongBao -> {
                        dialogController.dismiss { dest ->
                            dest is NamedDialogDestination && dest.name == "hongbao"
                        }
                        viewModel.sendEvent(RoomEvent.CustomDialog(name = "hongbao") {
                            HongBaoDialogContent(event.id, hongBaoViewModel, onDismiss = {
                                dismiss()
                            })
                        })
                    }

                    else -> Unit
                }
            }.launchIn(this)
    }

    giftViewModel.GiftEffectView()
}
