package com.buque.wakoo.ui.screens.voice

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.PagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.FeedItemData
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.VoiceListTab
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.ui.widget.state.CStateVerticalPagerPaginateLayout
import com.buque.wakoo.ui.widget.voice.UserVoiceListCard
import com.buque.wakoo.viewmodel.VoiceListViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

@Composable
fun VoiceLazyListPage(
    user: User,
    tagPrefix: String,
    tab: VoiceListTab.IUser,
    indexInParent: Int,
    parentPagerState: PagerState,
    modifier: Modifier = Modifier,
    refreshEnable: Boolean = true,
    pagerContentPadding: PaddingValues = PaddingValues.Zero,
) {
    val isActivePage by remember(indexInParent) {
        derivedStateOf {
            parentPagerState.currentPage == indexInParent
        }
    }

    val context = LocalContext.current
    val viewModel = viewModel<VoiceListViewModel>(factory = VoiceListViewModel.Factory(context))

    val rootNavController = LocalAppNavController.root

    CStateVerticalPagerPaginateLayout<String, Int, VoiceListTab, FeedItemData, VoiceListViewModel>(
        reqKey = "",
        tabKey = tab,
        modifier = modifier,
        viewModel = viewModel,
        refreshEnable = refreshEnable,
        emptyText = tab.emptyText,
        emptyId = tab.emptyId,
        emptyButton = tab.emptyButton,
        onEmptyClick = { _, _ ->
            rootNavController.push(Route.VoicePublish)
        },
    ) { paginateState, pagerState, list ->

        list as List<VoiceCardItem>

        var deleteVoiceItem by remember {
            mutableStateOf<VoiceCardItem?>(null)
        }

        val lifecycleOwner = LocalLifecycleOwner.current

        LazyColumn(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = pagerContentPadding,
        ) {
            itemsIndexed(list) { index, item ->

                val tag = "$tagPrefix$tab-${item.id}"

                val playItem =
                    remember(tag) {
                        PlayMediaItem.audio(item.resource, tag)
                    }

                val isPlaying by remember {
                    derivedStateOf {
                        MediaPlayerManager.currentPlayingTag.value == playItem.tag
                    }
                }

                var refreshFlag by remember {
                    mutableIntStateOf(0)
                }

                if (isPlaying) {
                    LaunchedEffect(Unit) {
                        while (isActive) {
                            delay(1000)
                            refreshFlag++
                        }
                    }
                }

                val duration by remember(item) {
                    derivedStateOf {
                        if (isPlaying) {
                            refreshFlag // 这是一个刷新标志，不能删除
                            MediaPlayerManager
                                .getPlayerDuration(playItem, item.duration.toLong())
                                .minus(
                                    MediaPlayerManager.getPlayerPosition(playItem),
                                ).div(
                                    1000,
                                ).toInt()
                        } else {
                            item.duration
                        }
                    }
                }

                // 每个页面的内容
                UserVoiceListCard(
                    item = item,
                    showOwnerInfo = tab !is VoiceListTab.MyPublish,
                    modifier =
                        Modifier
                            .padding(horizontal = 16.dp)
                            .fillMaxWidth(),
                    isPlaying = isPlaying,
                    duration = duration,
                    onToggleLike = {
                        viewModel.requestLikeVoice(item, tab)
                    },
                    onToggleFavorite = {
                        viewModel.requestFavoriteVoice(item, tab)
                    },
                    onDelete = {
                        deleteVoiceItem = item
                    },
                    onReport = {
                        rootNavController.push(Route.Report(5, item.id))
                    },
                    onUserClick = {
                        rootNavController.push(Route.UserProfile(item.user))
                    },
                ) {
                    MediaPlayerManager.toggle(playItem)
                }
            }
        }

        if (deleteVoiceItem != null) {
            Dialog(onDismissRequest = {
                deleteVoiceItem = null
            }) {
                SimpleDoubleActionDialog(
                    content = "确定要删除这条声音日记吗？".localized,
                    cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消".localized),
                    confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认".localized),
                    onCancel = {
                        deleteVoiceItem = null
                    },
                    onConfirm = {
                        viewModel.requestDeleteVoice(deleteVoiceItem!!, tab)
                        deleteVoiceItem = null
                    },
                )
            }
        }
    }
}
