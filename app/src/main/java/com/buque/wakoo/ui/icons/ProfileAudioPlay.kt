package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.ProfileAudioPlay: ImageVector
    get() {
        if (_ProfileAudioPlay != null) {
            return _ProfileAudioPlay!!
        }
        _ProfileAudioPlay =
            ImageVector
                .Builder(
                    name = "ProfileAudioPlay",
                    defaultWidth = 32.dp,
                    defaultHeight = 32.dp,
                    viewportWidth = 32f,
                    viewportHeight = 32f,
                ).apply {
                    path(
                        fill = SolidColor(Color(0xFF111111)),
                        stroke = SolidColor(Color(0xFFFFFFFF)),
                        strokeLineWidth = 2f,
                    ) {
                        moveTo(16f, 4f)
                        curveTo(22.627f, 4f, 28f, 9.373f, 28f, 16f)
                        curveTo(28f, 22.627f, 22.627f, 28f, 16f, 28f)
                        curveTo(9.373f, 28f, 4f, 22.627f, 4f, 16f)
                        curveTo(4f, 9.373f, 9.373f, 4f, 16f, 4f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF66FE6B))) {
                        moveTo(20.071f, 15.135f)
                        curveTo(20.738f, 15.52f, 20.738f, 16.482f, 20.071f, 16.867f)
                        lineTo(14.714f, 19.96f)
                        curveTo(14.047f, 20.345f, 13.214f, 19.864f, 13.214f, 19.094f)
                        lineTo(13.214f, 12.908f)
                        curveTo(13.214f, 12.138f, 14.047f, 11.657f, 14.714f, 12.042f)
                        lineTo(20.071f, 15.135f)
                        close()
                    }
                }.build()

        return _ProfileAudioPlay!!
    }

@Suppress("ObjectPropertyName")
private var _ProfileAudioPlay: ImageVector? = null
