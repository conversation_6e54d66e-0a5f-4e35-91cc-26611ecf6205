package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val WakooIcons.CloseCircle: ImageVector
    get() {
        if (_CloseCircle != null) {
            return _CloseCircle!!
        }
        _CloseCircle =
            ImageVector
                .Builder(
                    name = "CloseCircle",
                    defaultWidth = 33.dp,
                    defaultHeight = 32.dp,
                    viewportWidth = 33f,
                    viewportHeight = 32f,
                ).apply {
                    path(fill = SolidColor(Color.White)) {
                        moveTo(21.371f, 9.553f)
                        curveTo(21.718f, 9.206f, 22.28f, 9.206f, 22.628f, 9.553f)
                        curveTo(22.975f, 9.9f, 22.975f, 10.463f, 22.628f, 10.81f)
                        lineTo(17.439f, 15.998f)
                        lineTo(22.628f, 21.186f)
                        curveTo(22.975f, 21.533f, 22.975f, 22.096f, 22.628f, 22.443f)
                        curveTo(22.28f, 22.79f, 21.718f, 22.79f, 21.371f, 22.443f)
                        lineTo(16.182f, 17.254f)
                        lineTo(11.314f, 22.124f)
                        curveTo(10.967f, 22.471f, 10.404f, 22.471f, 10.057f, 22.124f)
                        curveTo(9.71f, 21.777f, 9.71f, 21.214f, 10.057f, 20.867f)
                        lineTo(14.925f, 15.998f)
                        lineTo(10.057f, 11.13f)
                        curveTo(9.71f, 10.782f, 9.71f, 10.22f, 10.057f, 9.873f)
                        curveTo(10.404f, 9.526f, 10.967f, 9.526f, 11.314f, 9.873f)
                        lineTo(16.182f, 14.741f)
                        lineTo(21.371f, 9.553f)
                        close()
                    }
                    path(
                        stroke = SolidColor(Color.White),
                        strokeLineWidth = 1.77778f,
                    ) {
                        moveTo(16.5f, 16f)
                        moveToRelative(-15.111f, 0f)
                        arcToRelative(15.111f, 15.111f, 0f, isMoreThanHalf = true, isPositiveArc = true, 30.222f, 0f)
                        arcToRelative(15.111f, 15.111f, 0f, isMoreThanHalf = true, isPositiveArc = true, -30.222f, 0f)
                    }
                }.build()

        return _CloseCircle!!
    }

@Suppress("ObjectPropertyName")
private var _CloseCircle: ImageVector? = null

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.CloseCircle,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}
