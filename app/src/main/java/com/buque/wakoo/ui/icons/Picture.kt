package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Picture: ImageVector
    get() {
        if (_Picture != null) {
            return _Picture!!
        }
        _Picture =
            ImageVector
                .Builder(
                    name = "Picture",
                    defaultWidth = 24.dp,
                    defaultHeight = 24.dp,
                    viewportWidth = 24f,
                    viewportHeight = 24f,
                ).apply {
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(2.992f, 21f)
                        curveTo(2.444f, 21f, 2f, 20.555f, 2f, 20.007f)
                        verticalLineTo(3.993f)
                        curveTo(2f, 3.445f, 2.455f, 3f, 2.992f, 3f)
                        horizontalLineTo(21.008f)
                        curveTo(21.556f, 3f, 22f, 3.445f, 22f, 3.993f)
                        verticalLineTo(20.007f)
                        curveTo(22f, 20.555f, 21.545f, 21f, 21.008f, 21f)
                        horizontalLineTo(2.992f)
                        close()
                        moveTo(20f, 15f)
                        verticalLineTo(5f)
                        horizontalLineTo(4f)
                        verticalLineTo(19f)
                        lineTo(14f, 9f)
                        lineTo(20f, 15f)
                        close()
                        moveTo(20f, 17.828f)
                        lineTo(14f, 11.828f)
                        lineTo(6.828f, 19f)
                        horizontalLineTo(20f)
                        verticalLineTo(17.828f)
                        close()
                        moveTo(8f, 11f)
                        curveTo(6.895f, 11f, 6f, 10.105f, 6f, 9f)
                        curveTo(6f, 7.895f, 6.895f, 7f, 8f, 7f)
                        curveTo(9.105f, 7f, 10f, 7.895f, 10f, 9f)
                        curveTo(10f, 10.105f, 9.105f, 11f, 8f, 11f)
                        close()
                    }
                }.build()

        return _Picture!!
    }

@Suppress("ObjectPropertyName")
private var _Picture: ImageVector? = null
