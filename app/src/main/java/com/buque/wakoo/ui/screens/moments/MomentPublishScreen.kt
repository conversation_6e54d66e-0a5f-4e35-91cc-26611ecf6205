package com.buque.wakoo.ui.screens.moments

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.LocationManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.AddRound
import com.buque.wakoo.ui.icons.CloseCircleFill
import com.buque.wakoo.ui.icons.Location
import com.buque.wakoo.ui.icons.VuesaxBoldSend
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.previewer.TransitionOverlay
import com.buque.wakoo.ui.widget.media.previewer.rememberPreviewState
import com.buque.wakoo.ui.widget.media.previewer.showMediaItemPreviewer
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.viewmodel.MomentPublishViewModel
import me.saket.telephoto.zoomable.rememberZoomablePeekOverlayState
import me.saket.telephoto.zoomable.zoomablePeekOverlay

@Composable
fun MomentPublishScreen(modifier: Modifier = Modifier) {
    val viewModel = viewModel<MomentPublishViewModel>()
    val scope = rememberCoroutineScope()
    val lm = LocalLoadingManager.current
    val rootNavController = LocalAppNavController.root

    val albumLauncher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            viewModel.addPicture(result.list)
        }

    var inputValue by viewModel.inputValue
    var address by viewModel.inputAddress

    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    val areaSelectorLauncher =
        rootNavController.rememberLauncherForResult<Route.LocationSelector, String> {
            address = it
        }

    val locationLauncher =
        rememberLauncherForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            val hasPermission = result.values.all { it }
            if (!hasPermission) {
                showToast("您拒绝了定位权限, 无法使用地址位置显示".localized)
            } else {
                LocationManager.requestLocationOnce()
            }
        }
    LifecycleEventEffect(Lifecycle.Event.ON_START) {
        locationLauncher.launch(viewModel.permissions)
    }

    val density = LocalDensity.current
    val previewState = rememberPreviewState()

    // 转场覆盖层，负责在转场期间显示动画元素和全屏查看器。
    TransitionOverlay(state = previewState)

    SegColorTitleScreenScaffold(title = "", actions = {
        Row(
            modifier =
                Modifier
                    .padding(end = 15.dp)
                    .height(28.dp)
                    .background(
                        brush =
                            Brush.horizontalGradient(
                                listOf(Color(0xffa3ff2c), Color(0xff31ffa1)),
                            ),
                        shape = CircleShape,
                    ).click(enabled = viewModel.selectPicture.isNotEmpty() && viewModel.inputValue.value.isNotEmpty(), onClick = {
                        lm.show(scope) {
                            if (viewModel.publish()) {
                                rootNavController.popIs<Route.MomentPublish>()
                            }
                        }
                    })
                    .padding(horizontal = 10.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                imageVector = WakooIcons.VuesaxBoldSend,
                contentDescription = "发布",
                modifier = Modifier.size(16.dp),
            )
            Spacer(Modifier.width(1.dp))
            Text(
                "发布".localized,
                fontSize = 12.sp,
                lineHeight = 12.sp,
                color = Color(0xff111111),
            )
        }
    }) { pv ->
        LazyVerticalGrid(
            GridCells.Fixed(3),
            modifier =
                Modifier
                    .fillMaxSize()
                    .onGloballyPositioned {
                        previewState.overlayClipRect = it.boundsInRoot()
                    }.background(color = Color.White)
                    .padding(pv),
        ) {
            // 文本输入
            item(span = { GridItemSpan(3) }) {
                AppTextField(
                    value = inputValue,
                    onValueChange = {
                        inputValue = it
                    },
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(156.dp),
                    maxLength = 200,
                    showLengthTip = false,
                    keyboardOptions =
                        KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Next,
                        ),
                    keyboardActions =
                        KeyboardActions(
                            onNext = {
                                focusManager.clearFocus()
                                keyboardController?.hide()
                            },
                        ),
                    placeholder = "记录下这个瞬间,你的心情和想法吧~".localized,
                    placeholderStyle = TextStyle(color = Color(0xff999999)),
                )
            }

            // 图片选择
            itemsIndexed(viewModel.selectPicture, key = { _, it -> it.id }) { index, pic ->
                Box(modifier = Modifier.aspectRatio(1f)) {
                    NetworkImage(
                        data = pic.uriString,
                        modifier =
                            with(previewState) {
                                Modifier
                                    .padding(10.dp)
                                    .fillMaxWidth()
                                    .aspectRatio(1f)
                                    .clip(RoundedCornerShape(12.dp))
                                    .clickable(onClick = {
                                        previewState.showMediaItemPreviewer(
                                            mediaItems = viewModel.selectPicture,
                                            item = pic,
                                            radius =
                                                with(density) {
                                                    12.dp.toPx()
                                                },
                                            applyIndexKey = true,
                                        )
                                    })
                                    .registerGridItem("$index-${pic.albumId}-${pic.id}")
                                    .zoomablePeekOverlay(rememberZoomablePeekOverlayState())
                            },
                        contentScale = ContentScale.Crop,
                        memoryCacheKey = pic.uriString,
                    )
                    Image(
                        WakooIcons.CloseCircleFill,
                        contentDescription = null,
                        modifier =
                            Modifier
                                .align(alignment = Alignment.TopEnd)
                                .size(24.dp)
                                .click {
                                    viewModel.removePicture(pic)
                                },
                    )
                }
            }

            if (viewModel.selectPicture.size < 9) {
                item {
                    Box(modifier = Modifier.aspectRatio(1f)) {
                        Image(
                            WakooIcons.AddRound,
                            contentDescription = null,
                            modifier =
                                Modifier
                                    .padding(10.dp)
                                    .fillMaxWidth()
                                    .aspectRatio(1f)
                                    .click {
                                        albumLauncher.launch(
                                            Route.MediaSelector(
                                                maxSelectCount = 9,
                                                selectedItem = viewModel.selectPicture,
                                            ),
                                        )
                                    }.background(color = Color(0xffE9EAEF), shape = RoundedCornerShape(8.dp))
                                    .padding(32.dp),
                        )
                    }
                }
            }

            // 地址选择
            item(span = { GridItemSpan(3) }) {
                Row(
                    modifier =
                        Modifier
                            .padding(horizontal = 10.dp, vertical = 4.dp)
                            .click {
                                if (viewModel.hasPermission()) {
                                    areaSelectorLauncher.launch(Route.LocationSelector())
                                }
                            },
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(WakooIcons.Location, contentDescription = null)
                    SizeWidth(2.dp)
                    Text(
                        if (!address.isNullOrBlank()) (address ?: "") else "所在位置".localized,
                        fontSize = 12.sp,
                        lineHeight = 12.sp,
                        color = Color(0xff111111),
                    )
                }
            }
        }
    }
}
