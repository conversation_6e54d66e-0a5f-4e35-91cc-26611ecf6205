package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.NetworkImage
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun NewFlowRecommendPanel(
    user: User,
    cityLabel: String = "",
    onTimeout: () -> Unit = {},
    onClick: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()
    var second by remember {
        mutableIntStateOf(60)
    }
    LaunchedEffect(key1 = user.id) {
        scope.launch {
            while (second > 0) {
                delay(1000)
                second -= 1
                if (second <= 0) {
                    onTimeout.invoke()
                }
            }
        }
    }
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .paint(painter = painterResource(R.drawable.ic_recommend_user_bg), contentScale = ContentScale.FillBounds),
    ) {
        Column(
            modifier =
                Modifier
                    .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = Color(0xfffe669e),
                            fontFamily = FontFamily.MI_SANS,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.W900,
                        ),
                    ) {
                        append("新用户流量推荐".localized)
                    }
                    appendLine()
                    withStyle(
                        SpanStyle(
                            color = Color(0xfffe669e),
                            fontFamily = FontFamily.MI_SANS,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.W900,
                        ),
                    ) {
                        append("请尽快主动发消息接待".localized)
                    }
                },
                lineHeight = 30.sp,
            )
            SizeHeight(20.dp)
            NetworkImage(
                user.avatar,
                Modifier
                    .size(80.dp)
                    .clip(CircleShape),
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = user.name, fontSize = 16.sp, color = Color(0xff111111))
            Spacer(modifier = Modifier.height(8.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                GenderAgeTag(user = user)
                if (cityLabel.isNotEmpty()) {
                    SizeWidth(4.dp)
                    Text(text = cityLabel, fontSize = 14.sp, color = Color(0xff999999))
                }
            }
            Spacer(modifier = Modifier.height(20.dp))
            Box(
                modifier =
                    Modifier
                        .widthIn(min = 195.dp)
                        .height(40.dp)
                        .click(onClick = onClick)
                        .background(color = Color(0xffFE669E), CircleShape),
                contentAlignment = Alignment.Center,
            ) {
                Text(text = "去接待(%d)".localizedFormat(second), fontSize = 16.sp, color = Color.White)
            }
            Spacer(modifier = Modifier.height(20.dp))
        }

        Icon(
            WakooIcons.Close,
            tint = Color(0xff999999),
            contentDescription = null,
            modifier =
                Modifier
                    .padding(16.dp)
                    .size(16.dp)
                    .align(Alignment.TopEnd),
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun Preview() {
    Box(modifier = Modifier.width(280.dp)) {
        NewFlowRecommendPanel(user = BasicUser.sampleBoy)
    }
}
