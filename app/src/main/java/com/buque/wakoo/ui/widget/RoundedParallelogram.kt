package com.buque.wakoo.ui.widget

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.sqrt

fun Modifier.roundedParallelogram(
    offset: Float,
    brush: Brush = Brush.horizontalGradient(listOf(Color(0x96244EB7), Color(0x96244EB7))),
    borderColor: Color = Color(0xFF4BA7FF),
    borderWidth: Dp = 1.dp,
    cornerRadius: Float = 2f
): Modifier {
    return this.drawWithCache {
        // 平行四边形顶点定义 (按顺序: A->B->C->D)
        val width = size.width
        val height = size.height
        val a = Offset(offset, 0f)
        val b = Offset(width, 0f)
        val c = Offset(width - offset, height)
        val d = Offset(0f, height)

        // 计算各顶点的入边/出边切点
        val (a1, a2) = calculateTangentPoints(a, d, b, cornerRadius)
        val (b1, b2) = calculateTangentPoints(b, a, c, cornerRadius)
        val (c1, c2) = calculateTangentPoints(c, b, d, cornerRadius)
        val (d1, d2) = calculateTangentPoints(d, c, a, cornerRadius)

        // 构建路径
        val path = Path().apply {
            moveTo(a1.x, a1.y)       // 起点: A的入边切点

            // 顶点A的曲线 (a1 -> a2)
            quadraticTo(a.x, a.y, a2.x, a2.y)
            lineTo(b1.x, b1.y)        // 直线到B的入边切点

            // 顶点B的曲线 (b1 -> b2)
            quadraticTo(b.x, b.y, b2.x, b2.y)
            lineTo(c1.x, c1.y)        // 直线到C的入边切点

            // 顶点C的曲线 (c1 -> c2)
            quadraticTo(c.x, c.y, c2.x, c2.y)
            lineTo(d1.x, d1.y)        // 直线到D的入边切点

            // 顶点D的曲线 (d1 -> d2)
            quadraticTo(d.x, d.y, d2.x, d2.y)
            close()                   // 闭合路径 (自动连接d2->a1)
        }

        this.onDrawBehind {
            // 绘制背景渐变
            drawPath(
                path = path,
                brush = brush,
                style = Fill
            )

            // 绘制边框
            drawPath(
                path = path,
                color = borderColor, // 半透明浅蓝色
                style = Stroke(width = borderWidth.toPx(), cap = StrokeCap.Round)
            )
        }
    }
}

/**
 * 计算顶点处的两个切点
 * @param vertex 当前顶点
 * @param prev 前一个顶点 (入边方向)
 * @param next 后一个顶点 (出边方向)
 * @param radius 圆角半径
 * @return Pair(入边切点, 出边切点)
 */
private fun calculateTangentPoints(
    vertex: Offset,
    prev: Offset,
    next: Offset,
    radius: Float
): Pair<Offset, Offset> {
    // 入边方向向量 (prev -> vertex) 并归一化
    val inVector = (vertex - prev).normalize()
    // 出边方向向量 (vertex -> next) 并归一化
    val outVector = (next - vertex).normalize()

    // 计算切点:
    // 入边切点 = 顶点 - 入边向量 × 半径
    // 出边切点 = 顶点 + 出边向量 × 半径
    return Pair(
        vertex - inVector * radius,
        vertex + outVector * radius
    )
}

/** 扩展函数：向量归一化 */
private fun Offset.normalize(): Offset {
    val length = sqrt(x * x + y * y)
    return if (length > 0) Offset(x / length, y / length) else this
}