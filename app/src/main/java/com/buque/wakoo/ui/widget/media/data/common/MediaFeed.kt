package com.buque.wakoo.ui.widget.media.data.common

import kotlinx.serialization.Serializable

/**
 * 代表媒体选择器UI的完整数据状态。
 * 这个对象将通过 Flow 发射，驱动整个UI的渲染。
 *
 * @property albums 相册列表。
 * @property mediaItems 媒体项目列表。这是一个特殊设计的列表，用于渐进式加载。
 *                      列表的尺寸是固定的（等于总媒体数），初始时所有元素为 null。
 *                      后台任务会逐步获取 MediaItem 详情，并填充到这个列表的对应位置。
 *                      UI层可以根据元素是否为 null 来决定显示占位符还是真实内容。
 * @property isLoading 是否处于初始加载状态。
 * @property error 如果发生错误，则持有异常信息。
 * @property mediaFilling 是否正在填充数据。
 */
data class MediaFeed(
    val albums: List<Album> = emptyList(),
    val allIds: List<String> = emptyList(),
    val mediaItems: List<IMediaItem> = emptyList(),
    val isLoading: Boolean = true,
    val error: Throwable? = null,
    val mediaFilling: Boolean = true,
) {
    val anchorIdIndex =
        mediaItems.indexOfFirst {
            it is PlaceholderMediaItem
        }
}

/**
 * 仅包含ID的占位符媒体项。
 */
@Serializable
data class PlaceholderMediaItem constructor(
    override val id: String,
    override val dateAdded: Long,
) : IMediaItem
