package com.buque.wakoo.ui.screens.japan.boost

import android.text.format.DateUtils
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.boost.MissionInfo
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.BoostApi
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.dialog.AnyPopDialogProperties
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.DirectionState
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.dialog.SingleActionDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooBlack
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.toLink
import com.buque.wakoo.utils.eventBus.tryToLink
import com.buque.wakoo.viewmodel.BoostState
import com.buque.wakoo.viewmodel.BoostViewModel
import kotlinx.coroutines.launch

object AwardType {
    // 女收益
    const val inc = 7

    // 男钻石
    const val dia = 5
}

@Composable
fun BoostTabPage() {
    val self = LocalSelfUserProvider.current
    if (self.isJP) {
        BoostPage(if (!self.isBoy) AwardType.inc else AwardType.dia)
    }
}

//日区用户注册第二天开始每天第一次打开弹签到dialog
@Composable
fun ShowTodaySignDialog() {
    val self = LocalSelfUserProvider.current
    val lm = LocalLoadingManager.current
    if (self.isJP && self.otherInfo.registerTimestamp != 0 && !DateUtils.isToday(self.otherInfo.registerTimestamp * 1000L)) {
        val awardType = if (!self.isBoy) AwardType.inc else AwardType.dia
        val viewModel: BoostViewModel = viewModel(initializer = { BoostViewModel(awardType) })
        LaunchedEffect(Unit) {
            val t = currentUserKV.getLong("last_sign_alert_data", 0L)
            if (!DateUtils.isToday(t)) {
                val api = ApiClient.createuserApiService<BoostApi>()
                executeApiCallExpectingData { api.getSignInfo() }
                    .onSuccess { signInfo ->
                        currentUserKV.putLong("last_sign_alert_data", System.currentTimeMillis())
                        EventBus.send(AppEvent.CustomDialog(dialogProperties = AnyPopDialogProperties(direction = DirectionState.CENTER)) {
                            DailySignInDialog(signInfo, onSign = {
                                dismiss()
                                lm.show(null) {
                                    viewModel
                                        .makeSign(
                                            signInfo.seriesId.toString(),
                                        ).onSuccess { obj ->
                                            val title = obj.parseValue<String>("award_info").orEmpty()
                                            val message = obj.parseValue<String>("message").orEmpty()
                                            val awardType = obj.parseValue<Int>("award_type") ?: awardType
                                            EventBus.send(AppEvent.CustomDialog(dialogProperties = AnyPopDialogProperties(direction = DirectionState.CENTER)) {
                                                MissionCompleteContent(title, message, awardType) {
                                                    dismiss()
                                                }
                                            })
                                        }.toastWhenError()
                                }
                            }) {
                                dismiss()
                            }
                        })
                    }
            }
        }
    }
}

@Composable
fun BoostPage(
    awardType: Int,
    viewModel: BoostViewModel =
        viewModel<BoostViewModel>(key = "boost_$awardType", initializer = {
            BoostViewModel(awardType)
        }),
) {
    val nav = LocalAppNavController.root
    val dc = rememberDialogController()
    val lm = LocalLoadingManager.current
    val scope = rememberCoroutineScope()
    LifecycleResumeEffect(viewModel) {
        viewModel.refreshState()
        UserManager.refreshSelfUserInfo()
        onPauseOrDispose { }
    }
    val state by viewModel.state
    val userInfo = LocalSelfUserProvider.current

    fun showExchangeDialog(isCash: Boolean) {
        val title = if (isCash) "积分兑换现金".localized else "积分兑换钻石".localized
        dc.easyPostBottomPanel {
            val stateFlow = if (isCash) viewModel.stateFlowCash else viewModel.stateFlowDiamond
            val list by stateFlow.collectAsState()
            var selectedItem by remember(list) { mutableStateOf(list.firstOrNull()) }
            ExchangeContent(title, "确认兑换".localized, centerContent = {
                val shape =
                    remember {
                        RoundedCornerShape(8.dp)
                    }
                LazyVerticalGrid(
                    GridCells.Fixed(3),
                    modifier =
                        Modifier
                            .fillMaxSize()
                            .padding(top = if (list.size < 7) 40.dp else 0.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    items(list) { item ->
                        ExchangeItem(
                            if (isCash) R.drawable.ic_currency else R.drawable.ic_green_diamond_straight,
                            item.amount,
                            item.desc,
                            modifier =
                                if (selectedItem?.id == item.id) {
                                    Modifier
                                        .background(Color(0x338CFFAD), shape)
                                        .border(1.dp, Color(0xFF8CFFBC), shape)
                                } else {
                                    Modifier
                                        .background(Color(0xFFF1F2F3), shape)
                                        .clickable(onClick = {
                                            selectedItem = item
                                        })
                                },
                        )
                    }
                }
            }, onClose = {
                dismiss()
            }, onEnsure = {
                val sel = selectedItem
                if (sel != null) {
                    lm.show(null) {
                        viewModel
                            .exchange(sel)
                            .toastWhenError()
                            .onSuccess {
                                showToast("兑换成功".localized)
                                viewModel.refreshState()
                                UserManager.refreshSelfUserInfo()
                            }
                    }
                    dismiss()
                }
            })
        }
    }

    CStateLayout(state, modifier = Modifier.fillMaxSize()) {
        val list = rememberBoostItems(awardType, it, userInfo)
        LazyColumn(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF5F7F9))
                    .systemBarsPadding(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp),
        ) {
            items(list) { item ->
                when (item) {
                    is CWidgetData ->
                        CWidget(item.title, item.cCount, item.buttonText, onClick = {
                            scope.launch {
                                viewModel
                                    .preCheck()
                                    .onSuccess { link ->
                                        LogUtils.d("go to link:$link")
                                        link.toLink()
                                    }.toastWhenError()
                            }
                        }, onCheckRecord = {
                            item.link.tryToLink()
                        })

                    is DWidgetData -> {
                        DWidget(item.ddCount, "说明：钻石可用于App内购买卡片背景，装扮，礼物等道具".localized, onCharge = {
                            nav.push(Route.Recharge)
                        }, onClickRecord = {
                            nav.push(Route.JaDiamondRecord)
                        })
                    }

                    is Distribution ->
                        InviteWidget(item.title, item.content, item.button) {
                            item.link.tryToLink()
                        }

                    is InputInviteCodeBanner -> {
                        NetworkImage(
                            item.banner,
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .aspectRatio(344 / 134f)
                                    .clickable(onClick = {
                                        dc.easyPost {
                                            InputInviteCodeContent(item.text) { code ->
                                                lm.show(null) {
                                                    viewModel
                                                        .fillInviteCode(code)
                                                        .onSuccess {
                                                            dismiss()
                                                        }
                                                }
                                            }
                                        }
                                    }),
                        )
                    }

                    is MissionWidgetData -> {
                        BoostMissionItem(item.missionInfo, onClick = { task ->
                            lm.show(null) {
                                viewModel
                                    .finishTask(task.id)
                                    .onSuccess { obj ->
                                        obj.parseValue<String>("jump_link")?.also { link ->
                                            link.toLink()
                                        }
                                    }
                            }
                        }, onAlert = { msg ->
                            dc.easyPost {
                                SingleActionDialog(content = msg, buttonConfig = DialogButtonStyles.Primary.copy("我知道了".localized)) {
                                    dismiss()
                                }
                            }
                        })
                    }

                    is PWidgetData -> {
                        PWidget(
                            item.pCount,
                            "说明：积分通过做任务获得，可用于兑换App内的道具".localized,
                            item.excTxt,
                            qaVisible = item.showQA,
                            recordVisible = item.showRecord,
                            onClickQA = {
                                dc.easyPost {
                                    SingleActionDialog(
                                        content = item.qaContent,
                                        buttonConfig = DialogButtonStyles.Primary.copy("我知道了".localized),
                                    ) {
                                        dismiss()
                                    }
                                }
                            },
                            onGetTool = {
                                if (item.canExcDiamond) {
                                    dc.easyPost {
                                        SimpleDoubleActionDialog(
                                            "请确认兑换装扮或兑换钻石？".localized,
                                            cancelButtonConfig =
                                                DialogButtonStyles.Primary.copy(
                                                    text = "兑换装扮".localized,
                                                    backgroundColor = SolidColor(WakooBlack),
                                                    textColor = WakooGreen,
                                                ),
                                            confirmButtonConfig = DialogButtonStyles.Primary.copy("兑换钻石".localized),
                                            onCancel = {
                                                nav.push(Route.DressUpSample)
                                                dismiss()
                                            },
                                            onConfirm = {
                                                // 兑换钻石
                                                dismiss()
                                                showExchangeDialog(false)
                                            },
                                        )
                                    }
                                } else {
                                    nav.push(Route.DressUpSample)
                                }
                            },
                            onClickExca = {
                                showExchangeDialog(true)
                            },
                            onClickRecord = {
                                nav.push(Route.PointsRecord)
                            },
                        )
                    }

                    is SignInWidgetData -> {
                        BoostSignWidget(item.missionSeries) {
                            dc.easyPost {
                                DailySignInDialog(item.missionSeries, onSign = {
                                    dismiss()
                                    lm.show(null) {
                                        viewModel
                                            .makeSign(
                                                item.missionSeries.seriesId.toString(),
                                            ).onSuccess { obj ->
                                                val title = obj.parseValue<String>("award_info").orEmpty()
                                                val message = obj.parseValue<String>("message").orEmpty()
                                                val awardType = obj.parseValue<Int>("award_type") ?: awardType
                                                dc.easyPost {
                                                    MissionCompleteContent(title, message, awardType) {
                                                        dismiss()
                                                    }
                                                }
                                            }.toastWhenError()
                                    }
                                }) {
                                    dismiss()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun rememberBoostItems(
    type: Int,
    state: BoostState,
    userInfo: SelfUserInfo,
): List<BoostPageWidgetData> =
    remember(type, state, userInfo.pt, userInfo.cash, userInfo.balance) {
        val config = state.config
        buildList {
            if (config.isShowInviteCodeBanner && config.inviteCodeAwardText.isNotEmpty()) {
                add(InputInviteCodeBanner(config.inviteCodeBannerUrl, config.inviteCodeAwardText))
            }
            if (type == AwardType.dia) {
                add(DWidgetData(userInfo.balance.toString()))
            }
            if (type == AwardType.inc) {
                add(
                    PWidgetData(
                        userInfo.pt,
                        config.isShowGainPointRuleEntry,
                        true,
                        config.exchangeJpyBtn,
                        config.gainPointRuleContent,
                        config.extractHistoryUrl,
                        config.canExchangeCoin,
                    ),
                )
                if (config.isShowExtract) {
                    add(CWidgetData(userInfo.cash, config.myExtract, config.extractBtn, config.extractHistoryUrl))
                }
                if (config.isShowDistribution) {
                    add(
                        Distribution(
                            link = config.distributionUrl,
                            title = config.distributionTitle,
                            content = config.distributionContent,
                            button = config.distributionBtn,
                        ),
                    )
                }
            }
            val s = state.signInfo
            if (s != null) {
                add(SignInWidgetData(s))
            }
            val m = state.missions
            if (m != null) {
                addAll(
                    m.missionSeriesList
                        .filter { it.seriesType != MissionInfo.MissionSeries.SERIES_TYPE_SIGN }
                        .map { MissionWidgetData(it) },
                )
            }
        }
    }
