package com.buque.wakoo.ui.screens.messages

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.LocalActivity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideIn
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LifecycleResumeEffect
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.GreenCircleChecked
import com.buque.wakoo.ui.icons.NotificationAlert
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.utils.rememberPermissionLauncher

private val bgGreen = Color(0xFFC8FFC9)

@Composable
fun NtfAlertBar(onClick: OnAction, onClose: OnAction) {
    Row(
        modifier = Modifier
            .background(bgGreen)
            .padding(16.dp, 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(WakooIcons.NotificationAlert, contentDescription = "alert", tint = Color(0xFF1A7D1D))
        SizeWidth(4.dp)
        Text("打开消息通知，不错过好友消息".localized, color = Color(0xFF1A7D1D), fontSize = 12.sp, modifier = Modifier.weight(1f))
        SolidButton("打开通知".localized, onClick = onClick, backgroundColor = WakooText, textColor = bgGreen, height = 26.dp, fontSize = 10.sp)
        SizeWidth(10.dp)
        Icon(WakooIcons.Close, contentDescription = "close", modifier = Modifier.click(onClick = onClose), tint = WakooGrayText)
    }
    SizeHeight(10.dp)
}

@Composable
fun NtfFullScreenAlert(modifier: Modifier = Modifier, onOpen: OnAction = {}, onSkip: OnAction = {}) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(bgGreen)
            .padding(horizontal = 16.dp)
            .systemBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        SizeHeight(34.dp)
        val density = LocalDensity.current
        val lineHeight = with(density) {
            14.dp.roundToPx().toFloat()
        }
        Text(
            "打开消息通知".localized,
            modifier = Modifier
                .drawBehind {
                    drawRect(
                        Brush.linearGradient(listOf(Color(0xFFA3FF2C), Color(0xFF31FFA1))),
                        size = size.copy(height = lineHeight),
                        topLeft = Offset(0f, size.height - lineHeight)
                    )
                }
                .padding(horizontal = 18.dp),
            fontSize = 20.sp, color = WakooText, lineHeight = 22.sp, fontWeight = FontWeight.ExtraBold
        )
        SizeHeight(15.dp)
        Text("\uD83D\uDE04${"不错过每一条好友消息".localized}\uD83D\uDE04", color = WakooText)
        Weight()

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White.copy(alpha = 0.5f), RoundedCornerShape(25.dp))
                .padding(4.dp)
                .background(Color.White, RoundedCornerShape(25.dp))
                .padding(14.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(R.drawable.icon_bell),
                contentDescription = "bell",
                modifier = Modifier.size(60.dp),
                contentScale = ContentScale.FillWidth
            )
            SizeHeight(16.dp)
            Text("打开消息通知".localized, color = Color(0xFF00CC07))
            SizeHeight(8.dp)
            Text("不错过任何一条消息".localized, color = WakooText)
            SizeHeight(20.dp)
            Row(modifier = Modifier.fillMaxWidth()) {
                val mod = Modifier.size(90.dp, 162.dp)
                Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.CenterHorizontally) {
                    Image(
                        painter = painterResource(R.drawable.img_screen),
                        contentDescription = null,
                        contentScale = ContentScale.FillWidth,
                        modifier = mod
                    )
                    SizeHeight(12.dp)
                    Text("锁定屏幕".localized, color = WakooGrayText)
                    SizeHeight(12.dp)
                    Image(
                        WakooIcons.GreenCircleChecked,
                        contentDescription = "",
                        modifier = Modifier.size(22.dp),
                        contentScale = ContentScale.FillWidth
                    )
                }
                Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.CenterHorizontally) {
                    Image(
                        painter = painterResource(R.drawable.img_noti),
                        contentDescription = null,
                        contentScale = ContentScale.FillWidth,
                        modifier = mod
                    )
                    SizeHeight(12.dp)
                    Text("通知中心".localized, color = WakooGrayText)
                    SizeHeight(12.dp)
                    Image(
                        WakooIcons.GreenCircleChecked,
                        contentDescription = "",
                        modifier = Modifier.size(22.dp),
                        contentScale = ContentScale.FillWidth
                    )
                }
            }
            SizeHeight(20.dp)
        }

        Weight()
        SolidButton(
            "打开通知".localized,
            onClick = onOpen,
            backgroundColor = WakooText,
            textColor = Color(0xFFA3FF2C),
            modifier = Modifier.fillMaxWidth()
        )
        Text(
            "跳过".localized, fontSize = 16.sp, lineHeight = 44.sp, modifier = Modifier
                .click(onClick = onSkip)
                .padding(horizontal = 20.dp)
        )
        SizeHeight(16.dp)
    }
}

@Preview
@Composable
private fun AlertPreview() {
    NtfFullScreenAlert { }
}

@Preview
@Composable
private fun AlertBarPreview() {
    NtfAlertBar({}) { }
}

@Composable
fun rememberNtfState(key: Any? = null) = run {
    val context = LocalContext.current
    val state = remember() { mutableStateOf(false) }
    LifecycleResumeEffect(context, key) {
        state.value = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(context, android.Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
        onPauseOrDispose { }
    }
    state
}

@Composable
fun refPermissionReqCaller() = run {
    val context = LocalActivity.current
    var callbackDenied by remember {
        mutableStateOf({})
    }
    var callbackGranted by remember {
        mutableStateOf({})
    }
    val launcher = rememberPermissionLauncher(onDenied = {
        callbackDenied.invoke()
    }) {
        callbackGranted.invoke()
    }
    val callRequestPermission: (onDenied: () -> Unit, onGranted: () -> Unit) -> Unit = remember {
        { onDenied, onGranted ->
            val activity = context
            callbackDenied = onDenied
            callbackGranted = onGranted
            if (activity != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    val permission = android.Manifest.permission.POST_NOTIFICATIONS
                    val granted =
                        ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
                    if (!granted) {
                        val showRationale = ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
                        if (showRationale) {
                            activity.startActivity(getNotificationIntent(activity))
                        } else {
                            launcher.launch(arrayOf(permission))
                        }
                    }
                } else {
                    activity.startActivity(getNotificationIntent(activity))
                }
            }
        }
    }
    callRequestPermission
}

private fun getNotificationIntent(context: Context) = Intent().apply {
    val packageName = context.packageName
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
        putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
    } else {
        action = "android.settings.APP_NOTIFICATION_SETTINGS"
        putExtra("app_package", packageName)
        putExtra("app_uid", context.applicationInfo.uid)
    }
}


@Composable
fun NtfAlertState() {
    var key by remember { mutableIntStateOf(0) }
    val enable by rememberNtfState(key)
    val visible by AppConfigManager.alertNtfVisibleState
    if (!enable && visible) {
        val caller = refPermissionReqCaller()
        NtfAlertBar(onClick = {
            caller({}, {
                key += 1
            })
        }) {
            AppConfigManager.closeNtfAlert()
        }
    }
}

@Composable
fun NtfFullScreenAlertState() {
    var key by remember { mutableIntStateOf(0) }
    var visible by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        val enterKey = "first_enter_message_page"
        visible = currentUserKV.getBoolean(enterKey, true)
        if (visible) {
            currentUserKV.putBoolean(enterKey, false)
        }
    }
    val enable by rememberNtfState(key)
    val caller = refPermissionReqCaller()
    AnimatedVisibility(!enable && visible, enter = slideIn { size ->
        IntOffset(0, size.height)
    }) {
        NtfFullScreenAlert(onOpen = {
            caller.invoke({}, { key += 1 })
        }, onSkip = {
            visible = false
        })
    }
}