package com.buque.wakoo.ui.widget.media.manager

import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.referentialEqualityPolicy
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.IntSize
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import kotlinx.serialization.Serializable

@Serializable
sealed class PlayMediaItem {
    // 需要播放资源的地址，可以是远程url，也可以是uriString
    abstract val source: String

    // tag是一个真正意义上的唯一标识(建议使用)，一般是url加上使用环境拼接而成（因为考虑到同一个url可能会同时存在不同环境，使用唯一tag可以更精确控制，特别是停止播放）
    abstract val tag: String

    // 当前播放位置
    var position by mutableLongStateOf(0)

    var duration by mutableLongStateOf(-1)

    var isActuallyPlaying by mutableStateOf(false)

    var isBuffering by mutableStateOf(false)

    private var player by mutableStateOf<Player?>(
        value = null,
        policy = referentialEqualityPolicy(),
    )

    var playWhenReady = false
        private set

    val realPlayer by derivedStateOf {
        player?.takeIf {
            playWhenReady // 主要是为了感知currentMediaItem的变化
            it.currentMediaItem?.mediaId == tag
        }
    }

    val isLive: Boolean = this is AudioLive

    val isAudio: Boolean = this is Audio

    val isVideo: Boolean = this is Video

    val exoPlayerMediaItem by lazy {
        MediaItem
            .Builder()
            .setUri(source)
            .setMediaId(tag)
            .build()
    }

    fun bindPlayer(player: Player) {
        this.player = player
    }

    fun releasePlayer() {
        pause()
        this.player = null
    }

    fun play(seek: Boolean, overridePosition: Long? = null) {
        playWhenReady = true
        requireNotNull(realPlayer).apply {
            if (!isPlayerPrepared()) {
                prepare()
            }
            if (seek) {
                if (isLive) {
                    seekToDefaultPosition()
                } else {
                    seekTo(overridePosition ?: if (isAudio) 0 else position)
                }
            }
            playWhenReady = true
        }
    }

    fun pause(overridePosition: Long? = null) {
        playWhenReady = false
        realPlayer?.apply {
            if (isLive) {
                stop()
            } else {
                pause()
            }
            position = overridePosition ?: currentPosition
        }
    }

    fun recordCurrentPosition() {
        realPlayer?.apply {
            position = currentPosition
        }
    }

    fun isSameItem(item: PlayMediaItem?) = item != null && item::class == this::class && item.source == source && item.tag == tag

    private fun Player.isPlayerPrepared(): Boolean {
        return playbackState == Player.STATE_BUFFERING ||
            playbackState == Player.STATE_READY ||
            playbackState == Player.STATE_ENDED
    }

    @Stable
    @Serializable
    data class Audio(
        override val source: String, // 需要播放资源的地址，可以是远程url，也可以是uriString
        override val tag: String,
    ) : PlayMediaItem()

    @Stable
    @Serializable
    data class Video(
        override val source: String, // 需要播放资源的地址，可以是远程url，也可以是uriString
        override val tag: String,
    ) : PlayMediaItem() {
        var alignment by mutableStateOf(Alignment.Center)

        var contentScale by mutableStateOf(ContentScale.Fit)

        var renderedFirstFrame by mutableStateOf(false)

        var videoSize by mutableStateOf(IntSize.Zero)

        var isActive by mutableStateOf(false)

        var videoStill by mutableStateOf<ImageBitmap?>(
            value = null,
            policy = referentialEqualityPolicy(),
        )

        val canShowVideo
            get() = realPlayer != null

        val canShowStill
            get() = videoSize == IntSize.Zero || !renderedFirstFrame || realPlayer.let { it == null || it.playbackState == Player.STATE_IDLE }

    }

    @Stable
    @Serializable
    data class AudioLive(
        override val source: String, // 需要播放资源的地址，可以是远程url，也可以是uriString
        override val tag: String,
    ) : PlayMediaItem()

    companion object {
        fun audio(
            url: String,
            tag: String = url,
        ): Audio =
            Audio(
                source = url,
                tag = tag,
            )

        fun prefixTagAudio(
            url: String,
            prefix: String,
        ): Audio =
            Audio(
                source = url,
                tag = "$prefix-$url",
            )

        fun video(
            url: String,
            tag: String = url,
        ): Video =
            Video(
                source = url,
                tag = tag,
            )

        fun prefixTagVideo(
            url: String,
            prefix: String,
        ): Video =
            Video(
                source = url,
                tag = "$prefix-$url",
            )

        fun liveAudio(
            url: String,
            tag: String = url,
        ): AudioLive =
            AudioLive(
                source = url,
                tag = tag,
            )

        fun prefixTagLiveAudio(
            url: String,
            prefix: String,
        ): AudioLive =
            AudioLive(
                source = url,
                tag = "$prefix-$url",
            )
    }
}