package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSerializable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomRole
import com.buque.wakoo.ui.widget.GradientButton

@Composable
fun DialogScope.LiveRoomModeSettingsPanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
    onBack: () -> Unit = {},
) {
    val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)
    if (selfRole == RoomRole.Member) {
        // 失去管理员权限
        LaunchedEffect(Unit) {
            dismiss()
        }
    }

    BottomPanelScaffold(
        title = "房间模式设置".localized,
        useClose = false,
        modifier = modifier,
        contentPadding = 0.dp,
        onBack = onBack,
    ) {
        var selectMode by rememberSerializable {
            mutableStateOf(roomInfoState.basicInfo.roomMode)
        }

        RadioGroupItem("闲聊模式".localized, selectMode == LiveRoomMode.Normal) {
            selectMode = LiveRoomMode.Normal
        }

        val isJP = LocalSelfUserProvider.current.isJP

        if (!isJP) {
            RadioGroupItem("电台模式".localized, selectMode == LiveRoomMode.Radio) {
                selectMode = LiveRoomMode.Radio
            }
        }
        RadioGroupItem("PK模式".localized, selectMode == LiveRoomMode.PKMode) {
            selectMode = LiveRoomMode.PKMode
        }
        Spacer(modifier = Modifier.height(12.dp))

        GradientButton(
            text = "确认修改".localized,
            onClick = {
                dismiss()
                roomInfoState.sendEvent(RoomEvent.UpdateRoomMode(selectMode.value.toString()))
            },
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .width(320.dp),
        )
    }
}

@Composable
fun DialogScope.LiveMicModeSettingsPanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
    onBack: () -> Unit = {},
) {
    val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)
    if (selfRole == RoomRole.Member) {
        // 失去管理员权限
        LaunchedEffect(Unit) {
            dismiss()
        }
    }
    BottomPanelScaffold(
        title = "上麦模式设置".localized,
        useClose = false,
        modifier = modifier,
        contentPadding = 0.dp,
        onBack = onBack,
    ) {
        var selectMode by rememberSerializable {
            mutableStateOf(roomInfoState.basicInfo.micMode)
        }

        RadioGroupItem("自由上麦".localized, selectMode == LiveMicMode.Free) {
            selectMode = LiveMicMode.Free
        }

        val isJP = LocalSelfUserProvider.current.isJP

        if (!isJP) {
            RadioGroupItem("申请上麦".localized, selectMode == LiveMicMode.Request) {
                selectMode = LiveMicMode.Request
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        GradientButton(
            text = "确认修改".localized,
            onClick = {
                dismiss()
                roomInfoState.sendEvent(RoomEvent.UpdateMicMode(selectMode.value.toString()))
            },
            modifier =
                Modifier
                    .align(Alignment.CenterHorizontally)
                    .width(320.dp),
        )
    }
}

@Composable
private fun RadioGroupItem(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .clickable(onClick = onClick)
                .padding(horizontal = 16.dp, vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(text = text, style = MaterialTheme.typography.bodyMedium, color = Color(0xFF111111))
        RadioButton(
            selected = selected,
            modifier = Modifier.size(16.dp),
            onClick = onClick,
            colors =
                RadioButtonDefaults.colors(
                    selectedColor = Color(0xFF66FE6B),
                    unselectedColor = Color(0xFFE9EAEF),
                ),
        )
    }
}
