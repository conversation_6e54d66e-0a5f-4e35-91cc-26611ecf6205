package com.buque.wakoo.ui.widget.media.previewer

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.SnapSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.surfaceColorAtElevation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableFloatState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.buque.wakoo.ext.animatableClipToScreenRect
import com.buque.wakoo.ui.icons.MediaPlayer
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.media.VideoMediaView
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerControllerPool
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.pause
import com.buque.wakoo.ui.widget.media.manager.toggle
import com.buque.wakoo.ui.widget.media.previewer.flick.FlickToDismiss
import com.buque.wakoo.ui.widget.media.previewer.flick.FlickToDismissState
import com.buque.wakoo.ui.widget.media.previewer.flick.rememberFlickToDismissState
import kotlinx.coroutines.flow.collectLatest
import me.saket.telephoto.ExperimentalTelephotoApi
import me.saket.telephoto.zoomable.coil3.ZoomableAsyncImage
import me.saket.telephoto.zoomable.rememberZoomableImageState
import me.saket.telephoto.zoomable.rememberZoomableState
import me.saket.telephoto.zoomable.zoomable
import kotlin.math.abs

@Composable
@OptIn(ExperimentalMaterial3Api::class)
internal fun MediaPreviewViewer(
    state: MediaPreviewState,
    overlayClipRect: Rect?,
    modifier: Modifier = Modifier,
) {
    val pool =
        remember {
            MediaPlayerControllerPool(3, MediaPlayerManager.DefaultMediaPlayerController())
        }

    DisposableEffect(pool) {
        onDispose {
            pool.cleanUp()
        }
    }

    val currentState = state.transitionState as TransitionState.Transiting
    val key = currentState.key
    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        val pagerState =
            rememberPagerState(
                initialPage = key.initialIndex,
                pageCount = { key.album.items.size },
            )

        state.currentPageIndex = { pagerState.currentPage }

        val titleAlphaState =
            remember {
                mutableFloatStateOf(1f)
            }

        val clipped by remember {
            derivedStateOf {
                val currentState = state.transitionState
                currentState is TransitionState.Enter && currentState.anim < AnimState.Finished
            }
        }

        val backgroundAlphaState =
            remember {
                mutableFloatStateOf(0f)
            }

        val animatedAlpha by animateFloatAsState(
            targetValue = backgroundAlphaState.floatValue,
            animationSpec =
                if (currentState is TransitionState.Enter && currentState.anim == AnimState.End) {
                    spring()
                } else {
                    tween(250)
                },
            label = "Background alpha",
        )

        LaunchedEffect(pagerState) {
            // 自动播放
            snapshotFlow {
                pagerState.currentPage
            }.collectLatest {
                MediaPlayerManager.onPageSelected(it)
            }
        }

        HorizontalPager(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(
                        MaterialTheme.colorScheme
                            .surfaceColorAtElevation(2.dp)
                            .copy(alpha = animatedAlpha),
                    ).run {
                        if (overlayClipRect != null) {
                            animatableClipToScreenRect(clipped, overlayClipRect)
                        } else {
                            this
                        }
                    },
            state = pagerState,
            beyondViewportPageCount = 1,
            userScrollEnabled = currentState is TransitionState.Enter && currentState.anim == AnimState.End,
        ) { page ->
            MediaPage(
                page = page,
                state = state,
                titleAlphaState = titleAlphaState,
                backgroundAlphaState = backgroundAlphaState,
                pool = pool,
                modifier = Modifier.fillMaxSize(),
                model = key.album.items[page],
                isActivePage = pagerState.settledPage == page,
            )
        }

        var visibleState by remember {
            mutableStateOf(false)
        }

        LaunchedEffect(Unit) {
            snapshotFlow {
                val currentState = state.transitionState
                currentState is TransitionState.Enter && currentState.anim >= AnimState.Running
            }.collect {
                visibleState = it
            }
        }

        AnimatedVisibility(
            visible = visibleState,
            enter = fadeIn() + slideInVertically(),
            exit = fadeOut() + slideOutVertically(),
        ) {
            CenterAlignedTopAppBar(
                modifier = Modifier.alpha(titleAlphaState.floatValue),
                title = {
                    Box(
                        modifier =
                            Modifier
                                .background(MaterialTheme.colorScheme.background.copy(alpha = 0.4f), CircleShape)
                                .padding(horizontal = 10.dp, vertical = 6.dp),
                    ) {
                        Text(
                            text = "${pagerState.currentPage + 1}/${key.album.items.size}",
                            style = MaterialTheme.typography.titleMedium,
                        )
                    }
                },
                navigationIcon = { CloseNavIconButton() },
                colors = TopAppBarDefaults.topAppBarColors(containerColor = Color.Transparent),
            )
        }
    }
}

@Composable
private fun CloseNavIconButton() {
    val backDispatcher = LocalOnBackPressedDispatcherOwner.current!!.onBackPressedDispatcher
    IconButton(
        onClick = { backDispatcher.onBackPressed() },
        colors = titleBarIconButtonColors(),
    ) {
        Icon(Icons.Rounded.Close, contentDescription = "Go back")
    }
}

@Composable
private fun titleBarIconButtonColors() =
    IconButtonDefaults.iconButtonColors(
        containerColor = MaterialTheme.colorScheme.background.copy(alpha = 0.4f),
    )

@Composable
@OptIn(ExperimentalTelephotoApi::class)
private fun MediaPage(
    page: Int,
    state: MediaPreviewState,
    titleAlphaState: MutableFloatState,
    backgroundAlphaState: MutableFloatState,
    pool: MediaPlayerControllerPool,
    model: MediaViewerItem,
    isActivePage: Boolean,
    modifier: Modifier = Modifier,
) {
    val zoomableState = rememberZoomableState()

    val flickState = rememberFlickToDismissState(rotateOnDrag = false)

    if (isActivePage) {
        state.currentPageOffset = { flickState.offset }
    }

    CloseScreenOnFlickDismissEffect(flickState)

    LaunchedEffect(flickState) {
        snapshotFlow {
            flickState.gestureState
        }.collect {
            titleAlphaState.floatValue = if (it is FlickToDismissState.GestureState.Dragging) 0f else 1f
        }
    }

    LaunchedEffect(flickState) {
        snapshotFlow {
            val currentState = state.transitionState
            if (currentState is TransitionState.Enter) {
                if (currentState.anim == AnimState.End) {
                    val flickGestureState = flickState.gestureState
                    when (flickGestureState) {
                        is FlickToDismissState.GestureState.Dismissed,
                        is FlickToDismissState.GestureState.Dismissing,
                        -> 0f

                        is FlickToDismissState.GestureState.Dragging ->
                            if (flickGestureState.willDismissOnRelease) {
                                0f
                            } else {
                                1f - (abs(flickGestureState.offsetFraction) / 0.2f)
                            }

                        is FlickToDismissState.GestureState.Idle,
                        is FlickToDismissState.GestureState.Resetting,
                        -> 1f
                    }
                } else if (currentState.anim == AnimState.Ready) {
                    0f
                } else {
                    1f
                }
            } else {
                0f
            }
        }.collect {
            backgroundAlphaState.floatValue = it
        }
    }

    val currentState = state.transitionState as TransitionState.Transiting

    FlickToDismiss(
        state = flickState,
        modifier = modifier,
    ) {
        Box(
            modifier =
                Modifier.alpha(
                    if ((currentState is TransitionState.Enter && currentState.anim >= AnimState.Finished) ||
                        (currentState is TransitionState.Exit && currentState.anim == AnimState.Ready)
                    ) {
                        1f
                    } else {
                        0f
                    },
                ),
        ) {
            when (model) {
                is MediaViewerItem.Image -> {
                    val imageState = rememberZoomableImageState(zoomableState)
                    ZoomableAsyncImage(
                        modifier = Modifier.fillMaxSize(),
                        state = imageState,
                        model =
                            ImageRequest
                                .Builder(LocalContext.current)
                                .data(model.localFilePath ?: model.fullSizedUrl)
                                .placeholderMemoryCacheKey(model.localFilePath ?: model.placeholderImageUrl)
                                .crossfade(300)
                                .build(),
                        contentDescription = null,
                    )

                    AnimatedVisibility(
                        modifier = Modifier.align(Alignment.Center),
                        visible = !imageState.isImageDisplayed,
                    ) {
                        CircularProgressIndicator(color = Color.White)
                    }
                }

                is MediaViewerItem.Video -> {
                    val playItem = model.playMediaItem

                    val controller =
                        remember(pool, playItem) {
                            pool.acquire().also {
                                it.prepare(playItem)
                            }
                        }

                    val lifecycleOwner = LocalLifecycleOwner.current

                    DisposableEffect(pool, playItem) {
                        val observer =
                            LifecycleEventObserver { source, event ->
                                if (event == Lifecycle.Event.ON_STOP) {
                                    controller.pause(playItem)
                                }
                            }

                        lifecycleOwner.lifecycle.addObserver(observer)
                        MediaPlayerManager.addPreload(playItem, page)
                        onDispose {
                            lifecycleOwner.lifecycle.removeObserver(observer)
                            MediaPlayerManager.removePreload(playItem)
                            playItem.releasePlayer()
                            pool.release(controller)
                        }
                    }

                    Box {
                        VideoMediaView(
                            state = playItem,
                            modifier =
                                Modifier
                                    .fillMaxSize()
                                    .zoomable(state = zoomableState, onClick = {
                                        controller.toggle(playItem)
                                    }),
                        )

                        if (!playItem.isActuallyPlaying) {
                            Image(
                                imageVector = WakooIcons.MediaPlayer,
                                contentDescription = null,
                                modifier =
                                    Modifier
                                        .align(Alignment.Center)
                                        .size(80.dp),
                            )
                        }
                    }

                    LaunchedEffect(playItem.tag, isActivePage) {
                        if (isActivePage) {
                            controller.play(playItem)
                        } else {
                            controller.pause(playItem)
                        }
                    }
                }
            }
        }
    }

    if (flickState.gestureState is FlickToDismissState.GestureState.Dragging) {
        LaunchedEffect(Unit) {
            zoomableState.resetZoom()
        }
    }
    if (!isActivePage) {
        LaunchedEffect(Unit) {
            zoomableState.resetZoom(animationSpec = SnapSpec())
        }
    }
}

@Composable
private fun CloseScreenOnFlickDismissEffect(flickState: FlickToDismissState) {
    val gestureState = flickState.gestureState

    if (gestureState is FlickToDismissState.GestureState.Dismissing) {
        val backDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
        LaunchedEffect(Unit) {
            backDispatcher?.onBackPressed()
        }
    }
}
