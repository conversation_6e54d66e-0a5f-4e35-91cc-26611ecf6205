package com.buque.wakoo.ui.screens.chatgroup.panel

import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SingleActionDialog
import com.buque.wakoo.ui.theme.WakooTheme

@Composable
fun DialogScope.ChatGroupTipDialog(
    content: String = "您的群组已被解散".localized,
    buttonText: String = "知道了".localized,
    onClick: OnAction = {}
) {
    SingleActionDialog(
        content = content,
        buttonConfig = DialogButtonStyles.Primary.copy(text = buttonText),
        onButtonClick = {
            onClick()
            dismiss()
        },
    )
}

@Preview
@Composable
private fun PreviewEditGroupInfoPanel2() {
    WakooTheme {
        DialogController.preview.apply {
            ChatGroupTipDialog()
        }
    }
}
