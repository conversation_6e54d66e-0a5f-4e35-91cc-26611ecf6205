package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.bean.IconLabel
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.im_business.viewmodel.C2CChatViewModel
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.icons.AddRelations
import com.buque.wakoo.ui.icons.At
import com.buque.wakoo.ui.icons.ChatC2c
import com.buque.wakoo.ui.icons.DownMic
import com.buque.wakoo.ui.icons.UpMic
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomRole
import com.buque.wakoo.ui.screens.messages.chat.C2CChatScreen
import com.buque.wakoo.ui.screens.messages.chat.TargetUserCPCard
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.UserLevelWidget
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage

// @TA
private const val MENU_TYPE_AT_USER = 1

// 私信
private const val MENU_TYPE_C2C_MESSAGE = 2

// 邀请上麦
private const val MENU_TYPE_INVITE_MIC = 3

// 抱下麦
private const val MENU_TYPE_REMOVE_MIC = 4

// 关注
private const val MENU_TYPE_FOLLOWED = 5

// 更多
private const val MENU_TYPE_MORE = 6

@Composable
fun DialogScope.LiveRoomUserInfoPanel(
    user: User,
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val rootNavController = LocalAppNavController.root

    val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)
    val targetRole by roomInfoState.rememberRoomRoleState(user.id)
    val targetInMic by roomInfoState.rememberInMicState(user.id)
    val targetMember = roomInfoState.trackRoomMember(user)
    val uiConfig by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()
    Box(
        modifier = modifier.fillMaxWidth(),
    ) {
        Spacer(
            modifier = Modifier
                .padding(top = 28.dp)
                .matchParentSize()
                .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .background(WakooWhite)
                .background(
                    brush = Brush.verticalGradient(
                        0f to Color(0xFFD6FFD7),
                        0.3f to Color(0x00D6FFD7),
                    ),
                ),
        )
        Column(
            modifier = Modifier
                .animateContentSize()
                .fillMaxWidth()
                .heightIn(min = 240.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            ) {
                if (!user.isSelf) {
                    Text(
                        text = "举报".localized,
                        style = MaterialTheme.typography.labelLarge,
                        color = WakooSecondarySelected,
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .padding(top = 10.dp)
                            .clickable {
                                rootNavController.push(Route.Report(1, user.id))
                            },
                    )
                }

                Box(modifier = Modifier.align(Alignment.Center)) {
                    AvatarNetworkImage(
                        user = targetMember.roomUser,
                        size = 80.dp,
                        modifier = Modifier
                            .padding(bottom = 4.dp)
                            .align(Alignment.Center)
                            .border(1.5.dp, color = WakooWhite, CircleShape),
                    )

                    if (!uiConfig.partnerHasEscaped && targetMember.roomUser.cpRelationInfo?.publicCp != null && !LocalSelfUserProvider.isJP) {
                        NetworkImage(
                            data = targetMember.roomUser.cpRelationInfo.publicCpMedalNormalUrl,
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .size(64.dp, 20.dp),
                        )
                    }
                }

                Text(
                    text = "查看主页".localized,
                    style = MaterialTheme.typography.labelLarge,
                    color = WakooSecondarySelected,
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(top = 10.dp)
                        .clickable {
                            rootNavController.push(Route.UserProfile(targetMember.toBasic()))
                        },
                )
            }

            Column(
                modifier = Modifier.padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = targetMember.roomUser.name,
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color(0xFF111111),
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        textAlign = TextAlign.Center,
                    )

                    SizeWidth(4.dp)

                    GenderAgeTag(user = targetMember.roomUser)
                }

                SizeHeight(8.dp)

                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterHorizontally),
                    verticalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterVertically),
                    itemVerticalAlignment = Alignment.CenterVertically,
                ) {
                    if (targetMember.roomUser.isVip) {
                        VipCrownTag()
                    }
                    if (LocalSelfUserProvider.isJP) {
                        ExpLevelWidget(targetMember.roomUser.decorations)
                    } else {
                        UserLevelWidget(targetMember.roomUser.level)
                    }
                }
            }

            if (user.isSelf) {
                GradientButton(
                    text = "查看我的主页".localized,
                    onClick = {
                        rootNavController.push(Route.UserProfile(targetMember.toBasic()))
                    },
                    modifier = Modifier.padding(bottom = 20.dp),
                    fontSize = 14.sp,
                    height = 40.dp,
                )
                return
            }

            if (!uiConfig.partnerHasEscaped && targetMember.roomUser.cpRelationInfo?.publicCp != null) {
                TargetUserCPCard(
                    targetUser = targetMember.roomUser.user,
                    cpInfo = targetMember.roomUser.cpRelationInfo,
                    modifier = Modifier.padding(horizontal = 16.dp),
                )
            } else {
                SizeHeight(0.dp)
            }

            GradientButton(
                text = "送礼物".localized,
                onClick = {
                    roomInfoState.sendEvent(RoomEvent.OpenGiftPanel(onlyUser = targetMember.roomUser))
                },
                minWidth = 215.dp,
                height = 40.dp,
            )

            val isFollowed by remember(user.id) {
                derivedStateOf {
                    roomInfoState.requireRoomUser(user).isFollowed
                }
            }

            val buttonItems by remember {
                derivedStateOf {
                    buildList {
                        add(IconLabel(MENU_TYPE_AT_USER, WakooIcons.At, "TA".localized))
                        if (selfRole == RoomRole.Member && !isFollowed) {
                            add(IconLabel(MENU_TYPE_FOLLOWED, WakooIcons.AddRelations, "关注".localized))
                        }
                        add(IconLabel(MENU_TYPE_C2C_MESSAGE, WakooIcons.ChatC2c, "私信".localized))
                        if (selfRole != RoomRole.Member) {
                            if (targetInMic && selfRole.value < targetRole.value) {
                                add(
                                    IconLabel(
                                        MENU_TYPE_REMOVE_MIC,
                                        WakooIcons.DownMic,
                                        "抱下麦".localized,
                                        iconTint = WakooSecondarySelected,
                                    ),
                                )
                            } else {
                                add(
                                    IconLabel(
                                        MENU_TYPE_INVITE_MIC,
                                        WakooIcons.UpMic,
                                        "邀请上麦".localized,
                                        iconTint = WakooSecondarySelected,
                                    ),
                                )
                            }
                        }
                        if (selfRole != RoomRole.Member) {
                            add(IconLabel(MENU_TYPE_MORE, null, "更多".localized))
                        }
                    }
                }
            }

            val onClick = { item: IconLabel<*> ->
                when (item.id) {
                    MENU_TYPE_AT_USER -> {
                        dismiss()
                        roomInfoState.sendEvent(RoomEvent.At(user))
                    }

                    MENU_TYPE_FOLLOWED -> {
                        roomInfoState.sendEvent(RoomEvent.FollowUser(user.id))
                    }

                    MENU_TYPE_C2C_MESSAGE -> {
                        val user = targetMember.roomUser
                        roomInfoState.sendEvent(
                            RoomEvent.PanelDialog {
                                val viewModel = remember(user.id) {
                                    C2CChatViewModel(user)
                                }
                                Box(
                                    modifier = Modifier
                                        .fillMaxHeight(0.7f)
                                        .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
                                ) {
                                    C2CChatScreen(
                                        user = user,
                                        viewModel = viewModel,
                                        modifier = Modifier.consumeWindowInsets(
                                            WindowInsets.statusBars,
                                        ),
                                    )
                                }
                            },
                        )
                        dismiss()
                    }

                    MENU_TYPE_REMOVE_MIC -> {
                        roomInfoState.sendEvent(RoomEvent.KickMic(user.id))
                    }

                    MENU_TYPE_INVITE_MIC -> {
                        roomInfoState.sendEvent(RoomEvent.InviteUpMic(user.id))
                    }

                    MENU_TYPE_MORE -> {
                        dismiss()
                        roomInfoState.sendEvent(
                            RoomEvent.PanelDialog {
                                LiveRoomUserManagePanel(user, roomInfoState)
                            },
                        )
                    }
                }
            }

            Column {
                HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFE5E5E5))

                Row(
                    modifier = Modifier
                        .padding(horizontal = 8.dp)
                        .height(IntrinsicSize.Min),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    buttonItems.forEachIndexed { index, item ->
                        if (index > 0) {
                            VerticalDivider(
                                thickness = 0.5.dp,
                                color = Color(0xFFE5E5E5),
                                modifier = Modifier.fillMaxHeight(0.6f),
                            )
                        }
                        Row(
                            modifier = Modifier
                                .weight(1f)
                                .clickable(onClick = {
                                    onClick(item)
                                })
                                .padding(vertical = 16.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                        ) {
                            if (item.icon != null) {
                                Icon(
                                    imageVector = item.icon,
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp),
                                    tint = item.iconTint ?: WakooSecondarySelected,
                                )
                                SizeWidth(5.dp)
                            }
                            Text(
                                text = item.label,
                                style = MaterialTheme.typography.labelLarge,
                                color = WakooSecondarySelected,
                                fontWeight = FontWeight.Medium,
                            )
                        }
                    }
                }

                SizeHeight(25.dp)
            }
        }
    }
}
