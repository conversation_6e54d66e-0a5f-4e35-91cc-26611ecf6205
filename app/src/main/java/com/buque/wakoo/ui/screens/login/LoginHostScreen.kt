package com.buque.wakoo.ui.screens.login

import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.toast
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.GoogleManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavController
import com.buque.wakoo.navigation.AppNavDisplay
import com.buque.wakoo.navigation.CtrlKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.LoginRoute
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.appEntry
import com.buque.wakoo.navigation.appEntryProvider
import com.buque.wakoo.navigation.rememberAppNavController
import com.buque.wakoo.navigation.useRoot
import com.buque.wakoo.ui.dialog.ColumnDoubleActionDialog
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.viewmodel.LoginViewModel

object LoginNavCtrlKey : CtrlKey<AppNavController>

@Composable
fun LoginHostScreen() {
    val controller =
        rememberAppNavController(
            key = LoginNavCtrlKey,
            LoginRoute.Start,
        )
    val viewModel = viewModel<LoginViewModel>()
    val scope = rememberCoroutineScope()
    val loading = LocalLoadingManager.current
    val context = LocalContext.current
    LocalAppNavController.ProvideController(controller) {
        AppNavDisplay(
            backStack = controller.backStack,
            entryProvider =
                appEntryProvider {
                    appEntry<LoginRoute.Start> {
                        Box {
                            var recoverAccountData by viewModel.recoverState

                            LoginScreen(
                                onQuickLogin = {
                                    loading.show(scope) {
                                        viewModel.quickLogin()?.apply {
                                            if (forcePhoneLogin) {
                                                controller.push(LoginRoute.Phone)
                                            } else if (needRegister) {
                                                controller.push(LoginRoute.Fill)
                                            }
                                        }
                                    }
                                },
                                onPhoneLogin = {
                                    controller.push(LoginRoute.Phone)
                                },
                                onGoogleLogin = {
                                    loading.show(scope) {
                                        val result = GoogleManager.signIn(context)
                                        if (result.isSuccess) {
                                            viewModel.googleLogin(result.getOrThrow())?.apply {
                                                if (forcePhoneLogin) {
                                                    controller.push(LoginRoute.Phone)
                                                } else if (needRegister) {
                                                    controller.push(LoginRoute.Fill)
                                                }
                                            }
                                        } else {
                                            result.onFailure {
                                                it.toast()
                                                it.printStackTrace()
                                            }
                                        }
                                    }
                                },
                                onUserAgreementClick = {
                                    controller.useRoot?.push(
                                        Route.Web(
                                            url = "${EnvironmentManager.current.apiUrl}h5/agreement",
                                            title = "用户服务协议".localized,
                                            isRequiresLogin = false,
                                        ),
                                    )
                                },
                                onPrivacyPolicyClick = {
                                    controller.useRoot?.push(
                                        Route.Web(
                                            url = "${EnvironmentManager.current.apiUrl}h5/privacy",
                                            title = "隐私协议".localized,
                                            isRequiresLogin = false,
                                        ),
                                    )
                                },
                            )

                            if (recoverAccountData != null) {
                                Dialog(onDismissRequest = {
                                    recoverAccountData = null
                                }) {
                                    ColumnDoubleActionDialog(
                                        title = "恢复账号".localized,
                                        content = recoverAccountData?.title.orEmpty(),
                                        confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "取消登录".localized),
                                        cancelButtonConfig =
                                            DialogButtonStyles.Transparent.copy(
                                                text = "恢复账号".localized,
                                            ),
                                        onConfirm = {
                                            recoverAccountData = null
                                        },
                                        onCancel = {
                                            val tempData = recoverAccountData
                                            recoverAccountData = null
                                            loading.show(scope) {
                                                viewModel.recoverAccount(tempData)?.apply {
                                                    if (forcePhoneLogin) {
                                                        controller.push(LoginRoute.Phone)
                                                    } else if (needRegister) {
                                                        controller.push(LoginRoute.Fill)
                                                    }
                                                }
                                            }
                                        },
                                    )
                                }
                            }
                        }
                    }
                    appEntry(LoginRoute.Phone) {
                        PhoneLoginScreen(
                            onSendVerifyCode = {
                                viewModel.sendVerifyCode(it)
                            },
                            onLogin = { phone, code ->
                                loading.show(scope) {
                                    viewModel.phoneLogin(phone, code)?.apply {
                                        if (forcePhoneLogin) {
                                            controller.push(LoginRoute.Phone)
                                        } else if (needRegister) {
                                            controller.push(LoginRoute.Fill)
                                        }
                                    }
                                }
                            },
                        )
                    }
                    appEntry<LoginRoute.Fill> {
                        RegisterFillInfoScreen(EnvironmentManager.isGoogleChannel) { request ->
                            loading.show(scope) {
                                viewModel.register(request)
                            }
                        }
                    }
                },
        )
    }
}
