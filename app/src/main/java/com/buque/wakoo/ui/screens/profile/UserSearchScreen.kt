package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.Search
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.StandardScreenScaffold
import com.buque.wakoo.ui.widget.TextFieldPadding
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.viewmodel.SearchResultBean
import com.buque.wakoo.viewmodel.SearchViewModel

@Composable
fun UserSearchScreen(
    onCancel: () -> Unit,
    onItemClick: (SearchResultBean<*>) -> Unit,
) {
    var query by rememberSaveable { mutableStateOf("") }
    val viewModel = viewModel<SearchViewModel>()
    val results by viewModel.searchResultsState.collectAsStateWithLifecycle()

    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    LaunchedEffect(Unit) {
        focusRequester.requestFocus() // 请求焦点
        keyboardController?.show() // 请求显示键盘
    }

    StandardScreenScaffold(topBar = {
        // 顶部搜索栏
        Row(
            Modifier
                .fillMaxWidth()
                .systemBarsPadding(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            AppTextField(
                value = query,
                onValueChange = {
                    query = it
                },
                shape = CircleShape,
                placeholder = {
                    Text("请输入用户ID/房间/群组ID".localized, color = Color(0xFFB6B6B6))
                },
                textStyle =
                    MaterialTheme.typography.titleSmall.merge(
                        color = Color(0xFF111111),
                        lineHeight = 14.sp,
                        fontSize = 14.sp,
                    ),
                leadingIcon = {
                    Icon(
                        imageVector = WakooIcons.Search,
                        contentDescription = null,
                        modifier = Modifier.padding(start = 8.dp),
                    )
                },
                colors =
                    TextFieldDefaults.colors(
                        focusedContainerColor = Color(0xffE9EAEF),
                        unfocusedContainerColor = Color(0xffE9EAEF),
                        disabledContainerColor = WakooWhite,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent,
                    ),
                contentPadding =
                    PaddingValues(
                        start = 16.dp,
                        end = 16.dp,
                        top = TextFieldPadding,
                        bottom = TextFieldPadding,
                    ),
                modifier =
                    Modifier
                        .padding(start = 16.dp)
                        .weight(1f)
                        .height(48.dp)
                        .focusRequester(focusRequester),
                singleLine = true,
                maxLines = 1,
                keyboardOptions =
                    KeyboardOptions(
                        keyboardType = KeyboardType.Number,
                        imeAction = ImeAction.Search,
                    ),
                keyboardActions =
                    KeyboardActions(
                        onSearch = {
                            viewModel.updateInput(query)
                            focusRequester.freeFocus()
                            focusManager.clearFocus()
                        },
                    ),
            )

            Text(
                "取消".localized,
                color = Color(0xFF111111),
                fontSize = 16.sp,
                modifier =
                    Modifier
                        .click(noEffect = true, onClick = onCancel)
                        .padding(horizontal = 16.dp),
            )
        }
    }) { pv ->
        // 搜索结果列表
        LazyColumn(modifier = Modifier.padding(pv)) {
            items(results) { item ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .click {
                            onItemClick(item)
                        }.padding(horizontal = 16.dp, vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    NetworkImage(
                        item.avatar,
                        modifier =
                            Modifier
                                .size(48.dp)
                                .clip(shape = CircleShape),
                    )
                    Column(
                        Modifier
                            .padding(start = 12.dp)
                            .weight(1f),
                    ) {
                        Text(item.name, color = Color(0xFF111111), fontSize = 16.sp)
                        Text("ID:${item.id}", color = Color(0xFFB6B6B6), fontSize = 13.sp)
                    }
                    Text(
                        when (item.type) {
                            1 -> "语音房搜索结果".localized
                            2 -> "群组搜索结果".localized
                            else -> "用户搜索结果".localized
                        },
                        color = Color(0xFFB6B6B6),
                        fontSize = 13.sp,
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun previewUserSearchScreen() {
    UserSearchScreen({}) { }
}
