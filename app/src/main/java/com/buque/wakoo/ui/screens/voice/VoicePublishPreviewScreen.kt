package com.buque.wakoo.ui.screens.voice

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.R
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.network.api.bean.VoiceBackground
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.GreenChecked
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.VerticalGrid
import com.buque.wakoo.ui.widget.VipTag
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.ui.widget.voice.UserVoiceListCard
import com.buque.wakoo.viewmodel.VoicePublishViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoicePublishPreviewScreen(
    item: VoiceCardItem,
    playItem: PlayMediaItem,
    viewModel: VoicePublishViewModel,
    backTo: () -> Unit = {},
    closeTo: () -> Unit = {},
) {
    val previewItem by remember(viewModel) {
        derivedStateOf {
            item.copy(background = viewModel.selectedItem.resource)
        }
    }

    val coroutineScope = rememberCoroutineScope()

    val dialogController = rememberDialogController()

    TitleScreenScaffold(
        title = "预览声音内容".localized,
        modifier = Modifier.fillMaxSize(),
    ) { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues)) {
            val isPlaying by remember {
                derivedStateOf {
                    MediaPlayerManager.currentPlayingTag.value == playItem.tag
                }
            }

            var refreshFlag by remember {
                mutableIntStateOf(0)
            }

            if (isPlaying) {
                LaunchedEffect(Unit) {
                    while (isActive) {
                        delay(1000)
                        refreshFlag++
                    }
                }
            }

            val duration by remember(item) {
                derivedStateOf {
                    if (isPlaying) {
                        refreshFlag // 这是一个刷新标志，不能删除
                        MediaPlayerManager
                            .getPlayerDuration(playItem, item.duration.toLong())
                            .minus(
                                MediaPlayerManager.getPlayerPosition(playItem),
                            ).div(
                                1000,
                            ).toInt()
                    } else {
                        item.duration
                    }
                }
            }

            val lifecycleOwner = LocalLifecycleOwner.current

            // 清理资源
            DisposableEffect(Unit) {
                val observer =
                    LifecycleEventObserver { source, event ->
                        if (event == Lifecycle.Event.ON_STOP) {
                            MediaPlayerManager.pause(playItem)
                        }
                    }

                lifecycleOwner.lifecycle.addObserver(observer)

                onDispose {
                    lifecycleOwner.lifecycle.removeObserver(observer)
                }
            }

            UserVoiceListCard(
                item = previewItem,
                showOwnerInfo = true,
                modifier =
                    Modifier
                        .padding(
                            start = 24.dp,
                            top = 12.dp,
                            end = 24.dp,
                            bottom = 20.dp,
                        ).fillMaxWidth()
                        .weight(1f),
                isPlaying = isPlaying,
                duration = duration,
                useWeight = true,
                showActionArea = false,
            ) {
                MediaPlayerManager.toggle(playItem)
            }

            Text(
                text = "请选择卡片主题样式".localized,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF111111),
                modifier = Modifier.padding(start = 16.dp),
            )

            SizeHeight(12.dp)

            val configCState by viewModel.configStateFlow.collectAsStateWithLifecycle()
            // 主题选择器
            val sections by remember {
                derivedStateOf {
                    configCState.dataOrNull?.background?.chunked(8) ?: emptyList()
                }
            }
            val pagerState = rememberPagerState { sections.size }
            HorizontalPager(pagerState) { page ->
                VerticalGrid(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    columns = 4,
                    horizontalSpace = 8.dp,
                    verticalSpace = 8.dp,
                    wrapRowHeight = true,
                ) {
                    sections[page].forEachIndexed { index, background ->
                        ThemeOption(
                            background = background,
                            isSelected = background.id == viewModel.selectedId,
                            modifier =
                                Modifier
                                    .weight(1f)
                                    .aspectRatio(1f),
                            onClick = { viewModel.selectedId = background.id },
                        )
                    }
                }
            }

            SizeHeight(24.dp)

            val loading = LocalLoadingManager.current

            // 底部按钮
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                // 返回修改按钮
                OutlinedButton(
                    text = "返回修改".localized,
                    onClick = backTo,
                    modifier = Modifier.weight(1f),
                )

                // 确认发布按钮
                GradientButton(
                    text = "确认发布".localized,
                    onClick = {
                        val selectedItem = viewModel.selectedItem
                        if (selectedItem.roleType == 1 && selectedItem.costType == 2) {
                            dialogController.easyPost {
                                SimpleDoubleActionDialog(
                                    content =
                                        "本次选择的卡片主题需要花费%s钻石，确认扣除钻石并发布作品吗？".localizedFormat(
                                            selectedItem.cost,
                                        ),
                                    cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消".localized),
                                    confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认".localized),
                                    onCancel = { dismiss() },
                                    onConfirm = {
                                        dismiss()
                                        loading.show(coroutineScope) {
                                            if (viewModel.publish(previewItem, dialogController)) {
                                                closeTo()
                                            }
                                        }
                                    },
                                )
                            }
                        } else {
                            loading.show(coroutineScope) {
                                if (viewModel.publish(previewItem, dialogController)) {
                                    closeTo()
                                }
                            }
                        }
                    },
                    modifier = Modifier.weight(1f),
                )
            }

            SizeHeight(24.dp)
        }
    }
}

@Composable
private fun ThemeOption(
    background: VoiceBackground,
    isSelected: Boolean,
    modifier: Modifier,
    onClick: () -> Unit,
) {
    Box(
        modifier =
            modifier
                .clip(RoundedCornerShape(8.dp))
                .clickable(onClick = onClick)
                .border(
                    width = if (isSelected) 1.5.dp else 0.dp,
                    color = if (isSelected) Color(0xFF66FE6B) else Color.Transparent,
                    shape = RoundedCornerShape(8.dp),
                ),
        contentAlignment = Alignment.BottomEnd,
    ) {
        NetworkImage(
            data = background.icon,
            modifier = Modifier.fillMaxSize(),
        )
        if (isSelected) {
            Image(
                imageVector = WakooIcons.GreenChecked,
                contentDescription = null,
                modifier =
                    Modifier
                        .size(20.dp),
            )
        }

        if (background.roleType == 1 && background.costType == 2) {
            Row(
                modifier =
                    Modifier
                        .align(Alignment.TopStart)
                        .height(20.dp)
                        .background(Color(0xFFE8FFE9), RoundedCornerShape(topStart = 10.dp, bottomEnd = 10.dp))
                        .padding(horizontal = 4.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_green_diamond_straight),
                    contentDescription = null,
                    modifier = Modifier.size(12.dp),
                )
                Text(
                    text = background.cost.toString(),
                    style = MaterialTheme.typography.labelLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF0B570E),
                )
            }
        }

        if (background.roleType == 2) {
            VipTag(modifier = Modifier.align(Alignment.TopStart))
        }
    }
}

@Preview
@Composable
private fun VoicePublishPreviewScreenPreview() {
    WakooTheme {
        val context = LocalContext.current
        VoicePublishPreviewScreen(
            VoiceCardItem.preview,
            playItem =
                PlayMediaItem.prefixTagAudio(
                    url = "",
                    prefix = "publish-voice-",
                ),
            remember {
                VoicePublishViewModel(context)
            },
        )
    }
}
