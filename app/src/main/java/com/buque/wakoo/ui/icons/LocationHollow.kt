package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.LocationHollow: ImageVector
    get() {
        if (_LocationHollow != null) {
            return _LocationHollow!!
        }
        _LocationHollow =
            ImageVector
                .Builder(
                    name = "LocationHollow",
                    defaultWidth = 12.dp,
                    defaultHeight = 12.dp,
                    viewportWidth = 12f,
                    viewportHeight = 12f,
                ).apply {
                    group(
                        clipPathData =
                            PathData {
                                moveTo(0f, 0f)
                                horizontalLineToRelative(12f)
                                verticalLineToRelative(12f)
                                horizontalLineToRelative(-12f)
                                close()
                            },
                    ) {
                        path(fill = SolidColor(Color(0xFF999999))) {
                            moveTo(6f, 11.864f)
                            lineTo(2.818f, 8.682f)
                            curveTo(2.189f, 8.053f, 1.76f, 7.251f, 1.586f, 6.378f)
                            curveTo(1.413f, 5.505f, 1.502f, 4.6f, 1.843f, 3.778f)
                            curveTo(2.183f, 2.956f, 2.76f, 2.253f, 3.5f, 1.758f)
                            curveTo(4.24f, 1.264f, 5.11f, 1f, 6f, 1f)
                            curveTo(6.89f, 1f, 7.76f, 1.264f, 8.5f, 1.758f)
                            curveTo(9.24f, 2.253f, 9.817f, 2.956f, 10.158f, 3.778f)
                            curveTo(10.498f, 4.6f, 10.587f, 5.505f, 10.413f, 6.378f)
                            curveTo(10.24f, 7.251f, 9.811f, 8.053f, 9.182f, 8.682f)
                            lineTo(6f, 11.864f)
                            close()
                            moveTo(8.475f, 7.975f)
                            curveTo(8.964f, 7.485f, 9.298f, 6.862f, 9.433f, 6.183f)
                            curveTo(9.568f, 5.504f, 9.498f, 4.8f, 9.234f, 4.161f)
                            curveTo(8.969f, 3.521f, 8.52f, 2.975f, 7.944f, 2.59f)
                            curveTo(7.369f, 2.205f, 6.692f, 2f, 6f, 2f)
                            curveTo(5.308f, 2f, 4.631f, 2.205f, 4.056f, 2.59f)
                            curveTo(3.48f, 2.975f, 3.031f, 3.521f, 2.766f, 4.161f)
                            curveTo(2.502f, 4.8f, 2.432f, 5.504f, 2.567f, 6.183f)
                            curveTo(2.702f, 6.862f, 3.036f, 7.485f, 3.525f, 7.975f)
                            lineTo(6f, 10.45f)
                            lineTo(8.475f, 7.975f)
                            close()
                            moveTo(6f, 6.5f)
                            curveTo(5.735f, 6.5f, 5.48f, 6.395f, 5.293f, 6.207f)
                            curveTo(5.105f, 6.02f, 5f, 5.765f, 5f, 5.5f)
                            curveTo(5f, 5.235f, 5.105f, 4.98f, 5.293f, 4.793f)
                            curveTo(5.48f, 4.605f, 5.735f, 4.5f, 6f, 4.5f)
                            curveTo(6.265f, 4.5f, 6.52f, 4.605f, 6.707f, 4.793f)
                            curveTo(6.895f, 4.98f, 7f, 5.235f, 7f, 5.5f)
                            curveTo(7f, 5.765f, 6.895f, 6.02f, 6.707f, 6.207f)
                            curveTo(6.52f, 6.395f, 6.265f, 6.5f, 6f, 6.5f)
                            close()
                        }
                    }
                }.build()

        return _LocationHollow!!
    }

@Suppress("ObjectPropertyName")
private var _LocationHollow: ImageVector? = null
