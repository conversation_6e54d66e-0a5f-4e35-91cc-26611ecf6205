package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.LiveRoomEditInfo
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LiveRoomRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.screens.liveroom.RoomRole
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SingleTextSettingsMenuItem
import com.buque.wakoo.ui.widget.SizeHeight

@Composable
fun DialogScope.LiveRoomManagePanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
    onBack: () -> Unit = {},
) {
    val navController = LocalAppNavController.current

    val scope = rememberCoroutineScope()

    BottomPanelScaffold(
        title = "房间管理".localized,
        useClose = false,
        modifier = modifier,
        onBack = onBack,
        contentPadding = 0.dp,
    ) {
        val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)

        if (selfRole == RoomRole.Member) {
            // 失去管理员权限
            LaunchedEffect(Unit) {
                dismiss()
            }
        }

        SingleTextSettingsMenuItem(
            titleText = "房间资料".localized,
            onClick = {
                dismiss()
                roomInfoState.sendEvent(
                    RoomEvent.PanelDialog { roomInfoState ->
                        val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)

                        if (selfRole == RoomRole.Member) {
                            // 失去管理员权限
                            LaunchedEffect(Unit) {
                                dismiss()
                            }
                        }
                        EditRoomInfoPanel(
                            title = "修改房间资料".localized,
                            buttonText = "确认修改".localized,
                            info =
                                LiveRoomEditInfo(
                                    title = roomInfoState.basicInfo.title,
                                    desc = roomInfoState.basicInfo.desc.orEmpty(),
                                    tagIds = roomInfoState.basicInfo.tagIds.orEmpty(),
                                ),
                        ) { edit ->
                            dismiss()
                            roomInfoState.sendEvent(RoomEvent.EditRoomInfo(edit))
                        }
                    },
                )
            },
        )
        AnimatedVisibility(selfRole == RoomRole.Owner) {
            SingleTextSettingsMenuItem(
                titleText = "房间管理员".localized,
                onClick = {
                    dismiss()
                    navController.push(LiveRoomRoute.AdminList)
                },
            )
        }
        SingleTextSettingsMenuItem(
            titleText = "房间黑名单".localized,
            onClick = {
                dismiss()
                navController.push(LiveRoomRoute.BlackList)
            },
        )
    }
}

@Composable
fun DialogScope.LiveRoomUserManagePanel(
    user: User,
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    val selfRole by roomInfoState.rememberRoomRoleState(LocalSelfUserProvider.currentId)
    if (selfRole == RoomRole.Member) {
        // 失去管理员权限
        LaunchedEffect(Unit) {
            dismiss()
        }
    }

    roomInfoState.trackRoomMember(user)

    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .background(
                    color = WakooWhite,
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
                ).navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val targetRole by roomInfoState.rememberRoomRoleState(user.id)
        val targetInBlack by roomInfoState.rememberInBlackListState(user.id)

        if (selfRole == RoomRole.Owner) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .clickable {
                            dismiss()
                            roomInfoState.sendEvent(RoomEvent.SetAdminEvent(user.id, targetRole == RoomRole.Member))
                        }.padding(16.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = if (targetRole == RoomRole.Member) "设置为房间管理员".localized else "取消房间管理员".localized,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Normal,
                    color = if (targetRole == RoomRole.Member) WakooSecondarySelected else Color(0xFFF53F3F),
                )
            }

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
            )
        }
        val isFollowed by remember(user.id) {
            derivedStateOf {
                roomInfoState.requireRoomUser(user).isFollowed
            }
        }

        if (!isFollowed) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .clickable {
                            dismiss()
                            roomInfoState.sendEvent(RoomEvent.FollowUser(user.id))
                        }.padding(16.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = "关注".localized,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Normal,
                    color = WakooSecondarySelected,
                )
            }

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
            )
        }

        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .clickable {
                        dismiss()
                        roomInfoState.sendEvent(RoomEvent.SetBlackEvent(user.id, !targetInBlack))
                    }.padding(16.dp),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = if (!targetInBlack) "加入黑名单".localized else "取消拉黑".localized,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Normal,
                color = if (!targetInBlack) WakooSecondarySelected else Color(0xFFF53F3F),
            )
        }

        HorizontalDivider(
            color = Color(0xFFF2F3F5),
            thickness = 8.dp,
        )

        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .clickable {
                        dismiss()
                    }.padding(16.dp),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = "取消".localized,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Normal,
                color = WakooSecondarySelected,
            )
        }

        SizeHeight(15.dp)
    }
}
