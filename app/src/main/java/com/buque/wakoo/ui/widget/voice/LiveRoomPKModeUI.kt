package com.buque.wakoo.ui.widget.voice

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.animateValue
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.network.api.bean.PkInfo
import com.buque.wakoo.ui.dialog.CheersGroupListDialogContent
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.roundedParallelogram
import kotlinx.coroutines.delay
import kotlin.random.Random

@Composable
fun PKCountdownTimer(
    totalSeconds: Int,
    modifier: Modifier = Modifier,
    onDone: () -> Unit = {},
) {
    var remainingSeconds by remember { mutableStateOf(totalSeconds) }

    LaunchedEffect(totalSeconds) {
        remainingSeconds = totalSeconds
        while (remainingSeconds > 0) {
            delay(1000)
            remainingSeconds--
        }
        onDone()
    }

    val minutes = remainingSeconds / 60
    val seconds = remainingSeconds % 60

    Text(
        if (remainingSeconds == 0) "结算中".localized else "${String.format("%02d", minutes)}:${String.format("%02d", seconds)}",
        color = Color.White,
        fontSize = 13.sp,
        fontWeight = FontWeight.W500,
        textAlign = TextAlign.Center,
        maxLines = 1,
    )
}

@Preview(showBackground = true)
@Composable
fun PreviewCountdownTimer() {
    PKCountdownTimer(totalSeconds = 10) // 2分5秒
}


/**
 * pk的progressBar
 */
@Composable
fun PKProgressBar(
    leftVotes: Int = 0,
    rightVotes: Int = 0,
    width: Int = 10,
    winnerSide: Int = -2,//-2 没人赢 -1平局 1左边赢 2右边赢
    pkStatus: Int = PkInfo.STATE_IDE,
    modifier: Modifier = Modifier,
) {
    val leftProgress = (if (leftVotes + rightVotes > 0) leftVotes.toFloat() / (leftVotes + rightVotes) else 0.5f).coerceIn(0.208f, 0.792f)

    val animatedProgress by animateFloatAsState(
        targetValue = leftProgress, animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy, stiffness = Spring.StiffnessLow
        ), label = "progress"
    )

    val offset = if (pkStatus == PkInfo.STATE_RUNNING) {
        val tran = rememberInfiniteTransition(label = "color")
        val shapeWidthSize by with(LocalDensity.current) {
            derivedStateOf {
                width.dp.toPx()
            }
        }
        val value by tran.animateValue(
            initialValue = 0f,
            targetValue = shapeWidthSize * 2,
            typeConverter = Float.VectorConverter,
            animationSpec = infiniteRepeatable(TweenSpec(durationMillis = 2000, easing = LinearEasing)),
            label = ""
        )
        value
    } else {
        0f
    }

    val leftVotesStr = animateIntAsState(
        targetValue = leftVotes,
        animationSpec = tween(durationMillis = 200, easing = FastOutSlowInEasing)
    )

    val rightVotesStr = animateIntAsState(
        targetValue = rightVotes,
        animationSpec = tween(durationMillis = 200, easing = FastOutSlowInEasing)
    )

    Box(
        modifier = modifier, contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(16.dp)
                .clip(RoundedCornerShape(2.dp))
                .drawWithCache {
                    val shapeWidth = width.dp.toPx()
                    val boxWidth = size.width
                    val boxHeight = size.height
                    val path = Path().apply {
                        lineTo(0f, 1f)
                        lineTo(shapeWidth, 1f)
                        lineTo(shapeWidth / 3, boxHeight - 1f)
                        lineTo(-shapeWidth / 3 * 2, boxHeight - 1f)
                        close()
                    }
                    this.onDrawWithContent {
                        val centerPoint = boxWidth * animatedProgress

                        drawRect(
                            Color(0xff00B0EF),
                            topLeft = Offset(0f, -2f),
                            size = Size(centerPoint, boxHeight + 2f)
                        )

                        drawRect(
                            Color(0xffF232AC),
                            topLeft = Offset(centerPoint, -2f),
                            size = Size(boxWidth * (1 - animatedProgress), boxHeight + 2f)
                        )

                        var cr = -shapeWidth + offset
                        while (cr < boxWidth + 10) {
                            translate(cr, 0f) {
                                drawPath(path, color = Color(0x5Cffffff))
                            }
                            cr += shapeWidth * 2
                        }
                        drawContent()
                    }
                }
        ) {
            // 左侧票数
            Text(
                text = leftVotesStr.value.toString(), color = Color.White, modifier = Modifier.padding(start = 8.dp),
                fontSize = 12.sp,
                lineHeight = 16.sp,
                textAlign = TextAlign.Start,
                fontWeight = FontWeight.W900,
                fontFamily = FontFamily.MI_SANS

            )
            Weight(1f)
            // 右侧票数
            Text(
                text = rightVotesStr.value.toString(),
                color = Color.White,
                modifier = Modifier.padding(end = 8.dp),
                fontSize = 12.sp,
                lineHeight = 16.sp,
                textAlign = TextAlign.End,
                fontWeight = FontWeight.W900,
                fontFamily = FontFamily.MI_SANS
            )
        }
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Weight(animatedProgress)
            Image(
                painter = painterResource(R.drawable.ic_pkmode_cursor), contentDescription = null, modifier = Modifier
                    .height(24.dp)
                    .width(4.dp), contentScale = ContentScale.FillHeight
            )
            Weight(1f - animatedProgress)
        }
        if (pkStatus == PkInfo.STATE_FINISHED && winnerSide >= -1) {
            Row(modifier = Modifier.padding(horizontal = 44.dp), verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                if (winnerSide != -1) {
                    Image(
                        painter = painterResource(if (winnerSide == 1) R.drawable.ic_pk_win else R.drawable.ic_pk_lose),
                        modifier = Modifier.height(32.dp),
                        contentScale = ContentScale.FillHeight,
                        contentDescription = null
                    )
                }
                if (winnerSide == -1) {
                    Image(
                        painter = painterResource(R.drawable.ic_pk_draw),
                        modifier = Modifier.height(32.dp),
                        contentScale = ContentScale.FillHeight,
                        contentDescription = null
                    )
                } else {
                    Weight(1f)
                }
                if (winnerSide != -1) {
                    Image(
                        painter = painterResource(if (winnerSide == 2) R.drawable.ic_pk_win else R.drawable.ic_pk_lose),
                        modifier = Modifier.height(32.dp),
                        contentScale = ContentScale.FillHeight,
                        contentDescription = null
                    )
                }
            }
        }
    }
}


@Preview(showBackground = true)
@Composable
private fun PreviewPKProgressBar(modifier: Modifier = Modifier) {
    var left by remember {
        mutableStateOf(0)
    }
    var right by remember {
        mutableStateOf(0)
    }
    LaunchedEffect(Unit) {
        while (true) {
            left += Random.nextInt(1000)
            right += Random.nextInt(1000)
            delay(2000)
        }
    }
    PKProgressBar(left, right)
}

/**
 * PK底部的助威团
 */
@Composable
fun PKCheersGroupWidget(roomInfoState: LiveRoomInfoState) {
    val pkInfo = roomInfoState.extraInfo.pkRoomInfo ?: PkInfo()

    //助威团
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        Row(
            modifier = Modifier
                .weight(1f)
                .aspectRatio(4.36111111f)
                .paint(
                    painter = painterResource(R.drawable.ic_pkmode_blue_team_bg), contentScale = ContentScale.FillBounds
                )
                .click(noEffect = true) {
                    roomInfoState.sendEvent(RoomEvent.PanelDialog {
                        CheersGroupListDialogContent(roomInfoState.id, 1)
                    })
                }
                .padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (pkInfo.blueSideValue != 0 && pkInfo.blueSideCheers.isNotEmpty()) {
                Row(
                    modifier = Modifier.weight(1f), horizontalArrangement = Arrangement.Start
                ) {
                    pkInfo.blueSideCheers.take(3).forEach {
                        AvatarNetworkImage(it, size = 24.dp, enabled = false)
                    }
                }
                SizeWidth(6.dp)
                Text(
                    "%s人\n助威".localizedFormat(pkInfo.blueSideCheersCnt), color = Color.White, fontSize = 12.sp
                )
                SizeWidth(16.dp)
            } else {
                Text(
                    "助威团虚位以待".localized, color = Color.White, fontSize = 12.sp
                )
            }
        }
        SizeWidth(28.dp)
        Row(
            modifier = Modifier
                .weight(1f)
                .aspectRatio(4.36111111f)
                .paint(
                    painter = painterResource(R.drawable.ic_pkmode_red_team_bg), contentScale = ContentScale.FillBounds
                )
                .click(noEffect = true) {
                    roomInfoState.sendEvent(RoomEvent.PanelDialog {
                        CheersGroupListDialogContent(roomInfoState.id, 2)
                    })
                }
                .padding(horizontal = 8.dp), verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.End

        ) {
            if (pkInfo.redSideValue != 0 && pkInfo.redSideCheers.isNotEmpty()) {
                SizeWidth(16.dp)

                Text(
                    "%s人\n助威".localizedFormat(pkInfo.redSideCheersCnt), color = Color.White, fontSize = 12.sp
                )
                SizeWidth(6.dp)

                Row(
                    modifier = Modifier.weight(1f), horizontalArrangement = Arrangement.End
                ) {
                    pkInfo.redSideCheers.take(3).forEach {
                        AvatarNetworkImage(it, size = 24.dp, enabled = false)
                    }
                }

            } else {
                Text(
                    "助威团虚位以待".localized, color = Color.White, fontSize = 12.sp
                )
            }
        }
    }
}

@Composable
fun PKParallelogramCard(
    text: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color(0x96244EB7),
    borderColor: Color = Color(0xFF4BA7FF),
    borderWidth: Dp = 1.dp,
    offset: Float = 8f,
    paddingValues: PaddingValues = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
) {
    PKParallelogramCard(
        modifier, backgroundColor, borderColor, borderWidth, offset, paddingValues
    ) {
        Text(
            text = text,
            color = Color.White,
            fontSize = 13.sp,
            fontWeight = FontWeight.W500,
            textAlign = TextAlign.Center,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.basicMarquee(iterations = Int.MAX_VALUE)
        )
    }
}

@Composable
fun PKParallelogramCard(
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color(0x96244EB7),
    borderColor: Color = Color(0xFF4BA7FF),
    borderWidth: Dp = 1.dp,
    offset: Float = 8f,
    paddingValues: PaddingValues = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
    content: @Composable BoxScope.() -> Unit
) {
    PKParallelogramCard(
        modifier,
        Brush.horizontalGradient(listOf(backgroundColor, backgroundColor)),
        borderColor,
        borderWidth,
        offset,
        paddingValues,
        content
    )
}

@Composable
fun PKParallelogramCard(
    modifier: Modifier = Modifier,
    brush: Brush = Brush.horizontalGradient(listOf(Color(0x96244EB7))),
    borderColor: Color = Color(0xFF4BA7FF),
    borderWidth: Dp = 1.dp,
    offset: Float = 8f,
    paddingValues: PaddingValues = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
    content: @Composable BoxScope.() -> Unit
) {
    Box(
        modifier = modifier
            .roundedParallelogram(offset, brush, borderColor, borderWidth)
            .padding(paddingValues), contentAlignment = Alignment.Center
    ) {
        content()
    }
}