package com.buque.wakoo.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.lifecycle.compose.LifecyclePauseOrDisposeEffectResult
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.compose.LifecycleStopOrDisposeEffectResult
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation3.ui.LocalNavAnimatedContentScope
import com.buque.wakoo.app.OnContent
import com.buque.wakoo.bean.PendantConfig
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.WebViewContent
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.drag.FloatingLayoutScope
import com.buque.wakoo.ui.widget.drag.rememberDraggableFloatingState
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.datapoints.Analytics
import com.buque.wakoo.utils.datapoints.PageEnterReport
import com.buque.wakoo.utils.eventBus.tryToLink
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

@Composable
fun rememberLoopPagerState(
    pageCount: Int,
    initialPage: Int = 0,
) = rememberPagerState(initialPage = initialPage) {
    pageCount
}

interface SwiperData {
    val id: Int
    val picUrl: String

    val isH5Pic: Boolean
}

data class ComposeSwiperData(
    override val id: Int = 0,
    val content: OnContent,
) : SwiperData {
    override val picUrl: String = ""
    override val isH5Pic: Boolean = false
}

@Composable
fun CommonBanner(
    position: Int,
    modifier: Modifier = Modifier,
) {
    val dataState = AppConfigManager.flowBannerList.collectAsStateWithLifecycle()
    val list by remember(dataState, position) {
        derivedStateOf {
            dataState.value.filter { it.support(position) }
        }
    }
    Swiper(
        list = list,
        modifier = modifier
            .fillMaxWidth(1f)
            .aspectRatio(343f / 76),
    ) {
        it.jumpLink.tryToLink()
    }
}

private fun SwiperData.isBeginnerPack() = this is PendantConfig.ActivityPendant && this.jumpLink == "wakoo://page/beginner_pack"

@Composable
fun FloatingLayoutScope.CommonPendantBanner(
    position: Int,
    composePendants: List<ComposeSwiperData> = emptyList(),
    initialAlignment: Alignment = Alignment.BottomEnd,
    initialOffsetDp: DpOffset = DpOffset(x = 0.dp, y = (-25).dp),
) {
    val dataState = AppConfigManager.flowPendantList.collectAsStateWithLifecycle()
    val h5List by remember(dataState, position) {
        derivedStateOf {
            buildList {
                addAll(dataState.value.filter { it.isH5Pic && it.support(position) })
                addAll(composePendants.filter { it.isH5Pic })
            }
        }
    }

    val nativeList by remember(dataState, position) {
        derivedStateOf {
            buildList {
                addAll(dataState.value.filter { !it.isH5Pic && it.support(position) })
                addAll(composePendants.filter { !it.isH5Pic })
            }
        }
    }

    if (h5List.isNotEmpty()) {
        DraggableSwiperItem(h5List, initialOffsetDp, initialAlignment = initialAlignment, position = position)
    }

    if (nativeList.isNotEmpty()) {
        DraggableSwiperItem(
            nativeList, if (h5List.isNotEmpty()) {
                initialOffsetDp.minus(DpOffset(x = 0.dp, y = 100.dp))
            } else {
                initialOffsetDp
            }, initialAlignment = initialAlignment, position = position
        )
    }
}

@Composable
private fun FloatingLayoutScope.DraggableSwiperItem(
    list: List<SwiperData>,
    initialOffsetDp: DpOffset,
    modifier: Modifier = Modifier,
    position: Int,
    initialAlignment: Alignment = Alignment.BottomEnd,
) {
    val floatingState = rememberDraggableFloatingState(
        initialAlignment = initialAlignment,
        initialOffsetDp = initialOffsetDp,
        initialIsVisible = false,
        initialIsStickyToEdge = true,
        allowDragOutOfBounds = true,
    )

    var visible by rememberSaveable {
        mutableStateOf(true)
    }

    // nav过渡结束后再显示悬浮框会更流畅，位置也不会出错
    if (LocalNavAnimatedContentScope.current.transition.isRunning) {
        return
    }

    LaunchedEffect(floatingState) {
        snapshotFlow {
            visible && list.isNotEmpty()
        }.collectLatest {
            floatingState.isVisible = it
        }
    }

    DraggableItem(state = floatingState) {
        Box(modifier = modifier.size(76.dp, 91.dp)) {
            Swiper(list = list, modifier = Modifier.fillMaxSize(), showIndicator = false, contentScale = ContentScale.FillHeight, overlay = {
                //这里为什么要写死呢,因为我也不知道这个路径会不会变
                if (it.isBeginnerPack()) {
                    PageEnterReport(Analytics.Event.NEWBIE_PACKAGE_PENDANT_SHOW, properties = { mapOf("position_id" to position) })
                }
            }) {
                if (it is PendantConfig.ActivityPendant) {
                    it.jumpLink.toUri().buildUpon().apply {
                        appendQueryParameter("position", position.toString())
                    }.toString().tryToLink()
                    if (it.isBeginnerPack()) {
                        Analytics.trace(Analytics.Event.NEWBIE_PACKAGE_PENDANT_CLICK, mapOf("position_id" to position))
                    }
                }

            }

            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .noEffectClick(onClick = {
                        visible = false
                    })
                    .padding(3.dp)
                    .size(16.dp)
                    .background(Color(0x99000000), CircleShape),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    imageVector = WakooIcons.Close,
                    contentDescription = null,
                    modifier = Modifier.size(8.dp),
                    tint = WakooWhite,
                )
            }
        }
    }
}

@Composable
fun <T : SwiperData> Swiper(
    list: List<T>,
    overlay: @Composable (T) -> Unit = {},
    modifier: Modifier = Modifier,
    showIndicator: Boolean = true,
    contentScale: ContentScale = ContentScale.Crop,
    onClick: (T) -> Unit,
) {
    val visible = list.isNotEmpty()

    if (visible) {
        val rootNavController = LocalAppNavController.root
        Box(
            modifier = modifier,
        ) {
            val realPageCount = list.size
            val state = rememberViewPagerState(initialPage = 0, pageCount = realPageCount)
            val cur by remember {
                derivedStateOf { state.currentPage.coerceIn(0, state.pageCount.minus(1).coerceAtLeast(0)) }
            }

            LoopViewPagerEffect(pagerState = state, realCount = realPageCount)

            HorizontalPager(
                modifier = Modifier.fillMaxSize(1f),
                state = state,
                pageSpacing = 4.dp,
                userScrollEnabled = realPageCount > 1,
            ) {
                val model = list.getOrNull(mapLoopViewPagerPage(it, realPageCount)) ?: return@HorizontalPager
                Box(
                    modifier = Modifier.fillMaxSize(1f),
                ) {
                    if (model.isH5Pic) {
                        WebViewContent(
                            url = model.picUrl,
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(RoundedCornerShape(16.dp)),
                            onOpenPage = {
                                rootNavController.push(it)
                            },
                        )
                    } else if (model is ComposeSwiperData) {
                        model.content.invoke()
                    } else {
                        NetworkImage(
                            data = model.picUrl,
                            modifier = Modifier
                                .fillMaxSize()
                                .noEffectClick {
                                    onClick(model)
                                },
                            contentScale = contentScale
                        )
                    }
                    overlay(model)
                }
            }
            if (showIndicator && list.size > 1) {
                CircleIndicator(
                    count = realPageCount,
                    currentIndex = mapLoopViewPagerPage(cur, realPageCount),
                    modifier = Modifier
                        .align(alignment = Alignment.BottomCenter)
                        .padding(bottom = 8.dp),
                )
            }
        }
    }
}

@Composable
fun LoopHorizontalPager(
    modifier: Modifier = Modifier,
    state: PagerState,
    interval: Long = 5000L,
    pageCreator: @Composable PagerScope.(Int) -> Unit = {},
) {
    if (state.pageCount > 1) {
        val scope = rememberCoroutineScope()
        LifecycleResumeEffect(key1 = state.pageCount) {
            val job = scope.launch(Dispatchers.Main) {
                while (isActive) {
                    delay(interval)
                    state.animateScrollToPage((state.currentPage + 1) % state.pageCount)
                }
            }
            object : LifecyclePauseOrDisposeEffectResult {
                override fun runPauseOrOnDisposeEffect() {
                    job.cancel()
                }
            }
        }
    }
    HorizontalPager(modifier = modifier, state = state) { page ->
        pageCreator(page)
    }
}

@Composable
private fun CircleIndicator(
    modifier: Modifier = Modifier,
    count: Int,
    currentIndex: Int,
    size: Dp = 5.dp,
    spacing: Dp = 10.dp,
    colorSelected: Color = Color.White,
    colorUnSelected: Color = Color(0x80FFFFFF),
) {
    Row(modifier = modifier) {
        repeat(count) {
            val color = if (it == currentIndex) colorSelected else colorUnSelected
            Box(
                modifier = Modifier
                    .size(size)
                    .background(color, RoundedCornerShape(50)),
            ) {}
            if (it < count - 1) {
                Spacer(modifier = Modifier.width(spacing))
            }
        }
    }
}

@Composable
fun rememberViewPagerState(
    initialPage: Int,
    pageCount: Int,
): PagerState {
    val loopPageCount = if (pageCount > 1) pageCount.times(3) else pageCount
    val loopInitialPage = if (pageCount > 1) initialPage.plus(pageCount) else initialPage
    return rememberPagerState(loopInitialPage) {
        loopPageCount
    }
}

fun mapLoopViewPagerPage(
    page: Int,
    realCount: Int,
): Int = if (realCount > 0) page.rem(realCount) else 0

@Composable
fun LoopViewPagerEffect(
    realCount: Int,
    pagerState: PagerState,
    interval: Long = 5000L,
) {
    if (realCount > 1) {
        val scope = rememberCoroutineScope()
        LifecycleStartEffect(key1 = pagerState) {
            val job = scope.launch(Dispatchers.Main) {
                while (this.isActive) {
                    try {
                        delay(interval)
                        pagerState.animateScrollToPage(pagerState.currentPage.plus(1).rem(pagerState.pageCount))
                    } catch (e: CancellationException) {
                        throw e
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
            object : LifecycleStopOrDisposeEffectResult {
                override fun runStopOrDisposeEffect() {
                    job.cancel()
                }
            }
        }
    }
}
