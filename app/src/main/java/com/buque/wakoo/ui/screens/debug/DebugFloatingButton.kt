package com.buque.wakoo.ui.screens.debug

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Build
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.ui.widget.drag.rememberDraggableFloatingState
import kotlinx.coroutines.delay

/**
 * 调试浮动按钮
 * 仅在 Debug 模式下显示，提供快速访问调试功能的入口
 */
@Composable
fun DebugFloatingButton() {
    // 只在 Debug 模式下显示
    if (EnvironmentManager.isProdRelease) {
        return
    }

    var showDebugMenu by remember { mutableStateOf(false) }

    FloatingLayoutManager {
        val floatingState =
            rememberDraggableFloatingState(
                initialAlignment = Alignment.TopEnd,
                initialOffsetDp = DpOffset(x = 0.dp, y = 90.dp),
                initialIsVisible = false,
                initialIsStickyToEdge = true,
                allowDragOutOfBounds = true,
            )

        LaunchedEffect(floatingState) {
            delay(300)
            floatingState.isVisible = true
        }

        DraggableItem(state = floatingState) {
            // 主调试按钮
            FloatingActionButton(
                onClick = { showDebugMenu = true },
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onPrimary,
            ) {
                Icon(
                    imageVector = Icons.Default.Build,
                    contentDescription = "调试菜单",
                )
            }
        }
    }

    // 调试菜单对话框
    if (showDebugMenu) {
        DebugMenu(
            onDismiss = {
                showDebugMenu = false
            },
        )
    }
}
