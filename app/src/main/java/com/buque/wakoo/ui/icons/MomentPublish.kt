package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.MomentPublish: ImageVector
    get() {
        if (_MomentPublish != null) {
            return _MomentPublish!!
        }
        _MomentPublish = ImageVector.Builder(
            name = "MomentPublish",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(20f)
                    verticalLineToRelative(20f)
                    horizontalLineToRelative(-20f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFFFFFFFF))) {
                    moveTo(16.667f, 2.5f)
                    curveTo(17.127f, 2.5f, 17.5f, 2.873f, 17.5f, 3.333f)
                    verticalLineTo(4.798f)
                    lineTo(15.833f, 6.464f)
                    verticalLineTo(4.167f)
                    horizontalLineTo(4.167f)
                    verticalLineTo(10.917f)
                    lineTo(7.5f, 7.583f)
                    lineTo(11.107f, 11.191f)
                    lineTo(10.001f, 12.297f)
                    lineTo(9.996f, 15.829f)
                    lineTo(13.534f, 15.834f)
                    lineTo(14.642f, 14.726f)
                    lineTo(15.749f, 15.833f)
                    horizontalLineTo(15.833f)
                    verticalLineTo(13.535f)
                    lineTo(17.5f, 11.868f)
                    verticalLineTo(16.667f)
                    curveTo(17.5f, 17.127f, 17.127f, 17.5f, 16.667f, 17.5f)
                    horizontalLineTo(3.333f)
                    curveTo(2.873f, 17.5f, 2.5f, 17.127f, 2.5f, 16.667f)
                    verticalLineTo(3.333f)
                    curveTo(2.5f, 2.873f, 2.873f, 2.5f, 3.333f, 2.5f)
                    horizontalLineTo(16.667f)
                    close()
                    moveTo(18.148f, 6.507f)
                    lineTo(19.327f, 7.685f)
                    lineTo(12.845f, 14.167f)
                    lineTo(11.665f, 14.165f)
                    lineTo(11.667f, 12.988f)
                    lineTo(18.148f, 6.507f)
                    close()
                    moveTo(12.917f, 5.833f)
                    curveTo(13.607f, 5.833f, 14.167f, 6.393f, 14.167f, 7.083f)
                    curveTo(14.167f, 7.773f, 13.607f, 8.333f, 12.917f, 8.333f)
                    curveTo(12.227f, 8.333f, 11.667f, 7.773f, 11.667f, 7.083f)
                    curveTo(11.667f, 6.393f, 12.227f, 5.833f, 12.917f, 5.833f)
                    close()
                }
            }
        }.build()

        return _MomentPublish!!
    }

@Suppress("ObjectPropertyName")
private var _MomentPublish: ImageVector? = null
