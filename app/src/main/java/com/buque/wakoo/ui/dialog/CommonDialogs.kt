package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight

/**
 * 弹窗按钮配置
 * @param text 按钮文本
 * @param backgroundColor 按钮背景色（支持渐变）
 * @param textColor 按钮文字颜色
 */
data class DialogButtonConfig(
    val text: String,
    val backgroundColor: Brush,
    val textColor: Color,
)

/**
 * 预定义的按钮样式
 */
object DialogButtonStyles {
    // 主要按钮（绿色渐变）
    val Primary =
        DialogButtonConfig(
            text = "",
            backgroundColor =
                Brush.linearGradient(
                    colors =
                        listOf(
                            Color(0xFF66FE6B),
                            Color(0xFF66FE6B),
                        ),
                ),
            textColor = Color(0xFF111111),
        )

    // 次要按钮（灰色）
    val Secondary =
        DialogButtonConfig(
            text = "",
            backgroundColor =
                Brush.linearGradient(
                    colors =
                        listOf(
                            Color(0xFFF8F8F8),
                            Color(0xFFF8F8F8),
                        ),
                ),
            textColor = Color(0xFF999999),
        )

    // VIP按钮（金色渐变）
    val VIP =
        DialogButtonConfig(
            text = "",
            backgroundColor =
                Brush.linearGradient(
                    colors =
                        listOf(
                            Color(0xFFFFE072),
                            Color(0xFFFFF799),
                        ),
                ),
            textColor = Color(0xFF674E0F),
        )

    val Transparent =
        DialogButtonConfig(
            text = "",
            backgroundColor =
                Brush.linearGradient(
                    colors =
                        listOf(
                            Color.Transparent,
                            Color.Transparent,
                        ),
                ),
            textColor = Color(0xFF999999),
        )

    val Red =
        DialogButtonConfig(
            text = "",
            backgroundColor =
                SolidColor(Color(0xFFF53F3F)),
            textColor = Color.White,
        )
}

@Composable
fun CustomActionDialog(
    title: String,
    content: String,
    buttonGroup: @Composable () -> Unit,
) {
    Surface(
        modifier =
            Modifier
                .width(270.dp)
                .wrapContentHeight(),
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 标题
            Text(
                text = title,
                style =
                    MaterialTheme.typography.titleMedium.copy(
                        fontSize = 17.sp,
                        lineHeight = 24.sp,
                    ),
                color = Color(0xFF111111),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
            )

            SizeHeight(4.dp)

            // 内容
            Text(
                text = content,
                style =
                    MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 15.sp,
                        lineHeight = 22.sp,
                    ),
                color = Color(0xFF666666),
                textAlign = TextAlign.Left,
                modifier = Modifier.fillMaxWidth(),
            )

            SizeHeight(20.dp)

            // 按钮区域
            buttonGroup()
        }
    }
}

/**
 * 双按钮确认弹窗
 * 用于需要用户确认的重要操作，如注销账号、删除内容等
 *
 * @param title 弹窗标题
 * @param content 弹窗内容文本
 * @param cancelButtonConfig 取消按钮配置
 * @param confirmButtonConfig 确认按钮配置
 * @param onCancel 取消按钮点击回调
 * @param onConfirm 确认按钮点击回调
 */
@Composable
fun ColumnDoubleActionDialog(
    title: String,
    content: String,
    cancelButtonConfig: DialogButtonConfig,
    confirmButtonConfig: DialogButtonConfig,
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
) {
    CustomActionDialog(title, content) {
        // 按钮区域
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            // 确认按钮
            DialogButton(
                config = confirmButtonConfig,
                onClick = onConfirm,
                modifier = Modifier.fillMaxWidth(),
            )
            // 取消按钮
            DialogButton(
                config = cancelButtonConfig,
                onClick = onCancel,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

/**
 * 双按钮确认弹窗
 * 用于需要用户确认的重要操作，如注销账号、删除内容等
 *
 * @param title 弹窗标题
 * @param content 弹窗内容文本
 * @param cancelButtonConfig 取消按钮配置
 * @param confirmButtonConfig 确认按钮配置
 * @param onCancel 取消按钮点击回调
 * @param onConfirm 确认按钮点击回调
 */
@Composable
fun RowDoubleActionDialog(
    title: String,
    content: String,
    cancelButtonConfig: DialogButtonConfig,
    confirmButtonConfig: DialogButtonConfig,
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
) {
    CustomActionDialog(title, content) {
        // 按钮区域
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            // 取消按钮
            DialogButton(
                config = cancelButtonConfig,
                onClick = onCancel,
                modifier = Modifier.weight(1f),
            )
            // 确认按钮
            DialogButton(
                config = confirmButtonConfig,
                onClick = onConfirm,
                modifier = Modifier.weight(1f),
            )
        }
    }
}

/**
 * 单按钮提示弹窗
 * 用于显示提示信息，只需要用户确认的场景
 *
 * @param title 弹窗标题，可选
 * @param content 弹窗内容文本
 * @param buttonConfig 按钮配置
 * @param onButtonClick 按钮点击回调
 */
@Composable
fun SingleActionDialog(
    title: String? = null,
    content: String,
    buttonConfig: DialogButtonConfig,
    onButtonClick: () -> Unit,
) {
    Surface(
        modifier =
            Modifier
                .width(270.dp)
                .wrapContentHeight(),
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 标题（可选）
            if (title != null) {
                Text(
                    text = title,
                    style =
                        MaterialTheme.typography.titleMedium.copy(
                            fontSize = 17.sp,
                            lineHeight = 24.sp,
                        ),
                    color = Color(0xFF111111),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(),
                )

                SizeHeight(4.dp)
            }

            // 内容
            Text(
                text = content,
                style =
                    MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 15.sp,
                        lineHeight = 22.sp,
                    ),
                color = Color(0xFF999999),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
            )

            SizeHeight(20.dp)

            // 按钮
            DialogButton(
                config = buttonConfig,
                onClick = onButtonClick,
                modifier = Modifier.width(160.dp),
            )
        }
    }
}

/**
 * 通用弹窗按钮组件
 */
@Composable
fun DialogButton(
    config: DialogButtonConfig,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .height(36.dp)
                .clip(RoundedCornerShape(30.dp))
                .background(config.backgroundColor)
                .clickable { onClick() },
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = config.text,
            style =
                MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 16.sp,
                    lineHeight = 22.sp,
                ),
            color = config.textColor,
        )
    }
}

/**
 * 长文本内容弹窗
 * 用于显示长文本信息，如规则说明、条款等
 * 严格按照 Figma 设计图实现：270x308dp，底部分割线+按钮
 *
 * @param title 弹窗标题
 * @param content 弹窗内容文本
 * @param buttonText 按钮文本，默认为"确定"
 * @param onButtonClick 按钮点击回调
 */
@Composable
fun LongTextDialog(
    title: String,
    content: String,
    buttonText: String = "确定".localized,
    onButtonClick: () -> Unit,
) {
    LongTextDialog(
        title = title,
        content = content,
        buttonContent = {
            // 分割线
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(0.5.dp)
                        .background(Color(0xFFE5E6EB)),
            )

            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(44.dp)
                        .clickable { onButtonClick() },
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = buttonText,
                    style =
                        MaterialTheme.typography.titleMedium.copy(
                            fontSize = 18.sp,
                            lineHeight = 26.sp,
                        ),
                    color = Color(0xFF111111),
                )
            }
        },
    )
}

/**
 * 长文本内容弹窗
 * 用于显示长文本信息，如规则说明、条款等
 * 严格按照 Figma 设计图实现：270x308dp，底部分割线+按钮
 *
 * @param title 弹窗标题
 * @param content 弹窗内容文本
 * @param buttonText 按钮文本，默认为"确定"
 * @param onButtonClick 按钮点击回调
 */
@Composable
fun LongTextDialog(
    title: String,
    content: String,
    buttonContent: @Composable ColumnScope.() -> Unit,
) {
    Surface(
        modifier =
            Modifier
                .width(270.dp)
                .height(308.dp),
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
        ) {
            // 标题区域
            Text(
                text = title,
                style =
                    MaterialTheme.typography.titleMedium.copy(
                        fontSize = 17.sp,
                        lineHeight = 24.sp,
                    ),
                color = Color(0xFF111111),
                textAlign = TextAlign.Center,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(top = 20.dp, start = 16.dp, end = 16.dp),
            )

            // 内容区域 - 可滚动
            Box(
                modifier =
                    Modifier
                        .weight(1f, false)
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = 16.dp, vertical = 12.dp),
            ) {
                Text(
                    text = content,
                    style =
                        MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 15.sp,
                            lineHeight = 22.sp,
                        ),
                    color = Color(0xFF666666),
                    textAlign = TextAlign.Left,
                )
            }
            // 按钮区域

            buttonContent()
        }
    }
}

// 预览组件
@Preview
@Composable
private fun RowDoubleActionDialogPreview() {
    WakooTheme {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.6f))
                    .padding(16.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            RowDoubleActionDialog(
                title = "注销账号",
                content = "注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。",
                cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消"),
                confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认注销"),
                onCancel = { },
                onConfirm = { },
            )
        }
    }
}

// 预览组件
@Preview
@Composable
private fun ColumnDoubleActionDialogPreview() {
    WakooTheme {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.6f))
                    .padding(16.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            ColumnDoubleActionDialog(
                title = "注销账号",
                content = "注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。",
                confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "取消"),
                cancelButtonConfig = DialogButtonStyles.Transparent.copy(text = "确认注销"),
                onCancel = { },
                onConfirm = { },
            )
        }
    }
}

@Preview
@Composable
private fun SingleActionDialogPreview() {
    WakooTheme {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.6f))
                    .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 充值钻石弹窗
            SingleActionDialog(
                content = "本次选择的卡片主题需要花费200钻石，您的钻石余额不足，请充值",
                buttonConfig = DialogButtonStyles.Primary.copy(text = "充值钻石"),
                onButtonClick = { },
            )

            // VIP弹窗
            SingleActionDialog(
                content = "本次选择的卡片主题仅会员用户可以使用，快去开通会员吧！",
                buttonConfig = DialogButtonStyles.VIP.copy(text = "开通会员"),
                onButtonClick = { },
            )
        }
    }
}

/**
 * 简单双操作弹窗
 * 用于简单的确认操作，只有一行文本内容
 * 严格按照 Figma 设计图实现：270x140dp
 *
 * @param content 弹窗内容文本
 * @param cancelButtonConfig 取消按钮配置
 * @param confirmButtonConfig 确认按钮配置
 * @param onCancel 取消按钮点击回调
 * @param onConfirm 确认按钮点击回调
 */
@Composable
fun SimpleDoubleActionDialog(
    content: String,
    cancelButtonConfig: DialogButtonConfig,
    confirmButtonConfig: DialogButtonConfig,
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
) {
    Surface(
        modifier =
            Modifier
                .width(270.dp)
                .height(140.dp),
        shape = RoundedCornerShape(8.dp),
        color = Color.White,
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 内容区域
            Box(
                modifier =
                    Modifier
                        .weight(1f)
                        .padding(horizontal = 16.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = content,
                    style =
                        MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 15.sp,
                            lineHeight = 22.sp,
                        ),
                    color = Color(0xFF111111),
                    textAlign = TextAlign.Center,
                )
            }

            // 按钮区域
            Row(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                // 取消按钮
                DialogButton(
                    config = cancelButtonConfig,
                    onClick = onCancel,
                    modifier = Modifier.weight(1f),
                )

                // 确认按钮
                DialogButton(
                    config = confirmButtonConfig,
                    onClick = onConfirm,
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

@Preview
@Composable
private fun SimpleDoubleActionDialogPreview() {
    WakooTheme {
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.6f)),
            contentAlignment = Alignment.Center,
        ) {
            SimpleDoubleActionDialog(
                content = "本次选择的卡片主题需要花费200钻石，确认扣除钻石并发布作品吗？",
                cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "取消"),
                confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "确认"),
                onCancel = { },
                onConfirm = { },
            )
        }
    }
}

@Preview
@Composable
private fun LongTextDialogPreview() {
    WakooTheme {
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.6f)),
            contentAlignment = Alignment.Center,
        ) {
            LongTextDialog(
                title = "内容合规准则",
                content = "我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。\n\n我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容，需要左侧对齐。我是长段的正文文本内容。",
                buttonText = "确定",
                onButtonClick = { },
            )
        }
    }
}
