package com.buque.wakoo.ui.screens.profile

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.EnumEntity
import com.buque.wakoo.bean.InputEdit
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.isAnimated
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.ui.dialog.CommonMultiPickerPanel
import com.buque.wakoo.ui.dialog.CommonPickerPanel
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.AddRound
import com.buque.wakoo.ui.icons.ArrowRight
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.login.Gender
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SingleTextSettingsMenuItem
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.TowTextsSettingsMenuItem
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.ui.widget.wheelPicker.AppDateWheelPickerPanel
import com.buque.wakoo.ui.widget.wheelPicker.WheelPicker
import com.buque.wakoo.ui.widget.wheelPicker.WheelPickerPanelScaffold
import com.buque.wakoo.ui.widget.wheelPicker.rememberWheelPickerState
import com.buque.wakoo.viewmodel.ChangeableProperty
import com.buque.wakoo.viewmodel.EditUserInfoViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.todayIn
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

@OptIn(ExperimentalTime::class)
@Composable
fun EditUserInfoScreen(onNavigateTo: (AppNavKey) -> Unit) {
    val user = LocalSelfUserProvider.current

    val viewModel = viewModel<EditUserInfoViewModel>()

    val scope = rememberCoroutineScope()

    val loading = LocalLoadingManager.current

    val rootNavController = LocalAppNavController.root

    val dialogController = rememberDialogController(render = false)

    val context = LocalContext.current

    val avatarLauncher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            if (result.list.isNotEmpty()) {
                val uriString =
                    result.list
                        .single()
                        .uriString
                loading.show(scope) {
                    val isAnimated =
                        withContext(Dispatchers.IO) {
                            isAnimated(context.contentResolver, uriString.toUri())
                        }
                    if (isAnimated) {
                        showToast("不支持使用动态头像，请重新选择".localized)
                        return@show
                    }
                    viewModel.updateAvatar(
                        uriString
                            .toUri(),
                    )
                }
            }
        }

    val albumLauncher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            if (result.list.isNotEmpty()) {
                loading.show(scope) {
                    viewModel.updateAlbum(result.list)
                }
            }
        }

    SegColorTitleScreenScaffold(
        title = "编辑资料".localized,
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
        ) {
            SingleTextSettingsMenuItem(
                titleText = "头像".localized,
                previewContent = {
                    AvatarNetworkImage(
                        user = user,
                        size = 48.dp,
                        enabled = false,
                    )
                },
                onClick = {
                    avatarLauncher.launch(Route.MediaSelector())
                },
            )

            Divider()

            TowTextsSettingsMenuItem(
                titleText = "昵称".localized,
                previewText = user.name,
                onClick = {
                    onNavigateTo(
                        Route.EditTextUserInfo(
                            InputEdit.createNickNameEdit(
                                user.name,
                            ),
                        ),
                    )
                },
            )

            Divider()

            TowTextsSettingsMenuItem(
                titleText = "生日".localized,
                previewText = user.birthday.ifEmpty { "请选择".localized },
                onClick = {
                    dialogController.easyPostBottomPanel(
                        useSystemDialog = false,
                    ) {
                        val initSelectedDate =
                            user.birthday.takeIf { it.isNotEmpty() }?.let {
                                LocalDate.parse(it)
                            } ?: Clock.System
                                .todayIn(TimeZone.currentSystemDefault())
                                .minus(18, DateTimeUnit.YEAR)
                        AppDateWheelPickerPanel(
                            title = "选择生日".localized,
                            initSelectedDate = initSelectedDate,
                            onConfirm = { changed, date ->
                                if (changed) {
                                    loading.show(scope) {
                                        viewModel.updateUserInfo(ChangeableProperty.BIRTHDAY, date.toString())
                                    }
                                }
                            },
                        )
                    }
                },
            )

            Divider()

            val uiConfig by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()

            AnimatedVisibility(uiConfig.showGenderSetting) {
                Column {
                    TowTextsSettingsMenuItem(
                        titleText = "性别".localized,
                        previewText = user.displayGender,
                        onClick = {
                            dialogController.easyPostBottomPanel(
                                useSystemDialog = false,
                            ) {
                                val state =
                                    rememberWheelPickerState(
                                        initialIndex = if (user.genderIsSet) user.gender else 0,
                                        itemCount = Gender.entries.size,
                                    )

                                WheelPickerPanelScaffold(
                                    title = "选择性别".localized,
                                    onDismissRequest = {
                                        dismiss()
                                    },
                                    onConfirm = {
                                        dismiss()
                                        Gender.entries
                                            .getOrNull(state.snappedIndex)
                                            ?.toIntVale()
                                            ?.takeIf {
                                                it > 0
                                            }?.also { gender ->
                                                loading.show(scope) {
                                                    viewModel.updateUserInfo(ChangeableProperty.GENDER, gender)
                                                }
                                            }
                                    },
                                ) {
                                    WheelPicker(
                                        items = Gender.entries,
                                        state = state,
                                        visibleItemsCount = Gender.entries.size.coerceAtMost(5),
                                    )
                                }
                            }
                        },
                    )

                    HorizontalDivider(
                        color = Color(0xFFE5E5E5),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )
                }
            }

            TowTextsSettingsMenuItem(
                titleText = "个人简介".localized,
                previewText = user.intro,
                onClick = {
                    onNavigateTo(
                        Route.EditTextUserInfo(
                            InputEdit.createShortIntroEdit(
                                user.intro,
                            ),
                        ),
                    )
                },
            )

            Divider()

            TowTextsSettingsMenuItem(
                titleText = "身高".localized,
                previewText = user.formatHeight.ifEmpty { "未填写".localized },
                onClick = {
                    dialogController.easyPostBottomPanel(
                        useSystemDialog = false,
                    ) {
                        CommonPickerPanel(
                            title = "修改身高".localized,
                            initialValue = EnumEntity(user.height.toString(), user.height.toString()),
                            items = viewModel.getSelectorOptions(ChangeableProperty.HEIGHT),
                            toString = {
                                it.name
                            },
                            onDismissRequest = {
                                dismiss()
                            },
                            onConfirm = { value ->
                                dismiss()
                                loading.show(scope) {
                                    viewModel.updateUserInfo(ChangeableProperty.HEIGHT, value)
                                }
                            },
                        )
                    }
                },
            )

            Divider()

            // 相册
            Column(
                modifier =
                    Modifier.click {
                        onNavigateTo(Route.UserAlbum)
                    },
            ) {
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(
                                horizontal = 16.dp,
                                vertical = 20.dp,
                            ),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    Text(
                        text = "相册".localized,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF111111),
                    )

                    Weight()

                    Icon(
                        imageVector = WakooIcons.ArrowRight,
                        contentDescription = null,
                        modifier = Modifier.size(12.dp),
                        tint = Color(0xFF999999),
                    )
                }
                Row(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(
                                horizontal = 16.dp,
                            ),
                    horizontalArrangement = Arrangement.spacedBy(14.dp, alignment = Alignment.End),
                ) {
                    user.albums?.forEach { item ->
                        NetworkImage(
                            item.mediaUrl,
                            modifier =
                                Modifier
                                    .size(44.dp)
                                    .clip(RoundedCornerShape(8.dp)),
                        )
                    }
                    Image(
                        WakooIcons.AddRound,
                        contentDescription = null,
                        modifier =
                            Modifier
                                .size(44.dp)
                                .click(onClick = {
                                    albumLauncher.launch(Route.MediaSelector(maxSelectCount = 9))
                                })
                                .background(color = Color(0xffE9EAEF), shape = RoundedCornerShape(8.dp))
                                .padding(15.dp),
                    )
                }
                SizeHeight(20.dp)
            }

            // 日语区
            if (user.socialInfo.isJP || LocalInspectionMode.current) {
                Divider()

                val nativeProfile = user.socialInfo.nativeProfile
                TowTextsSettingsMenuItem(
                    titleText = "居住地".localized,
                    previewText = nativeProfile.cityCode.name,
                    onClick = {
                        dialogController.easyPostBottomPanel(
                            useSystemDialog = false,
                        ) {
                            CommonMultiPickerPanel(
                                "修改居住地".localized,
                                viewModel.getAddressSelectorOptions(),
                                level = 2,
                                initialIndexs = viewModel.getAddressIndexs(nativeProfile.cityCode),
                                onDismissRequest = {
                                    dismiss()
                                },
                                onConfirm = { values ->
                                    dismiss()
                                    loading.show(scope) {
                                        viewModel.updateAddress(ChangeableProperty.LIVE_ADDRESS, values)
                                    }
                                },
                            )
                        }
                    },
                )

                Divider()

                TowTextsSettingsMenuItem(
                    titleText = "出生地".localized,
                    previewText = nativeProfile.birthCityCode.name,
                    onClick = {
                        dialogController.easyPostBottomPanel(
                            useSystemDialog = false,
                        ) {
                            CommonMultiPickerPanel(
                                "修改出生地".localized,
                                viewModel.getAddressSelectorOptions(),
                                level = 1,
                                initialIndexs = viewModel.getAddressIndexs(nativeProfile.birthCityCode),
                                onDismissRequest = {
                                    dismiss()
                                },
                                onConfirm = { values ->
                                    dismiss()
                                    loading.show(scope) {
                                        viewModel.updateAddress(ChangeableProperty.BORN_ADDRESS, values)
                                    }
                                },
                            )
                        }
                    },
                )
                Divider()

                TowTextsSettingsMenuItem(
                    titleText = "学历".localized,
                    previewText = nativeProfile.educationalHistory.name,
                    onClick = {
                        dialogController.easyPostBottomPanel(
                            useSystemDialog = false,
                        ) {
                            CommonPickerPanel(
                                title = "修改学历".localized,
                                initialValue = nativeProfile.educationalHistory,
                                items = viewModel.getSelectorOptions(ChangeableProperty.ACADEMY),
                                toString = {
                                    it.name
                                },
                                onDismissRequest = {
                                    dismiss()
                                },
                                onConfirm = { value ->
                                    dismiss()
                                    loading.show(scope) {
                                        viewModel.updateUserInfo(ChangeableProperty.ACADEMY, value)
                                    }
                                },
                            )
                        }
                    },
                )

                Divider()

                TowTextsSettingsMenuItem(
                    titleText = "职业".localized,
                    previewText = nativeProfile.job.name,
                    onClick = {
                        dialogController.easyPostBottomPanel(
                            useSystemDialog = false,
                        ) {
                            CommonPickerPanel(
                                title = "修改职业".localized,
                                initialValue = nativeProfile.job,
                                items = viewModel.getSelectorOptions(ChangeableProperty.JOB),
                                toString = {
                                    it.name
                                },
                                onDismissRequest = {
                                    dismiss()
                                },
                                onConfirm = { value ->
                                    dismiss()
                                    loading.show(scope) {
                                        viewModel.updateUserInfo(ChangeableProperty.JOB, value)
                                    }
                                },
                            )
                        }
                    },
                )
                Divider()

                TowTextsSettingsMenuItem(
                    titleText = "体型".localized,
                    previewText = nativeProfile.bodyType.name,
                    onClick = {
                        dialogController.easyPostBottomPanel(
                            useSystemDialog = false,
                        ) {
                            CommonPickerPanel(
                                title = "修改体型".localized,
                                initialValue = nativeProfile.bodyType,
                                items = viewModel.getSelectorOptions(ChangeableProperty.BODY_SIZE),
                                toString = {
                                    it.name
                                },
                                onDismissRequest = {
                                    dismiss()
                                },
                                onConfirm = { value ->
                                    dismiss()
                                    loading.show(scope) {
                                        viewModel.updateUserInfo(ChangeableProperty.BODY_SIZE, value)
                                    }
                                },
                            )
                        }
                    },
                )
            }
        }
    }

    dialogController.RenderDialogs(Unit)
}

@Composable
private fun Divider() {
    HorizontalDivider(
        color = Color(0xFFE5E5E5),
        thickness = 0.5.dp,
        modifier = Modifier.padding(horizontal = 16.dp),
    )
}

@Preview(showBackground = true)
@Composable
private fun EditProfileScreenPreview() {
    WakooTheme {
        EditUserInfoScreen({})
    }
}
