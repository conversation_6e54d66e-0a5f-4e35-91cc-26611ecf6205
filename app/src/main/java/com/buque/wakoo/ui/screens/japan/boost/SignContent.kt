package com.buque.wakoo.ui.screens.japan.boost

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.boost.MissionInfo
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.CheckFill
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooSecondaryText
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DailySignInDialog(
    info: MissionInfo.MissionSeries,
    onSign: OnAction = {},
    onDismiss: () -> Unit = {},
) {
    val isPt = info.prizeType == AwardType.inc
    Box(
        modifier =
            Modifier
                .width(289.dp)
                .aspectRatio(289 / 331f)
                .paint(
                    painterResource(if (isPt) R.drawable.bg_sign_pt else R.drawable.bg_sign_dia),
                    contentScale = ContentScale.FillBounds,
                ),
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly,
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .heightIn(min = 72.dp),
            ) {
                Box(modifier = Modifier.fillMaxWidth().padding(horizontal = 12.dp)) {
                    BasicText(
                        text = info.seriesTitle, // 每日签到
                        autoSize = TextAutoSize.StepBased(minFontSize = 12.sp, maxFontSize = 18.sp),
                        maxLines = 1,
                        style =
                            TextStyle(
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF118C6E),
                            ),
                        modifier =
                            Modifier
                                .align(Alignment.Center)
                                .padding(horizontal = 30.dp),
                    )
                    // 关闭按钮
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = Color.White,
                        modifier =
                            Modifier
                                .align(Alignment.CenterEnd)
                                .size(24.dp)
                                .clickable(onClick = onDismiss),
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))
                BasicText(
                    text = info.finishedTotips, // 签到第3天和第7天有豪华特权
                    autoSize = TextAutoSize.StepBased(minFontSize = 8.sp, maxFontSize = 12.sp),
                    maxLines = 2,
                    style =
                        TextStyle(
                            fontSize = 12.sp,
                            textAlign = TextAlign.Center,
                            color = Color(0xFF118C6E),
                        ),
                    modifier = Modifier.padding(horizontal = 20.dp),
                )
            }
            // 奖励网格
            LazyVerticalGrid(
                columns = GridCells.Fixed(4), // 每行4个奖励项
                contentPadding = PaddingValues(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(), // 根据内容自适应高度
            ) {
                itemsIndexed(info.tasks, span = { index, item ->
                    GridItemSpan(2.takeIf { index == 6 } ?: 1)
                }) { index, reward ->
                    RewardItem(reward, day = index + 1)
                }
            }

            // 连续签到信息
            Text(
                text = info.treasureTotips, // 连续X天签到
                fontSize = 14.sp,
                color = Color(0xFFFFB71A),
                fontWeight = FontWeight.SemiBold,
            )

            // 签到按钮
            SolidButton(
                "签到领金币".localized, // 签到并获取硬币
                onClick = onSign,
                modifier =
                    Modifier
                        .padding(horizontal = 16.dp)
                        .height(36.dp),
                backgroundColor = Color(0xFFFFB71A),
                textColor = Color.White,
                fontSize = 16.sp,
            )
        }
    }
}

@Composable
fun RewardItem(
    item: MissionInfo.MissionSeries.Task,
    day: Int = 1,
) {
    val isSigned = item.finished
    val backgroundColor = if (isSigned) Color(0xFFE8FFE8) else Color(0xFFF8F8F8) // 已签到浅绿色，未签到浅灰色
    val borderColor = if (isSigned) Color(0xFF6DE8AA) else Color(0xFFE0E0E0) // 已签到绿色边框，未签到灰色边框

    Column(
        modifier =
            Modifier
                .aspectRatio(if (day == 7) (126 / 72f) else (60 / 72f)) // 使项保持正方形
                .clip(RoundedCornerShape(8.dp)) // 圆角
                .background(backgroundColor)
                .border(1.dp, borderColor, RoundedCornerShape(8.dp)),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // 天数标记
        Text(
            text = "$day",
            fontSize = 10.sp,
            fontWeight = FontWeight.Bold,
            lineHeight = 14.sp,
            color = Color.White,
            textAlign = TextAlign.Center,
            modifier =
                Modifier
                    .align(Alignment.Start)
                    .width(14.dp)
                    .background(
                        Color(0xFFFFB71A),
                        RoundedCornerShape(bottomEnd = 4.dp), // 底部右侧圆角
                    ),
        )
        NetworkImage(
            item.extra.prizeIcon,
            modifier =
                Modifier
                    .height(24.dp)
                    .align(Alignment.CenterHorizontally),
            contentScale = ContentScale.FillHeight,
        )

        // 奖励图标或打钩
        if (isSigned) {
            SizeHeight(2.dp)
            Icon(
                painter = rememberVectorPainter(WakooIcons.CheckFill), // 打钩图标
                contentDescription = "已签到",
                tint = Color(0xFF6DE8AA), // 绿色
                modifier =
                    Modifier
                        .size(16.dp)
                        .align(Alignment.CenterHorizontally),
            )
        } else {
            SizeHeight(4.dp)
            Text(item.prize, fontSize = 10.sp, color = WakooSecondaryText, modifier = Modifier.align(Alignment.CenterHorizontally))
        }
        Spacer(modifier = Modifier.height(2.dp))
    }
}

// 定义一个空的Painter，用于图片占位符 (如果需要)
// 沿用之前的EmptyPainter
object EmptyPainter : Painter() {
    override val intrinsicSize: Size = Size.Unspecified

    override fun DrawScope.onDraw() { // Do nothing
    }
}

@Preview(showBackground = true, widthDp = 375, heightDp = 600) // 模拟弹窗高度
@Composable
fun DailySignInDialogPreview() {
    MaterialTheme {
        // 使用MaterialTheme包裹以应用默认主题
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f)),
            // 模拟背景遮罩
            contentAlignment = Alignment.Center,
        ) {
            val info =
                AppJson.decodeFromString<MissionInfo.MissionSeries>(
                    "{\"series_id\":33,\"series_title\":\"ログインしてポイントを受け取る\",\"series_slug\":\"wkjp_female_check_in_task\",\"series_type\":3,\"prize_type\":7,\"tasks\":[{\"id\":132,\"title\":\"已领取\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+60\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":true,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":133,\"title\":\"已领取\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+150\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":true,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":134,\"title\":\"3日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"最大+1500\",\"prize_type\":8,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point2.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":135,\"title\":\"4日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+300\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":136,\"title\":\"5日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+450\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":137,\"title\":\"6日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"+600\",\"prize_type\":7,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point1.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1},{\"id\":138,\"title\":\"7日目\",\"desc\":\"その日のチェックインを完了する\",\"prize\":\"最大+6000\",\"prize_type\":8,\"extra\":{\"hint\":\"\",\"task_icon\":\"\",\"prize_icon\":\"https://media.wakooclub.com/opsite/itasks/wakoo_point3.webp\",\"min_version\":\"\",\"big_gold_box\":false,\"predecessors\":null,\"twd_prize_value\":null,\"finish_times_limit\":null},\"finished\":false,\"progress\":\"\",\"condition_type\":1,\"condition_times\":1}],\"today_finished\":true,\"treasure_totips\":\"2 日連続サインイン \",\"finished_totips\":\"3日目と7日目に登録してメガポイントをゲットしよう\",\"all_finished\":false,\"multiplier_help\":\"\",\"light_prize_infos\":[],\"current_light_count\":0,\"light_prize_rule_title\":\"\",\"light_prize_rule_content\":\"\"}",
                )
            DailySignInDialog(info)
        }
    }
}
