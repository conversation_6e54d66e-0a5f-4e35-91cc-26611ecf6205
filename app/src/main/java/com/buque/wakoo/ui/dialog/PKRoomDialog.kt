package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.getValue
import com.buque.wakoo.ext.rememberIsFirstComposition
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.network.api.bean.PKEvent
import com.buque.wakoo.network.api.bean.PkUser
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.AutoSizeText
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.StandardListItemScaffold
import com.buque.wakoo.ui.widget.VerticalGrid
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.CheersGroupListViewModel

//region pk设置dialog
@Composable
fun DialogScope.PKSettingDialogContent(roomInfoState: LiveRoomInfoState) {
    val status = roomInfoState.extraInfo.pkRoomInfo?.pkStatus ?: 0
    //1未开始 2进行中 3已结束
    if (status == 1) {
        BottomPanelScaffold(
            title = "PK设置".localized,
            useClose = true,
            modifier = Modifier.fillMaxHeight(0.7f),
            backgroundColor = WakooWhite,
            contentPadding = 0.dp,
            bottomPadding = 0.dp,
        ) {
            PKStartSetting { penalty, duration ->
                dismiss()
                roomInfoState.sendEvent(RoomEvent.OnPkSettingsEvent(1, penalty, duration))
            }
        }
    } else if (status == 2) {
        BottomPanelScaffold(
            title = "PK设置".localized,
            useClose = true,
            modifier = Modifier.fillMaxHeight(0.7f),
            backgroundColor = WakooWhite,
            contentPadding = 0.dp,
            bottomPadding = 0.dp,
        ) {
            PKModifySetting(roomInfoState.extraInfo.pkRoomInfo?.title.orEmpty(), roomInfoState.extraInfo.pkRoomInfo?.pkDuration ?: 0, onRestart = {
                dismiss()
                roomInfoState.sendEvent(RoomEvent.OnPkSettingsEvent(3))
            }, onAddTime = {
                dismiss()
                roomInfoState.sendEvent(RoomEvent.OnPkSettingsEvent(2, duration = 5))
            })
        }
    }
}

@Composable
private fun PKStartSetting(modifier: Modifier = Modifier, onConfirm: (penalty: String, duration: Int) -> Unit = { _, _ -> }) {
    var battleText by remember { mutableStateOf("") }
    var selectedTime by remember { mutableStateOf(5) }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(color = Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 输入框标签
        Text(
            text = "PK惩罚".localized, style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp, fontWeight = FontWeight.Normal
            ), color = Color(0xFF111111), modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp)
        )

        // 输入框
        AppTextField(
            battleText,
            onValueChange = {
                battleText = it
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp),
            placeholder = "最多输入15个字".localized,
            maxLength = 15,
            backgroundColor = Color(0xFFF8F8F8),
            supportingStyle = MaterialTheme.typography.bodyMedium.copy(
                color = Color(0xFFB6B6B6),
            ),
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 时间选择标签
        Text(
            text = "本轮PK时间".localized, style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp, fontWeight = FontWeight.Normal
            ), color = Color(0xFF111111), modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp)
        )


        VerticalGrid(columns = 3, horizontalSpace = 8.dp, verticalSpace = 8.dp) {
            listOf(5, 15, 30, 60).forEach {
                val isSelected = it == selectedTime
                Box(
                    modifier = modifier
                        .fillMaxWidth()
                        .clickable {
                            selectedTime = it
                        }
                        .background(
                            if (isSelected) {
                                Brush.horizontalGradient(
                                    colors = listOf(
                                        Color(0xFF66FE6B), Color(0xFF31FFA1)
                                    )
                                )
                            } else {
                                Brush.horizontalGradient(
                                    colors = listOf(
                                        Color(0xFFF8F8F8), Color(0xFFF8F8F8)
                                    )
                                )
                            }, shape = CircleShape
                        )
                        .padding(vertical = 13.dp), contentAlignment = Alignment.Center) {
                    Text(
                        text = it.toString(), style = MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 14.sp, fontWeight = FontWeight.Normal
                        ), color = if (isSelected) Color.White else Color(0xFF666666)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        GradientButton(
            "确认".localized, onClick = {
                onConfirm(battleText, selectedTime)
            }, gradientColors = listOf(
                Color(0xFFA3FF2C), Color(0xFF31FFA1)
            ), modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .height(48.dp)
        )
        SizeHeight(24.dp)
    }
}

@Composable
private fun PKModifySetting(
    currentPenalty: String = "",
    currentDuration: Int = 15,
    modifier: Modifier = Modifier,
    onRestart: () -> Unit,
    onAddTime: () -> Unit,
) {
    var battleText by remember { mutableStateOf(currentPenalty) }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(color = Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 输入框标签
        Text(
            text = "PK惩罚".localized, style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp, fontWeight = FontWeight.Normal
            ), color = Color(0xFF111111), modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp)
        )

        // 输入框
        AppTextField(
            battleText,
            onValueChange = {
                battleText = it
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp),
            placeholder = "最多输入15个字".localized,
            maxLength = 15,
            enabled = false,
            backgroundColor = Color(0xFFF8F8F8),
            supportingStyle = MaterialTheme.typography.bodyMedium.copy(
                color = Color(0xFFB6B6B6),
            ),
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 时间选择标签
        Text(
            text = "本轮PK时间".localized, style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp, fontWeight = FontWeight.Normal
            ), color = Color(0xFF111111), modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp)
        )

        VerticalGrid(columns = 3, horizontalSpace = 8.dp, verticalSpace = 8.dp) {
            Box(
                modifier = modifier
                    .fillMaxWidth()
                    .background(
                        Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF66FE6B), Color(0xFF31FFA1)
                            )
                        ), shape = CircleShape
                    )
                    .padding(vertical = 13.dp), contentAlignment = Alignment.Center
            ) {
                Text(
                    text = currentDuration.toString(), style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp, fontWeight = FontWeight.Normal
                    ), color = Color.White
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        Row(
            modifier = Modifier.padding(horizontal = 16.dp)
        ) {
            OutlinedButton(
                "加时5分钟".localized, onClick = {
                    onAddTime()
                })
            SizeWidth(12.dp)
            GradientButton(
                "重新开始".localized, onClick = {
                    onRestart()
                }, gradientColors = listOf(
                    Color(0xFFA3FF2C), Color(0xFF31FFA1)
                ), modifier = Modifier
                    .weight(1f)
                    .height(48.dp)
            )
        }

        SizeHeight(24.dp)
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewPKSetting(modifier: Modifier = Modifier) {
    Column {
        PKStartSetting()
        SizeHeight(20.dp)
        PKModifySetting(onRestart = {}, onAddTime = {})
    }
}

//endregion


//region pk助威榜
/**
 * @param pkSideInt 左边蓝色1  右边红色2
 */
@Composable
fun DialogScope.CheersGroupListDialogContent(roomId: String, pkSideInt: Int, modifier: Modifier = Modifier) {
    val viewModel = viewModel<CheersGroupListViewModel>(key = "$roomId-$pkSideInt", initializer = {
        CheersGroupListViewModel(pkSideInt)
    })
    val listState = rememberLazyListState()
    val firstEnter by rememberIsFirstComposition()

    BottomPanelScaffold(
        title = "本轮助威榜".localized,
        useClose = false,
        modifier = modifier.fillMaxHeight(0.7f),
        backgroundColor = WakooWhite,
        contentPadding = 0.dp,
        bottomPadding = 0.dp,
    ) {
        SizeHeight(12.dp)

        CStateListPaginateLayout<String, Int, PkUser, CheersGroupListViewModel>(
            reqKey = roomId, listState = listState, modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.5f)
                .background(
                    Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                ), viewModel = viewModel, emptyText = "助威榜虚位以待".localized, emptyId = R.drawable.ic_empty_for_all, autoRefresh = firstEnter
        ) { paginateState, list ->
            LazyColumn(modifier = Modifier.fillMaxSize(), state = listState) {
                stickyHeader {
                    if (viewModel.desc.isNotBlank()) {
                        Text(
                            viewModel.desc,
                            color = Color(0xff666666),
                            fontWeight = FontWeight.W400,
                            fontSize = 12.sp,
                            modifier = Modifier
                                .padding(bottom = 24.dp)
                                .fillMaxWidth(),
                            textAlign = TextAlign.Center
                        )
                    }
                }

                itemsIndexed(list) { index, user ->
                    StandardListItemScaffold(modifier = Modifier.padding(horizontal = 16.dp), startContent = {//排名
                        AutoSizeText(
                            "${index + 1}",
                            modifier = Modifier.widthIn(min = 27.dp),
                            color = if (index == 0) Color(0xffF53F3F) else if (index == 1) Color(0xffFF7D00) else if (index == 2) Color(0xffFFC700) else Color(0xffb6b6b6),
                            minTextSize = 12.sp,
                            maxTextSize = 16.sp,
                            fontFamily = FontFamily.MI_SANS,
                            fontWeight = FontWeight.W900
                        )
                    }, centerContent = {//头像名字
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            user.user?.let {
                                AvatarNetworkImage(
                                    it, modifier = Modifier.border(
                                        1.dp, if (index == 0) Color(0xffF53F3F) else if (index == 1) Color(0xffFF7D00) else if (index == 2) Color(0xffFFC700) else Color.Transparent, CircleShape
                                    )
                                )
                                SizeWidth(8.dp)
                                Text(
                                    it.name, fontSize = 14.sp, fontWeight = FontWeight.W400, color = Color(0xff111111)
                                )
                            }
                        }
                    }, endContent = {//数组
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(user.value.toString(), color = Color(0xffb6b6b6), fontWeight = FontWeight.W400, fontSize = 12.sp)
                            SizeWidth(4.dp)
                            Image(painter = painterResource(R.drawable.ic_pkmode_rank_rocket), modifier = Modifier.size(20.dp), contentDescription = null)
                        }
                    })
                }
            }
        }
    }
}
//endregion


//region pk结果弹窗
@Composable
fun DialogScope.PKResultDialog(pkEvent: PKEvent) {
    PKResultDialogContent(pkEvent) {
        dismiss()
    }
}

@Composable
private fun PKResultDialogContent(pkEvent: PKEvent, onConfirm: () -> Unit = {}) {
    Box(modifier = Modifier.width(270.dp)) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 27.dp)
                .background(
                    brush = Brush.verticalGradient(
                        if (pkEvent.winnerSide == 1) listOf(Color(0xff1a3d74), Color(0xff3173da))
                        else listOf(Color(0xff8A0075), Color(0xffFF9496))
                    ), shape = RoundedCornerShape(size = 16.dp)
                )
                .paint(
                    painter = painterResource(if (pkEvent.winnerSide == 1) R.drawable.ic_pk_result_blue_bg else R.drawable.ic_pk_result_red_bg), contentScale = ContentScale.FillWidth
                )
                .border(width = 1.dp, color = if (pkEvent.winnerSide == 1) Color(0xFF58AEFF) else Color(0xffFF4AC6), shape = RoundedCornerShape(size = 16.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SizeHeight(72.dp)

            Text(
                pkEvent.digest,
                fontSize = 18.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFFFDFFA3),
                textAlign = TextAlign.Center,
            )

            pkEvent.winnerInfo?.let { info ->
                Row(
                    modifier = Modifier
                        .padding(top = 24.dp)
                        .fillMaxWidth()
                ) {
                    info.cheerUserInfo?.let {
                        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.weight(1f)) {
                            Box(contentAlignment = Alignment.BottomCenter) {
                                AvatarNetworkImage(it.user, size = 72.dp, modifier = Modifier.border(1.dp, Color.White, CircleShape), enabled = false)
                                Image(
                                    painter = painterResource(R.drawable.ic_pk_support),
                                    contentDescription = null, modifier = Modifier.height(20.dp), contentScale = ContentScale.FillHeight
                                )
                            }
                            Text(
                                it.user.name, fontSize = 14.sp, color = Color.White,
                                lineHeight = 14.sp,
                                maxLines = 1, overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.padding(vertical = 8.dp, horizontal = 4.dp)
                            )
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.ic_pkmode_rank_rocket),
                                    contentDescription = null,
                                    modifier = Modifier.height(20.dp),
                                )
                                SizeWidth(1.dp)
                                Text(
                                    text = it.value.toString(),
                                    color = WakooWhite,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                )
                            }
                        }
                    }

                    info.heartUserInfo?.let {
                        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.weight(1f)) {
                            Box(contentAlignment = Alignment.BottomCenter) {
                                AvatarNetworkImage(
                                    it.user, size = 72.dp,
                                    modifier = Modifier.border(1.dp, Color.White, CircleShape), enabled = false
                                )
                                Image(
                                    painter = painterResource(R.drawable.ic_pk_beauty),
                                    contentDescription = null, modifier = Modifier.height(20.dp), contentScale = ContentScale.FillHeight
                                )
                            }
                            Text(
                                it.user.name, fontSize = 14.sp, color = Color.White,
                                lineHeight = 14.sp,
                                maxLines = 1, overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.padding(vertical = 8.dp, horizontal = 4.dp)
                            )
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.ic_mic_score),
                                    contentDescription = null,
                                    modifier = Modifier.height(20.dp),
                                )
                                SizeWidth(1.dp)
                                Text(
                                    text = it.value.toString(),
                                    color = WakooWhite,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                )
                            }
                        }
                    }
                }
            }


            SolidButton(
                "确定", onClick = {
                    onConfirm()
                }, backgroundColor = if (pkEvent.winnerSide == 1) Color(0xff4A90FF) else Color(0xffC94F86),
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 20.dp, top = 16.dp)
                    .fillMaxWidth()
                    .height(36.dp),
                fontSize = 16.sp,
                textColor = Color.White
            )
        }
        Image(
            painter = painterResource(if (pkEvent.winnerSide == 1) R.drawable.ic_pk_blue_win_top else R.drawable.ic_pk_red_win_top), contentDescription = null, modifier = Modifier
                .align(Alignment.TopCenter)
                .size(132.dp, 80.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewPKResultDialog(modifier: Modifier = Modifier) {
    val bean = AppJson.decodeFromString<PKEvent>(
        "{\"action\":\"end\",\"audioroom\":{\"id\":447,\"title\":\"啊啊啊啊啊\",\"room_locked\":false,\"text_only\":false,\"public_id\":\"100470\",\"owner\":{\"userid\":4847,\"public_id\":\"107233\",\"nickname\":\"华语区0001女的胡嘟嘟嘟嘟出嘟嘟嘟嘟嘟\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceLj?x-oss-process=image/format,webp\",\"gender\":2,\"age\":18,\"avatar_frame\":\"\",\"is_high_quality\":true,\"level\":50,\"exp_level_info\":{\"charm_level\":50,\"wealth_level\":50}}},\"digest\":\"恭喜蓝色方获得本轮PK胜利\",\"pk_info\":{\"title\":\"民宿瑞嘻嘻嘻休息洗\",\"pk_status\":3,\"duration\":300,\"remain_duration\":86399,\"end_time\":1754302828,\"blue_side_value\":5200,\"red_side_value\":0,\"blue_side_cheers\":[{\"userid\":4592,\"public_id\":\"106728\",\"nickname\":\"hh\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceIj?x-oss-process=image/format,webp\",\"gender\":2,\"age\":18,\"height\":0,\"avatar_frame\":\"https://s.test.wakooclub.com/aaceFf\",\"medal\":null,\"medal_list\":[{\"icon\":\"https://s.wakooclub.com/aabOPn\",\"width\":72,\"height\":24,\"product_series\":0}],\"level\":32,\"country_flag\":\"https://media.wakooclub.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\",\"exp_level_info\":{\"charm_level\":1,\"wealth_level\":1},\"have_certified\":true}],\"red_side_cheers\":[],\"blue_side_cheers_cnt\":1,\"red_side_cheers_cnt\":0,\"winner_side\":2,\"winner_info\":{\"cheer_user_info\":{\"user_info\":{\"userid\":4592,\"public_id\":\"106728\",\"nickname\":\"hh\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceIj?x-oss-process=image/format,webp\",\"gender\":2,\"age\":18,\"height\":0,\"avatar_frame\":\"https://s.test.wakooclub.com/aaceFf\",\"medal\":null,\"medal_list\":[{\"icon\":\"https://s.wakooclub.com/aabOPn\",\"width\":72,\"height\":24,\"product_series\":0}],\"level\":32,\"country_flag\":\"https://media.wakooclub.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\",\"exp_level_info\":{\"charm_level\":1,\"wealth_level\":1},\"have_certified\":true},\"value\":5200},\"heart_user_info\":{\"user_info\":{\"userid\":4847,\"public_id\":\"107233\",\"nickname\":\"华语区0001女的胡嘟嘟嘟嘟出嘟嘟嘟嘟嘟\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceLj?x-oss-process=image/format,webp\",\"gender\":2,\"age\":18,\"height\":0,\"avatar_frame\":\"\",\"medal\":{\"icon\":\"https://s.wakooclub.com/aabOPn\",\"width\":72,\"height\":24,\"product_series\":0},\"medal_list\":[{\"icon\":\"https://s.wakooclub.com/aabOPn\",\"width\":72,\"height\":24,\"product_series\":0}],\"level\":50,\"country_flag\":\"https://media.wakooclub.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\",\"exp_level_info\":{\"charm_level\":50,\"wealth_level\":50},\"have_certified\":true},\"value\":5200}}},\"winner_side\":2,\"winner_info\":{\"cheer_user_info\":{\"user_info\":{\"userid\":4592,\"public_id\":\"106728\",\"nickname\":\"hh\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceIj?x-oss-process=image/format,webp\",\"gender\":2,\"age\":18,\"height\":0,\"avatar_frame\":\"https://s.test.wakooclub.com/aaceFf\",\"medal\":null,\"medal_list\":[{\"icon\":\"https://s.wakooclub.com/aabOPn\",\"width\":72,\"height\":24,\"product_series\":0}],\"level\":32,\"country_flag\":\"https://media.wakooclub.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\",\"exp_level_info\":{\"charm_level\":1,\"wealth_level\":1},\"have_certified\":true},\"value\":5200},\"heart_user_info\":{\"user_info\":{\"userid\":4847,\"public_id\":\"107233\",\"nickname\":\"华语区0001女的胡嘟嘟嘟嘟出嘟嘟嘟嘟嘟\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceLj?x-oss-process=image/format,webp\",\"gender\":2,\"age\":18,\"height\":0,\"avatar_frame\":\"\",\"medal\":{\"icon\":\"https://s.wakooclub.com/aabOPn\",\"width\":72,\"height\":24,\"product_series\":0},\"medal_list\":[{\"icon\":\"https://s.wakooclub.com/aabOPn\",\"width\":72,\"height\":24,\"product_series\":0}],\"level\":50,\"country_flag\":\"https://media.wakooclub.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\",\"exp_level_info\":{\"charm_level\":50,\"wealth_level\":50},\"have_certified\":true},\"value\":5200}}}"
    )
    PKResultDialogContent(bean)
}
//endregion
