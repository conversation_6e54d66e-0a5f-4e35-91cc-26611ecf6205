package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.BrokeUp: ImageVector
    get() {
        if (_Frame != null) {
            return _Frame!!
        }
        _Frame = ImageVector.Builder(
            name = "Frame",
            defaultWidth = 16.dp,
            defaultHeight = 16.dp,
            viewportWidth = 16f,
            viewportHeight = 16f
        ).apply {
            path(fill = SolidColor(Color(0xFFCCCCCC))) {
                moveTo(11.129f, 1.352f)
                curveTo(10.516f, 1.352f, 10.381f, 1.485f, 9.829f, 1.734f)
                lineTo(8.355f, 4.152f)
                lineTo(10f, 5.396f)
                lineTo(7.303f, 8.427f)
                lineTo(8.82f, 11.301f)
                lineTo(6.399f, 8.374f)
                lineTo(8.355f, 5.778f)
                lineTo(6.399f, 4.916f)
                lineTo(7.674f, 2.712f)
                curveTo(6.865f, 1.849f, 6.053f, 1.36f, 4.862f, 1.36f)
                curveTo(2.489f, 1.352f, 0.551f, 3.307f, 0.507f, 5.734f)
                verticalLineTo(5.867f)
                curveTo(0.515f, 6.996f, 0.933f, 8.054f, 1.689f, 8.872f)
                curveTo(1.707f, 8.889f, 1.715f, 8.907f, 1.733f, 8.925f)
                lineTo(1.795f, 8.987f)
                lineTo(7.324f, 14.401f)
                curveTo(7.502f, 14.578f, 7.742f, 14.676f, 8f, 14.676f)
                curveTo(8.258f, 14.676f, 8.507f, 14.569f, 8.693f, 14.392f)
                curveTo(8.871f, 14.232f, 13.218f, 10f, 14.222f, 8.987f)
                lineTo(14.231f, 8.978f)
                lineTo(14.267f, 8.934f)
                curveTo(15.058f, 8.089f, 15.502f, 6.969f, 15.493f, 5.814f)
                curveTo(15.493f, 3.36f, 13.538f, 1.352f, 11.129f, 1.352f)
                close()
            }
        }.build()

        return _Frame!!
    }

@Suppress("ObjectPropertyName")
private var _Frame: ImageVector? = null
