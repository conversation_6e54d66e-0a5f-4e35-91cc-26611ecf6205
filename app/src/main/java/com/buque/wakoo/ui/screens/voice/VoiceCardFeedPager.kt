package com.buque.wakoo.ui.screens.voice

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.FeedItemData
import com.buque.wakoo.bean.LiveRoomCardItem
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.VoiceListTab
import com.buque.wakoo.ui.screens.liveroom.LiveMicMode
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMode
import com.buque.wakoo.ui.screens.liveroom.toRoomUser
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.CommonBanner
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.ui.widget.state.CStateVerticalPagerPaginateLayout
import com.buque.wakoo.ui.widget.voice.LiveRoomFeedCard
import com.buque.wakoo.ui.widget.voice.UserVoiceFeedCard
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBusEffect
import com.buque.wakoo.viewmodel.VoiceListViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

@Composable
fun VoiceVerticalListPager(
    tagPrefix: String,
    tab: VoiceListTab.ISquare,
    indexInParent: Int,
    parentPagerState: PagerState,
    showBanner: Boolean = false,
    modifier: Modifier = Modifier,
) {
    val isActivePageState =
        remember(indexInParent) {
            derivedStateOf {
                parentPagerState.currentPage == indexInParent
            }
        }
    val context = LocalContext.current
    val viewModel = viewModel<VoiceListViewModel>(factory = VoiceListViewModel.Factory(context))

    val rootNavController = LocalAppNavController.root
    val scope = rememberCoroutineScope()

    CStateVerticalPagerPaginateLayout<String, Int, VoiceListTab, FeedItemData, VoiceListViewModel>(
        reqKey = "",
        tabKey = tab,
        modifier = modifier,
        viewModel = viewModel,
        emptyButton = "刷新列表".localized,
        onEmptyClick = { paginateState, viewModel ->
            scope.launch {
                paginateState.setEnabled(false)
                val result = viewModel.refreshTabList("", tab, false)
                if (result.success && result.nextPageKey != null) {
                    paginateState.reset(result.nextPageKey, true)
                }
            }
        },
        wrapperBox = { content ->
            if (showBanner) {
                Column {
                    CommonBanner(
                        204,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp)
                                .padding(horizontal = 16.dp),
                    )
                    content()
                }
            } else {
                content()
            }
        },
    ) { paginateState, pagerState, list ->

        if (isActivePageState.value) {
            LaunchedEffect(pagerState) {
                launch {
                    snapshotFlow {
                        pagerState.currentPage
                    }.collectLatest {
                        MediaPlayerManager.onPageSelected(it)
                    }
                }

                launch {
                    snapshotFlow {
                        list.getOrNull(pagerState.settledPage)
                    }.collectLatest {
                        if (it is LiveRoomCardItem) {
                            while (isActive) {
                                delay(5000)
                                viewModel.refreshRoomState(it, tab)
                            }
                        }
                    }
                }
            }

            EventBusEffect<AppEvent.Refresh> {
                if (it.flag == "square") {
                    scope.launch {
                        pagerState.scrollToPage(0, 0f)
                        paginateState.setEnabled(false)
                        val result = viewModel.refreshTabList("", tab, true)
                        if (result.success && result.nextPageKey != null) {
                            paginateState.reset(result.nextPageKey, true)
                        }
                    }
                }
            }
        }

        VerticalPager(
            state = pagerState,
            modifier =
                Modifier
                    .fillMaxSize(),
            beyondViewportPageCount = 2,
        ) { page ->

            when (val item = list[page]) {
                is VoiceCardItem -> {
                    // 每个页面的内容
                    VoiceCardItemContent(
                        tagPrefix = tagPrefix,
                        tab = tab,
                        page = page,
                        item = item,
                        downloadPath = viewModel.downloadPath,
                        pagerState = pagerState,
                        isActivePageState = isActivePageState,
                        modifier =
                            Modifier
                                .padding(
                                    start = 24.dp,
                                    top = 16.dp,
                                    end = 24.dp,
                                ).fillMaxWidth(),
                        onToggleLike = {
                            viewModel.requestLikeVoice(item, tab)
                        },
                        onToggleFavorite = {
                            viewModel.requestFavoriteVoice(item, tab)
                        },
                        onToggleFollow = {
                            viewModel.requestFollowUser(item, tab)
                        },
                        onReport = {
                            rootNavController.push(Route.Report(5, item.id))
                        },
                        onDisLiked = {
                            viewModel.requestMarkAsBored(item, tab)
                        },
                        onBlacked = {
                            viewModel.blackUser(item.user.id)
                        },
                        onUserClick = {
                            rootNavController.push(Route.UserProfile(item.user))
                        },
                    )
                }

                is LiveRoomCardItem -> {
                    val tag = "$tagPrefix$tab-${item.identityId}"

                    val playItem =
                        remember(tag) {
                            PlayMediaItem.liveAudio(item.roomAudioStream, tag)
                        }
                    // 自动播放
                    LaunchedEffect(item.identityId) {
                        snapshotFlow {
                            isActivePageState.value && pagerState.currentPage == page
                        }.filter { it }.collectLatest {
                            if (it) {
                                MediaPlayerManager.play(playItem)
                            } else {
                                MediaPlayerManager.pause(playItem)
                            }
                        }
                    }

                    LiveRoomFeedCard(
                        item = item,
                        modifier =
                            Modifier
                                .padding(
                                    start = 24.dp,
                                    top = 16.dp,
                                    end = 24.dp,
                                ).fillMaxWidth(),
                    ) {
                        LiveRoomManager.joinRoom(
                            BasicRoomInfo(
                                id = item.id,
                                publicId = "",
                                title = item.title,
                                owner = item.user.toRoomUser(),
                                roomMode = LiveRoomMode.valueOf(item.roomMode),
                                micMode = LiveMicMode.UnKnown(-1),
                                notice = null,
                                desc = item.desc,
                                background = item.background,
                                tagIds =
                                    item.tags.map {
                                        it.id
                                    },
                            ),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun VoiceCardItemContent(
    tagPrefix: String,
    tab: VoiceListTab.ISquare,
    page: Int,
    item: VoiceCardItem,
    downloadPath: String,
    pagerState: PagerState,
    isActivePageState: State<Boolean>,
    modifier: Modifier = Modifier,
    onToggleLike: () -> Unit = {},
    onToggleFavorite: () -> Unit = {},
    onToggleFollow: () -> Unit = {},
    onReport: () -> Unit = {},
    onDisLiked: () -> Unit = {},
    onBlacked: () -> Unit = {},
    onUserClick: () -> Unit = {},
) {
    val tag = "$tagPrefix$tab-${item.identityId}"

    val playItem =
        remember(tag) {
            PlayMediaItem.audio(item.resource, tag)
        }

    val isPlaying by remember {
        derivedStateOf {
            MediaPlayerManager.currentPlayingTag.value == playItem.tag
        }
    }

    var refreshFlag by remember {
        mutableIntStateOf(0)
    }

    DisposableEffect(playItem) {
        MediaPlayerManager.addPreload(playItem, page)
        onDispose {
            MediaPlayerManager.removePreload(playItem)
        }
    }

    if (isPlaying) {
        LaunchedEffect(Unit) {
            while (isActive) {
                delay(1000)
                refreshFlag++
            }
        }
    }

    val duration by remember(item) {
        derivedStateOf {
            if (isPlaying) {
                refreshFlag // 这是一个刷新标志，不能删除
                MediaPlayerManager
                    .getPlayerDuration(playItem, item.duration.toLong())
                    .minus(
                        MediaPlayerManager.getPlayerPosition(playItem),
                    ).div(
                        1000,
                    ).toInt()
            } else {
                item.duration
            }
        }
    }

    LaunchedEffect(item.identityId) {
        // 自动播放
        snapshotFlow {
            isActivePageState.value && pagerState.currentPage == page
        }.filter { it }.collectLatest {
            if (it) {
                MediaPlayerManager.play(playItem)
            } else {
                MediaPlayerManager.pause(playItem)
            }
        }
    }
    // 每个页面的内容
    UserVoiceFeedCard(
        item = item,
        modifier = modifier,
        isPlaying = isPlaying,
        duration = duration,
        onToggleLike = onToggleLike,
        onToggleFavorite = onToggleFavorite,
        onToggleFollow = onToggleFollow,
        onReport = onReport,
        onDisLiked = onDisLiked,
        onBlacked = onBlacked,
        onUserClick = onUserClick,
    ) {
        MediaPlayerManager.toggle(playItem)
    }
}

@Preview
@Composable
private fun VoicesVerticalPagerPreview() {
    WakooTheme {
        Box(modifier = Modifier.padding(20.dp)) {
            UserVoiceFeedCard(item = VoiceCardItem.preview)
        }
    }
}
