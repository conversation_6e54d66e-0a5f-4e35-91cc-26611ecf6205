package com.buque.wakoo.ui.floating

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.im_business.GlobalMessageNotification
import com.buque.wakoo.ui.screens.debug.DebugFloatingButton
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManager
import com.buque.wakoo.viewmodel.GlobalFloatingViewModel

@Composable
fun AppFloatingWidgets() {
    Box(modifier = Modifier.fillMaxSize()) {
        FloatingLayoutManager {
            LiveRoomFloatingItem()
        }

        GlobalMessageNotification()

        val floatingViewModel = viewModel<GlobalFloatingViewModel>()
        floatingViewModel.FloatingBannerWidget()

        // 调试浮动按钮（仅在 Debug 模式下显示）
        DebugFloatingButton()
    }
}
