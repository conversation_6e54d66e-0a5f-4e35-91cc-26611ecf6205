package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Location: ImageVector
    get() {
        if (_Location != null) {
            return _Location!!
        }
        _Location = ImageVector.Builder(
            name = "Location",
            defaultWidth = 16.dp,
            defaultHeight = 16.dp,
            viewportWidth = 16f,
            viewportHeight = 16f
        ).apply {
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(13.747f, 5.635f)
                curveTo(13.047f, 2.555f, 10.36f, 1.168f, 8f, 1.168f)
                curveTo(8f, 1.168f, 8f, 1.168f, 7.993f, 1.168f)
                curveTo(5.64f, 1.168f, 2.947f, 2.548f, 2.247f, 5.628f)
                curveTo(1.467f, 9.068f, 3.573f, 11.981f, 5.48f, 13.815f)
                curveTo(6.187f, 14.495f, 7.093f, 14.835f, 8f, 14.835f)
                curveTo(8.907f, 14.835f, 9.813f, 14.495f, 10.513f, 13.815f)
                curveTo(12.42f, 11.981f, 14.527f, 9.075f, 13.747f, 5.635f)
                close()
                moveTo(8f, 8.975f)
                curveTo(6.84f, 8.975f, 5.9f, 8.035f, 5.9f, 6.875f)
                curveTo(5.9f, 5.715f, 6.84f, 4.775f, 8f, 4.775f)
                curveTo(9.16f, 4.775f, 10.1f, 5.715f, 10.1f, 6.875f)
                curveTo(10.1f, 8.035f, 9.16f, 8.975f, 8f, 8.975f)
                close()
            }
        }.build()

        return _Location!!
    }

@Suppress("ObjectPropertyName")
private var _Location: ImageVector? = null
