package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.ThumbUp: ImageVector
    get() {
        if (_ThumbUpDone != null) {
            return _ThumbUpDone!!
        }
        _ThumbUpDone =
            ImageVector
                .Builder(
                    name = "ThumbUpDone",
                    defaultWidth = 18.dp,
                    defaultHeight = 18.dp,
                    viewportWidth = 18f,
                    viewportHeight = 18f,
                ).apply {
                    group(
                        clipPathData =
                            PathData {
                                moveTo(0f, 0f)
                                horizontalLineToRelative(18f)
                                verticalLineToRelative(18f)
                                horizontalLineToRelative(-18f)
                                close()
                            },
                    ) {
                        path(fill = SolidColor(Color(0xFF999999))) {
                            moveTo(10.95f, 6.002f)
                            horizontalLineTo(15.75f)
                            curveTo(16.148f, 6.002f, 16.529f, 6.16f, 16.811f, 6.441f)
                            curveTo(17.092f, 6.722f, 17.25f, 7.104f, 17.25f, 7.502f)
                            verticalLineTo(9.08f)
                            curveTo(17.25f, 9.276f, 17.212f, 9.47f, 17.138f, 9.651f)
                            lineTo(14.816f, 15.288f)
                            curveTo(14.76f, 15.425f, 14.663f, 15.542f, 14.54f, 15.625f)
                            curveTo(14.416f, 15.708f, 14.271f, 15.752f, 14.123f, 15.752f)
                            horizontalLineTo(1.5f)
                            curveTo(1.301f, 15.752f, 1.11f, 15.673f, 0.97f, 15.532f)
                            curveTo(0.829f, 15.391f, 0.75f, 15.201f, 0.75f, 15.002f)
                            verticalLineTo(7.502f)
                            curveTo(0.75f, 7.303f, 0.829f, 7.112f, 0.97f, 6.971f)
                            curveTo(1.11f, 6.831f, 1.301f, 6.752f, 1.5f, 6.752f)
                            horizontalLineTo(4.111f)
                            curveTo(4.232f, 6.752f, 4.35f, 6.723f, 4.457f, 6.668f)
                            curveTo(4.563f, 6.613f, 4.655f, 6.533f, 4.724f, 6.435f)
                            lineTo(8.814f, 0.639f)
                            curveTo(8.866f, 0.566f, 8.942f, 0.514f, 9.029f, 0.492f)
                            curveTo(9.116f, 0.47f, 9.208f, 0.48f, 9.288f, 0.52f)
                            lineTo(10.649f, 1.2f)
                            curveTo(11.031f, 1.392f, 11.337f, 1.708f, 11.515f, 2.098f)
                            curveTo(11.693f, 2.487f, 11.733f, 2.925f, 11.627f, 3.34f)
                            lineTo(10.95f, 6.002f)
                            close()
                            moveTo(5.25f, 7.943f)
                            verticalLineTo(14.252f)
                            horizontalLineTo(13.62f)
                            lineTo(15.75f, 9.08f)
                            verticalLineTo(7.502f)
                            horizontalLineTo(10.95f)
                            curveTo(10.722f, 7.502f, 10.496f, 7.45f, 10.291f, 7.349f)
                            curveTo(10.086f, 7.249f, 9.906f, 7.103f, 9.766f, 6.922f)
                            curveTo(9.626f, 6.742f, 9.529f, 6.532f, 9.482f, 6.308f)
                            curveTo(9.435f, 6.085f, 9.44f, 5.853f, 9.497f, 5.632f)
                            lineTo(10.174f, 2.971f)
                            curveTo(10.195f, 2.888f, 10.187f, 2.8f, 10.151f, 2.722f)
                            curveTo(10.116f, 2.644f, 10.055f, 2.581f, 9.978f, 2.543f)
                            lineTo(9.482f, 2.295f)
                            lineTo(5.95f, 7.299f)
                            curveTo(5.762f, 7.565f, 5.522f, 7.782f, 5.25f, 7.943f)
                            close()
                            moveTo(3.75f, 8.252f)
                            horizontalLineTo(2.25f)
                            verticalLineTo(14.252f)
                            horizontalLineTo(3.75f)
                            verticalLineTo(8.252f)
                            close()
                        }
                    }
                }.build()

        return _ThumbUpDone!!
    }

@Suppress("ObjectPropertyName")
private var _ThumbUpDone: ImageVector? = null
