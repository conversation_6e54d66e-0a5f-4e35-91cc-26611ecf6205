package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.widget.wheelPicker.MultiWheelPicker
import com.buque.wakoo.ui.widget.wheelPicker.PickerColumn
import com.buque.wakoo.ui.widget.wheelPicker.WheelData
import com.buque.wakoo.ui.widget.wheelPicker.WheelPicker
import com.buque.wakoo.ui.widget.wheelPicker.WheelPickerPanelScaffold
import com.buque.wakoo.ui.widget.wheelPicker.rememberWheelPickerState

@Composable
fun <T> CommonPickerPanel(
    title: String,
    initialValue: T,
    items: List<T>,
    toString: (T) -> String = { it.toString() },
    onDismissRequest: () -> Unit,
    onConfirm: (T) -> Unit,
) {
    val state =
        rememberWheelPickerState(
            initialIndex = items.indexOf(initialValue).coerceIn(minimumValue = 0, maximumValue = items.size - 1),
            itemCount = items.size,
        )

    WheelPickerPanelScaffold(
        title = title,
        onDismissRequest = onDismissRequest,
        onConfirm = {
            items.getOrNull(state.snappedIndex)?.also {
                onConfirm(it)
            }
        },
    ) {
        WheelPicker(
            items = items,
            state = state,
            modifier = Modifier.fillMaxWidth(),
            visibleItemsCount = 5,
            itemHeight = 48.dp,
            itemContent = { item, isSelected, isEnabled ->
                Text(
                    text = toString(item),
                    style = MaterialTheme.typography.bodyLarge,
                    color =
                        when {
                            !isEnabled -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                            isSelected -> MaterialTheme.colorScheme.primary
                            else -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        },
                    fontSize = if (isSelected) 18.sp else 16.sp,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                )
            },
        )
    }
}

private fun getList(
    source: List<WheelData>,
    selectedIdxs: List<Int>,
): List<List<WheelData>> {
    if (selectedIdxs.size == 1) {
        return listOf(source)
    } else if (selectedIdxs.isEmpty()) {
        return listOf()
    }
    val results = mutableListOf<List<WheelData>>()
    results.add(source)
    for (i in 0 until selectedIdxs.size - 1) {
        results.add(results.last()[selectedIdxs[i]].children() ?: listOf())
    }
    return results
}

@Composable
fun <T : WheelData> CommonMultiPickerPanel(
    title: String,
    items: List<T>,
    level: Int,
    initialIndexs: List<Int> =
        buildList {
            for (i in 0 until level) {
                add(0)
            }
        },
    onDismissRequest: () -> Unit,
    onConfirm: (List<Int>) -> Unit,
) {
    var selectedIdxs by remember {
        mutableStateOf(
            buildList {
                for (i in 0 until level) {
                    add(initialIndexs.getOrNull(i) ?: 0)
                }
            }
        )
    }

    val selectSources by remember {
        derivedStateOf {
            getList(items, selectedIdxs)
        }
    }

    WheelPickerPanelScaffold(
        title = title,
        onDismissRequest = onDismissRequest,
        onConfirm = {
            onConfirm(selectedIdxs)
        },
    ) {
        MultiWheelPicker(
            columns =
                selectSources.mapIndexed { index, item ->
                    PickerColumn(
                        items = item, 
                        convert = {
                            it.uniqueId
                        }, 
                        initialIndex = selectedIdxs.getOrNull(index) ?: 0
                    )
                },
            onValueChange = { selected ->
                selectedIdxs = selected
            },
            visibleItemsCount = 5,
            itemHeight = 48.dp,
        )
    }
}

@Composable
@Preview(showBackground = true)
private fun previewCommonPicker() {
    val heightOptions by lazy {
        buildList {
            for (i in 140 until 201) {
                add(i)
            }
        }
    }

    CommonPickerPanel(
        "修改身高".localized,
        150,
        heightOptions,
        {
            it.toString()
        },
        {},
        {
        },
    )
}
