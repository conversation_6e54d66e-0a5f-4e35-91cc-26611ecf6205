package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.UserPages
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.liveroom.RoomReqMicListViewModel

@Composable
fun DialogScope.LiveRoomUpMicReqPanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    BottomPanelScaffold(
        title = "申请上麦列表".localized,
        useClose = true,
        modifier = modifier.fillMaxHeight(0.7f),
        backgroundColor = WakooWhite,
        contentPadding = 0.dp,
        bottomPadding = 0.dp,
    ) {
        val listState = rememberLazyListState()
        val viewModel = viewModel<RoomReqMicListViewModel>()
        CStateListPaginateLayout<String, Int, UserPages, RoomReqMicListViewModel>(
            reqKey = roomInfoState.id,
            modifier = Modifier.fillMaxSize(),
            viewModel = viewModel,
            listState = listState,
            autoRefresh = true,
        ) { paginateState, list ->
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                state = listState,
                contentPadding = PaddingValues(bottom = 36.dp),
            ) {
                items(list) { item ->
                    UserListItem(
                        user = item.user,
                        modifier = Modifier.padding(vertical = 10.dp),
                        endContent = {
                            OutlinedButton(
                                text = "拒绝".localized,
                                height = 32.dp,
                                onClick = {
                                    viewModel.deleteItem(item.id)
                                    roomInfoState.sendEvent(RoomEvent.HandleMicReq(item.id, false))
                                },
                            )

                            SolidButton(
                                text = "同意".localized,
                                height = 32.dp,
                                onClick = {
                                    viewModel.deleteItem(item.id)
                                    roomInfoState.sendEvent(RoomEvent.HandleMicReq(item.id, true))
                                },
                            )
                        },
                    )
                }
            }
        }
    }
}

@Composable
fun DialogScope.LiveInviteUpMicPanel(
    user: User,
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxWidth(),
    ) {
        Spacer(
            modifier =
                Modifier
                    .padding(top = 28.dp)
                    .matchParentSize()
                    .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                    .background(WakooWhite)
                    .background(
                        brush =
                            Brush.verticalGradient(
                                0f to Color(0xFFD6FFD7),
                                0.3f to Color(0x00D6FFD7),
                            ),
                    ),
        )
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
            ) {
                AvatarNetworkImage(
                    user = user,
                    size = 80.dp,
                    modifier =
                        Modifier
                            .align(Alignment.Center)
                            .border(1.5.dp, color = WakooWhite, CircleShape),
                )
            }

            SizeHeight(16.dp)

            Text(
                text = user.name,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(horizontal = 30.dp),
                color = Color(0xFF111111),
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center,
            )

            SizeHeight(8.dp)

            Row {
                GenderAgeTag(user = user)
                if (user.isVip) {
                    SizeWidth(4.dp)
                    VipCrownTag()
                }
            }

            Text(
                text = "邀请你上麦互动".localized,
                style = MaterialTheme.typography.labelLarge,
                color = WakooSecondarySelected,
                modifier = Modifier.padding(vertical = 24.dp),
            )

            GradientButton(
                text = "接受邀请".localized,
                onClick = {
                    dismiss()
                    roomInfoState.sendEvent(RoomEvent.AgreeUpMic(1))
                },
                minWidth = 215.dp,
                height = 40.dp,
                fontSize = 14.sp,
            )

            Text(
                text = "狠心拒绝".localized,
                style = MaterialTheme.typography.bodyMedium,
                color = WakooGrayText,
                modifier =
                    Modifier
                        .padding(top = 12.dp)
                        .clickable {
                            dismiss()
                        },
            )

            SizeHeight(15.dp)
        }
    }
}
