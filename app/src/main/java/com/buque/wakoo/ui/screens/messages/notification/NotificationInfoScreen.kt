package com.buque.wakoo.ui.screens.messages.notification

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.R
import com.buque.wakoo.bean.NotificationInfo
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.NotificationInfoViewModel

@Composable
fun NotificationInfoScreen() {
    val rootNavController = LocalAppNavController.root
    val listState = rememberLazyListState()
    CStateListPaginateLayout<Any, Int, NotificationInfo, NotificationInfoViewModel>(
        reqKey = "",
        listState = listState,
        emptyText = "暂无通知".localized,
        emptyId = R.drawable.ic_empty_for_notification,
        emptyButton = "发布声音内容".localized,
        onEmptyClick = {
            rootNavController.push(Route.VoicePublish)
        },
    ) { paginateState, list ->
        LazyColumn(modifier = Modifier.fillMaxSize(), state = listState) {
            items(list) { item ->
                UserListItem(
                    user = item.user,
                    modifier = Modifier.padding(16.dp),
                    centerContent = {
                        Column {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    text = it.name,
                                    modifier = Modifier.weight(1f),
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = Color(0xFF111111),
                                    fontWeight = FontWeight.Medium,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                )
                                Text(
                                    text = item.formatCreateTime,
                                    style = MaterialTheme.typography.labelLarge,
                                    color = Color(0xFF999999),
                                )
                            }
                            SizeHeight(8.dp)
                            Text(
                                text = item.content,
                                style = MaterialTheme.typography.labelLarge,
                                color = Color(0xFF999999),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                            )
                        }
                    },
                )
            }
        }
    }
}

@Preview
@Composable
private fun NotificationScreenPreview() {
    WakooTheme {
        NotificationInfoScreen()
    }
}
