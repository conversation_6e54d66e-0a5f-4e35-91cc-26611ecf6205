package com.buque.wakoo.ui.screens.liveroom

import androidx.activity.compose.BackHandler
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.formatUnreadCount
import com.buque.wakoo.ext.imeVisible
import com.buque.wakoo.ext.isX86Device
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.rememberSaveableRefWithPrevious
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.ChatC2cLine
import com.buque.wakoo.ui.icons.DownMic
import com.buque.wakoo.ui.icons.MicOff
import com.buque.wakoo.ui.icons.MicOn
import com.buque.wakoo.ui.icons.MicSettings
import com.buque.wakoo.ui.icons.UpMic
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.panel.LiveRoomUpMicReqPanel
import com.buque.wakoo.ui.screens.liveroom.panel.MessagePanel
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.serialization.Serializable

@Composable
fun ColumnScope.LiveRoomBottomLayout(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    val selfId = LocalSelfUserProvider.currentId
    val selfRole by roomInfoState.rememberRoomRoleState(selfId)
    val selfInMic by roomInfoState.rememberInMicState(selfId)

    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.Bottom,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        InputRoomButton(
            onClick = onClick,
        )

        Row(
            modifier =
                Modifier
                    .animateContentSize()
                    .padding(top = 10.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {

            val config by AppConfigManager.uiConfigFlow.collectAsState()
            if (config.packetEnabled) {
                //红包
                Box(
                    modifier =
                        Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(Color(0x26FFFFFF))
                            .clickable(onClick = {
                                roomInfoState.sendEvent(RoomEvent.EditHongBao)
                            }),
                    contentAlignment = Alignment.Center,
                ) {
                    Image(
                        painterResource(R.drawable.ic_hongbao), contentDescription = "hb",
                        contentScale = ContentScale.Inside,
                        modifier = Modifier.size(18.dp, 24.dp)
                    )
                }
            }


            if (selfRole != RoomRole.Member && roomInfoState.basicInfo.micMode == LiveMicMode.Request) {
                ActionIconButton(
                    tag = "micSettings",
                    imageVector = WakooIcons.MicSettings,
                    onBadgeNumber = {
                        roomInfoState.extraInfo.reqUpMicCount
                    },
                ) {
                    roomInfoState.sendEvent(
                        RoomEvent.PanelDialog { roomInfoState ->
                            LiveRoomUpMicReqPanel(roomInfoState)
                        },
                    )
                }
            }

            if (selfInMic) {
                val isMuted by roomInfoState.rememberMuteState(selfId)

                ActionIconButton(
                    tag = "mic_muted",
                    imageVector = if (isMuted) WakooIcons.MicOff else WakooIcons.MicOn,
                ) {
                    roomInfoState.sendEvent(RoomEvent.ToggleMic)
                }

                ActionIconButton(
                    tag = "mic_down",
                    imageVector = WakooIcons.DownMic,
                ) {
                    roomInfoState.sendEvent(RoomEvent.DownMic)
                }
            } else {
                if (roomInfoState.extraInfo.reqUpMicIng) {
                    ActionButton({
                        BasicText(
                            "申请中".localized,
                            style = TextStyle(color = WakooWhite),
                            autoSize =
                                TextAutoSize.StepBased(
                                    minFontSize = 5.sp,
                                    maxFontSize = 10.sp,
                                ),
                        )
                    }) {
                        roomInfoState.sendEvent(RoomEvent.CancelMicReq)
                    }
                } else {
                    ActionIconButton(
                        tag = "mic_up",
                        imageVector = WakooIcons.UpMic,
                    ) {
                        roomInfoState.sendEvent(RoomEvent.UpMic(-1))
                    }
                }
            }
        }

        ActionButton(
            onButton = {
                Image(
                    painter = painterResource(R.drawable.ic_gift_open),
                    contentDescription = "礼物",
                    modifier = Modifier.size(24.dp),
                )
            },
        ) {
            roomInfoState.sendEvent(RoomEvent.OpenGiftPanel())
        }

        val unReadCount by AppConversationManger.unReadCountFlow.collectAsStateWithLifecycle()
        ActionIconButton(
            tag = "message",
            imageVector = WakooIcons.ChatC2cLine,
            onBadgeNumber = {
                unReadCount
            },
        ) {
            roomInfoState.sendEvent(
                RoomEvent.PanelDialog { roomInfoState ->
                    MessagePanel(roomInfoState)
                },
            )
        }
    }
}

@Composable
fun ColumnScope.PrivateRoomBottomLayout(
    roomInfoState: LiveRoomInfoState,
    giftViewModel: GiftViewModel,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    val selfId = LocalSelfUserProvider.currentId
    val selfInMic by roomInfoState.rememberInMicState(selfId)

    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.Bottom,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        InputRoomButton(
            onClick = onClick,
        )

        Row(
            modifier =
                Modifier
                    .animateContentSize()
                    .padding(top = 10.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            if (selfInMic) {
                ActionIconButton(
                    tag = "mic_down",
                    imageVector = WakooIcons.DownMic,
                ) {
                    roomInfoState.sendEvent(RoomEvent.DownMic)
                }
            } else {
                ActionIconButton(
                    tag = "mic_up",
                    imageVector = WakooIcons.UpMic,
                ) {
                    roomInfoState.sendEvent(RoomEvent.UpMic(-1))
                }
            }
        }

        ActionButton(
            onButton = {
                Image(
                    painter = painterResource(R.drawable.ic_gift_open),
                    contentDescription = "礼物",
                    modifier = Modifier.size(24.dp),
                )
            },
        ) {
            roomInfoState.sendEvent(RoomEvent.OpenGiftPanel())
        }

        val unReadCount by AppConversationManger.unReadCountFlow.collectAsStateWithLifecycle()
        ActionIconButton(
            tag = "message",
            imageVector = WakooIcons.ChatC2cLine,
            onBadgeNumber = {
                unReadCount
            },
        ) {
            roomInfoState.sendEvent(
                RoomEvent.PanelDialog { roomInfoState ->
                    MessagePanel(roomInfoState)
                },
            )
        }
    }
}

@Composable
private fun RowScope.InputRoomButton(onClick: () -> Unit) {
    Row(
        modifier =
            Modifier
                .weight(1f)
                .height(40.dp)
                .background(Color(0x26FFFFFF), CircleShape)
                .noEffectClick(onClick = onClick)
                .padding(horizontal = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(text = "聊一聊...".localized, color = WakooWhite, style = MaterialTheme.typography.bodyMedium)
    }
}

@Composable
private fun RowScope.ActionButton(
    onButton: @Composable () -> Unit,
    onBadgeNumber: (() -> Int)? = null,
    onClick: () -> Unit,
) {
    val button: @Composable () -> Unit = {
        Box(
            modifier =
                Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(Color(0x26FFFFFF))
                    .clickable(onClick = onClick),
            contentAlignment = Alignment.Center,
        ) {
            onButton()
        }
    }

    if (onBadgeNumber == null) {
        button()
    } else {
        BadgedBox(badge = {
            val badgeNumber = onBadgeNumber()
            if (badgeNumber > 0) {
                Badge(
                    containerColor = Color(0xFFF76560),
                    contentColor = Color.White,
                    modifier = Modifier.offset(x = (-5).dp, y = (-5).dp),
                ) {
                    Text(text = badgeNumber.formatUnreadCount(99))
                }
            }
        }) {
            button()
        }
    }
}

@Composable
private fun RowScope.ActionIconButton(
    tag: String,
    imageVector: ImageVector,
    onBadgeNumber: (() -> Int)? = null,
    onClick: () -> Unit,
) {
    ActionButton(
        onButton = {
            Icon(
                imageVector = imageVector,
                contentDescription = tag,
                modifier = Modifier.size(24.dp),
                tint = Color.Unspecified,
            )
        },
        onBadgeNumber = onBadgeNumber,
        onClick = onClick,
    )
}

sealed interface InputTextState {
    @Serializable
    data class Visible(
        val initialText: String = "",
    ) : InputTextState

    @Serializable
    data object Hidden : InputTextState
}

@Composable
fun LiveRoomInputLayout(
    inputState: InputTextState,
    onInputStateChange: (InputTextState) -> Unit,
    modifier: Modifier = Modifier,
    onSend: (String) -> Unit,
) {
    if (inputState !is InputTextState.Visible) {
        return
    }

    val context = LocalContext.current

    val hasIme =
        remember(context) {
            // context.hasInputMethod() 判断不准确，有些模拟器返回true，启用了输入法，实际软键盘也没法弹出来
            !isX86Device()
        }

    var inputText by rememberSaveableRefWithPrevious<String>(key1 = inputState.initialText) {
        mutableStateOf("${it.orEmpty()}${inputState.initialText}")
    }

    val focusManager = LocalFocusManager.current

    val focusRequester =
        remember {
            FocusRequester()
        }

    var hasFocus by remember { mutableStateOf(false) }

    val softwareKeyboardController = LocalSoftwareKeyboardController.current

    fun dismiss() {
        if (hasFocus) {
            focusManager.clearFocus(true)
        }
        if (hasIme) {
            if (!hasFocus) {
                softwareKeyboardController?.hide()
            }
        } else {
            onInputStateChange(InputTextState.Hidden)
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        val imeVisible by imeVisible(minKeyboardHeight = 10.dp)

        Spacer(
            modifier =
                Modifier
                    .animateContentSize()
                    .fillMaxWidth()
                    .run {
                        if (!hasIme || imeVisible) {
                            weight(1f)
                        } else {
                            fillMaxHeight()
                        }
                    }
                    .noEffectClick {
                        dismiss()
                    },
        )

        BackHandler {
            onInputStateChange(InputTextState.Hidden)
        }

        DisposableEffect(key1 = Unit) {
            if (!hasFocus) {
                focusRequester.requestFocus()
            }
            if (hasIme && hasFocus) {
                softwareKeyboardController?.show()
            }
            onDispose {
                inputText = ""
                if (hasFocus) {
                    focusManager.clearFocus(true)
                } else if (hasIme) {
                    softwareKeyboardController?.hide()
                }
            }
        }

        if (hasIme) {
            LaunchedEffect(key1 = Unit) {
                delay(500)

                if (!imeVisible) {
                    onInputStateChange(InputTextState.Hidden)
                } else {
                    snapshotFlow {
                        imeVisible
                    }.filter {
                        !it
                    }.collectLatest {
                        onInputStateChange(InputTextState.Hidden)
                    }
                }
            }
        }

        Row(
            modifier = modifier.padding(16.dp),
            verticalAlignment = Alignment.Bottom,
        ) {
            AppTextField(
                value = inputText,
                onValueChange = {
                    inputText = it
                },
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .heightIn(min = 40.dp)
                        .focusRequester(focusRequester)
                        .onFocusChanged { hasFocus = it.isFocused },
                boxModifier = Modifier.weight(1f),
                placeholder = "一起聊聊吧".localized,
                lineHeight = 18.5.sp,
                placeholderStyle =
                    MaterialTheme.typography.bodyMedium.merge(
                        color = Color(0xFF999999),
                        lineHeight = 18.5.sp,
                    ),
                maxLines = 6,
                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                backgroundColor = Color(0xFFF5F7F9),
                shape = RoundedCornerShape(20.dp),
            )

            SizeWidth(16.dp)

            GradientButton(
                text = "发送".localized,
                height = 40.dp,
                paddingValues = PaddingValues(horizontal = 8.dp),
                config = ButtonStyles.Gradient.copy(minWidth = 80.dp),
                fontSize = 14.sp,
                onClick = {
                    val text = inputText
                    if (text.isNotBlank()) {
                        onSend(text)
                        dismiss()
                    } else {
                        showToast("不能发送空白消息".localized)
                    }
                },
            )
        }
    }
}
