package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.AddRound
import com.buque.wakoo.ui.icons.CloseCircleFill
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.viewmodel.EditUserInfoViewModel

@Composable
fun UserAlbumScreen(modifier: Modifier = Modifier) {
    val viewModel = viewModel<EditUserInfoViewModel>()
    val userInfo = LocalSelfUserProvider.current
    val scope = rememberCoroutineScope()
    val lm = LocalLoadingManager.current

    val rootNavController = LocalAppNavController.root

    val albumLauncher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            if (result.list.isNotEmpty()) {
                lm.show(scope) {
                    viewModel.updateAlbum(result.list)
                }
            }
        }

    SegColorTitleScreenScaffold(title = "个人相册".localized) { pv ->
        LazyVerticalGrid(GridCells.Fixed(3), modifier = Modifier.padding(pv)) {
            item {
                Box(modifier = Modifier.aspectRatio(1f)) {
                    Image(
                        WakooIcons.AddRound,
                        contentDescription = null,
                        modifier =
                            Modifier
                                .padding(10.dp)
                                .fillMaxWidth()
                                .aspectRatio(1f)
                                .click {
                                    albumLauncher.launch(Route.MediaSelector(maxSelectCount = 9))
                                }.background(color = Color(0xffE9EAEF), shape = RoundedCornerShape(8.dp))
                                .padding(32.dp),
                    )
                }
            }

            items(userInfo.albums ?: emptyList()) { pic ->
                Box(modifier = Modifier.aspectRatio(1f)) {
                    NetworkImage(
                        pic.mediaUrl,
                        modifier =
                            Modifier
                                .padding(10.dp)
                                .fillMaxWidth()
                                .aspectRatio(1f)
                                .clip(RoundedCornerShape(12.dp)),
                        contentScale = ContentScale.Crop,
                    )
                    Image(
                        WakooIcons.CloseCircleFill,
                        contentDescription = null,
                        modifier =
                            Modifier
                                .align(alignment = Alignment.TopEnd)
                                .size(24.dp)
                                .click {
                                    lm.show(scope) {
                                        viewModel.deletePictureFromAlbum(pic)
                                    }
                                },
                    )
                }
            }
        }
    }
}
