package com.buque.wakoo.ui.widget.media.manager

import android.content.Context
import androidx.annotation.OptIn
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.IntSize
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.Timeline
import androidx.media3.common.VideoSize
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.preload.DefaultPreloadManager
import androidx.media3.exoplayer.util.EventLogger

@OptIn(UnstableApi::class)
class MediaPlayerController(
    private val context: Context,
    private val preloadManager: DefaultPreloadManager,
    preloadManagerBuilder: DefaultPreloadManager.Builder,
) : Player.Listener {
    var currentPlayItem: PlayMediaItem? by mutableStateOf(null)

    val currentPlayingTag =
        derivedStateOf {
            currentPlayItem
                ?.takeIf {
                    it.isActuallyPlaying
                }?.tag
        }

    val currentBufferingTag =
        derivedStateOf {
            currentPlayItem
                ?.takeIf {
                    it.isBuffering
                }?.tag
        }

    private var lastPlayItem: PlayMediaItem? = null

    private var currentTag: String? = null

    private var releaseFlag = 0

    private val mediaExoPlayer: ExoPlayer by lazy {
        releaseFlag += 1
        preloadManagerBuilder
            .buildExoPlayer(
                ExoPlayer
                    .Builder(context)
                    .setVideoScalingMode(C.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING),
            ).also {
                it.addAnalyticsListener(EventLogger())
                it.repeatMode = Player.REPEAT_MODE_ONE
                it.addListener(this)
                it.playWhenReady = false
            }
    }

    private val hlsExoPlayer: ExoPlayer by lazy {
        releaseFlag += 2
        ExoPlayer
            .Builder(context)
            .setMediaSourceFactory(
                DefaultMediaSourceFactory(context)
                    .setLiveTargetOffsetMs(1500) // 目标延迟1.5秒
                    .setLiveMinOffsetMs(500) // 最小延迟0.5秒
                    .setLiveMaxOffsetMs(3000) // 最大延迟3秒
                    .setLiveMinSpeed(0.95f) // 允许的最小播放速度
                    .setLiveMaxSpeed(1.05f), // 允许的最大播放速度
            ).build()
            .also {
                it.addListener(this)
            }
    }

    override fun onVideoSizeChanged(size: VideoSize) {
        matchPlayMediaItem(currentTag) {
            updateVideoSize(size, it)
        }
    }

    override fun onMediaItemTransition(
        mediaItem: MediaItem?,
        reason: Int,
    ) {
        currentTag = mediaItem?.mediaId
        if (reason == Player.MEDIA_ITEM_TRANSITION_REASON_REPEAT) {
            matchPlayMediaItem(mediaItem) {
                if (it.isAudio) {
                    it.pause(0)
                }
            }
        }
    }

    override fun onTimelineChanged(
        timeline: Timeline,
        reason: Int,
    ) {
        // 当媒体信息（包括时长）可用或发生变化时，这个回调会被触发
        if (!timeline.isEmpty) {
            val timelineWindow = Timeline.Window()
            loop@ for (i in 0 until timeline.windowCount) {
                timeline.getWindow(i, timelineWindow)
                matchPlayMediaItem(timelineWindow.mediaItem.mediaId) {
                    val newDuration = timelineWindow.durationMs // window.durationMs 包含了当前媒体项的时长（毫秒）
                    // C.TIME_UNSET 表示时长未知（例如，直播流）
                    if (newDuration != C.TIME_UNSET) {
                        it.duration = newDuration
                    }
                    break@loop
                }
            }
        }
    }

    override fun onPlayWhenReadyChanged(
        playWhenReady: Boolean,
        reason: Int,
    ) {
        matchPlayMediaItem(currentTag) {
            updateVideoSize(it)
        }
    }

    override fun onPlaybackStateChanged(playbackState: Int) {
        matchPlayMediaItem(currentTag) {
            it.isBuffering = (playbackState == Player.STATE_BUFFERING)

            val player = if (it.isLive) hlsExoPlayer else mediaExoPlayer

            updateVideoSize(player.videoSize, it)

            player.duration.let { newDuration ->
                if (newDuration != C.TIME_UNSET) {
                    it.duration = newDuration
                }
            }
        }
    }

    override fun onRenderedFirstFrame() {
        matchPlayMediaItem(currentTag) {
            if (it is PlayMediaItem.Video) {
                it.renderedFirstFrame = true
                updateVideoSize(it)
            }
        }
    }

    override fun onIsPlayingChanged(isPlaying: Boolean) {
        matchPlayMediaItem(currentTag) {
            it.isActuallyPlaying = isPlaying
        }
    }

    fun prepare(playItem: PlayMediaItem) {
        val player = if (playItem.isLive) hlsExoPlayer else mediaExoPlayer
        playItem.bindPlayer(player)
        val mediaItem = playItem.exoPlayerMediaItem
        val currentMediaSource = preloadManager.getMediaSource(mediaItem)
        if (currentMediaSource == null) {
            player.setMediaItem(mediaItem)
        } else {
            player.setMediaSource(currentMediaSource)
        }
        player.prepare()
        player.playWhenReady = false
    }

    fun play(playItem: PlayMediaItem) {
        val currentItem = currentPlayItem
        val player = if (playItem.isLive) hlsExoPlayer else mediaExoPlayer
        playItem.bindPlayer(player)
        if (playItem.isSameItem(currentItem)) {
            playItem.play(false)
        } else {
            currentItem?.pause()
            setCurrentPlayMediaItem(playItem)
            val mediaItem = playItem.exoPlayerMediaItem
            val currentMediaSource = preloadManager.getMediaSource(mediaItem)
            if (currentMediaSource == null) {
                player.setMediaItem(mediaItem)
            } else {
                player.setMediaSource(currentMediaSource)
            }
            playItem.play(true)
        }
    }

    fun pauseIf(predicate: (PlayMediaItem) -> Boolean) {
        currentPlayItem?.also { playItem ->
            if (predicate(playItem)) {
                playItem.pause()
            }
        }
    }

    fun getPlayerPosition(tag: String): Long =
        matchPlayMediaItem(tag) {
            val player = if (it.isLive) hlsExoPlayer else mediaExoPlayer
            player.takeIf { p -> p.currentMediaItem?.mediaId == tag }?.currentPosition ?: it.position
        } ?: 0

    fun getPlayerDuration(
        tag: String,
        default: Long = -1,
    ): Long =
        matchPlayMediaItem(tag) {
            val player = if (it.isLive) hlsExoPlayer else mediaExoPlayer
            player.takeIf { p -> p.currentMediaItem?.mediaId == tag }?.duration ?: it.duration
        }?.takeIf { it > -1 } ?: default

    fun clear() {
        if (releaseFlag and 1 != 0) {
            mediaExoPlayer.stop()
            mediaExoPlayer.clearMediaItems()
            mediaExoPlayer.clearVideoSurface()
        }
        if (releaseFlag and 2 != 0) {
            hlsExoPlayer.stop()
            hlsExoPlayer.clearMediaItems()
            hlsExoPlayer.clearVideoSurface()
        }
        currentPlayItem = null
        lastPlayItem = null
        currentTag = null
    }

    fun release() {
        if (releaseFlag and 1 != 0) {
            mediaExoPlayer.clearVideoSurface()
            mediaExoPlayer.stop()
            mediaExoPlayer.clearMediaItems()
            mediaExoPlayer.release()
        }
        if (releaseFlag and 2 != 0) {
            hlsExoPlayer.clearVideoSurface()
            hlsExoPlayer.stop()
            hlsExoPlayer.clearMediaItems()
            hlsExoPlayer.release()
        }
        releaseFlag = 0
    }

    private fun setCurrentPlayMediaItem(playItem: PlayMediaItem?) {
        lastPlayItem = currentPlayItem
        currentPlayItem = playItem
    }

    private inline fun <R> matchPlayMediaItem(
        player: ExoPlayer,
        block: (PlayMediaItem) -> R?,
    ): R? = matchPlayMediaItem(player.currentMediaItem, block)

    private inline fun <R> matchPlayMediaItem(
        mediaItem: MediaItem?,
        block: (PlayMediaItem) -> R?,
    ): R? = matchPlayMediaItem(mediaItem?.mediaId, block)

    private inline fun <R> matchPlayMediaItem(
        mediaId: String?,
        block: (PlayMediaItem) -> R?,
    ): R? {
        mediaId ?: return null
        var playItem = currentPlayItem
        if (playItem != null && mediaId == playItem.tag) {
            return block(playItem)
        }
        playItem = lastPlayItem
        if (playItem != null && mediaId == playItem.tag) {
            return block(playItem)
        }
        return null
    }

    private fun updateVideoSize(playItem: PlayMediaItem) {
        if (playItem is PlayMediaItem.Video) {
            val player = if (playItem.isLive) hlsExoPlayer else mediaExoPlayer
            val size = player.videoSize
            updateVideoSize(size, playItem)
        }
    }

    private fun updateVideoSize(
        size: VideoSize,
        playItem: PlayMediaItem,
    ) {
        if (playItem is PlayMediaItem.Video) {
            if (size.width > 0 && size.height > 0) {
                playItem.videoSize = IntSize(size.width, size.height)
            }
        }
    }

    fun interface Factory {
        fun create(): MediaPlayerController
    }
}

fun MediaPlayerController.pause() {
    pauseIf {
        true
    }
}

fun MediaPlayerController.pause(tag: String) {
    pauseIf {
        it.tag == tag
    }
}

fun MediaPlayerController.pause(playItem: PlayMediaItem) {
    pauseIf {
        it.tag == playItem.tag
    }
}

fun MediaPlayerController.toggle(playItem: PlayMediaItem) {
    if (playItem.playWhenReady) {
        pause(playItem)
    } else {
        play(playItem)
    }
}
