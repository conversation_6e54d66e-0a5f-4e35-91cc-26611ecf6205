package com.buque.wakoo.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite

/**
 * 按钮配置数据类
 * 支持完全自定义的按钮样式
 *
 * @param backgroundColor 背景颜色（支持渐变）
 * @param textColor 文字颜色
 * @param borderColor 边框颜色，null表示无边框
 * @param borderWidth 边框宽度
 */
data class ButtonConfig(
    val backgroundColor: Brush,
    val textColor: Color,
    val fonSize: TextUnit = 16.sp,
    val borderColor: Color? = null,
    val borderWidth: Float = 0.5f,
    val minWidth: Dp = Dp.Unspecified,
    val height: Dp = 44.dp,
    val shape: Shape = CircleShape,
)

/**
 * 预定义的按钮样式
 */
object ButtonStyles {
    // 实心按钮（黑色背景，白色文字）
    val Solid =
        ButtonConfig(
            backgroundColor =
                Brush.linearGradient(
                    colors =
                        listOf(
                            Color(0xFF66FE6B),
                            Color(0xFF66FE6B),
                        ),
                ),
            textColor = Color(0xFF111111),
        )

    // 描边按钮（透明背景，黑色边框，黑色文字）
    val Outlined =
        ButtonConfig(
            backgroundColor =
                Brush.linearGradient(
                    colors =
                        listOf(
                            Color.Transparent,
                            Color.Transparent,
                        ),
                ),
            textColor = Color(0xFF111111),
            borderColor = Color(0xFF111111),
            borderWidth = 0.5f,
        )

    // 渐变按钮（绿色渐变背景，黑色文字）
    val Gradient =
        ButtonConfig(
            backgroundColor =
                Brush.linearGradient(
                    colors =
                        listOf(
                            Color(0xFFA3FF2C),
                            Color(0xFF31FFA1),
                        ),
                ),
            textColor = Color(0xFF111111),
        )
}

/**
 * 通用按钮组件
 * 严格按照 Figma 设计图实现：319x44dp，圆角28px
 *
 * @param text 按钮文字
 * @param onClick 点击回调
 * @param config 按钮配置
 * @param modifier Modifier
 * @param enabled 是否启用
 */
@Composable
fun WakooButton(
    text: AnnotatedString,
    onClick: () -> Unit,
    config: ButtonConfig,
    modifier: Modifier = Modifier,
    minWidth: Dp = Dp.Unspecified,
    height: Dp = Dp.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    paddingValues: PaddingValues = PaddingValues(horizontal = 16.dp),
    enabled: Boolean = true,
) {
    val shape = config.shape

    val finalHeight = if (height != Dp.Unspecified) height else config.height
    val finalFontSize = if (fontSize != TextUnit.Unspecified) fontSize else config.fonSize

    Box(
        modifier =
            modifier
                .alpha(if (enabled) 1.0f else 0.5f)
                .height(finalHeight)
                .widthIn(min = if (minWidth != Dp.Unspecified) minWidth else config.minWidth)
                .clip(shape)
                .background(config.backgroundColor)
                .clickable(
                    enabled = enabled,
                    onClick = onClick,
                ).then(
                    if (config.borderColor != null) {
                        Modifier.border(
                            width = config.borderWidth.dp,
                            color = config.borderColor,
                            shape = shape,
                        )
                    } else {
                        Modifier
                    },
                ).padding(paddingValues),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.titleSmall,
            color = config.textColor,
            textAlign = TextAlign.Center,
            fontSize = finalFontSize,
            fontWeight = FontWeight.Medium,
        )
    }
}

/**
 * 实心按钮
 * 黑色背景，白色文字
 *
 * @param text 按钮文字
 * @param onClick 点击回调
 * @param modifier Modifier
 * @param enabled 是否启用
 * @param backgroundColor 自定义背景色，默认为黑色
 * @param textColor 自定义文字颜色，默认为白色
 */
@Composable
fun SolidButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    backgroundColor: Color = Color(0xFF66FE6B),
    textColor: Color = Color(0xFF111111),
    config: ButtonConfig = ButtonStyles.Solid,
    minWidth: Dp = Dp.Unspecified,
    height: Dp = Dp.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    paddingValues: PaddingValues = PaddingValues(horizontal = 16.dp),
) {
    WakooButton(
        text =
            buildAnnotatedString {
                append(text)
            },
        onClick = onClick,
        config =
            config.copy(
                backgroundColor =
                    Brush.linearGradient(
                        colors =
                            listOf(
                                backgroundColor,
                                backgroundColor,
                            ),
                    ),
                textColor = textColor,
            ),
        modifier = modifier,
        enabled = enabled,
        minWidth = minWidth,
        height = height,
        fontSize = fontSize,
        paddingValues = paddingValues,
    )
}

/**
 * 描边按钮
 * 透明背景，黑色边框，黑色文字
 *
 * @param text 按钮文字
 * @param onClick 点击回调
 * @param modifier Modifier
 * @param enabled 是否启用
 * @param borderColor 自定义边框颜色，默认为黑色
 * @param textColor 自定义文字颜色，默认为黑色
 * @param borderWidth 边框宽度，默认为0.5dp
 */
@Composable
fun OutlinedButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    borderColor: Color = Color(0xFF111111),
    textColor: Color = Color(0xFF111111),
    backgroundColor: Color = Color.Transparent,
    borderWidth: Float = 0.5f,
    config: ButtonConfig = ButtonStyles.Outlined,
    minWidth: Dp = Dp.Unspecified,
    height: Dp = Dp.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    paddingValues: PaddingValues = PaddingValues(horizontal = 16.dp),
) {
    WakooButton(
        text =
            buildAnnotatedString {
                append(text)
            },
        onClick = onClick,
        config =
            config.copy(
                backgroundColor =
                    Brush.linearGradient(
                        colors =
                            listOf(
                                backgroundColor,
                                backgroundColor,
                            ),
                    ),
                textColor = textColor,
                borderColor = borderColor,
                borderWidth = borderWidth,
            ),
        modifier = modifier,
        enabled = enabled,
        minWidth = minWidth,
        height = height,
        fontSize = fontSize,
        paddingValues = paddingValues,
    )
}

/**
 * 渐变按钮
 * 绿色渐变背景，黑色文字
 *
 * @param text 按钮文字
 * @param onClick 点击回调
 * @param modifier Modifier
 * @param enabled 是否启用
 * @param gradientColors 自定义渐变颜色，默认为绿色渐变
 * @param textColor 自定义文字颜色，默认为黑色
 */
@Composable
fun GradientButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    gradientColors: List<Color> =
        listOf(
            Color(0xFFA3FF2C),
            Color(0xFF31FFA1),
        ),
    textColor: Color = Color(0xFF111111),
    config: ButtonConfig = ButtonStyles.Gradient,
    minWidth: Dp = Dp.Unspecified,
    height: Dp = Dp.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    paddingValues: PaddingValues = PaddingValues(horizontal = 16.dp),
) {
    WakooButton(
        text =
            buildAnnotatedString {
                append(text)
            },
        onClick = onClick,
        config =
            config.copy(
                backgroundColor = Brush.horizontalGradient(colors = gradientColors),
                textColor = textColor,
            ),
        modifier = modifier,
        enabled = enabled,
        minWidth = minWidth,
        height = height,
        fontSize = fontSize,
        paddingValues = paddingValues,
    )
}

@Composable
fun GradientVerticalButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    gradientColors: List<Color> =
        listOf(
            Color(0xFFA3FF2C),
            Color(0xFF31FFA1),
        ),
    textColor: Color = Color(0xFF111111),
    config: ButtonConfig = ButtonStyles.Gradient,
    minWidth: Dp = Dp.Unspecified,
    height: Dp = Dp.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    paddingValues: PaddingValues = PaddingValues(horizontal = 16.dp),
) {
    WakooButton(
        text =
            buildAnnotatedString {
                append(text)
            },
        onClick = onClick,
        config =
            config.copy(
                backgroundColor = Brush.verticalGradient(colors = gradientColors),
                textColor = textColor,
            ),
        modifier = modifier,
        enabled = enabled,
        minWidth = minWidth,
        height = height,
        fontSize = fontSize,
        paddingValues = paddingValues,
    )
}

// 预览组件
@Preview
@Composable
private fun ButtonUIPreview() {
    WakooTheme {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(24.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 实心按钮
            SolidButton(
                text = "退出登录",
                onClick = { },
            )

            // 描边按钮
            OutlinedButton(
                text = "注销账号",
                onClick = { },
            )

            // 渐变按钮
            GradientButton(
                text = "关注她",
                onClick = { },
            )

            // 自定义颜色示例
            SolidButton(
                text = "自定义实心按钮",
                onClick = { },
                backgroundColor = Color(0xFF2196F3),
                textColor = Color.White,
            )

            // 禁用状态示例
            GradientButton(
                text = "禁用状态",
                onClick = { },
                enabled = false,
            )
        }
    }
}

@Composable
fun IconTextButton(
    text: String,
    imageVector: ImageVector,
    modifier: Modifier = Modifier,
    iconSize: Dp = Dp.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    color: Color = WakooWhite,
    backgroundColor: Color = Color(0x26FFFFFF),
    shape: Shape = CircleShape,
    onClick: () -> Unit = {},
) {
    Row(
        modifier =
            modifier
                .clip(shape)
                .background(backgroundColor, shape)
                .clickable(onClick = onClick)
                .padding(3.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            imageVector = imageVector,
            contentDescription = null,
            modifier = Modifier.size(iconSize),
            tint = color,
        )
        SizeWidth(4.dp)
        Text(
            text = text,
            color = color,
            fontSize = fontSize,
            style = MaterialTheme.typography.labelLarge,
        )
    }
}
