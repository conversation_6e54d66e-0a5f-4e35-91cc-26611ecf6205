package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.chatgroup.JoinApplyItem
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupApplyJoinListViewModel

@Composable
fun ApplyJoinChatGroupScreen(
    chatGroupId: String,
    onRefresh: OnAction,
) {
    val viewModel =
        viewModel<ChatGroupApplyJoinListViewModel>(
            factory =
                viewModelFactory {
                    initializer { ChatGroupApplyJoinListViewModel(chatGroupId) }
                },
        )

    val scope = rememberCoroutineScope()
    val lm = LocalLoadingManager.current

    var needRefresh by remember { mutableStateOf(false) }
    DisposableEffect(Unit) {
        onDispose {
            if (needRefresh) {
                onRefresh()
            }
        }
    }
    TitleScreenScaffold("加入群组申请".localized) { pd ->
        Box(modifier = Modifier.padding(pd)) {
            StateListPaginateLayout<Int, JoinApplyItem, ChatGroupApplyJoinListViewModel>(
                viewModel = viewModel,
                emptyText = "暂无加入群组申请".localized,
                content = { pk, data ->
                    LazyColumn {
                        items(data) { item ->
                            ApplyJoinChatGroupItem(item.user, onReject = {
                                lm.show(scope) {
                                    viewModel
                                        .handleApply(item.user.id, false)
                                        .onSuccess {
                                            needRefresh = true
                                        }
                                }
                            }, onAgree = {
                                lm.show(scope) {
                                    viewModel
                                        .handleApply(item.user.id, true)
                                        .onSuccess {
                                            needRefresh = true
                                        }
                                }
                            })
                        }
                    }
                },
            )
        }
    }
}

@Composable
fun ApplyJoinChatGroupItem(
    user: User,
    onReject: OnAction = {},
    onAgree: OnAction = {},
) {
    UserListItem(user, modifier = Modifier.padding(16.dp, 12.dp), endContent = {
        OutlinedButton("拒绝".localized, height = 32.dp, minWidth = 64.dp, fontSize = 14.sp, onClick = onReject)
        SolidButton("同意".localized, height = 32.dp, minWidth = 64.dp, fontSize = 14.sp, onClick = onAgree)
    })
}

@Preview
@Composable
private fun ApplyJoinChatGroupItemPreview() {
    WakooTheme {
        ApplyJoinChatGroupItem(BasicUser.sampleGirl)
    }
}
