package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.LiveRoomExtraInfo
import com.buque.wakoo.bean.RoomOnlineInfo
import com.buque.wakoo.bean.message.BBoxInfo
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.CpRelationInfo
import com.buque.wakoo.bean.user.IUserDecorations
import com.buque.wakoo.bean.user.IWithRelationInfo
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.UserDecorations
import com.buque.wakoo.bean.user.WithRelationInfo
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.repository.LiveRoomRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.Serializable

@Suppress("ktlint:standard:property-naming")
enum class RoomRole(
    val value: Int,
) {
    Owner(0),
    Admin(1),
    Member(2),
}

@Serializable
data class RoomUser(
    val user: BasicUser,
    val decorations: UserDecorations = UserDecorations.empty,
    val withRelationInfo: WithRelationInfo = WithRelationInfo.empty,
    val cpRelationInfo: CpRelationInfo? = null,
) : User by user,
    IUserDecorations by decorations,
    IWithRelationInfo by withRelationInfo {
    override val isFollowed: Boolean
        get() =
            if (withRelationInfo.isEmpty) {
                true
            } else {
                withRelationInfo.isFollowed
            }

    companion object {
        fun fromResponse(response: UserResponse): RoomUser =
            RoomUser(
                user = BasicUser.fromResponse(response),
                decorations = UserDecorations.fromResponse(response),
                withRelationInfo = WithRelationInfo.fromResponse(response),
                cpRelationInfo = CpRelationInfo.fromResponse(response),
            )
    }
}

fun User.toRoomUser() = this as? RoomUser ?: RoomUser(this.toBasic())

fun SelfUserInfo.toRoomUser() =
    RoomUser(
        user = user,
        decorations = decorations,
        cpRelationInfo = cpRelationInfo,
    )

data class RoomMember(
    val roomUser: RoomUser,
    @Volatile var inRoom: Boolean = false, // 是否在房间(房主始终认为在房间), 可以随时变更，不需要感知, 这个值不是那么重要，主要是缓存大小控制
) : User by roomUser

sealed interface MicSeatsInfo {
    val index: Int

    val isEmpty: Boolean
        get() = this is Empty

    data class Empty(
        override val index: Int,
    ) : MicSeatsInfo

    data class User(
        override val index: Int,
        val user: RoomUser,
        val score: Int,
    ) : MicSeatsInfo
}

/**
 * 房间基础信息，基本不变化或变化很小的
 */
data class LiveRoomInfoState(
    private val basicInfoState: State<BasicRoomInfo>,
    private val onlineInfo: RoomOnlineInfo,
    private val allUserMapState: Map<String, RoomMember>,
    private val micListState: List<MicSeatsInfo>,
    private val adminIdsState: Set<String>,
    private val blackIdsState: Set<String>,
    private val extraInfoState: State<LiveRoomExtraInfo>,
    private val loadingState: State<Boolean>,
    private val repository: LiveRoomRepository,
) {
    companion object {
        val preview get() = LiveRoomRepository(BasicRoomInfo.preview, { _, _ -> }).roomInfoState
    }

    val events: Flow<RoomEvent>
        get() = repository.events

    val id: String = basicInfo.id

    val imId: String = basicInfo.imId

    val isPrivateRoom: Boolean = basicInfo.roomMode.isPrivateRoom

    val isPkRoom get() = basicInfo.roomMode == LiveRoomMode.PKMode

    val basicInfo: BasicRoomInfo
        get() = basicInfoState.value

    val onlineCount: Int
        get() = onlineInfo.totalCount.value

    val onlinePreviewList: List<User>
        get() = onlineInfo.previewList

    val extraInfo: LiveRoomExtraInfo
        get() = extraInfoState.value

    val allUserMap: Map<String, RoomMember>
        get() = allUserMapState

    val micList: List<MicSeatsInfo>
        get() = micListState

    val adminIds: Set<String>
        get() = adminIdsState

    val blackIds: Set<String>
        get() = blackIdsState

    val isLoading: Boolean
        get() = loadingState.value

    val blindBoxState: State<BBoxInfo?>
        get() = repository.blindBoxInfoState

    fun sendEvent(event: RoomEvent) {
        repository.sendEvent(event)
    }

    @Composable
    fun rememberInBlackListState(userId: String): State<Boolean> =
        remember(userId, blackIdsState) {
            derivedStateOf {
                isInBlackList(userId)
            }
        }

    @Composable
    fun rememberRoomRoleState(userId: String): State<RoomRole> =
        remember(userId, basicInfo.ownerId, adminIdsState) {
            derivedStateOf {
                getRoomRole(userId)
            }
        }

    @Composable
    fun rememberInMicState(userId: String): State<Boolean> =
        remember(userId, micListState) {
            derivedStateOf {
                isInMic(userId)
            }
        }

    @Composable
    fun rememberMuteState(userId: String) =
        remember(userId) {
            derivedStateOf {
                repository.getMicStateById(userId)?.isMuted ?: false
            }
        }

    @Composable
    fun rememberVolumeState(userId: String) =
        remember(userId) {
            derivedStateOf {
                repository.getMicStateById(userId)?.let {
                    if (it.isMuted) {
                        0f
                    } else {
                        it.volume / 90f
                    }
                } ?: 0f
            }
        }

    @Composable
    fun trackRoomMember(
        user: User,
        needFetchLatest: Boolean = true,
    ): RoomMember = repository.trackRoomMember(user, needFetchLatest)

    fun isInBlackList(userId: String): Boolean = blackIdsState.contains(userId)

    fun getRoomRole(userId: String): RoomRole =
        if (basicInfo.ownerId == userId) {
            RoomRole.Owner
        } else if (adminIdsState.contains(userId)) {
            RoomRole.Admin
        } else {
            RoomRole.Member
        }

    fun isInMic(userId: String): Boolean = micListState.any { it is MicSeatsInfo.User && it.user.id == userId }

    fun requireRoomUser(user: User): RoomUser = repository.requireRoomUser(user)
}

@Serializable
sealed interface LiveRoomMode {
    val value: Int

    companion object {
        fun valueOf(value: Int): LiveRoomMode =
            when (value) {
                Normal.value -> Normal
                Radio.value -> Radio
                PKMode.value -> PKMode
                else -> UnKnown(value)
            }
    }

    fun stringOf(): String

    val isUnKnown: Boolean
        get() = this is UnKnown

    val isPrivateRoom: Boolean
        get() = this is Private

    @Serializable
    object Normal : LiveRoomMode {
        override val value: Int = 5

        override fun stringOf(): String = "闲聊模式".localized
    }

    @Serializable
    object Radio : LiveRoomMode {
        override val value: Int = 4

        override fun stringOf(): String = "电台模式".localized
    }

    @Serializable
    object PKMode : LiveRoomMode {
        override val value: Int = 6

        override fun stringOf(): String = "PK模式".localized
    }

    @Serializable
    object Private : LiveRoomMode {
        override val value: Int = 3

        override fun stringOf(): String = "私密小屋".localized
    }

    @Serializable
    data class UnKnown(
        override val value: Int,
    ) : LiveRoomMode {
        override fun stringOf(): String = "未知模式".localized
    }

    val micSeatCount: Int
        get() =
            when (this) {
                Private -> 2
                Radio -> 5
                Normal -> 8
                PKMode -> 9
                else -> 8
            }
}

@Serializable
sealed interface LiveMicMode {
    val value: Int

    companion object {
        fun valueOf(value: Int): LiveMicMode =
            when (value) {
                Free.value -> Free
                Request.value -> Request
                else -> UnKnown(value)
            }
    }

    val isUnKnown: Boolean
        get() = this is UnKnown

    fun stringOf(): String

    @Serializable
    object Free : LiveMicMode {
        override val value: Int = 1

        override fun stringOf(): String = "自由上麦模式".localized
    }

    @Serializable
    object Request : LiveMicMode {
        override val value: Int = 2

        override fun stringOf(): String = "请求上麦模式".localized
    }

    @Serializable
    data class UnKnown(
        override val value: Int,
    ) : LiveMicMode {
        override fun stringOf(): String = "未知模式".localized
    }
}
