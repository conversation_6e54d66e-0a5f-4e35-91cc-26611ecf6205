package com.buque.wakoo.ui.widget.gift

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.toRect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.addOutline
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.GiftBean
import com.buque.wakoo.bean.GiftTab
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.keepLastNonNullState
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.AddRound
import com.buque.wakoo.ui.icons.ArrowLeft
import com.buque.wakoo.ui.icons.ArrowRight
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.MicSeatsInfo
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AutoSizeText
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.PopupActionMenu
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.rememberOverscrollFlingBehavior
import com.buque.wakoo.ui.widget.state.StateComponent
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import java.math.BigDecimal
import java.math.RoundingMode

//region 声明

/**
 * 礼物位置
 */
@Serializable
data class GiftPosition(
    val giftId: Int,
    val indexType: Int = 0, // 0=优先背包，1=仅背包，2=按tab顺序
    val tabId: Int? = null,
)

@Serializable
data class OpenGiftPanelAction(
    val userId: String,
    val position: GiftPosition,
)

data class GiftSendParams(
    val number: Int = 1,
    val fromPacket: Boolean = false,
    val greetings: String = "",
    val isIntimate: Boolean = false,
)
//endregion

//region 外部调用

/**
 * 私聊底部送礼
 *
 * @param position 默认定位
 * @param giftListModelState 礼物数据
 * @param onRecharge 充值回调
 * @param onSend 送礼回调
 */
@Composable
fun C2CBottomGiftPanel(
    position: GiftPosition?,
    giftListModelState: State<GiftListModel>,
    onRecharge: () -> Unit = {
        LocalAppNavController.useRoot?.push(Route.Recharge)
    },
    onSend: (GiftBean, GiftSendParams) -> Unit,
) {
    GiftPanelScaffold { onSelectedChange ->
        val density = LocalDensity.current
        Box(
            modifier =
                Modifier
                    .background(Color(0xFF11151E), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                    .padding(16.dp)
                    .padding(
                        bottom =
                            with(density) {
                                WindowInsets.navigationBars
                                    .getBottom(this)
                                    .toDp()
                            }.coerceAtLeast(15.dp),
                    ),
        ) {
            GiftPanelContent(
                giftModel = giftListModelState.value,
                selectCountList = intArrayOf(1, 5, 10, 20, 50, 100),
                onRecharge = onRecharge,
                onSelectedChange = onSelectedChange,
                onSend = { v1, v2 ->
                    onSend(v1, v2)
                    return@GiftPanelContent true
                },
                giftPosition = position,
            )
        }
    }
}

/**
 * 语音房底部送礼面板
 *
 * @param position 默认定位
 * @param giftListModelState 礼物数据
 * @param onRecharge 充值回调
 * @param onSend 送礼回调
 */
@Composable
fun ChatroomGiftPanel(
    position: GiftPosition?,
    giftListModelState: State<GiftListModel>,
    onlyUser: User? = null,
    roomInfoState: LiveRoomInfoState,
    onRecharge: () -> Unit = {
        LocalAppNavController.useRoot?.push(Route.Recharge)
    },
    onSend: (gift: GiftBean, params: GiftSendParams, selectedUsers: List<User>) -> Unit,
) {
    val giftModel by giftListModelState

    val selfId = LocalSelfUserProvider.currentId

    var selectedUsers by remember {
        mutableStateOf(emptyList<User>())
    }

    val giftReceivers by remember(selfId) {
        derivedStateOf {
            buildList {
                val owner = roomInfoState.basicInfo.owner
                if (owner != null && selfId != owner.id) {
                    add(owner)
                }
                roomInfoState.micList.forEach {
                    if (it is MicSeatsInfo.User && it.user.id != owner?.id && it.user.id != selfId) {
                        add(it.user)
                    }
                }
            }
        }
    }

    GiftPanelScaffold(giftModel) { onSelectedChange ->
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.inverseSurface)
                    .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                    .padding(
                        bottom =
                            with(LocalDensity.current) {
                                WindowInsets.navigationBars
                                    .getBottom(this)
                                    .toDp()
                            }.coerceAtLeast(15.dp),
                    ),
        ) {
            if (onlyUser != null) {
                UserSingleAtHeader(
                    onlyUser,
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(vertical = 20.dp),
                )
            } else {
                UserSelectorHeader(
                    allUsers = giftReceivers,
                    selectedUsers = selectedUsers,
                    onUserClick = {
                        if (selectedUsers.contains(it)) {
                            selectedUsers -= it
                        } else {
                            selectedUsers += it
                        }
                    },
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(vertical = 20.dp),
                ) {
                    selectedUsers =
                        if (it) {
                            giftReceivers
                        } else {
                            emptyList()
                        }
                }
            }

            GiftPanelContent(
                giftPosition = position,
                giftModel = giftModel,
                onRecharge = {
                    onRecharge()
                },
                onSelectedChange = {
                    onSelectedChange(it)
                },
                onSend = { gift, params ->
                    val displaySelectUsers =
                        if (onlyUser == null) {
                            val users = selectedUsers.filter { giftReceivers.contains(it) }
                            if (users.isEmpty()) {
                                showToast("请选择收礼人".localized)
                                return@GiftPanelContent false
                            }
                            users
                        } else {
                            listOf(onlyUser)
                        }
                    onSend(gift, params, displaySelectUsers)
                    return@GiftPanelContent true
                },
            )
        }
    }
}

/**
 * 群组/部落(随便其他什么都行,反正ConversationType = GROUP就得用这个)
 *
 * @param position 默认定位
 * @param giftListModelState 礼物数据
 * @param selectUsers 选中的用户
 * @param onAddClick 点击添加更多用户 的 回调
 * @param onRecharge 充值回调
 * @param onSend 送礼回调
 */
@Composable
fun GroupGiftPanel(
    position: GiftPosition?,
    giftListModelState: State<GiftListModel>,
    selectUsers: List<User>,
    onAddClick: () -> Unit = {},
    onRecharge: () -> Unit = {
        LocalAppNavController.useRoot?.push(Route.Recharge)
    },
    onSend: (gift: GiftBean, params: GiftSendParams, selectedUsers: List<User>) -> Unit,
) {
    val giftModel by giftListModelState
    GiftPanelScaffold(giftModel) { onSelectedChange ->
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.inverseSurface)
                    .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                    .padding(
                        bottom =
                            with(LocalDensity.current) {
                                WindowInsets.navigationBars
                                    .getBottom(this)
                                    .toDp()
                            }.coerceAtLeast(15.dp),
                    ),
        ) {
            UserContainerHeader(selectUsers, onAddClick)
            GiftPanelContent(
                giftModel = giftModel,
                onRecharge = onRecharge,
                onSelectedChange = {
                    onSelectedChange(it)
                },
                onSend = { gift, params ->
                    if (selectUsers.isEmpty()) {
                        showToast("请选择收礼人".localized)
                        return@GiftPanelContent false
                    }
                    onSend(gift, params, selectUsers)
                    return@GiftPanelContent true
                },
                giftPosition = position,
            )
        }
    }
}
//endregion

//region 组合

//region 头部送礼对象

@Composable
private fun UserSingleAtHeader(
    user: User,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = "送给".localized,
            color = WakooGrayText,
            fontSize = 14.sp,
        )
        Spacer(modifier = Modifier.width(8.dp))
        NetworkImage(
            user.avatar,
            modifier =
                Modifier
                    .size(24.dp)
                    .border(
                        1.dp,
                        WakooGreen,
                        CircleShape,
                    ).clip(CircleShape),
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(user.name, maxLines = 1, overflow = TextOverflow.Ellipsis, color = WakooWhite, fontSize = 13.sp)
    }
}

/**
 * 选择对个送礼对象, 语音房用的
 */
@Composable
private fun UserSelectorHeader(
    allUsers: List<User>,
    selectedUsers: List<User>,
    onUserClick: (User) -> Unit,
    modifier: Modifier = Modifier,
    toggleAll: (Boolean) -> Unit = {},
) {
    val idAllUsers =
        remember(allUsers) {
            allUsers.identifyId()
        }
    val idSelectedUsers =
        remember(selectedUsers) {
            selectedUsers.identifyId()
        }
    val isSelectAll = (idAllUsers == idSelectedUsers) && idAllUsers.isNotEmpty()
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = "送给".localized,
            color = WakooGrayText,
            fontSize = 14.sp,
        )
        Spacer(modifier = Modifier.width(8.dp))
        LazyRow(modifier = Modifier.weight(1f), horizontalArrangement = Arrangement.spacedBy(4.dp)) {
            items(allUsers) {
                NetworkImage(
                    it.avatar,
                    modifier =
                        Modifier
                            .size(24.dp)
                            .then(
                                if (selectedUsers.contains(it)) {
                                    Modifier.border(
                                        1.dp,
                                        WakooGreen,
                                        CircleShape,
                                    )
                                } else {
                                    Modifier
                                },
                            ).clip(CircleShape)
                            .click { onUserClick(it) },
                )
            }
        }
        Spacer(modifier = Modifier.width(8.dp))
        Box(
            modifier =
                Modifier
                    .clip(RoundedCornerShape(50))
                    .height(24.dp)
                    .click { toggleAll(!isSelectAll) }
                    .background(
                        if (isSelectAll) WakooGreen else Color(0xFF4A4A4A),
                        RoundedCornerShape(50),
                    ).padding(horizontal = 10.dp),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = if (isSelectAll) "全不选".localized else "全选".localized,
                fontSize = 12.sp,
                color = if (isSelectAll) WakooText else WakooWhite,
            )
        }
    }
}

/**
 * 可以添加送礼对象, 群组聊天用的
 */
@Composable
private fun UserContainerHeader(
    selectUserList: List<User> = emptyList(),
    onAddClick: () -> Unit = {},
) {
    val listState = rememberLazyListState()
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(bottom = 15.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Image(
            WakooIcons.AddRound,
            "add",
            modifier =
                Modifier
                    .size(24.dp)
                    .click(onClick = onAddClick),
        )

        if (selectUserList.isEmpty()) {
            Row(
                modifier =
                    Modifier
                        .height(32.dp)
                        .click(onClick = onAddClick)
                        .padding(start = 8.dp)
                        .weight(1f),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = "选择送礼对象".localized,
                    color = WakooGreen,
                    modifier =
                        Modifier
                            .weight(1f),
                )
                Icon(
                    WakooIcons.ArrowRight,
                    "arrow-right",
                    modifier =
                        Modifier
                            .size(24.dp)
                            .padding(4.dp),
                    tint = Color.White.copy(0.5f),
                )
            }
        } else {
            LazyRow(
                state = listState,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(start = 8.dp),
            ) {
                items(selectUserList) { u ->
                    NetworkImage(
                        u.avatar,
                        modifier =
                            Modifier
                                .size(32.dp)
                                .border(1.dp, WakooGreen, CircleShape)
                                .clip(CircleShape),
                    )
                    Spacer(modifier = Modifier.width(5.dp))
                }
            }
        }
    }
}
//endregion

@Composable
private fun GiftPanelScaffold(
    giftListModel: GiftListModel? = null,
    content: @Composable ColumnScope.((GiftBean?) -> Unit) -> Unit,
) {
    val navController = LocalAppNavController.root

    Column {
        var bannerItem by rememberSaveable {
            mutableStateOf<Pair<String, String>?>(null)
        }
        var selectedGift by rememberSaveable(stateSaver = GiftBean.Saver) {
            mutableStateOf<GiftBean?>(null)
        }
        val showItem = keepLastNonNullState(newState = bannerItem)
        AnimatedVisibility(
            visible = bannerItem != null,
            modifier = Modifier.padding(start = 8.dp, end = 8.dp, bottom = 8.dp),
        ) {
            showItem ?: return@AnimatedVisibility

            NetworkImage(
                showItem.first,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .heightIn(max = 96.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .noEffectClick(enabled = bannerItem != null) {
                            AppLinkNavigator.go(showItem.second, navController)
                        },
                contentScale = ContentScale.FillWidth,
            )
        }

        content {
            selectedGift = it
            bannerItem =
                if (it?.bannerImg?.isNotEmpty() == true) {
                    Pair(it.bannerImg, it.bannerLink)
                } else {
                    null
                }
        }
    }
}

@Composable
private fun GiftPanelContent(
    giftModel: GiftListModel,
    selectCountList: IntArray =
        remember {
            intArrayOf(1, 5, 10)
        },
    onRecharge: () -> Unit = {
        LocalAppNavController.useRoot?.push(Route.Recharge)
    },
    onSelectedChange: (GiftBean?) -> Unit = {},
    onSend: (GiftBean, GiftSendParams) -> Boolean = { _, _ -> true },
    giftPosition: GiftPosition? = null,
) {
    val navController = LocalAppNavController.root

    val position = if (giftPosition != null && giftPosition.giftId == -1) null else giftPosition

    val density = LocalDensity.current

    Box {
        var giftCount by rememberSaveable {
            mutableIntStateOf(1)
        }

        val showCountPop =
            rememberSaveable {
                mutableStateOf(false)
            }

        val initialPage =
            if (giftModel.preview) {
                -1
            } else {
                remember {
                    if (position != null) {
                        if (position.tabId != null) {
                            giftModel.list.indexOfFirst { it.tabId == position.tabId }.takeIf { it != -1 } ?: 0
                        } else {
                            when (position.indexType) {
                                0 -> {
                                    val index =
                                        giftModel.list.indexOfFirst { it.tabId == -1 && it.gifts.any { it.id == position.giftId } }
                                    if (index != -1) {
                                        index
                                    } else {
                                        giftModel.list
                                            .indexOfFirst { it.tabId != -1 && it.gifts.any { it.id == position.giftId } }
                                            .takeIf { it != -1 }
                                            ?: 0
                                    }
                                }

                                1 -> {
                                    giftModel.list.indexOfFirst { it.tabId == -1 }.takeIf { it != -1 } ?: 0
                                }

                                else -> {
                                    giftModel.list
                                        .indexOfFirst { it.gifts.any { it.id == position.giftId } }
                                        .takeIf { it != -1 } ?: 0
                                }
                            }
                        }
                    } else {
                        0
                    }
                }
            }

        val pagerState =
            if (giftModel.preview) {
                rememberPagerState {
                    1
                }
            } else {
                rememberPagerState(initialPage = initialPage) {
                    giftModel.list.size
                }
            }

        LaunchedEffect(key1 = pagerState.currentPage) {
        }

        val selectedItemState =
            rememberSaveable(giftModel.preview, pagerState.settledPage, stateSaver = GiftBean.Saver) {
                onSelectedChange(null)
                mutableStateOf<GiftBean?>(null)
            }

        var selectedItem by selectedItemState

        var showDescIcon by rememberSaveable {
            mutableStateOf("")
        }

        LaunchedEffect(selectedItem) {
            val descIcon = selectedItem?.descIcon
            if (!descIcon.isNullOrBlank()) {
                showDescIcon = descIcon
            }
        }

        val popupShape =
            with(density) {
                remember {
                    val radius = 8.dp.toPx()
                    val caretLeft = 8.dp.toPx()
                    val caretWidth = 12.dp.toPx()
                    val caretHeight = 6.dp.toPx()
                    GenericShape { size, layoutDirection ->
                        val rectH = size.height.minus(caretHeight)
                        addOutline(
                            Outline.Rounded(
                                RoundRect(
                                    rect = size.copy(height = rectH).toRect(),
                                    topLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    topRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    bottomRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    bottomLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                ),
                            ),
                        )
                        moveTo(caretLeft, rectH)
                        lineTo(caretLeft.plus(caretWidth.div(2)), rectH.plus(caretHeight))
                        lineTo(caretLeft.plus(caretWidth), rectH)
                    }
                }
            }

        val scope = rememberCoroutineScope()

        Column(modifier = Modifier.fillMaxWidth()) {
            if (selectCountList.isNotEmpty()) {
                PopupActionMenu(
                    expanded = showCountPop,
                    modifier =
                        Modifier
                            .width(80.dp)
                            .background(Color(0xCC000000), popupShape)
                            .graphicsLayer {
                                shadowElevation =
                                    with(density) {
                                        10.dp.toPx()
                                    }
                                shape = popupShape
                            }.padding(top = 4.dp, bottom = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(5.dp),
                    alignment = Alignment.BottomEnd,
                    offset =
                        LocalDensity.current.run {
                            IntOffset((-30).dp.roundToPx(), (-45).dp.roundToPx())
                        },
                ) {
                    selectCountList.forEach {
                        Text(
                            text = "$it",
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        giftCount = it
                                        showCountPop.value = false
                                    },
                            color = Color.White,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                if (giftModel.preview) {
                    repeat(3) {
                        Spacer(
                            modifier =
                                Modifier
                                    .size(60.dp, 28.dp)
                                    .clip(RoundedCornerShape(2.dp))
                                    .background(Color(0xFF3A3A3A)),
                        )
                    }
                } else {
                    val selectedTabIndex = pagerState.settledPage
                    ScrollableTabRow(
                        modifier =
                            Modifier
                                .animateContentSize()
                                .weight(1f),
                        selectedTabIndex = selectedTabIndex,
                        containerColor = Color.Transparent,
                        divider = {},
                        edgePadding = 0.dp,
                        indicator = { tabPositions ->
                            if (selectedTabIndex < tabPositions.size) {
                                Box(
                                    modifier =
                                        Modifier
                                            .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                            .offset {
                                                IntOffset(-16.dp.roundToPx(), 0)
                                            }.requiredWidth(12.dp)
                                            .height(3.dp)
                                            .background(
                                                brush =
                                                    Brush.horizontalGradient(
                                                        listOf(Color(0xffA3FF2C), Color(0xff31FFA1)),
                                                    ),
                                                CircleShape,
                                            ),
                                )
                            }
                        },
                    ) {
                        giftModel.list.forEachIndexed { index, tab ->
                            Tab(
                                selected = selectedTabIndex == index,
                                selectedContentColor = Color.White,
                                unselectedContentColor = WakooSecondaryUnSelected,
                                interactionSource = remember { NoIndicationInteractionSource() },
                                onClick = {
                                    scope.launch {
                                        pagerState.animateScrollToPage(index)
                                    }
                                },
                                content = {
                                    Box(
                                        modifier =
                                            Modifier
                                                .padding(bottom = 6.dp, end = 32.dp),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        Text(
                                            text = tab.tabName,
                                            style = MaterialTheme.typography.bodyMedium,
                                            fontWeight = if (selectedTabIndex == index) FontWeight.Medium else FontWeight.Normal,
                                        )
                                    }
                                },
                            )
                        }
                    }
                }

                AnimatedVisibility(!selectedItem?.descLink.isNullOrBlank()) {
                    NetworkImage(
                        showDescIcon,
                        modifier =
                            Modifier
                                .size(72.dp, 28.dp)
                                .click {
                                    AppLinkNavigator.go(selectedItem?.descLink, navController)
                                },
                        contentScale = ContentScale.FillWidth,
                    )
                }
            }

            HorizontalPager(
                state = pagerState,
                modifier =
                    Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                beyondViewportPageCount = 2,
                verticalAlignment = Alignment.Top,
                key = {
                    if (giftModel.preview) {
                        "preview"
                    } else {
                        "giftPanel-${giftModel.list[it].tabId}"
                    }
                },
            ) { page ->

                val giftTab = giftModel.list.getOrNull(page)

                val listItem = giftTab?.gifts.orEmpty()

                val state =
                    if (position == null || giftModel.preview || page != initialPage) {
                        rememberLazyGridState()
                    } else {
                        rememberLazyGridState(
                            remember(giftModel) {
                                listItem.forEachIndexed { index, gift ->
                                    if (gift.id == position.giftId) {
                                        selectedItem = gift
                                        onSelectedChange(gift)
                                        return@remember index
                                    }
                                }
                                0
                            },
                        )
                    }
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(260.dp),
                ) {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(4),
                        modifier = Modifier.fillMaxSize(),
                        state = state,
                        horizontalArrangement = Arrangement.spacedBy(5.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                        userScrollEnabled = !giftModel.preview,
                        flingBehavior = rememberOverscrollFlingBehavior { state },
                    ) {
                        if (giftModel.preview) {
                            items(12, key = { "preview_$it" }, contentType = { 0 }) {
                                Spacer(
                                    modifier =
                                        Modifier
                                            .height(104.dp)
                                            .clip(RoundedCornerShape(8.dp))
                                            .background(Color(0xFF3A3A3A)),
                                )
                            }
                        } else {
                            items(listItem, contentType = { 1 }) { item ->
                                val selected = item.id == selectedItem?.id
                                Box(
                                    Modifier
                                        .fillMaxWidth()
                                        .height(104.dp),
                                ) {
                                    Column(
                                        modifier =
                                            Modifier
                                                .fillMaxSize()
                                                .clip(RoundedCornerShape(8.dp))
                                                .run {
                                                    if (selected) {
                                                        background(Color(0x14FFFFFF)).border(
                                                            1.dp,
                                                            Color(0xFF66FE6B),
                                                            RoundedCornerShape(8.dp),
                                                        )
                                                    } else {
                                                        this
                                                    }
                                                }.clickable {
                                                    selectedItem = item
                                                    onSelectedChange(item)
                                                },
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                    ) {
                                        Spacer(modifier = Modifier.height(6.dp))

                                        val badgeNumber = item.giftCount

                                        Box(
                                            modifier =
                                                Modifier
                                                    .fillMaxWidth()
                                                    .height(58.dp),
                                        ) {
                                            NetworkImage(
                                                item.icon,
                                                modifier =
                                                    Modifier
                                                        .align(Alignment.Center)
                                                        .size(58.dp),
                                                contentScale = ContentScale.Inside,
                                            )
                                            Badge(
                                                modifier =
                                                    Modifier
                                                        .align(Alignment.TopEnd)
                                                        .alpha(if (badgeNumber > 0) 1f else 0f),
                                                containerColor = Color(0xFFF76560),
                                                contentColor = Color.White,
                                            ) {
                                                Text(text = badgeNumber.formatCount())
                                            }
                                        }
                                        Spacer(modifier = Modifier.height(4.dp))
                                        AutoSizeText(
                                            text = item.name,
                                            color = Color.White,
                                            fontSize = 12.sp,
                                            maxLines = 1,
                                            minTextSize = 10.sp,
                                            maxTextSize = 12.sp,
                                        )
                                        Spacer(modifier = Modifier.height(3.dp))
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            Image(
                                                painter = painterResource(R.drawable.ic_green_diamond_straight),
                                                contentDescription = null,
                                                modifier = Modifier.size(12.dp),
                                            )
                                            Spacer(modifier = Modifier.width(2.dp))
                                            Text(text = "${item.price}", color = Color(0x73FFFFFF), fontSize = 10.sp)
                                        }
                                    }
                                    if (item.superscriptIcon.isNotEmpty()) {
                                        NetworkImage(
                                            item.superscriptIcon,
                                            contentScale = ContentScale.FillHeight,
                                            modifier =
                                                Modifier
                                                    .padding(top = 2.dp, start = 4.dp)
                                                    .fillMaxWidth(0.7f)
                                                    .height(16.dp)
                                                    .align(Alignment.TopStart),
                                        )
                                    }
                                    if (item.blindBoxCount != null && item.blindBoxCount > 0) {
                                        Badge(
                                            modifier =
                                                Modifier
                                                    .padding(top = 2.dp, end = 4.dp)
                                                    .align(Alignment.TopEnd),
                                        ) {
                                            Text(
                                                text = item.blindBoxCount.toString(),
                                                modifier =
                                                    Modifier
                                                        .semantics {
                                                            contentDescription = "${item.id} blindBoxCount"
                                                        },
                                                fontSize = 10.sp,
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!giftModel.preview && listItem.isEmpty()) {
                        Column(
                            modifier =
                                Modifier
                                    .align(Alignment.Center)
                                    .padding(bottom = 20.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            StateComponent.Empty(text = "什么都没有".localized)
                        }
                    }
                }
            }

            // 底部操作栏
            Row(
                modifier =
                    Modifier
                        .padding(top = 15.dp)
                        .height(48.dp)
                        .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Row(
                    modifier =
                        Modifier
                            .padding(end = 10.dp)
                            .weight(1f),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_green_diamond_straight),
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                    )
                    Text(
                        text = formatCoinCount(giftModel.balance),
                        modifier =
                            Modifier
                                .padding(start = 4.dp, end = 8.dp)
                                .weight(1f, false),
                        color = Color(0xFFFFFFFF),
                        fontSize = 14.sp,
                        maxLines = 1,
                    )

                    SolidButton(
                        "充值".localized,
                        onClick = {
                            onRecharge()
                        },
                        height = 27.dp,
                        minWidth = 60.dp,
                        paddingValues = PaddingValues(horizontal = 18.dp),
                        fontSize = 12.sp,
                        backgroundColor = Color(0x26FFFFFF),
                        textColor = WakooGreen,
                    )
                }

                Row(
                    modifier =
                        Modifier
                            .height(28.dp)
                            .clip(CircleShape)
                            .border(1.dp, Color(0xFF66FE6B), CircleShape)
                            .click(enabled = !giftModel.preview, noEffect = true) {
                                showCountPop.value = true
                            },
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Spacer(modifier = Modifier.width(7.dp))
                    Icon(
                        imageVector = WakooIcons.ArrowLeft,
                        contentDescription = "arrow-left",
                        modifier = Modifier.size(12.dp),
                        tint = Color.White,
                    )
                    Spacer(modifier = Modifier.width(3.dp))
                    Text(
                        text = giftCount.toString(),
                        modifier = Modifier.widthIn(min = 24.dp),
                        color = Color.White,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center,
                    )
                    Spacer(modifier = Modifier.width(3.dp))
                    SolidButton(
                        text = "赠送".localized,
                        modifier =
                            Modifier
                                .width(60.dp)
                                .fillMaxHeight(),
                        fontSize = 12.sp,
                        config =
                            ButtonStyles.Solid.copy(
                                textColor = Color(0xCC000000),
                                backgroundColor =
                                    Brush.horizontalGradient(
                                        listOf(
                                            Color(0xFFA3FF2C),
                                            Color(0xFF31FFA1),
                                        ),
                                    ),
                            ),
                        enabled = !giftModel.preview,
                        onClick = {
                            val giftListItem = giftModel.list[pagerState.settledPage]
                            val good =
                                giftListItem.gifts.find {
                                    it.id == selectedItem?.id
                                }
                            if (good != null) {
                                onSend(good, GiftSendParams(giftCount, giftListItem.tabId == -1))
                            } else {
                                showToast("请选择礼物".localized)
                                selectedItem = null
                                onSelectedChange(null)
                            }
                        },
                    )
                }
            }
        }
    }
}
//endregion

//region 工具
private fun Int.formatCount(max: Int = 999): String =
    if (this > max) {
        "$max+"
    } else {
        this.toString()
    }

private fun formatCoinCount(count: Int): String =
    if (count < 10000) {
        count.toString()
    } else {
        BigDecimal(count).divide(BigDecimal(10000.0)).setScale(1, RoundingMode.FLOOR).toString() + "w"
    }

private fun List<User>.identifyId(): String = this.sortedBy { it.id }.joinToString(separator = "-")

//endregion

@Composable
@Preview
private fun PreviewGiftPanel() {
//    val model = remember { mutableStateOf(GiftListModel()) }

    val giftList =
        buildList {
            for (i in 0..100) {
                add(GiftBean(icon = "", name = "礼物${this.size}", price = 100, id = this.size))
            }
        }

    val model =
        remember {
            mutableStateOf(
                GiftListModel(
                    false,
                    10000,
                    listOf(
                        GiftTab(
                            1,
                            "tab1",
                            tabType = 2,
                            gifts = giftList,
                        ),
                        GiftTab(
                            2,
                            "tab2",
                            tabType = 2,
                            gifts = giftList,
                        ),
                        GiftTab(
                            3,
                            "tab3",
                            tabType = 2,
                            gifts = giftList,
                        ),
                    ),
                ),
            )
        }
    C2CBottomGiftPanel(GiftPosition(0, 0, 1), model, {}) { bean, params ->
    }
}

@Composable
@Preview
private fun previewChatRoomGiftPanel() {
//    val model = remember { mutableStateOf(GiftListModel()) }

    val giftList =
        buildList {
            for (i in 0..100) {
                add(GiftBean(icon = "", name = "礼物${this.size}", price = 100, id = this.size, superscriptIcon = "asdf"))
            }
        }

    val model =
        remember {
            mutableStateOf(
                GiftListModel(
                    false,
                    10000,
                    listOf(
                        GiftTab(
                            1,
                            "tab1",
                            tabType = 2,
                            gifts = giftList,
                        ),
                        GiftTab(
                            2,
                            "tab2",
                            tabType = 2,
                            gifts = giftList,
                        ),
                        GiftTab(
                            3,
                            "tab3",
                            tabType = 2,
                            gifts = giftList,
                        ),
                    ),
                ),
            )
        }
    WakooTheme {
        ChatroomGiftPanel(
            GiftPosition(0, 0, 1),
            model,
            BasicUser.sampleGirl,
            LiveRoomInfoState.preview,
            {},
        ) { bean, params, users ->
        }
    }
}

@Composable
@Preview
private fun previewGroupGiftPanel() {
    val giftList =
        buildList {
            for (i in 0..100) {
                add(GiftBean(icon = "", name = "礼物${this.size}", price = 100, id = this.size))
            }
        }

    val model =
        remember {
            mutableStateOf(
                GiftListModel(
                    false,
                    10000,
                    listOf(
                        GiftTab(
                            1,
                            "tab1",
                            tabType = 2,
                            gifts = giftList,
                        ),
                        GiftTab(
                            2,
                            "tab2",
                            tabType = 2,
                            gifts = giftList,
                        ),
                        GiftTab(
                            3,
                            "tab3",
                            tabType = 2,
                            gifts = giftList,
                        ),
                    ),
                ),
            )
        }
    GroupGiftPanel(GiftPosition(0, 0, 1), model, listOf(), {}, {}) { bean, params, list ->
    }
}
