package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.GiftWallItemBG: ImageVector
    get() {
        if (_GiftWallItemBG != null) {
            return _GiftWallItemBG!!
        }
        _GiftWallItemBG =
            ImageVector
                .Builder(
                    name = "GiftWallItemBG",
                    defaultWidth = 54.dp,
                    defaultHeight = 63.dp,
                    viewportWidth = 54f,
                    viewportHeight = 63f,
                ).apply {
                    path(fill = SolidColor(Color(0xFFFFFFFF))) {
                        moveTo(27f, 0f)
                        curveTo(40.671f, 0f, 51.876f, 10.876f, 52.92f, 24f)
                        horizontalLineTo(53f)
                        verticalLineTo(63f)
                        horizontalLineTo(1f)
                        verticalLineTo(24f)
                        horizontalLineTo(1.025f)
                        curveTo(1.603f, 11.391f, 13.01f, 0f, 27f, 0f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(52.339f, 36.54f)
                        verticalLineTo(62.664f)
                        horizontalLineTo(1.534f)
                        curveTo(1.534f, 59.286f, 1.534f, 54.014f, 1.534f, 54.014f)
                        horizontalLineTo(1.368f)
                        horizontalLineTo(1.201f)
                        verticalLineTo(63.001f)
                        horizontalLineTo(52.672f)
                        verticalLineTo(36.449f)
                        horizontalLineTo(52.339f)
                        verticalLineTo(36.54f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(1.534f, 41.198f)
                        verticalLineTo(26.241f)
                        curveTo(1.534f, 11.959f, 12.929f, 0.34f, 26.935f, 0.34f)
                        curveTo(39.985f, 0.34f, 51.101f, 10.71f, 52.239f, 23.949f)
                        lineTo(52.405f, 23.933f)
                        lineTo(52.572f, 23.918f)
                        curveTo(52.012f, 17.409f, 49.097f, 11.376f, 44.365f, 6.93f)
                        curveTo(39.602f, 2.462f, 33.414f, 0f, 26.935f, 0f)
                        curveTo(12.747f, 0f, 1.201f, 11.771f, 1.201f, 26.241f)
                        curveTo(1.201f, 36.022f, 1.201f, 31.506f, 1.201f, 41.286f)
                        horizontalLineTo(1.534f)
                        verticalLineTo(41.198f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(3.341f, 60.654f)
                        verticalLineTo(54.147f)
                        horizontalLineTo(3.008f)
                        verticalLineTo(60.992f)
                        horizontalLineTo(50.865f)
                        verticalLineTo(37.332f)
                        verticalLineTo(36.258f)
                        horizontalLineTo(50.698f)
                        horizontalLineTo(50.532f)
                        verticalLineTo(36.355f)
                        verticalLineTo(60.654f)
                        horizontalLineTo(3.341f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(26.935f, 2.766f)
                        curveTo(39.018f, 2.766f, 49.103f, 11.988f, 50.393f, 24.221f)
                        lineTo(50.724f, 24.184f)
                        curveTo(49.415f, 11.779f, 39.187f, 2.426f, 26.935f, 2.426f)
                        curveTo(13.743f, 2.426f, 3.008f, 13.37f, 3.008f, 26.825f)
                        verticalLineTo(41.422f)
                        horizontalLineTo(3.341f)
                        verticalLineTo(26.825f)
                        curveTo(3.341f, 13.557f, 13.926f, 2.766f, 26.935f, 2.766f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(51.669f, 25.297f)
                        horizontalLineTo(51.338f)
                        curveTo(51.232f, 26.638f, 51.208f, 26.665f, 50.001f, 26.782f)
                        verticalLineTo(27.119f)
                        curveTo(51.206f, 27.234f, 51.232f, 27.263f, 51.338f, 28.604f)
                        horizontalLineTo(51.669f)
                        curveTo(51.775f, 27.263f, 51.801f, 27.236f, 53.007f, 27.119f)
                        verticalLineTo(26.782f)
                        curveTo(51.801f, 26.667f, 51.775f, 26.638f, 51.669f, 25.297f)
                        close()
                        moveTo(51.503f, 27.497f)
                        curveTo(51.405f, 27.212f, 51.252f, 27.051f, 50.981f, 26.949f)
                        curveTo(51.252f, 26.848f, 51.405f, 26.687f, 51.503f, 26.402f)
                        curveTo(51.6f, 26.687f, 51.754f, 26.848f, 52.024f, 26.949f)
                        curveTo(51.754f, 27.051f, 51.6f, 27.212f, 51.503f, 27.497f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(51.669f, 29.938f)
                        horizontalLineTo(51.338f)
                        curveTo(51.159f, 32.223f, 51.07f, 32.318f, 49.008f, 32.517f)
                        verticalLineTo(32.854f)
                        curveTo(51.07f, 33.053f, 51.157f, 33.15f, 51.338f, 35.434f)
                        horizontalLineTo(51.669f)
                        curveTo(51.849f, 33.148f, 51.938f, 33.053f, 54f, 32.854f)
                        verticalLineTo(32.517f)
                        curveTo(51.938f, 32.318f, 51.851f, 32.223f, 51.669f, 29.938f)
                        close()
                        moveTo(51.503f, 34.026f)
                        curveTo(51.332f, 33.196f, 51.016f, 32.865f, 50.237f, 32.685f)
                        curveTo(51.016f, 32.504f, 51.332f, 32.173f, 51.503f, 31.345f)
                        curveTo(51.674f, 32.175f, 51.989f, 32.506f, 52.769f, 32.685f)
                        curveTo(51.989f, 32.865f, 51.674f, 33.196f, 51.503f, 34.026f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(2.33f, 52.827f)
                        horizontalLineTo(2.661f)
                        curveTo(2.767f, 51.486f, 2.793f, 51.459f, 3.999f, 51.342f)
                        verticalLineTo(51.005f)
                        curveTo(2.793f, 50.888f, 2.767f, 50.861f, 2.661f, 49.52f)
                        horizontalLineTo(2.33f)
                        curveTo(2.224f, 50.861f, 2.201f, 50.888f, 0.993f, 51.005f)
                        verticalLineTo(51.342f)
                        curveTo(2.198f, 51.457f, 2.224f, 51.486f, 2.33f, 52.827f)
                        close()
                        moveTo(2.497f, 50.627f)
                        curveTo(2.594f, 50.912f, 2.748f, 51.073f, 3.018f, 51.174f)
                        curveTo(2.748f, 51.276f, 2.594f, 51.437f, 2.497f, 51.722f)
                        curveTo(2.4f, 51.437f, 2.246f, 51.276f, 1.976f, 51.174f)
                        curveTo(2.246f, 51.073f, 2.4f, 50.912f, 2.497f, 50.627f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF79FFC2))) {
                        moveTo(2.33f, 48.188f)
                        horizontalLineTo(2.661f)
                        curveTo(2.841f, 45.902f, 2.93f, 45.807f, 4.992f, 45.608f)
                        verticalLineTo(45.271f)
                        curveTo(2.93f, 45.072f, 2.843f, 44.977f, 2.661f, 42.691f)
                        horizontalLineTo(2.33f)
                        curveTo(2.151f, 44.977f, 2.062f, 45.072f, 0f, 45.271f)
                        verticalLineTo(45.608f)
                        curveTo(2.062f, 45.807f, 2.149f, 45.902f, 2.33f, 48.188f)
                        close()
                        moveTo(2.497f, 44.099f)
                        curveTo(2.668f, 44.929f, 2.984f, 45.26f, 3.763f, 45.441f)
                        curveTo(2.984f, 45.622f, 2.668f, 45.952f, 2.497f, 46.782f)
                        curveTo(2.326f, 45.952f, 2.01f, 45.622f, 1.231f, 45.441f)
                        curveTo(2.01f, 45.26f, 2.326f, 44.929f, 2.497f, 44.099f)
                        close()
                    }
                }.build()

        return _GiftWallItemBG!!
    }

@Suppress("ObjectPropertyName")
private var _GiftWallItemBG: ImageVector? = null
