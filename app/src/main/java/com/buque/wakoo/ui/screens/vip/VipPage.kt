package com.buque.wakoo.ui.screens.vip

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.consts.Contracts
import com.buque.wakoo.consts.Pay
import com.buque.wakoo.core.pay.AppPayCoreKit
import com.buque.wakoo.core.pay.PayRequest
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.network.api.bean.ActivateOption
import com.buque.wakoo.network.api.bean.VipInfo
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.utils.DateTimeUtils
import com.buque.wakoo.utils.toCState
import com.buque.wakoo.viewmodel.GoodsEvent
import com.buque.wakoo.viewmodel.GoodsViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.days
import kotlin.time.DurationUnit

@Composable
fun MemberScreen(onOpenWeb: (String) -> Unit = {}) {
    val viewModel = viewModel<GoodsViewModel>()
    LaunchedEffect(viewModel) {
        viewModel.sendEvent(GoodsEvent.FetchVipConfig)
    }
    LaunchedEffect(Unit) {
        AppPayCoreKit.actionFlow.collectLatest { event ->
            if (event == AppPayCoreKit.ACTION_BUY_COMPLETE) {
                viewModel.sendEvent(GoodsEvent.FetchVipConfig)
                UserManager.refreshSelfUserInfo()
            }
        }
    }
    val state by viewModel.vipConfigState.collectAsState()
    val cState = state.toCState()
    MemberPageContent(cState, onOpenWeb)
}

@Composable
private fun MemberPageContent(
    state: CState<VipInfo>,
    onOpenWeb: (String) -> Unit = {},
) {
    val act = LocalActivity.current
    val loadingMgr = LocalLoadingManager.current
    val loading by AppPayCoreKit.loading
    if (!loading) {
        loadingMgr.dismiss()
    }
    val selfUser = LocalSelfUserProvider.current.user
    val scope = rememberCoroutineScope()
    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F7F9))
                .navigationBarsPadding(),
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .background(Brush.verticalGradient(listOf(Color(0xFFFFFBBD), Color.Transparent))),
        )
        val scrollState = rememberScrollState()
        var selectedGood by remember {
            mutableStateOf<ActivateOption?>(null)
        }
        var isMember by remember { mutableStateOf(false) }
        Column(modifier = Modifier.fillMaxSize()) {
            WakooTitleBar("开通VIP".localized)
            CStateLayout(state) { vipInfo ->
                LaunchedEffect(vipInfo.isVip) {
                    isMember = vipInfo.isVip
                }
                val goods = vipInfo.activateOption
                LaunchedEffect(goods) {
                    if (selectedGood == null) {
                        selectedGood = goods.firstOrNull()
                    }
                }
                Column(modifier = Modifier.fillMaxSize()) {
                    Column(
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .weight(1f)
                                .verticalScroll(scrollState)
                                .padding(horizontal = 16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        SizeHeight(16.dp)
                        val inactiveMember = "尚未开通会员".localized
                        val expireDate =
                            remember(vipInfo) {
                                if (vipInfo.isVip) {
                                    val date = DateTimeUtils.formatDate(vipInfo.expireTimestamp, "yyyy-MM-dd")
                                    "%s到期".localizedFormat(date)
                                } else {
                                    inactiveMember
                                }
                            }
                        UserVipCard(selfUser.name, expireDate = expireDate, avatar = selfUser.avatar)
                        SizeHeight(20.dp)
                        LazyRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                        ) {
                            items(goods) {
                                GoodsItem(
                                    it.equity,
                                    "${it.currencyMark}${it.currencyNumber}",
                                    it == selectedGood,
                                    label = it.label,
                                    extraNote = it.extraNote,
                                    extraNoteDelLine = it.extraNoteDel,
                                    needCountDown = it.needCountDown,
                                ) {
                                    selectedGood = it
                                }
                            }
                        }
                        SizeHeight(20.dp)
                        if (selectedGood != null) {
                            val rights =
                                if (selectedGood?.isWeekMember == true) {
                                    vipInfo.weekMemberRightsInfos
                                } else {
                                    vipInfo.memberRightsInfos
                                }
                            Column(
                                modifier =
                                    Modifier
                                        .fillMaxWidth()
                                        .background(Color.White, RoundedCornerShape(12.dp))
                                        .padding(16.dp, 20.dp),
                                verticalArrangement = Arrangement.spacedBy(24.dp),
                            ) {
                                Row(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        "%d项会员权益".localizedFormat(rights.size),
                                        color = Color(0xFF674E0F),
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.ExtraBold,
                                    )
                                }
                                rights.forEach { rightItem ->
                                    VipBenefitItem(
                                        icon = {
                                            NetworkImage(rightItem.icon, modifier = Modifier.size(48.dp))
                                        },
                                        title = rightItem.name,
                                        description = rightItem.prompt,
                                    )
                                }
                            }
                        }
                        SizeHeight(120.dp)
                    }
                }
            }
        }
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .background(Brush.verticalGradient(listOf(Color.Transparent, Color(0xFFF5F7F9)))),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            SizeHeight(16.dp)
            BottomButton(
                if (isMember) "续费会员".localized else "开通VIP".localized,
                modifier =
                    Modifier
                        .width(320.dp)
                        .shadow(2.dp, RoundedCornerShape(50))
                        .clickable {
                            loadingMgr.show(scope) {
                                val sel = selectedGood
                                if (sel != null) {
                                    act?.let { a ->
                                        scope.launch {
                                            AppPayCoreKit.buy(
                                                a,
                                                PayRequest(
                                                    sel.fkType,
                                                    sel.productId,
                                                    sel.fkLink,
                                                    orderType = Pay.ORDER_TYPE_MEMBERSHIP,
                                                ),
                                            )
                                        }
                                    }
                                }
                            }
                        },
            )
            SizeHeight(16.dp)
            // 协议文本
            Text(
                text = "开通即代表同意《会员协议》".localized,
                fontSize = 12.sp,
                color = Color(0xFFB6B6B6),
                textAlign = TextAlign.Center,
                modifier =
                    Modifier.clickable {
                        onOpenWeb(Contracts.member)
                    },
            )
            SizeHeight(16.dp)
        }
    }
}

/**
 * 用户VIP信息卡片
 */
@Composable
internal fun UserVipCard(
    userName: String,
    expireDate: String,
    modifier: Modifier = Modifier,
    avatar: String? = null,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .height(120.dp)
                .background(
                    brush =
                        Brush.verticalGradient(
                            colors =
                                listOf(
                                    Color(0xFFFFFBD3),
                                    Color(0xFFFFE897),
                                ),
                        ),
                    shape = RoundedCornerShape(24.dp),
                ),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 用户头像
            if (avatar == null) {
                Image(
                    painter = painterResource(id = R.drawable.ic_app_logo),
                    contentDescription = "用户头像",
                    modifier =
                        Modifier
                            .size(56.dp)
                            .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                )
            } else {
                NetworkImage(
                    avatar,
                    modifier =
                        Modifier
                            .size(56.dp)
                            .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                )
            }

            SizeWidth(16.dp)

            // 用户信息
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = userName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF674E0F),
                )

                SizeHeight(12.dp)

                Text(
                    text = expireDate,
                    fontSize = 12.sp,
                    color = Color(0xFF674E0F),
                )
            }

            // 皇冠图标
            Box(
                modifier = Modifier.size(72.dp),
            ) {
                // 背景皇冠（模糊效果）
                Image(
                    painter = painterResource(id = R.drawable.ic_crown),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit,
                )

                // 主皇冠
                Image(
                    painter = painterResource(id = R.drawable.ic_crown),
                    contentDescription = "VIP皇冠",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit,
                )
            }
        }
    }
}

/**
 * 单个权益项目
 */
@Composable
private fun VipBenefitItem(
    icon: @Composable () -> Unit,
    title: String,
    description: String,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
    ) {
        icon()

        SizeWidth(12.dp)

        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF674E0F),
            )

            SizeHeight(8.dp)

            Text(
                text = description,
                fontSize = 12.sp,
                color = Color(0xFF674E0F),
            )
        }
    }
}

@Composable
fun GoodsItem(
    name: String,
    price: String,
    isSelected: Boolean,
    label: String = "",
    extraNote: String = "",
    extraNoteDelLine: Boolean = false,
    needCountDown: Boolean = false,
    onClick: () -> Unit,
) {
    val backgroundColor = if (isSelected) Color(0xFFFFFCDB) else Color.White
    val borderColor = if (isSelected) Color(0xFFFFBF48) else Color.White
    val textColor = if (isSelected) Color(0xFF674E0F) else Color(0xFF999999)

    Column(
        modifier =
            Modifier
                .width(106.dp)
                .height(120.dp)
                .clip(RoundedCornerShape(12.dp))
                .clickable(onClick = onClick)
                .then(
                    if (isSelected && (label.isNotEmpty() || needCountDown)) {
                        Modifier.paint(
                            painterResource(R.drawable.bg_cheap_goods),
                            contentScale = ContentScale.FillBounds,
                        )
                    } else {
                        Modifier
                            .background(
                                color = backgroundColor,
                                shape = RoundedCornerShape(12.dp),
                            ).then(
                                if (label.isNotEmpty() || needCountDown) {
                                    Modifier
                                } else {
                                    Modifier.border(
                                        width = 2.dp,
                                        color = borderColor,
                                        shape = RoundedCornerShape(12.dp),
                                    )
                                },
                            )
                    },
                ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            Modifier
                .fillMaxWidth()
                .height(20.dp),
        ) {
            Box(
                modifier =
                    Modifier
                        .fillMaxHeight()
                        .then(
                            if (label.isNotEmpty()) {
                                Modifier
                                    .background(
                                        Brush.linearGradient(listOf(Color(0xFFFB3E01), Color(0xFFFF7E14))),
                                        RoundedCornerShape(topStart = 12.dp, bottomEnd = 12.dp),
                                    ).padding(horizontal = 8.dp)
                            } else {
                                Modifier
                            },
                        ),
                contentAlignment = Alignment.Center,
            ) {
                Text(label, fontSize = 11.sp, color = Color(0xFFFFFBA8))
            }
        }
        SizeHeight(8.dp)
        Text(
            text = name,
            fontSize = 12.sp,
            fontWeight = FontWeight.Black,
            color = textColor,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )
        SizeHeight(8.dp)
        Text(
            text = price,
            fontSize = 18.sp,
            fontWeight = FontWeight.ExtraBold,
            color = textColor,
            textAlign = TextAlign.Center,
        )
        // extra
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .weight(1f),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                extraNote,
                color = WakooGrayText,
                fontSize = 11.sp,
                textDecoration =
                    if (extraNoteDelLine) TextDecoration.LineThrough else null,
            )
        }

        // count down
        val isPreview = LocalInspectionMode.current
        var counter by rememberSaveable(needCountDown) {
            mutableIntStateOf(
                if (needCountDown) {
                    val secondsOfDay = 1.days.toInt(DurationUnit.SECONDS)
                    if (isPreview) {
                        secondsOfDay
                    } else {
                        currentUserKV.getLong("price_tick_start_mills", 0).let {
                            if (it <= 0L) {
                                currentUserKV.putLong("price_tick_start_mills", System.currentTimeMillis())
                                secondsOfDay
                            } else {
                                secondsOfDay -
                                    System
                                        .currentTimeMillis()
                                        .minus(it)
                                        .coerceAtLeast(0)
                                        .div(1000)
                                        .rem(secondsOfDay)
                                        .toInt()
                            }
                        }
                    }
                } else {
                    0
                },
            )
        }
        var counterStr by remember {
            mutableStateOf(formatCounterString(counter))
        }
        if (counter > 0) {
            LaunchedEffect(Unit) {
                while (isActive) {
                    delay(1000)
                    counter--
                    counterStr = formatCounterString(counter)
                }
            }
        }

        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(24.dp)
                    .background(if (counter > 0) Color(0xFFF5CB77) else Color.Transparent),
            contentAlignment = Alignment.Center,
        ) {
            if (counter > 0) {
                Text(
                    counterStr,
                    fontSize = 11.sp,
                    color = Color(0xFF803E00),
                    fontWeight = FontWeight.Bold,
                    fontFamily = FontFamily.Monospace,
                )
            }
        }
    }
}

private fun formatCounterString(value: Int): String {
    if (value < 0) {
        return ""
    }

    val hours = value / 3600 // 计算小时部分
    val minutes = (value % 3600) / 60 // 计算分钟部分
    val seconds = value % 60 // 计算秒部分

    return "限时%s".localizedFormat(String.format(java.util.Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, seconds))
}

@Composable
fun BottomButton(
    text: String,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier =
            modifier
                .height(44.dp)
                .clip(RoundedCornerShape(50))
                then
                (modifier)
                    .background(
                        Brush.horizontalGradient(listOf(Color(0xFFFFF799), Color(0xFFFFE072))),
                        RoundedCornerShape(50),
                    ),
        contentAlignment = Alignment.Center,
    ) {
        Text(text, color = Color(0xFF674E0F), fontSize = 16.sp)
    }
}

@Preview
@Composable
private fun ButtonPreview() {
    BottomButton("立即开通".localized, modifier = Modifier.width(320.dp))
}
