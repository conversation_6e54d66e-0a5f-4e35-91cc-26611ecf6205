package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.NotificationAlert: ImageVector
    get() {
        if (_Notification2Fill != null) {
            return _Notification2Fill!!
        }
        _Notification2Fill = ImageVector.Builder(
            name = "Notification2Fill",
            defaultWidth = 12.dp,
            defaultHeight = 12.dp,
            viewportWidth = 12f,
            viewportHeight = 12f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(12f)
                    verticalLineToRelative(12f)
                    horizontalLineToRelative(-12f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFF1A7D1D))) {
                    moveTo(11f, 10f)
                    horizontalLineTo(1f)
                    verticalLineTo(9f)
                    horizontalLineTo(1.5f)
                    verticalLineTo(5.516f)
                    curveTo(1.5f, 3.022f, 3.515f, 1f, 6f, 1f)
                    curveTo(8.485f, 1f, 10.5f, 3.022f, 10.5f, 5.516f)
                    verticalLineTo(9f)
                    horizontalLineTo(11f)
                    verticalLineTo(10f)
                    close()
                    moveTo(4.75f, 10.5f)
                    horizontalLineTo(7.25f)
                    curveTo(7.25f, 10.832f, 7.118f, 11.149f, 6.884f, 11.384f)
                    curveTo(6.649f, 11.618f, 6.332f, 11.75f, 6f, 11.75f)
                    curveTo(5.668f, 11.75f, 5.351f, 11.618f, 5.116f, 11.384f)
                    curveTo(4.882f, 11.149f, 4.75f, 10.832f, 4.75f, 10.5f)
                    close()
                }
            }
        }.build()

        return _Notification2Fill!!
    }

@Suppress("ObjectPropertyName")
private var _Notification2Fill: ImageVector? = null
