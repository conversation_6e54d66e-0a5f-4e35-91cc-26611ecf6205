package com.buque.wakoo.ui.screens.debug

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.ui.dialog.EnvironmentSwitchDialog
import com.buque.wakoo.ui.dialog.GitCommitsDialog
import com.buque.wakoo.ui.widget.SizeWidth
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 调试菜单
 * 仅在 Debug 模式下显示，提供各种调试功能
 */
@Composable
fun DebugMenu(onDismiss: () -> Unit) {
    var showEnvironmentDialog by remember { mutableStateOf(false) }
    var showGitCommitsDialog by remember { mutableStateOf(false) }

    // 只在 Debug 模式下显示
    if (EnvironmentManager.isProdRelease) {
        LaunchedEffect(Unit) {
            onDismiss()
        }
        return
    }

    val scope = rememberCoroutineScope()

    Dialog(
        onDismissRequest = onDismiss,
        properties =
            DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
            ),
    ) {
        Card(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 10.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        ) {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = "调试菜单",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                    )

                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 应用信息
                Card(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Text(
                        text = "应用信息",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    InfoRow("版本", "${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})")
                    InfoRow("构建类型", BuildConfig.BUILD_TYPE)
                    InfoRow("环境", BuildConfig.FLAVOR_environment)
                    InfoRow("渠道", BuildConfig.FLAVOR_channel)
                    InfoRow("当前环境", EnvironmentManager.getCurrentEnvironmentKey())
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Git 信息
                Card(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Text(
                        text = "Git 信息",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    InfoRow("分支", BuildConfig.GIT_BRANCH)
                    InfoRow("提交", BuildConfig.GIT_SHORT_COMMIT_HASH)
                    InfoRow("消息", BuildConfig.GIT_COMMIT_MESSAGE)
                    InfoRow("作者", BuildConfig.GIT_COMMIT_AUTHOR)
                    InfoRow("提交时间", BuildConfig.GIT_COMMIT_DATE)
                    InfoRow(
                        "构建时间",
                        SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                            .format(Date(BuildConfig.GIT_BUILD_TIME)),
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 调试功能列表
                Text(
                    text = "调试功能",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                )

                Spacer(modifier = Modifier.height(8.dp))

                LazyColumn(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .weight(1f, false),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                ) {
                    item {
                        DebugMenuItem(
                            icon = Icons.Default.Settings,
                            title = "环境切换",
                            description = "切换到不同的环境配置",
                            onClick = {
                                showEnvironmentDialog = true
                            },
                        )
                    }

                    item {
                        DebugMenuItem(
                            icon = Icons.Default.Info,
                            title = "Git 提交记录",
                            description = "查看最近5条提交记录",
                            onClick = {
                                showGitCommitsDialog = true
                            },
                        )
                    }

                    item {
                        DebugSwitchMenuItem(
                            icon = Icons.Default.Info,
                            title = "日志开关",
                            description = "切换将重启app",
                            enabled = EnvironmentManager.current.enableLog,
                            onCheckedChange = {
                                scope.launch {
                                    delay(500)
                                    EnvironmentManager.switchLogEnable(it)
                                }
                            },
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 关闭按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Text("关闭")
                }
            }
        }
    }

    // 环境切换对话框
    if (showEnvironmentDialog) {
        EnvironmentSwitchDialog(
            onDismiss = {
                showEnvironmentDialog = false
            },
        )
    }

    // Git 提交记录对话框
    if (showGitCommitsDialog) {
        GitCommitsDialog(
            onDismiss = { showGitCommitsDialog = false },
        )
    }
}

/**
 * 信息行组件
 */
@Composable
private fun InfoRow(
    label: String,
    value: String,
) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
        )
        SizeWidth(5.dp)
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.End,
        )
    }
}

/**
 * 调试菜单项组件
 */
@Composable
private fun DebugMenuItem(
    icon: ImageVector,
    title: String,
    description: String,
    enabled: Boolean = true,
    onClick: () -> Unit,
) {
    Card(
        modifier =
            Modifier
                .fillMaxWidth()
                .clickable(enabled = enabled) { onClick() },
        colors =
            CardDefaults.cardColors(
                containerColor =
                    if (enabled) {
                        MaterialTheme.colorScheme.surface
                    } else {
                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                    },
            ),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint =
                    if (enabled) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    },
                modifier = Modifier.size(24.dp),
            )

            Spacer(modifier = Modifier.width(8.dp))

            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color =
                        if (enabled) {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                        },
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color =
                        if (enabled) {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                        },
                )
            }

            if (enabled) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }
        }
    }
}

@Composable
private fun DebugSwitchMenuItem(
    icon: ImageVector,
    title: String,
    description: String,
    enabled: Boolean = true,
    onCheckedChange: (Boolean) -> Unit,
) {
    Card(
        modifier =
            Modifier
                .fillMaxWidth(),
        colors =
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
            ),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp),
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface,
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }

            var checked by remember {
                mutableStateOf(enabled)
            }

            Switch(checked, onCheckedChange = {
                checked = it
                onCheckedChange(it)
            })
        }
    }
}
