package com.buque.wakoo.ui.widget

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.Surface
import androidx.compose.material3.TabRowDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.debugInspectorInfo
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlin.math.max

/**
 * 一个经过修改的 ScrollableTabRow，它可以根据内容自适应宽度。
 *
 * @param selectedTabIndex 当前选中的 tab 索引。
 * @param modifier 应用于此 TabRow 的 Modifier。
 * @param containerColor TabRow 的背景颜色。
 * @param contentColor TabRow 内容（如图标和文本）的首选颜色。
 * @param edgePadding TabRow 边缘的内边距。
 * @param tabSpacing 在每个 Tab 之间应用的间距。
 * @param indicator 用于显示当前选中 Tab 的指示器。
 * @param divider 在 TabRow 底部绘制的分割线。
 * @param tabs TabRow 的内容，通常是多个 `Tab` Composable。
 */
@Composable
fun AdaptiveScrollableTabRow(
    selectedTabIndex: Int,
    modifier: Modifier = Modifier,
    containerColor: Color = Color.Transparent,
    contentColor: Color = TabRowDefaults.primaryContentColor,
    edgePadding: Dp = TabRowDefaults.ScrollableTabRowEdgeStartPadding,
    tabSpacing: Dp = 0.dp, // 新增：Tab之间的间距
    indicator: @Composable (tabPositions: List<TabPosition>) -> Unit = @Composable { tabPositions ->
        if (selectedTabIndex < tabPositions.size) {
            TabRowDefaults.SecondaryIndicator(
                Modifier.adaptiveTabIndicatorOffset(tabPositions[selectedTabIndex]),
            )
        }
    },
    divider: @Composable () -> Unit = @Composable {
    },
    tabs: @Composable () -> Unit,
) {
    Surface(
        modifier = modifier,
        color = containerColor,
        contentColor = contentColor,
    ) {
        val scrollState = rememberScrollState()
        val coroutineScope = rememberCoroutineScope()
        val scrollableTabData =
            remember(scrollState, coroutineScope) {
                ScrollableTabData(
                    scrollState = scrollState,
                    coroutineScope = coroutineScope,
                )
            }

        // SubcomposeLayout 是实现这一切的核心
        SubcomposeLayout(
            // 1. 关键修改：移除了 .fillMaxWidth()，使得组件的宽度可以小于父组件宽度
            Modifier
                .horizontalScroll(scrollState)
                .selectableGroup()
                .clipToBounds(),
        ) { constraints ->
            val edgePaddingPx = edgePadding.roundToPx()
            val tabSpacingPx = tabSpacing.roundToPx() // 2. 新增：将 Dp 间距转换为像素

            // 测量所有 Tab
            val tabMeasurables = subcompose(TabSlots.Tabs, tabs)
            val tabPlaceables = mutableListOf<Placeable>()
            var layoutHeight = 0
            var maxIntrinsicTabWidth = 0

            tabMeasurables.forEach {
                val placeable = it.measure(constraints.copy(minWidth = 0))
                tabPlaceables.add(placeable)
                layoutHeight = max(layoutHeight, placeable.height)
                maxIntrinsicTabWidth = max(maxIntrinsicTabWidth, it.maxIntrinsicWidth(placeable.height))
            }

            // 计算所有 Tab + Padding + 间距 所需的总宽度
            val tabsWidth = tabPlaceables.sumOf { it.width }
            val spacingWidth =
                if (tabPlaceables.isNotEmpty()) {
                    (tabPlaceables.size - 1) * tabSpacingPx
                } else {
                    0
                }
            val scrollableWidth = edgePaddingPx * 2 + tabsWidth + spacingWidth

            // 3. 关键修改：决定最终的布局宽度
            // 如果内容总宽小于父约束，则使用内容宽度（实现wrap_content）
            // 否则使用父约束的最大宽度（保持原可滚动逻辑）
            val finalLayoutWidth = scrollableWidth.coerceAtMost(constraints.maxWidth)

            layout(finalLayoutWidth, layoutHeight) {
                val tabPositions = mutableListOf<TabPosition>()
                var left = edgePaddingPx

                // 放置所有 Tab，并在它们之间添加间距
                tabPlaceables.forEachIndexed { index, placeable ->
                    placeable.placeRelative(left, 0)
                    val contentWidth =
                        minOf(
                            maxIntrinsicTabWidth.toDp(),
                            placeable.width.toDp(),
                        )
                    tabPositions.add(TabPosition(left.toDp(), placeable.width.toDp(), contentWidth))
                    left += placeable.width + tabSpacingPx // 4. 新增：在放置后累加间距
                }

                // 放置分割线
                subcompose(TabSlots.Divider, divider).forEach {
                    val placeable =
                        it.measure(
                            constraints.copy(
                                minHeight = 0,
                                minWidth = scrollableWidth, // 分割线应与可滚动内容等宽
                                maxWidth = scrollableWidth,
                            ),
                        )
                    placeable.placeRelative(0, layoutHeight - placeable.height)
                }

                // 放置指示器容器
                subcompose(TabSlots.Indicator) {
                    indicator(tabPositions)
                }.forEach {
                    it
                        .measure(Constraints.fixed(scrollableWidth, layoutHeight))
                        .placeRelative(0, 0)
                }

                scrollableTabData.onLaidOut(
                    density = this@SubcomposeLayout,
                    edgeOffset = edgePaddingPx,
                    tabPositions = tabPositions,
                    selectedTab = selectedTabIndex,
                )
            }
        }
    }
}

// --- 以下是 AdaptiveScrollableTabRow 依赖的辅助类和函数，无需修改 ---

private enum class TabSlots {
    Tabs,
    Divider,
    Indicator,
}

// 保持不变，源码中的 TabPosition
@Immutable
class TabPosition internal constructor(
    val left: Dp,
    val width: Dp,
    val contentWidth: Dp,
) {
    val right: Dp get() = left + width

    override fun equals(other: Any?): Boolean {
        if (this ===
            other
        ) {
            return true
        }
        if (other !is TabPosition) return false
        if (left !=
            other.left
        ) {
            return false
        }
        if (width !=
            other.width
        ) {
            return false
        }
        if (contentWidth != other.contentWidth) return false
        return true
    }

    override fun hashCode(): Int {
        var result = left.hashCode()
        result = 31 * result + width.hashCode()
        result =
            31 * result + contentWidth.hashCode()
        return result
    }

    override fun toString(): String = "TabPosition(left=$left, right=$right, width=$width, contentWidth=$contentWidth)"
}

// 保持不变，源码中的 Modifier.tabIndicatorOffset
fun Modifier.adaptiveTabIndicatorOffset(currentTabPosition: TabPosition): Modifier =
    composed(
        inspectorInfo =
            debugInspectorInfo {
                name = "tabIndicatorOffset"
                value = currentTabPosition
            },
    ) {
        val currentTabWidth by animateDpAsState(
            targetValue = currentTabPosition.width,
            animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing),
            label = "IndicatorWidth",
        )
        val indicatorOffset by animateDpAsState(
            targetValue = currentTabPosition.left,
            animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing),
            label = "IndicatorOffset",
        )
        fillMaxWidth()
            .wrapContentSize(Alignment.BottomStart)
            .offset { IntOffset(x = indicatorOffset.roundToPx(), y = 0) }
            .width(currentTabWidth)
    }

// 保持不变，源码中的 ScrollableTabData
private class ScrollableTabData(
    private val scrollState: ScrollState,
    private val coroutineScope: CoroutineScope,
) {
    private var selectedTab: Int? = null

    fun onLaidOut(
        density: Density,
        edgeOffset: Int,
        tabPositions: List<TabPosition>,
        selectedTab: Int,
    ) {
        if (this.selectedTab != selectedTab) {
            this.selectedTab = selectedTab
            tabPositions.getOrNull(selectedTab)?.let {
                val calculatedOffset = it.calculateTabOffset(density, edgeOffset, tabPositions)
                if (scrollState.value != calculatedOffset) {
                    coroutineScope.launch {
                        scrollState.animateScrollTo(
                            calculatedOffset,
                            animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing),
                        )
                    }
                }
            }
        }
    }

    private fun TabPosition.calculateTabOffset(
        density: Density,
        edgeOffset: Int,
        tabPositions: List<TabPosition>,
    ): Int =
        with(density) {
            val totalTabRowWidth =
                tabPositions
                    .lastOrNull()
                    ?.right
                    ?.roundToPx()
                    ?.plus(edgeOffset) ?: 0
            val visibleWidth = totalTabRowWidth - scrollState.maxValue
            val tabOffset = left.roundToPx()
            val scrollerCenter = visibleWidth / 2
            val tabWidth = width.roundToPx()
            val centeredTabOffset = tabOffset - (scrollerCenter - tabWidth / 2)
            val availableSpace = (totalTabRowWidth - visibleWidth).coerceAtLeast(0)
            return centeredTabOffset.coerceIn(0, availableSpace)
        }
}
