package com.buque.wakoo.ui.screens.liveroom

import android.os.VibrationEffect
import android.os.Vibrator
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.getSystemService
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnContent
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.hb.CoinType
import com.buque.wakoo.bean.hb.HBSettings
import com.buque.wakoo.bean.hb.HBSettings.DelayType
import com.buque.wakoo.bean.hb.HBSettings.GrabType
import com.buque.wakoo.bean.hb.HongBaoInfo
import com.buque.wakoo.bean.hb.PostHongBaoParams
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.consts.SceneType
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.Alert
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.Center
import com.buque.wakoo.ui.widget.ShapeCheckBox
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.popup.BubbleShape
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.Server
import com.buque.wakoo.viewmodel.HongBaoViewModel
import kotlinx.coroutines.delay

@Composable
fun HongBaoPendant(count: Int, time: String, modifier: Modifier = Modifier) {
    Box(modifier = modifier.width(44.dp)) {
        Image(
            modifier = Modifier
                .padding(top = 10.dp)
                .size(34.dp, 44.dp)
                .align(Alignment.TopCenter),
            painter = painterResource(R.drawable.ic_hongbao),
            contentDescription = "icon",
            contentScale = ContentScale.FillBounds
        )

        Text(
            count.toString(),
            modifier = Modifier
                .align(Alignment.TopEnd)
                .size(20.dp)
                .background(Color(0xFFFF5245), CircleShape)
                .border(1.dp, Color.White, CircleShape),
            lineHeight = 20.sp,
            fontSize = 10.sp, textAlign = TextAlign.Center, color = Color.White
        )

        Box(
            modifier = Modifier
                .padding(top = 50.dp)
                .size(40.dp, 14.dp)
                .align(Alignment.TopCenter)
                .background(
                    Brush.verticalGradient(
                        listOf(
                            Color(0x80000000),
                            Color(0xC9FF292C)
                        )
                    ), RoundedCornerShape(50)
                )
                .border(0.5.dp, Color(0xFFFFCD5D), RoundedCornerShape(50)),
            contentAlignment = Alignment.Center
        ) {
            Text(time, fontSize = 10.sp, color = Color(0xFFFFFDC0))
        }
    }
}

@Composable
fun HongBaoPendantContainer(list: List<HongBaoInfo>, modifier: Modifier = Modifier, onClick: OnDataCallback<HongBaoInfo>) {
    if (list.isEmpty()) return
    key(list) {
        val pagerState = rememberPagerState { list.size }

        Box(modifier = modifier) {
            HorizontalPager(pagerState) { page ->
                val hbInfo = list[page]
                var timeDesc by remember(hbInfo.id) { mutableStateOf("") }
                if (hbInfo.needCountDown) {
                    LaunchedEffect(hbInfo.id, hbInfo.openMillSecond) {
                        while (true) {
                            val cur = Server.currentTimeMillis()
                            val time = formatTime(hbInfo.openMillSecond - cur)
                            if (time.isEmpty()) {
                                timeDesc = ""
                                break
                            }
                            timeDesc = time
                            LogUtils.d("time changed:$timeDesc")
                            val t = hbInfo.openMillSecond - cur
                            if (t < 1000) {
                                delay(t)
                            } else {
                                delay(1000L)
                            }
                        }
                    }
                }
                Box(
                    modifier = Modifier
                        .width(44.dp)
                        .click(onClick = {
                            onClick.invoke(hbInfo)
                        })
                ) {
                    Image(
                        modifier = Modifier
                            .padding(top = 10.dp)
                            .size(34.dp, 44.dp)
                            .align(Alignment.TopCenter),
                        painter = painterResource(R.drawable.ic_hongbao),
                        contentDescription = "icon",
                        contentScale = ContentScale.FillBounds
                    )
                    if (timeDesc.isNotEmpty()) {
                        Box(
                            modifier = Modifier
                                .padding(top = 50.dp)
                                .size(40.dp, 14.dp)
                                .align(Alignment.TopCenter)
                                .background(
                                    Brush.verticalGradient(
                                        listOf(
                                            Color(0x80000000),
                                            Color(0xC9FF292C)
                                        )
                                    ), RoundedCornerShape(50)
                                )
                                .border(0.5.dp, Color(0xFFFFCD5D), RoundedCornerShape(50)),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(timeDesc, fontSize = 10.sp, color = Color(0xFFFFFDC0))
                        }
                    }
                }
            }
            Text(
                list.size.toString(),
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(20.dp)
                    .background(Color(0xFFFF5245), CircleShape)
                    .border(1.dp, Color.White, CircleShape),
                lineHeight = 20.sp,
                fontSize = 10.sp, textAlign = TextAlign.Center, color = Color.White
            )
        }
    }
}

private val colorEditBg = Color(0xB328000A)
private val colorEditText = Color(0xFFFFEB93)

@Composable
fun EditHongBaoContent(
    countDesc: String,
    minCostDesc: String,
    notificationDesc: String,
    blessTextHint: String,
    bottomDesc: String,
    delayTypes: List<DelayType> = listOf(),
    grabTypes: List<GrabType> = listOf(),
    hasTribe: Boolean = false,
    hasRelationship: Boolean = false,
    initDelayType: Int = -1,
    initGrabType: Int = -1,
    sceneType: Int = SceneType.WAKOO_ROOM,
    sceneId: String = "",
    onPost: (PostHongBaoParams) -> Unit = {}
) {

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1125 / 1638f)
            .paint(painterResource(R.drawable.bg_hongbao), contentScale = ContentScale.FillBounds)
            .padding(horizontal = 32.dp)
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Center(
            modifier = Modifier
                .fillMaxWidth()
                .height(54.dp),
        ) {
            Text("发钻石红包".localized, fontSize = 16.sp, color = Color.White)
        }

        val modEdit = Modifier
            .fillMaxWidth()
            .height(46.dp)
            .background(colorEditBg, RoundedCornerShape(8.dp))
            .padding(horizontal = 12.dp)

        val modTF = Modifier
            .width(200.dp)
            .padding(vertical = 8.dp)

        var count by rememberSaveable { mutableStateOf("") }
        var cost by rememberSaveable { mutableStateOf("") }
        var bless by rememberSaveable { mutableStateOf("") }
        var grabType by rememberSaveable { mutableIntStateOf(initGrabType) }
        var delayType by rememberSaveable { mutableIntStateOf(initDelayType) }

        Box(
            modifier = modEdit
        ) {
            EditBackend("红包个数".localized, countDesc.takeIf { count.isEmpty() })
            BasicTextField(
                count, onValueChange = {
                    count = it
                },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                cursorBrush = SolidColor(Color.White),
                textStyle = TextStyle(color = colorEditText, textAlign = TextAlign.End),
                modifier = modTF.align(Alignment.CenterEnd)
            )
        }
        SizeHeight(12.dp)
        Box(
            modifier = modEdit
        ) {
            EditBackend("钻石总数".localized, minCostDesc.takeIf { cost.isEmpty() })
            Row(modifier = Modifier.align(Alignment.CenterEnd), verticalAlignment = Alignment.CenterVertically) {
                BasicTextField(
                    cost, onValueChange = {
                        cost = it
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    cursorBrush = SolidColor(Color.White),
                    textStyle = TextStyle(color = colorEditText, textAlign = TextAlign.End),
                    modifier = modTF
                )

                AnimatedVisibility(cost.isNotEmpty()) {
                    Image(
                        painterResource(R.drawable.ic_green_diamond_straight),
                        modifier = Modifier.size(18.dp),
                        contentDescription = "dis",
                        contentScale = ContentScale.FillBounds
                    )
                }
            }
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(36.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                WakooIcons.Alert, contentDescription = "icon",
                tint = Color.White.copy(0.5f),
                modifier = Modifier.size(12.dp)
            )
            SizeWidth(4.dp)
            Text(notificationDesc, fontSize = 12.sp, color = Color.White.copy(0.5f))
        }
        Box(modifier = modEdit) {
            if (bless.isEmpty()) {
                Text(blessTextHint, color = Color.White.copy(0.5f), modifier = Modifier.align(Alignment.CenterStart))
            }
            BasicTextField(
                bless, onValueChange = {
                    bless = it
                },
                cursorBrush = SolidColor(Color.White),
                textStyle = TextStyle(color = colorEditText),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
                    .align(Alignment.Center)
            )
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(62.dp), contentAlignment = Alignment.Center
        ) {
            Text("- 红包开抢条件 -".localized, color = Color.White.copy(0.8f))
        }
        Box(
            modifier = modEdit
        ) {
            Text("可抢用户", color = Color.White.copy(0.8f), modifier = Modifier.align(Alignment.CenterStart))

            val grabUserText by remember {
                derivedStateOf {
                    grabTypes.firstOrNull { it.value == grabType }?.name ?: ""
                }
            }

            val bubbleShape =
                remember {
                    BubbleShape(arrowPositionBias = 0.85f)
                }

            var expanded by rememberSaveable { mutableStateOf(false) }

            Box(
                Modifier
                    .align(Alignment.CenterEnd)
            ) {
                Row(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .click(onClick = {
                            expanded = true
                        }),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(grabUserText, color = Color.White.copy(0.8f))
                    Icon(Icons.Default.KeyboardArrowDown, tint = Color.White, modifier = Modifier.size(14.dp), contentDescription = "")
                }
                DropdownMenu(
                    modifier = Modifier.width(108.dp),
                    expanded = expanded, // 菜单的展开状态
                    onDismissRequest = { expanded = false }, // 点击菜单外部或按返回键时关闭菜单
                    shape = bubbleShape,
                    offset = DpOffset((-8).dp, (2).dp),
                    tonalElevation = 0.dp,
                    shadowElevation = 0.dp,
                    containerColor = Color.White,
                ) {
                    grabTypes.forEach { type ->
                        DropdownMenuItem(text = {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(32.dp), contentAlignment = Alignment.Center
                            ) {
                                BasicText(
                                    type.name,
                                    autoSize = TextAutoSize.StepBased(
                                        minFontSize = 6.sp, maxFontSize = 12.sp
                                    ),
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    style = TextStyle(textAlign = TextAlign.Center, color = WakooText)
                                )
                            }
                        }, onClick = {
                            if (type.value == HBSettings.GrabType.ONLY_TRIBE_MEMBER && hasTribe.not()) {
                                showToast("你还没有群组哦".localized)
                                return@DropdownMenuItem
                            }
                            if (type.value == GrabType.ONLY_FAMILY_AND_FRIEND && hasRelationship.not()) {
                                showToast("你还没有亲友团成员哦".localized)
                                return@DropdownMenuItem
                            }
                            grabType = type.value
                            expanded = false
                        })
                    }
                }
            }
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            delayTypes.forEach { delay ->
                Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.click(onClick = {
                    delayType = delay.value
                })) {
                    val checked = delayType == delay.value
                    ShapeCheckBox(checked, onCheckChanged = {}, enabled = false, modifier = Modifier.size(21.6.dp))
                    SizeWidth(2.dp)
                    Text(delay.name, color = if (checked) Color.White else Color.White.copy(0.5f))
                }
            }
        }

        val enable = cost.isNotEmpty() && count.isNotEmpty()
        Center(
            modifier = Modifier
                .width(260.dp)
                .height(48.dp)
                .paint(painterResource(R.drawable.bg_yellow_button), contentScale = ContentScale.FillBounds)
                .graphicsLayer(
                    alpha = if (enable) 1f else 0.5f
                )
                .click(onClick = {
                    if (enable) {
                        val self = SelfUser ?: return@click
                        val coinType = if (self.isCN) CoinType.GOLD else CoinType.JA_GOLD
                        val coinValue = cost.toIntOrNull()
                        if (coinValue == null) {
                            showToast("钻石总数不正确".localized)
                            return@click
                        }
                        val number = count.toIntOrNull()
                        if (number == null) {
                            showToast("红包个数不正确".localized)
                            return@click
                        }
                        val params =
                            PostHongBaoParams(coinType, coinValue, grabType, bless.ifEmpty { blessTextHint }, number, sceneId, sceneType, delayType)
                        onPost(params)
                    }
                }),
        ) {
            Text(
                "发红包".localized,
                color = Color(0xFF9F002F),
                fontSize = 15.sp,
                textAlign = TextAlign.Center,
            )
        }
        SizeHeight(8.dp)
        Text(
            bottomDesc,
            modifier = Modifier.width(225.dp),
            textAlign = TextAlign.Center,
            fontSize = 11.sp, color = Color.White.copy(0.5f)
        )
    }
}

@Composable
fun HongBaoEditPanel(hbVm: HongBaoViewModel = viewModel<HongBaoViewModel>(), onDismiss: OnAction = {}) {
    val settingsState by hbVm.settingsState
    LaunchedEffect(Unit) {
        hbVm.refreshSettings()
    }
    val lm = LocalLoadingManager.current
    when (settingsState) {
        is CState.Success -> {
            val settings = (settingsState as CState.Success<HBSettings>).data
            EditHongBaoContent(
                "%d-%d个".localizedFormat(settings.minNumber, settings.maxNumber),
                "%d钻石起".localizedFormat(settings.minCoin),
                settings.notifyTooltips,
                "恭喜发财，大吉大利".localized,
                settings.tips.joinToString(separator = "\n"),
                delayTypes = settings.delayTypes,
                grabTypes = settings.grabTypes,
                hasTribe = settings.hasTribe,
                hasRelationship = settings.hasRelationship,
                initGrabType = settings.grabTypes.firstOrNull()?.value ?: -1,
                initDelayType = settings.delayTypes.firstOrNull()?.value ?: -1,
                sceneType = hbVm.sceneType,
                sceneId = hbVm.sceneId,
                onPost = { params ->
                    if (params.number < settings.minNumber) {
                        showToast("红包个数最小为%d".localizedFormat(settings.minNumber))
                        return@EditHongBaoContent
                    }
                    if (params.number > settings.maxNumber) {
                        showToast("红包个数最大为%d".localizedFormat(settings.maxNumber))
                        return@EditHongBaoContent
                    }
                    if (params.coinValue < settings.minCoin) {
                        showToast("钻石总数最少为%d".localizedFormat(settings.minCoin))
                        return@EditHongBaoContent
                    }
                    lm.show(null) {
                        hbVm.repo.postHongBao(params)
                            .onSuccess {
                                LogUtils.d(it.toString())
                                onDismiss()
                            }
                    }

                }
            )
        }

        else -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1125 / 1638f)
                    .paint(painterResource(R.drawable.bg_hongbao), contentScale = ContentScale.FillBounds)
            )
        }
    }
}

@Composable
fun EditBackend(title: String, desc: String?, titleColor: Color = Color.White) {
    Row(
        modifier = Modifier
            .fillMaxSize(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(title, color = titleColor)
        Weight()
        if (!desc.isNullOrEmpty()) {
            Text(desc, color = Color.White.copy(0.5f))
        }
    }
}

@Composable
fun HongBaoMessage(title: String, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .size(178.dp, 56.dp)
            .paint(painterResource(R.drawable.bg_hongbao_message), contentScale = ContentScale.FillBounds)
            .padding(horizontal = 6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.icon_hb),
            modifier = Modifier.size(28.dp, 36.dp),
            contentScale = ContentScale.FillBounds,
            contentDescription = "icon"
        )
        SizeWidth(6.dp)
        Column(modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.spacedBy(8.dp)) {
            Text(title, color = Color.White, fontSize = 12.sp)
            Text("领取红包".localized, color = Color.White, fontSize = 12.sp)
        }
    }
}


@Preview
@Composable
private fun HonBaoPreview() {
    Column(modifier = Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
        HongBaoPendant(1, "01:55")
        EditHongBaoContent(
            "2-50个", "50钻石起", "红包钻石数>999将触发全服飘屏通知",
            "恭喜发财，大吉大利",
            "未领取的红包过期后会发起退款，2人红包将扣除5%钻石手续费"
        )
    }
}

@Composable
fun HongBaoNotOpenContent(
    user: User,
    title: String, message: String,
    grabMessage: String,
    amountDesc: String,
    lastTimeDesc: String? = null,
    onOpen: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .size(321.dp, 418.dp)
            .paint(painterResource(R.drawable.bg_hb_new), contentScale = ContentScale.FillBounds),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        SizeHeight(44.dp)
        AvatarNetworkImage(
            user,
            Modifier.border(1.5.dp, colorEditText, CircleShape),
            72.dp
        )
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SizeHeight(20.dp)
            Text(title, color = Color.White, fontSize = 18.sp, maxLines = 1, overflow = TextOverflow.MiddleEllipsis)
            SizeHeight(8.dp)
            Text(message, color = Color.White, maxLines = 2, overflow = TextOverflow.Ellipsis)

            if (!lastTimeDesc.isNullOrEmpty()) {
                Weight(1f)
                Box(
                    modifier = Modifier
                        .height(28.dp)
                        .background(Color(0xFFA41F2C), RoundedCornerShape(8.dp))
                        .padding(horizontal = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(lastTimeDesc, color = colorEditText, fontSize = 12.sp, fontFamily = FontFamily.Monospace)
                }
                SizeHeight(4.dp)
            }

        }

        val context = LocalContext.current
        Box(
            modifier = Modifier
                .size(120.dp)
                .clip(CircleShape)
                .click(onClick = {
                    if (lastTimeDesc.isNullOrEmpty()) {
                        onOpen()
                    } else {
                        context.getSystemService<Vibrator>()?.vibrate(
                            VibrationEffect.createOneShot(500L, 255),
                        )
                        LogUtils.d("vibrate...")
                    }
                }), contentAlignment = Alignment.Center
        ) {
            Text("开".localized, color = Color(0xFFBA571A), fontSize = 38.sp, fontWeight = FontWeight.ExtraBold)
        }

        Column(modifier = Modifier.height(64.dp), horizontalAlignment = Alignment.CenterHorizontally) {
            SizeHeight(8.dp)
            Text(grabMessage, color = Color.White)
            SizeHeight(4.dp)
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(amountDesc, color = colorEditText)
                Image(painterResource(R.drawable.ic_green_diamond_straight), contentDescription = "", modifier = Modifier.size(14.dp))
            }
        }

    }
}

@Preview
@Composable
private fun HPreview() {
    HongBaoNotOpenContent(
        BasicUser.sampleBoy,
        "幼儿园的爆红hahahhahaahahhaahhahahaahah的红包",
        "恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,恭喜发财,",
        "本群组成员可抢",
        "红包金额：52钻石",
        lastTimeDesc = "02:49后开抢"
    )
}

@Composable
fun HongBaoOpenedContent(
    buttonText: String,
    message: OnContent,
    icon: Int = R.drawable.img_diamond,
    onClick: OnAction = {}
) {
    Column(
        modifier = Modifier
            .size(342.dp, 418.dp)
            .paint(painterResource(R.drawable.bg_hb_opened), contentScale = ContentScale.FillBounds),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        SizeHeight(55.dp)
        Image(
            painterResource(icon),
            modifier = Modifier.size(66.dp),
            contentScale = ContentScale.FillBounds,
            contentDescription = ""
        )
        SizeHeight(15.dp)
        message()
        Weight(1f)
        Box(
            modifier = Modifier
                .size(232.dp, 48.dp)
                .click(onClick = onClick)
                .paint(painterResource(R.drawable.bg_yellow_button), contentScale = ContentScale.FillBounds),
            contentAlignment = Alignment.Center
        ) {
            Text(buttonText, color = Color(0xFF9F002F), fontWeight = FontWeight.Medium)
        }
        SizeHeight(42.dp)
    }
}

@Preview
@Composable
private fun HBOpenedPreview() {
    HongBaoOpenedContent("收下", message = {
        Text("恭喜，抢到24钻石", fontSize = 18.sp, color = Color(0xFFCA803A))
    })
}

@Composable
fun HongBaoDialogContent(id: String, vm: HongBaoViewModel, onDismiss: OnAction = {}) {
    val infoState = vm.rememberHongBaoState(id)
    val hbInfo = infoState.value
    val settings by vm.settingsState
    val lm = LocalLoadingManager.current
    if (hbInfo != null && hbInfo.sender != null) {
        val grabSuccess = remember(hbInfo) {
            hbInfo.hasGrab && hbInfo.grabCoin > 0
        }
        val message by remember(hbInfo) {
            mutableStateOf(
                if (hbInfo.grabCoin > 0) "恭喜！抢到%d钻石".localizedFormat(hbInfo.grabCoin)
                else "抱歉！手速太慢没有抢到".localized
            )
        }

        val buttonText = if (grabSuccess) "收下".localized else "确定".localized
        val complete = hbInfo.status == 2 || hbInfo.remainedNumber <= 0 || hbInfo.hasGrab
        if (complete) {
            HongBaoOpenedContent(buttonText, {
                Text(message, fontSize = 18.sp, color = if (grabSuccess) Color(0xFFCA803A) else WakooGrayText)
            }, icon = if (grabSuccess) R.drawable.img_diamond else R.drawable.img_diamond_gray, onClick = onDismiss)
        } else {
            val grabDesc = settings.dataOrNull?.grabTypes?.firstOrNull { it.value == hbInfo.grabType }?.name.orEmpty()
            var timeDesc by remember { mutableStateOf("") }
            if (hbInfo.needCountDown) {
                LaunchedEffect(hbInfo.openMillSecond, hbInfo.delayTime) {
                    while (true) {
                        val cur = Server.currentTimeMillis()
                        val time = formatTime(hbInfo.openMillSecond - cur)
                        if (time.isEmpty()) {
                            timeDesc = ""
                            break
                        }
                        timeDesc = "%s后开抢".localizedFormat(time)
                        LogUtils.d("time changed:$timeDesc")
                        val t = hbInfo.openMillSecond - cur
                        if (t < 1000) {
                            delay(t)
                        } else {
                            delay(1000L)
                        }
                    }
                }
            }
            HongBaoNotOpenContent(
                hbInfo.sender,
                "%s的红包".localizedFormat(hbInfo.sender.name),
                hbInfo.greets,
                grabDesc,
                "红包金额：%d".localizedFormat(hbInfo.coinValue),
                timeDesc,
                onOpen = {
                    lm.show(null) {
                        vm.grab(hbInfo.id)
                            .onSuccess {
                                infoState.value = hbInfo.copy(hasGrab = true, grabCoin = if (it.success) it.coin else 0)
                            }
                    }
                }
            )
        }
    } else {
        Box(
            modifier = Modifier
                .size(321.dp, 418.dp)
                .paint(
                    painterResource(R.drawable.bg_hb_new),
                    contentScale = ContentScale.FillBounds
                ),
        )
    }
}

private fun formatTime(time: Long): String {
    if (time <= 0) {
        return ""
    }
    val secondTime = time.plus(500).div(1000).toInt() // 四舍五入
    val minute = secondTime.div(60)
    val second = secondTime.rem(60)
    return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
}