package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.PrivilegedGiftRemindBean
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage

@Composable
fun DialogScope.FreeGiftRemindContent(
    bean: PrivilegedGiftRemindBean,
    onDismiss: () -> Unit,
) {
    DisposableEffect(Unit) {
        onDispose {
            onDismiss()
        }
    }

    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(8.dp),
                ).padding(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            bean.desc,
            lineHeight = 22.sp,
            fontSize = 14.sp,
            color = Color(0xFF999999),
            modifier = Modifier.padding(horizontal = 16.dp),
            textAlign = TextAlign.Center,
        )
        Box(
            Modifier
                .padding(top = 16.dp)
                .background(
                    color = Color(0x3B66FE6B),
                    shape = RoundedCornerShape(size = 16.dp),
                ).border(
                    1.dp,
                    brush =
                        Brush.horizontalGradient(
                            listOf(
                                Color(0xffA3FF2C),
                                Color(0xff31FFA1),
                            ),
                        ),
                    shape = RoundedCornerShape(12.dp),
                ),
        ) {
            NetworkImage(
                bean.gift.icon,
                modifier =
                    Modifier
                        .padding(top = 16.dp)
                        .size(100.dp),
                contentScale = ContentScale.FillWidth,
            )
            Text(
                "x1",
                modifier =
                    Modifier
                        .align(Alignment.TopEnd)
                        .background(
                            brush =
                                Brush.horizontalGradient(
                                    listOf(
                                        Color(0xFFA3FF2C),
                                        Color(0xFF31FFA1),
                                    ),
                                ),
                            RoundedCornerShape(topEnd = 16.dp, bottomStart = 16.dp),
                        ).padding(horizontal = 12.dp, vertical = 4.dp),
                fontSize = 12.sp,
                color = Color(0xff111111),
            )
        }
        Text(
            bean.gift.name,
            fontSize = 12.sp,
            color = Color(0xff111111),
            textAlign = TextAlign.Center,
            lineHeight = 12.sp,
            modifier = Modifier.padding(top = 8.dp, bottom = 20.dp),
        )
        SolidButton(
            "免费赠送".localized,
            onClick = { dismiss() },
            modifier = Modifier.height(40.dp),
            textColor = WakooGreen,
            backgroundColor = Color(0xFF111111),
            paddingValues = PaddingValues(horizontal = 64.dp),
        )
    }
}
