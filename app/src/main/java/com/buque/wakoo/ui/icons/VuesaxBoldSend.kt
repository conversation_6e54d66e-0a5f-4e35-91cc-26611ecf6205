package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.VuesaxBoldSend: ImageVector
    get() {
        if (_VuesaxBoldSend != null) {
            return _VuesaxBoldSend!!
        }
        _VuesaxBoldSend = ImageVector.Builder(
            name = "VuesaxBoldSend2",
            defaultWidth = 16.dp,
            defaultHeight = 16.dp,
            viewportWidth = 16f,
            viewportHeight = 16f
        ).apply {
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(10.76f, 1.971f)
                lineTo(4.74f, 3.971f)
                curveTo(0.693f, 5.325f, 0.693f, 7.531f, 4.74f, 8.878f)
                lineTo(6.527f, 9.471f)
                lineTo(7.12f, 11.258f)
                curveTo(8.467f, 15.305f, 10.68f, 15.305f, 12.027f, 11.258f)
                lineTo(14.033f, 5.245f)
                curveTo(14.927f, 2.545f, 13.46f, 1.071f, 10.76f, 1.971f)
                close()
                moveTo(10.973f, 5.558f)
                lineTo(8.44f, 8.105f)
                curveTo(8.34f, 8.205f, 8.213f, 8.251f, 8.087f, 8.251f)
                curveTo(7.96f, 8.251f, 7.833f, 8.205f, 7.733f, 8.105f)
                curveTo(7.54f, 7.911f, 7.54f, 7.591f, 7.733f, 7.398f)
                lineTo(10.267f, 4.851f)
                curveTo(10.46f, 4.658f, 10.78f, 4.658f, 10.973f, 4.851f)
                curveTo(11.167f, 5.045f, 11.167f, 5.365f, 10.973f, 5.558f)
                close()
            }
        }.build()

        return _VuesaxBoldSend!!
    }

@Suppress("ObjectPropertyName")
private var _VuesaxBoldSend: ImageVector? = null
