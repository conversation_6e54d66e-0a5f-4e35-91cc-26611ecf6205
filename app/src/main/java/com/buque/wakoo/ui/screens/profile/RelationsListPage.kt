package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.UserRelations
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.RelationsKey
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.UserRelationsViewModel

@Composable
fun RelationsListPage(
    userId: String,
    tabKey: RelationsKey.TabContent,
    modifier: Modifier = Modifier,
) {
    val viewModel = viewModel<UserRelationsViewModel>()
    val listState = rememberLazyListState()

    CStateListPaginateLayout<String, Int, RelationsKey, UserRelations, UserRelationsViewModel>(
        reqKey = userId,
        tabKey = tabKey,
        modifier = modifier,
        listState = listState,
        viewModel = viewModel,
        emptyText = if (tabKey is RelationsKey.Followers) "暂无粉丝".localized else "暂无关注".localized,
    ) { paginateState, list ->
        LazyColumn(modifier = Modifier.fillMaxSize(), state = listState) {
            items(list) { item ->
                UserListItem(
                    user = item.user,
                    modifier = Modifier.padding(16.dp),
                ) {
                    if (item.hasRelations) {
                        OutlinedButton(
                            text = "取消关注".localized,
                            onClick = {
                                viewModel.toggleFollowState(item.id, tabKey)
                            },
                            height = 32.dp,
                            borderColor = Color(0xFF999999),
                            textColor = Color(0xFF999999),
                            fontSize = 14.sp,
                            config = ButtonStyles.Solid.copy(minWidth = 72.dp),
                            paddingValues = PaddingValues(horizontal = 12.dp),
                        )
                    } else {
                        SolidButton(
                            text = if (tabKey is RelationsKey.Followers) "回关".localized else "关注".localized,
                            onClick = {
                                viewModel.toggleFollowState(item.id, tabKey)
                            },
                            height = 32.dp,
                            textColor = Color(0xFF111111),
                            backgroundColor = Color(0xFF66FE6B),
                            fontSize = 14.sp,
                            config = ButtonStyles.Solid.copy(minWidth = 72.dp),
                            paddingValues = PaddingValues(horizontal = 12.dp),
                        )
                    }
                }
            }
        }
    }
}
