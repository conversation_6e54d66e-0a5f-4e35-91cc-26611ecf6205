package com.buque.wakoo.ui.screens.liveroom.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.consts.SceneType
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.ui.screens.liveroom.InputTextState
import com.buque.wakoo.ui.screens.liveroom.LiveMicLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomBottomLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInputLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomMessageLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomOverlayLayout
import com.buque.wakoo.ui.screens.liveroom.LiveRoomTopLayout
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.gift.GiftViewModel
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.viewmodel.HongBaoViewModel
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel

@Composable
fun LiveRoomScreen(
    viewModel: LiveRoomViewModel,
    giftViewModel: GiftViewModel,
) {
    val roomInfoState = viewModel.roomInfoState

    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        val data =
            if (!roomInfoState.basicInfo.background.isNullOrBlank()) {
                roomInfoState.basicInfo.background
            } else {
                R.drawable.bg_live_room_radio
            }
        NetworkImage(
            data = data,
            modifier = Modifier.fillMaxSize(),
            placeholder = painterResource(R.drawable.bg_live_room_radio),
            error = painterResource(R.drawable.bg_live_room_radio),
        )

        if (roomInfoState.basicInfo.owner == null || roomInfoState.basicInfo.roomMode.isUnKnown) {
            return
        }

        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .systemBarsPadding(),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            // 语音房顶部操作栏
            LiveRoomTopLayout(roomInfoState)

            Box(modifier = Modifier.fillMaxWidth()) {
                // 语音房麦位
                LiveMicLayout(roomInfoState)
            }

            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
            ) {
                LiveRoomMessageLayout(viewModel)
            }

            LiveRoomBottomLayout(roomInfoState = roomInfoState) {
                viewModel.setInputTextState(InputTextState.Visible())
            }

            SizeHeight(10.dp)
        }

        LiveRoomInputLayout(
            inputState = viewModel.inputTextState.value,
            onInputStateChange = {
                viewModel.setInputTextState(it)
            },
            modifier =
                Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .background(Color.White),
        ) {
            viewModel.sendMessage(MessageBundle.Text.create(it))
        }

        LiveRoomOverlayLayout(
            roomInfoState = roomInfoState,
            viewModel = viewModel,
            giftViewModel = giftViewModel,
            hongBaoViewModel = viewModel(initializer = {
                HongBaoViewModel(SceneType.WAKOO_ROOM,roomInfoState.id)
            })
        )
    }
}

@Preview
@Composable
private fun LiveRoomScreenPreview() {
    WakooTheme {
        LiveRoomScreen(
            viewModel = viewModel<LiveRoomViewModel>(factory = LiveRoomViewModel.Factory(BasicRoomInfo.preview)),
            giftViewModel =
                viewModel<GiftViewModel>(initializer = {
                    GiftViewModel(
                        bid = "10086",
                        rcId = "10086",
                        type = ConversationType.CHATROOM,
                        autoFetchGiftData = false,
                    )
                }),
        )
    }
}
