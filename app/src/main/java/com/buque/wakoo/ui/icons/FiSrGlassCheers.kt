package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.FiSrGlassCheers: ImageVector
    get() {
        if (_FiSrGlassCheers != null) {
            return _FiSrGlassCheers!!
        }
        _FiSrGlassCheers = ImageVector.Builder(
            name = "FiSrGlassCheers",
            defaultWidth = 18.dp,
            defaultHeight = 18.dp,
            viewportWidth = 18f,
            viewportHeight = 18f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(18f)
                    verticalLineToRelative(18f)
                    horizontalLineToRelative(-18f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFF111111))) {
                    moveTo(17.475f, 16.929f)
                    lineTo(14.131f, 17.967f)
                    curveTo(13.941f, 18.026f, 13.736f, 18.007f, 13.56f, 17.914f)
                    curveTo(13.384f, 17.822f, 13.252f, 17.663f, 13.192f, 17.473f)
                    curveTo(13.133f, 17.283f, 13.152f, 17.077f, 13.245f, 16.901f)
                    curveTo(13.337f, 16.725f, 13.496f, 16.593f, 13.686f, 16.534f)
                    lineTo(14.619f, 16.245f)
                    lineTo(13.93f, 14.22f)
                    curveTo(13.786f, 14.238f, 13.64f, 14.248f, 13.495f, 14.25f)
                    curveTo(12.677f, 14.248f, 11.881f, 13.979f, 11.23f, 13.483f)
                    curveTo(10.579f, 12.987f, 10.108f, 12.292f, 9.889f, 11.504f)
                    lineTo(9.794f, 11.223f)
                    lineTo(10.544f, 8.999f)
                    horizontalLineTo(16.95f)
                    lineTo(17.1f, 9.454f)
                    curveTo(17.338f, 10.262f, 17.294f, 11.126f, 16.976f, 11.906f)
                    curveTo(16.658f, 12.685f, 16.085f, 13.334f, 15.35f, 13.745f)
                    lineTo(16.048f, 15.798f)
                    lineTo(17.023f, 15.498f)
                    curveTo(17.213f, 15.439f, 17.419f, 15.457f, 17.595f, 15.55f)
                    curveTo(17.771f, 15.642f, 17.903f, 15.801f, 17.962f, 15.991f)
                    curveTo(18.021f, 16.181f, 18.003f, 16.387f, 17.91f, 16.563f)
                    curveTo(17.818f, 16.739f, 17.659f, 16.871f, 17.469f, 16.93f)
                    lineTo(17.475f, 16.929f)
                    close()
                    moveTo(10.992f, 7.5f)
                    horizontalLineTo(16.442f)
                    lineTo(15.718f, 5.349f)
                    curveTo(15.544f, 4.789f, 15.159f, 4.317f, 14.645f, 4.035f)
                    curveTo(14.13f, 3.752f, 13.526f, 3.679f, 12.96f, 3.833f)
                    lineTo(10.317f, 4.569f)
                    curveTo(10.424f, 4.705f, 10.522f, 4.848f, 10.61f, 4.998f)
                    curveTo(11.028f, 5.761f, 11.163f, 6.647f, 10.992f, 7.5f)
                    close()
                    moveTo(7.929f, 4.638f)
                    lineTo(5.039f, 3.833f)
                    curveTo(4.477f, 3.678f, 3.876f, 3.746f, 3.363f, 4.025f)
                    curveTo(2.85f, 4.303f, 2.465f, 4.769f, 2.289f, 5.325f)
                    lineTo(1.558f, 7.5f)
                    horizontalLineTo(9.464f)
                    lineTo(9.479f, 7.456f)
                    curveTo(9.566f, 7.169f, 9.594f, 6.868f, 9.563f, 6.57f)
                    curveTo(9.531f, 6.272f, 9.441f, 5.984f, 9.297f, 5.721f)
                    curveTo(9.152f, 5.459f, 8.957f, 5.228f, 8.722f, 5.042f)
                    curveTo(8.487f, 4.856f, 8.217f, 4.719f, 7.929f, 4.638f)
                    close()
                    moveTo(0.889f, 9.493f)
                    curveTo(0.661f, 10.299f, 0.712f, 11.159f, 1.034f, 11.932f)
                    curveTo(1.355f, 12.706f, 1.929f, 13.348f, 2.662f, 13.755f)
                    lineTo(1.954f, 15.801f)
                    lineTo(0.975f, 15.496f)
                    curveTo(0.881f, 15.467f, 0.782f, 15.456f, 0.684f, 15.465f)
                    curveTo(0.585f, 15.473f, 0.49f, 15.501f, 0.403f, 15.547f)
                    curveTo(0.315f, 15.592f, 0.238f, 15.655f, 0.174f, 15.73f)
                    curveTo(0.111f, 15.806f, 0.063f, 15.893f, 0.034f, 15.987f)
                    curveTo(0.004f, 16.081f, -0.007f, 16.18f, 0.002f, 16.278f)
                    curveTo(0.011f, 16.377f, 0.039f, 16.472f, 0.084f, 16.56f)
                    curveTo(0.13f, 16.647f, 0.192f, 16.725f, 0.268f, 16.788f)
                    curveTo(0.344f, 16.851f, 0.431f, 16.899f, 0.525f, 16.929f)
                    lineTo(3.868f, 17.967f)
                    curveTo(4.058f, 18.026f, 4.264f, 18.007f, 4.44f, 17.914f)
                    curveTo(4.616f, 17.822f, 4.748f, 17.663f, 4.807f, 17.473f)
                    curveTo(4.866f, 17.283f, 4.848f, 17.077f, 4.755f, 16.901f)
                    curveTo(4.663f, 16.725f, 4.504f, 16.593f, 4.314f, 16.534f)
                    lineTo(3.387f, 16.245f)
                    lineTo(4.088f, 14.22f)
                    curveTo(4.227f, 14.238f, 4.367f, 14.248f, 4.507f, 14.25f)
                    curveTo(5.318f, 14.248f, 6.107f, 13.983f, 6.754f, 13.496f)
                    curveTo(7.402f, 13.008f, 7.874f, 12.323f, 8.1f, 11.544f)
                    lineTo(8.959f, 9f)
                    horizontalLineTo(1.054f)
                    lineTo(0.889f, 9.493f)
                    close()
                    moveTo(11.665f, 2.925f)
                    curveTo(11.753f, 2.97f, 11.85f, 2.996f, 11.949f, 3.003f)
                    curveTo(12.047f, 3.01f, 12.147f, 2.998f, 12.241f, 2.966f)
                    curveTo(12.335f, 2.935f, 12.421f, 2.885f, 12.496f, 2.819f)
                    curveTo(12.57f, 2.754f, 12.631f, 2.675f, 12.675f, 2.586f)
                    lineTo(13.425f, 1.086f)
                    curveTo(13.514f, 0.908f, 13.528f, 0.701f, 13.465f, 0.513f)
                    curveTo(13.402f, 0.324f, 13.267f, 0.168f, 13.089f, 0.079f)
                    curveTo(12.911f, -0.01f, 12.705f, -0.024f, 12.516f, 0.039f)
                    curveTo(12.327f, 0.102f, 12.171f, 0.237f, 12.082f, 0.415f)
                    lineTo(11.332f, 1.915f)
                    curveTo(11.288f, 2.003f, 11.261f, 2.099f, 11.253f, 2.198f)
                    curveTo(11.246f, 2.296f, 11.258f, 2.395f, 11.288f, 2.489f)
                    curveTo(11.319f, 2.583f, 11.368f, 2.67f, 11.433f, 2.745f)
                    curveTo(11.498f, 2.82f, 11.576f, 2.881f, 11.665f, 2.925f)
                    close()
                    moveTo(5.329f, 2.586f)
                    curveTo(5.418f, 2.764f, 5.574f, 2.9f, 5.763f, 2.963f)
                    curveTo(5.857f, 2.995f, 5.955f, 3.007f, 6.054f, 3f)
                    curveTo(6.152f, 2.994f, 6.249f, 2.967f, 6.337f, 2.923f)
                    curveTo(6.425f, 2.879f, 6.504f, 2.818f, 6.569f, 2.744f)
                    curveTo(6.634f, 2.669f, 6.683f, 2.583f, 6.715f, 2.489f)
                    curveTo(6.746f, 2.396f, 6.759f, 2.297f, 6.752f, 2.198f)
                    curveTo(6.745f, 2.1f, 6.719f, 2.003f, 6.675f, 1.915f)
                    lineTo(5.925f, 0.415f)
                    curveTo(5.881f, 0.327f, 5.82f, 0.248f, 5.746f, 0.184f)
                    curveTo(5.671f, 0.119f, 5.585f, 0.07f, 5.491f, 0.039f)
                    curveTo(5.302f, -0.024f, 5.096f, -0.01f, 4.918f, 0.079f)
                    curveTo(4.74f, 0.168f, 4.605f, 0.324f, 4.542f, 0.513f)
                    curveTo(4.479f, 0.701f, 4.493f, 0.908f, 4.582f, 1.086f)
                    lineTo(5.329f, 2.586f)
                    close()
                    moveTo(9f, 0f)
                    curveTo(8.801f, 0f, 8.61f, 0.079f, 8.47f, 0.22f)
                    curveTo(8.329f, 0.361f, 8.25f, 0.551f, 8.25f, 0.75f)
                    verticalLineTo(2.25f)
                    curveTo(8.25f, 2.449f, 8.329f, 2.64f, 8.47f, 2.781f)
                    curveTo(8.61f, 2.921f, 8.801f, 3f, 9f, 3f)
                    curveTo(9.199f, 3f, 9.39f, 2.921f, 9.53f, 2.781f)
                    curveTo(9.671f, 2.64f, 9.75f, 2.449f, 9.75f, 2.25f)
                    verticalLineTo(0.75f)
                    curveTo(9.75f, 0.551f, 9.671f, 0.361f, 9.53f, 0.22f)
                    curveTo(9.39f, 0.079f, 9.199f, 0f, 9f, 0f)
                    close()
                }
            }
        }.build()

        return _FiSrGlassCheers!!
    }

@Suppress("ObjectPropertyName")
private var _FiSrGlassCheers: ImageVector? = null
