package com.buque.wakoo.ui.widget

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.annotation.FloatRange
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonColors
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBarColors
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnContent
import com.buque.wakoo.ext.conditional
import com.buque.wakoo.ext.hideKeyboardOnClickOutside
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ui.icons.ArrowLeft
import com.buque.wakoo.ui.icons.ArrowRight
import com.buque.wakoo.ui.icons.Ok
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooWhite

private class BadBackException : java.lang.Exception()

@Composable
fun DashedDivider(
    modifier: Modifier = Modifier,
    thickness: Dp = 1.dp,
    color: Color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f),
    dashWidth: Dp = 4.dp,
    dashGap: Dp = 4.dp,
) {
    Canvas(
        modifier =
            modifier
                .fillMaxWidth()
                .height(thickness),
    ) {
        drawLine(
            color = color,
            start = Offset(0f, 0f),
            end = Offset(size.width, 0f),
            strokeWidth = thickness.toPx(),
            pathEffect =
                PathEffect.dashPathEffect(
                    intervals = floatArrayOf(dashWidth.toPx(), dashGap.toPx()),
                    phase = 0f,
                ),
        )
    }
}

@Composable
fun ColumnScope.Weight(
    @FloatRange(
        from = 0.0,
        fromInclusive = false,
    ) weight: Float = 1f,
    fill: Boolean = true,
) {
    Spacer(
        modifier =
            Modifier.weight(
                weight = weight,
                fill = fill,
            ),
    )
}

@Composable
fun RowScope.Weight(
    @FloatRange(
        from = 0.0,
        fromInclusive = false,
    ) weight: Float = 1f,
    fill: Boolean = true,
) {
    Spacer(
        modifier =
            Modifier.weight(
                weight = weight,
                fill = fill,
            ),
    )
}

@Composable
fun SizeHeight(size: Dp) {
    Spacer(
        modifier = Modifier.height(height = size),
    )
}

@Composable
fun FractionHeight(
    @FloatRange(
        from = 0.0,
        to = 1.0,
    ) fraction: Float = 1f,
) {
    Spacer(
        modifier = Modifier.fillMaxHeight(fraction = fraction),
    )
}

@Composable
fun SizeWidth(size: Dp) {
    Spacer(
        modifier = Modifier.width(width = size),
    )
}

@Composable
fun SizeSpacer(size: Dp) {
    Spacer(
        modifier = Modifier.size(size = size),
    )
}

@Composable
fun SizeSpacer(
    width: Dp,
    height: Dp,
) {
    Spacer(
        modifier =
            Modifier.size(
                width = width,
                height = height,
            ),
    )
}

@Composable
fun FractionWidth(
    @FloatRange(
        from = 0.0,
        to = 1.0,
    ) fraction: Float = 1f,
) {
    Spacer(
        modifier = Modifier.fillMaxWidth(fraction = fraction),
    )
}

@Composable
fun WakooTitleBar(
    title: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    backIcon: ImageVector = WakooIcons.ArrowLeft,
    onBack: () -> Unit = {
        throw BadBackException()
    },
    titleContentColor: Color = Color(0xFF111111),
    containerColor: Color = Color.Transparent,
    navigationIcon: OnContent =
        WakooTitleBarDefaults.backIconNavigation(
            backIcon = backIcon,
            colors =
                IconButtonDefaults.iconButtonColors(
                    contentColor = titleContentColor,
                    containerColor = containerColor,
                ),
            onBack = onBack,
        ),
    colors: TopAppBarColors = TopAppBarDefaults.centerAlignedTopAppBarColors(),
    scrollBehavior: TopAppBarScrollBehavior? = null,
    actions: @Composable RowScope.() -> Unit = {},
) {
    CenterAlignedTopAppBar(
        title = title,
        modifier = modifier.hideKeyboardOnClickOutside(),
        navigationIcon = navigationIcon,
        colors =
            colors.copy(
                containerColor = containerColor,
                titleContentColor = titleContentColor,
            ),
        scrollBehavior = scrollBehavior,
        actions = actions,
    )
}

@Composable
fun WakooTitleBar(
    title: String?,
    modifier: Modifier = Modifier,
    backIcon: ImageVector = WakooIcons.ArrowLeft,
    onBack: OnAction = {
        throw BadBackException()
    },
    titleContentColor: Color = Color(0xFF111111),
    containerColor: Color = Color.Transparent,
    navigationIcon: OnContent =
        WakooTitleBarDefaults.backIconNavigation(
            backIcon = backIcon,
            colors =
                IconButtonDefaults.iconButtonColors(
                    contentColor = titleContentColor,
                    containerColor = containerColor,
                ),
            onBack = onBack,
        ),
    colors: TopAppBarColors = TopAppBarDefaults.centerAlignedTopAppBarColors(),
    scrollBehavior: TopAppBarScrollBehavior? = null,
    windowInsets: WindowInsets = TopAppBarDefaults.windowInsets,
    marquee: Boolean = true,
    actions: @Composable RowScope.() -> Unit = {},
) {
    CenterAlignedTopAppBar(
        title = {
            if (!title.isNullOrEmpty()) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    modifier =
                        Modifier
                            .padding(horizontal = 16.dp)
                            .conditional(marquee) {
                                basicMarquee()
                            },
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                )
            }
        },
        modifier = modifier.hideKeyboardOnClickOutside(),
        navigationIcon = navigationIcon,
        colors =
            colors.copy(
                containerColor = containerColor,
                titleContentColor = titleContentColor,
            ),
        windowInsets = windowInsets,
        scrollBehavior = scrollBehavior,
        actions = actions,
    )
}

object WakooTitleBarDefaults {
    @Composable
    fun backIconNavigation(
        backIcon: ImageVector = WakooIcons.ArrowLeft,
        colors: IconButtonColors =
            IconButtonDefaults.iconButtonColors(
                contentColor = Color(0xFF111111),
            ),
        onBack: () -> Unit = {
            throw BadBackException()
        },
    ): OnContent =
        {
            val onBackPressedDispatcher =
                LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
            val focusManager = LocalFocusManager.current
            IconButton(
                onClick = {
                    try {
                        onBack()
                    } catch (_: BadBackException) {
                        focusManager.clearFocus()
                        onBackPressedDispatcher?.onBackPressed()
                    }
                },
                colors = colors,
            ) {
                Icon(
                    imageVector = backIcon,
                    contentDescription = "返回",
                )
            }
        }

    @Composable
    fun TextButtonAction(
        text: String,
        onClick: () -> Unit,
        modifier: Modifier = Modifier,
        enabled: Boolean = true,
        colors: ButtonColors =
            ButtonDefaults.textButtonColors(
                contentColor = Color(0xFF111111),
                disabledContentColor = Color(0xFF86909C),
            ),
    ) {
        TextButton(
            onClick = onClick,
            modifier = modifier,
            enabled = enabled,
            colors = colors,
        ) {
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium,
            )
        }
    }

    @Composable
    fun IconButtonAction(
        imageVector: ImageVector,
        onClick: () -> Unit,
        modifier: Modifier = Modifier,
        iconModifier: Modifier = Modifier,
        enabled: Boolean = true,
        colors: IconButtonColors =
            IconButtonDefaults.iconButtonColors(
                contentColor = Color(0xFF111111),
            ),
    ) {
        IconButton(
            onClick = onClick,
            modifier = modifier,
            enabled = enabled,
            colors = colors,
        ) {
            Icon(
                imageVector = imageVector,
                contentDescription = "action",
                modifier = iconModifier,
            )
        }
    }

    @Composable
    fun ImageButtonAction(
        imageVector: ImageVector,
        onClick: () -> Unit,
        modifier: Modifier = Modifier,
        enabled: Boolean = true,
    ) {
        IconButton(
            onClick = onClick,
            modifier = modifier,
            enabled = enabled,
        ) {
            Image(
                imageVector = imageVector,
                contentDescription = "action",
            )
        }
    }
}

@Composable
fun SettingsMenuItem(
    titleContent: @Composable RowScope.() -> Unit,
    previewContent: @Composable RowScope.() -> Unit,
    onClick: () -> Unit,
    withArrow: Boolean = true,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .clickable(onClick = onClick)
                .padding(
                    horizontal = 16.dp,
                    vertical = 20.dp,
                ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        titleContent()

        Weight()

        previewContent()
        if (withArrow) {
            Icon(
                imageVector = WakooIcons.ArrowRight,
                contentDescription = null,
                modifier = Modifier.size(12.dp),
                tint = Color(0xFF999999),
            )
        }
    }
}

@Composable
fun SingleTextSettingsMenuItem(
    titleText: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    withArrow: Boolean = true,
    previewContent: @Composable RowScope.() -> Unit = {},
) {
    SettingsMenuItem(
        titleContent = {
            Text(
                text = titleText,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF111111),
            )
        },
        previewContent = previewContent,
        onClick = onClick,
        modifier = modifier,
        withArrow = withArrow,
    )
}

@Composable
fun TowTextsSettingsMenuItem(
    titleText: String,
    previewText: String,
    onClick: () -> Unit,
    withArrow: Boolean = true,
    modifier: Modifier = Modifier,
) {
    SingleTextSettingsMenuItem(
        titleText = titleText,
        previewContent = {
            Text(
                text = previewText,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF999999),
            )
        },
        onClick = onClick,
        modifier = modifier,
        withArrow = withArrow,
    )
}

@Composable
fun StandardListItemScaffold(
    startContent: @Composable RowScope.() -> Unit,
    centerContent: @Composable BoxScope.() -> Unit,
    endContent: @Composable RowScope.() -> Unit,
    modifier: Modifier = Modifier,
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
) {
    Row(
        modifier = modifier,
        verticalAlignment = verticalAlignment,
        horizontalArrangement = horizontalArrangement,
    ) {
        startContent()
        Box(
            modifier = Modifier.weight(1f),
        ) {
            centerContent()
        }
        endContent()
    }
}

@Composable
fun SpaceListItemScaffold(
    startContent: @Composable RowScope.() -> Unit,
    centerContent: @Composable BoxScope.() -> Unit,
    endContent: @Composable RowScope.() -> Unit,
    modifier: Modifier = Modifier,
    space: Dp = 10.dp,
) {
    StandardListItemScaffold(
        startContent = startContent,
        centerContent = centerContent,
        endContent = endContent,
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(space),
    )
}

@Composable
fun StandardScreenScaffold(
    topBar: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    containerColor: Color = MaterialTheme.colorScheme.background,
    contentColor: Color = contentColorFor(containerColor),
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit,
) {
    Scaffold(
        topBar = topBar,
        modifier = modifier,
        containerColor = containerColor,
        contentColor = contentColor,
        contentWindowInsets = contentWindowInsets,
        content = content,
    )
}

/**
 * 一般页面的通用槽位，标题颜色是透明的，视觉上标题和内容融为一体
 */
@Composable
fun TitleScreenScaffold(
    title: String,
    modifier: Modifier = Modifier,
    titleContentColor: Color = Color(0xFF111111),
    titleContainerColor: Color = Color.Transparent,
    actions: @Composable RowScope.() -> Unit = {},
    topBar: @Composable () -> Unit = {
        WakooTitleBar(
            title = title,
            titleContentColor = titleContentColor,
            containerColor = titleContainerColor,
            actions = actions,
        )
    },
    containerColor: Color = MaterialTheme.colorScheme.background,
    contentColor: Color = contentColorFor(containerColor),
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit,
) {
    StandardScreenScaffold(
        topBar = topBar,
        modifier = modifier,
        containerColor = containerColor,
        contentColor = contentColor,
        contentWindowInsets = contentWindowInsets,
        content = content,
    )
}

/**
 * 分段页面的通用槽位，标题颜色和内容颜色不一样，看上去是分段的
 */
@Composable
fun SegColorTitleScreenScaffold(
    title: String,
    modifier: Modifier = Modifier,
    titleContentColor: Color = Color(0xFF111111),
    titleContainerColor: Color = WakooWhite,
    actions: @Composable RowScope.() -> Unit = {},
    topBar: @Composable () -> Unit = {
        WakooTitleBar(
            title = title,
            titleContentColor = titleContentColor,
            containerColor = titleContainerColor,
            actions = actions,
        )
    },
    containerColor: Color = Color(0xFFF7F7F7),
    contentColor: Color = contentColorFor(containerColor),
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit,
) {
    TitleScreenScaffold(
        title = title,
        modifier = modifier,
        titleContentColor = titleContentColor,
        titleContainerColor = titleContainerColor,
        topBar = topBar,
        containerColor = containerColor,
        contentColor = contentColor,
        contentWindowInsets = contentWindowInsets,
        content = content,
    )
}

@Composable
fun ImeButtonScaffold(
    buttonContent: @Composable BoxScope.() -> Unit,
    modifier: Modifier = Modifier,
    buttonModifier: Modifier = Modifier,
    buttonContentMinimumPadding: Dp = 0.dp,
    weightFill: Boolean = true,
    useScrollableLayout: Boolean = true,
    content: @Composable BoxScope.() -> Unit,
) {
    Column(
        modifier = modifier.hideKeyboardOnClickOutside(),
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .weight(1f, weightFill)
                    .run {
                        if (useScrollableLayout) {
                            verticalScroll(rememberScrollState())
                        } else {
                            this
                        }
                    },
        ) {
            content()
        }

        // 提交按钮
        Box(
            modifier =
                buttonModifier.windowInsetsPadding(
                    WindowInsets.ime
                        .exclude(WindowInsets.navigationBars)
                        .union(WindowInsets(0.dp, 0.dp, 0.dp, buttonContentMinimumPadding))
                        .only(WindowInsetsSides.Bottom),
                ),
            contentAlignment = Alignment.Center,
        ) {
            buttonContent()
        }
    }
}

@Composable
fun Center(
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit,
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        content()
    }
}

@Composable
fun ShapeCheckBox(
    checked: Boolean,
    onCheckChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    shape: Shape = CircleShape,
    enabled: Boolean = true,
    checkedBgColor: Color = Color(0xFFFFEB93),
    unCheckedColor: Color = Color(0xFF877076),
) {
    Box(
        modifier =
            modifier
                .defaultMinSize(21.dp, 21.dp)
                .clip(shape)
                .then(
                    if (enabled) {
                        Modifier.noEffectClick(onClick = {
                            onCheckChanged(!checked)
                        })
                    } else {
                        Modifier
                    },
                ).then(
                    if (checked) {
                        Modifier.background(checkedBgColor)
                    } else {
                        Modifier.border(1.dp, unCheckedColor, shape)
                    },
                ),
        contentAlignment = Alignment.Center,
    ) {
        if (checked) {
            Icon(WakooIcons.Ok, tint = Color.Black, contentDescription = "ic", modifier = Modifier.fillMaxSize(0.8f))
        }
    }
}
