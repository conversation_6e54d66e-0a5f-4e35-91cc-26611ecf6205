package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupMemberListViewModel

@Composable
fun AddChatGroupAdminScreen(
    groupId: String,
    maxCountToSet: Int,
) {
    val commonRoleViewModel =
        viewModel<ChatGroupMemberListViewModel>(
            factory =
                viewModelFactory {
                    initializer { ChatGroupMemberListViewModel(groupId, ChatGroupMember.ROLE_COMMON.toString()) }
                },
        )
    val scope = rememberCoroutineScope()
    val lm = LocalLoadingManager.current
    var addTimes by remember { mutableIntStateOf(0) }

    TitleScreenScaffold("设置管理员".localized) { pd ->
        Box(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(pd),
        ) {
            StateListPaginateLayout<Int, ChatGroupMember, ChatGroupMemberListViewModel>(
                viewModel = commonRoleViewModel,
                emptyText = "无可添加成员".localized,
            ) { _, list ->
                LazyColumn {
                    items(list) { item ->
                        ChatGroupAdminAddItem(item.user) {
                            if (addTimes >= maxCountToSet) {
                                showToast("最多设置3名管理员".localized)
                                return@ChatGroupAdminAddItem
                            }
                            lm.show(scope) {
                                commonRoleViewModel
                                    .setAdmin(item, true)
                                    .onSuccess {
                                        addTimes += 1
                                        showToast("添加成功".localized)
                                    }.toastWhenError()
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ChatGroupAdminAddItem(
    user: User,
    onAdd: OnAction = {},
) {
    UserListItem(
        user,
        modifier = Modifier.padding(16.dp, 12.dp),
        centerContent = {
            Column {
                Text(
                    text = user.name,
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF111111),
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        },
        endContent = {
            SolidButton(
                "添加管理员".localized,
                height = 32.dp,
                minWidth = 64.dp,
                fontSize = 14.sp,
                onClick = onAdd,
            )
        },
    )
}

@Preview
@Composable
fun ChatGroupAdminAddItemPreview(modifier: Modifier = Modifier) {
    WakooTheme {
        ChatGroupAdminAddItem(BasicUser.sampleGirl)
    }
}
