package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Wing: ImageVector
    get() {
        if (_wing != null) {
            return _wing!!
        }
        _wing = ImageVector.Builder(
            name = "Frame 2087326014",
            defaultWidth = 12.dp,
            defaultHeight = 8.dp,
            viewportWidth = 12f,
            viewportHeight = 8f
        ).apply {
            path(fill = SolidColor(Color(0xFFFF84C6))) {
                moveTo(3.23f, 7.684f)
                curveTo(3.23f, 7.684f, 4.994f, 7.733f, 6.298f, 5.641f)
                curveTo(7.347f, 3.959f, 9.627f, 1.177f, 10.481f, 0.152f)
                curveTo(10.665f, -0.07f, 11.033f, -0.044f, 11.182f, 0.199f)
                curveTo(11.538f, 0.779f, 11.97f, 2.083f, 11.464f, 4.32f)
                curveTo(11.431f, 4.466f, 11.556f, 4.601f, 11.717f, 4.591f)
                curveTo(11.925f, 4.577f, 12.067f, 4.783f, 11.967f, 4.953f)
                curveTo(11.322f, 6.043f, 9.013f, 8.957f, 3.23f, 7.684f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFF84C6))) {
                moveTo(0f, 5.81f)
                curveTo(0f, 5.81f, 1.061f, 5.839f, 1.845f, 4.581f)
                curveTo(2.476f, 3.569f, 3.848f, 1.896f, 4.361f, 1.279f)
                curveTo(4.472f, 1.145f, 4.694f, 1.161f, 4.784f, 1.308f)
                curveTo(4.998f, 1.656f, 5.257f, 2.441f, 4.953f, 3.786f)
                curveTo(4.933f, 3.874f, 5.008f, 3.955f, 5.105f, 3.949f)
                curveTo(5.231f, 3.941f, 5.316f, 4.065f, 5.256f, 4.167f)
                curveTo(4.868f, 4.823f, 3.478f, 6.576f, 0f, 5.81f)
                close()
            }
        }.build()

        return _wing!!
    }

@Suppress("ObjectPropertyName")
private var _wing: ImageVector? = null
