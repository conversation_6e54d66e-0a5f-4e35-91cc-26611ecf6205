package com.buque.wakoo.ui.screens.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.saveable.rememberSerializable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import com.buque.wakoo.R
import com.buque.wakoo.ext.isAnimated
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.rememberFirstEnter
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.network.api.bean.RegisterRequest
import com.buque.wakoo.ui.icons.UserFemale
import com.buque.wakoo.ui.icons.UserMale
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooBlack
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooLightGrayBg
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.ui.widget.wheelPicker.AppDateWheelPickerPanel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.todayIn
import kotlinx.serialization.Serializable
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

@Serializable
enum class Gender {
    NOT_SELECTED,
    MALE,
    FEMALE,
    ;

    override fun toString(): String =
        when (this) {
            NOT_SELECTED -> "未选择".localized
            MALE -> "男".localized
            FEMALE -> "女".localized
        }

    fun toIntVale() = ordinal
}

@OptIn(ExperimentalTime::class)
@Composable
fun RegisterFillInfoScreen(
    skipMode: Boolean,
    onComplete: (RegisterRequest?) -> Unit = { },
) {
    var nickname by rememberSaveable { mutableStateOf("") }
    var gender by rememberSerializable { mutableStateOf(Gender.NOT_SELECTED) }

    var birthday by rememberSaveable { mutableStateOf("") }
    var inviteCode by rememberSaveable { mutableStateOf("") }
    var avatar by rememberSaveable { mutableStateOf("") }

    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    val dialogController = rememberDialogController(render = false)

    val firstEnter by rememberFirstEnter()

    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    LaunchedEffect(Unit) {
        if (nickname.isEmpty() || firstEnter) {
            focusRequester.requestFocus() // 请求焦点
            keyboardController?.show() // 请求显示键盘
        }
    }

    val fillEnable by remember {
        derivedStateOf {
            nickname.isNotEmpty() &&
                gender != Gender.NOT_SELECTED &&
                birthday.isNotEmpty() &&
                (skipMode || gender == Gender.MALE || avatar.isNotEmpty())
        }
    }

    fun callComplete() {
        if (fillEnable) {
            onComplete(
                RegisterRequest(
                    nickname = nickname,
                    gender = gender.toIntVale(),
                    birthday = birthday,
                    avatar_url = avatar,
                    invite_code = inviteCode,
                ),
            )
        } else {
            showToast("请输入完整资料".localized)
        }
    }

    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(WakooWhite)
                .paint(
                    painter = painterResource(id = R.drawable.bg_common_top),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter,
                ),
    ) {
        TitleScreenScaffold(
            title = "",
            actions = {
                if (skipMode) {
                    WakooTitleBarDefaults.TextButtonAction("跳过".localized, onClick = {
                        onComplete(null)
                    })
                }
            },
            containerColor = Color.Transparent,
        ) { paddingValues ->

            ImeButtonScaffold(
                buttonContent = {
                    // 底部按钮
                    GradientButton(
                        text = "进入wakoo".localized,
                        height = 56.dp,
                        enabled = fillEnable,
                        onClick = ::callComplete,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .align(Alignment.BottomCenter)
                                .padding(horizontal = 28.dp),
                    )
                },
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                buttonModifier =
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFFFFFFF))
                        .padding(
                            top = 20.dp,
                            bottom = 15.dp,
                        ),
                buttonContentMinimumPadding = 80.dp,
            ) {
                // 主要内容
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 28.dp)
                            .padding(top = 10.dp),
                ) {
                    // 标题
                    Text(
                        text = "请完善您的资料".localized,
                        color = WakooBlack,
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp,
                        style = MaterialTheme.typography.titleLarge,
                        fontFamily = FontFamily.MI_SANS,
                    )

                    SizeHeight(12.dp)

                    Text(
                        text = "为您更精确的推荐内容".localized,
                        color = WakooGrayText,
                        fontSize = 14.sp,
                    )

                    if (skipMode) {
                        SizeHeight(60.dp)
                    } else {
                        SizeHeight(20.dp)

                        val rootNavController = LocalAppNavController.root

                        val launcher =
                            rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
                                if (result.list.isNotEmpty()) {
                                    val uriString = result.list.single().uriString
                                    scope.launch {
                                        val isAnimated =
                                            withContext(Dispatchers.IO) {
                                                isAnimated(context.contentResolver, uriString.toUri())
                                            }
                                        if (isAnimated) {
                                            showToast("不支持使用动态头像，请重新选择".localized)
                                        } else {
                                            avatar = uriString
                                        }
                                    }
                                }
                            }

                        Box(
                            modifier =
                                Modifier
                                    .align(Alignment.CenterHorizontally)
                                    .size(80.dp)
                                    .noEffectClick {
                                        launcher.launch(Route.MediaSelector())
                                    },
                        ) {
                            NetworkImage(
                                data = avatar,
                                modifier =
                                    Modifier
                                        .fillMaxSize()
                                        .clip(CircleShape),
                                error = painterResource(R.drawable.ic_default_avatar),
                            )
                            Image(
                                painter = painterResource(R.drawable.ic_select_pic),
                                contentDescription = null,
                                modifier =
                                    Modifier
                                        .align(Alignment.BottomEnd)
                                        .size(24.dp),
                            )
                        }
                        SizeHeight(20.dp)
                    }

                    // 昵称输入
                    Text(
                        text = "昵称".localized,
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    AppTextField(
                        value = nickname,
                        onValueChange = {
                            nickname = it
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .focusRequester(focusRequester),
                        maxLength = 20,
                        showLengthTip = false,
                        shape = CircleShape,
                        placeholder = "请输入您的网络昵称".localized,
                        backgroundColor = WakooLightGrayBg,
                        textStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFF111111),
                                lineHeight = 21.sp,
                            ),
                        placeholderStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 21.sp,
                                fontWeight = FontWeight.Normal,
                            ),
                        keyboardOptions =
                            KeyboardOptions(
                                imeAction = ImeAction.Next,
                            ),
                        singleLine = true,
                        keyboardActions =
                            KeyboardActions(
                                onNext = {
                                    focusManager.moveFocus(FocusDirection.Down)
                                },
                            ),
                    )

                    SizeHeight(16.dp)

                    // 性别选择
                    Text(
                        text = "性别".localized,
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        // 男性选项
                        GenderOption(
                            icon = {
                                Image(
                                    imageVector = WakooIcons.UserMale,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp),
                                )
                            },
                            text = "男".localized,
                            isSelected = gender == Gender.MALE,
                            onClick = { gender = Gender.MALE },
                        )

                        SizeWidth(16.dp)

                        // 女性选项
                        GenderOption(
                            icon = {
                                Image(
                                    imageVector = WakooIcons.UserFemale,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp),
                                )
                            },
                            text = "女".localized,
                            isSelected = gender == Gender.FEMALE,
                            onClick = { gender = Gender.FEMALE },
                        )
                    }
                    SizeHeight(16.dp)

                    // 昵称输入
                    Text(
                        text = "出生日期".localized,
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    Box(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .clip(CircleShape)
                                .background(WakooLightGrayBg)
                                .clickable {
                                    focusManager.clearFocus()
                                    dialogController.easyPostBottomPanel(
                                        useSystemDialog = false,
                                    ) {
                                        val initSelectedDate =
                                            birthday.takeIf { it.isNotEmpty() }?.let {
                                                LocalDate.parse(it)
                                            } ?: Clock.System
                                                .todayIn(TimeZone.currentSystemDefault())
                                                .minus(18, DateTimeUnit.YEAR)
                                        AppDateWheelPickerPanel(
                                            title = "选择生日".localized,
                                            initSelectedDate = initSelectedDate,
                                            onConfirm = { changed, date ->
                                                birthday = date.toString()
                                            },
                                        )
                                    }
                                },
                        contentAlignment = Alignment.CenterStart,
                    ) {
                        Text(
                            text = birthday.ifEmpty { "请选择日期".localized },
                            fontSize = 16.sp,
                            modifier =
                                Modifier
                                    .align(Alignment.CenterStart)
                                    .padding(start = 16.dp),
                            style =
                                MaterialTheme.typography.titleSmall.merge(
                                    color = if (birthday.isEmpty()) Color(0xFFB6B6B6) else Color(0xFF111111),
                                    lineHeight = 21.sp,
                                    fontWeight = if (birthday.isEmpty()) FontWeight.Normal else FontWeight.Medium,
                                ),
                            maxLines = 1,
                        )
                    }

                    SizeHeight(16.dp)

                    // 昵称输入
                    Text(
                        text = "邀请码（选填）".localized,
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    AppTextField(
                        value = inviteCode,
                        onValueChange = {
                            inviteCode = it
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp),
                        maxLength = 8,
                        placeholder = "请输入邀请码".localized,
                        showLengthTip = false,
                        shape = CircleShape,
                        backgroundColor = WakooLightGrayBg,
                        textStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFF111111),
                                lineHeight = 21.sp,
                            ),
                        placeholderStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 21.sp,
                                fontWeight = FontWeight.Normal,
                            ),
                        singleLine = true,
                        keyboardOptions =
                            KeyboardOptions(
                                keyboardType = KeyboardType.Text,
                                imeAction = ImeAction.Done,
                            ),
                        keyboardActions =
                            KeyboardActions(
                                onDone = {
                                    callComplete()
                                },
                            ),
                    )

                    SizeHeight(16.dp)
                }
            }
        }

        dialogController.RenderDialogs(Unit)
    }
}

@Composable
private fun GenderOption(
    icon: @Composable () -> Unit,
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier =
            modifier
                .clip(RoundedCornerShape(24.dp))
                .background(if (isSelected) WakooBlack else WakooLightGrayBg)
                .clickable(onClick = onClick)
                .padding(
                    horizontal = 16.dp,
                    vertical = 6.dp,
                ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier.size(24.dp),
            contentAlignment = Alignment.Center,
        ) {
            icon()
        }

        SizeWidth(8.dp)

        Text(
            text = text,
            color = if (isSelected) WakooWhite else WakooBlack,
            fontSize = 16.sp,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProfileCompletionScreenPreview() {
    WakooTheme {
        RegisterFillInfoScreen(false)
    }
}
