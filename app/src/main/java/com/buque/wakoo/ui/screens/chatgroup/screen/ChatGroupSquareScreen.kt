package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.chatgroup.ChatGroupSquareListBean
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.FiSrGlassCheers
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.chatgroup.panel.CreateGroupPanel
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.CommonBanner
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SpaceListItemScaffold
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.SquareNetworkImage
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadUtils
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSquareViewModel

@Composable
fun GroupSquareScreen(modifier: Modifier = Modifier) {
    val rootNavController = LocalAppNavController.root
    val selfUserInfo = LocalSelfUserProvider.current
    val hasMyGroup = selfUserInfo.myGroup != null

    ShowCreateGroupWrapper { showCreateGroupDialog ->
        SegColorTitleScreenScaffold(
            title = "群组广场".localized,
            modifier = modifier,
            actions = {
                OutlinedButton(
                    text = if (hasMyGroup) "我的群组".localized else "创建群组".localized,
                    onClick = {
                        if (hasMyGroup) {
                            rootNavController.push(Route.ChatGroup(selfUserInfo.myGroup?.id.orEmpty()))
                            return@OutlinedButton
                        }
                        showCreateGroupDialog()
                    },
                    textColor = Color(0xff111111),
                    height = 28.dp,
                    fontSize = 12.sp,
                )
                SizeWidth(16.dp)
            },
        ) {
            ChatGroupSquareList(
                modifier = Modifier.padding(it),
                toChatGroupDetail = { id ->
                    rootNavController.push(Route.ChatGroupDetail(id))
                },
            )
        }
    }
}

@Composable
private fun ShowCreateGroupWrapper(body: @Composable (showCreateGroupFunc: () -> Unit) -> Unit) {
    val dialogController = rememberDialogController(render = false)
    var groupAvatar by rememberSaveable {
        mutableStateOf<String?>(null)
    }
    var inputTitle by rememberSaveable { mutableStateOf("") }
    val lm = LocalLoadingManager.current
    val rootNavController = LocalAppNavController.root

    val launcher =
        rootNavController.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            if (result.list.isNotEmpty()) {
                groupAvatar = result.list.single().uriString
            }
        }

    body({
        dialogController.easyPostBottomPanel {
            CreateGroupPanel(inputTitle, {
                inputTitle = it
            }, groupAvatar, {
                launcher.launch(Route.MediaSelector())
            }) {
                LogUtils.d("click create")
                lm.show(null) {
                    LogUtils.d("show(scope)")

                    if (groupAvatar == null) {
                        showToast("请选择头像".localized)
                        return@show
                    }
                    val uri = groupAvatar!!.toUri()
                    val path =
                        UploadUtils.generateOSSPath(
                            WakooApplication.instance,
                            uri,
                            UploadUtils.DEFAULT_AVATAR_PATH,
                        )
                    val cosTransferHelper = CosTransferHelper()
                    val currentTaskWrapper = cosTransferHelper.upload(cosPath = path, uri = uri)
                    val result = currentTaskWrapper.await()
                    when (result) {
                        is TransferResult.Failure -> {
                            showToast(result.exception.toString())
                        }

                        is TransferResult.Success -> {
                            GlobalRepository.chatGroupRepo
                                .createGroup(inputTitle, result.url)
                                .toastWhenError()
                                .onSuccess { obj ->
                                    // 刷新我的群组信息再进入
                                    UserManager
                                        .getRemoteSelfUserInfo()
                                        .onSuccess {
                                            val group = it.myGroup ?: return@show
                                            rootNavController.push(Route.ChatGroup(group.id))
                                        }
                                }
                        }
                    }
                    dismiss()
                }
            }
        }
    })

    dialogController.RenderDialogs(Unit)
}

@Composable
fun GroupSquarePage(
    modifier: Modifier = Modifier,
    toChatGroupDetail: (id: String) -> Unit = {},
    toMineGroup: (String) -> Unit = {},
    bottomPadding: Dp = 0.dp,
) {
    ShowCreateGroupWrapper { showCreateGroupFunc ->
        Box(modifier = modifier) {
            ChatGroupSquareList(
                toChatGroupDetail = toChatGroupDetail,
                showBanner = true,
                bottomPadding = bottomPadding,
            )
            GroupOperationButton(
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 24.dp),
                onCreateGroup = {
                    showCreateGroupFunc()
                },
                toMineGroup = toMineGroup,
            )
        }
    }
}

@Composable
private fun GroupOperationButton(
    modifier: Modifier = Modifier,
    onCreateGroup: () -> Unit = {},
    toMineGroup: (groupId: String) -> Unit = {},
) {
    val userInfo by AccountManager.userStateFlow.collectAsState()
    val hasMyGroup = userInfo?.myGroup != null

    Row(
        modifier =
            modifier
                .paint(painter = painterResource(R.drawable.ic_operation_gradient_bg), contentScale = ContentScale.FillBounds)
                .padding(horizontal = 48.dp, vertical = 24.dp)
                .click(noEffect = true) {
                    if (hasMyGroup) {
                        toMineGroup(userInfo?.myGroup?.id.orEmpty())
                    } else {
                        onCreateGroup()
                    }
                },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Image(WakooIcons.FiSrGlassCheers, contentDescription = "", modifier = Modifier.size(18.dp))
        SizeWidth(5.dp)
        Text(
            if (!hasMyGroup) "创建群组".localized else "我的群组".localized,
            fontSize = 14.sp,
            color = Color(0xff111111),
            lineHeight = 16.sp,
            fontWeight = FontWeight.W900,
            fontFamily = FontFamily.MI_SANS,
        )
    }
}

@Composable
fun ChatGroupSquareList(
    modifier: Modifier = Modifier,
    toChatGroupDetail: (id: String) -> Unit,
    showBanner: Boolean = false,
    bottomPadding: Dp = 0.dp,
) {
    val viewModel = viewModel<ChatGroupSquareViewModel>()
    val listState = rememberLazyListState()

    CStateListPaginateLayout<String, Int, ChatGroupSquareListBean, ChatGroupSquareViewModel>(
        reqKey = "",
        modifier = modifier,
        listState = listState,
        viewModel = viewModel,
        emptyText = "暂无群组".localized,
        emptyId = R.drawable.ic_empty_for_all,
        wrapperBox = { content ->
            Column(modifier = Modifier.fillMaxSize()) {
                if (showBanner) {
                    CommonBanner(
                        202,
                        modifier =
                            Modifier
                                .padding(horizontal = 16.dp)
                                .fillMaxWidth(),
                    )
                }
                content()
            }
        },
    ) { paginateState, list ->
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            state = listState,
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            items(list) { item ->
                ItemContent(item = item, onJoinButtonClick = {
                    viewModel.requestJoinGroup(item)
                }, onAvatarClick = {
                    toChatGroupDetail(item.id.toString())
                })
            }
            item {
                SizeHeight(bottomPadding)
            }
        }
    }
}

@Composable
private fun ItemContent(
    item: ChatGroupSquareListBean,
    onJoinButtonClick: () -> Unit = {},
    onAvatarClick: OnAction = {},
) {
    SpaceListItemScaffold(
        startContent = {
            SquareNetworkImage(
                data = item.avatarUrl,
                size = 80.dp,
                shape = RoundedCornerShape(8.dp),
            )
        },
        centerContent = {
            Column(verticalArrangement = Arrangement.spacedBy(5.dp)) {
                Text(
                    text = item.name,
                    style = MaterialTheme.typography.titleSmall,
                    color = WakooSecondarySelected,
                    lineHeight = 20.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )

                Text(
                    text = item.bulletin,
                    style = MaterialTheme.typography.labelLarge,
                    color = WakooSecondaryUnSelected,
                    lineHeight = 16.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy((-4).dp),
                    ) {
                        item.sampleMembers.forEach { user ->
                            AvatarNetworkImage(
                                user = user,
                                size = 20.dp,
                                modifier = Modifier.border(1.dp, WakooWhite, CircleShape),
                                enabled = false,
                            )
                        }
                    }
                    Text(
                        text = "${item.memberCnt}人",
                        style = MaterialTheme.typography.labelLarge,
                        color = WakooSecondaryUnSelected,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                    )
                    Weight(1f)

                    if (item.useInterfaceRequesting) {
                        Box(modifier = Modifier.size(64.dp, 24.dp))
                    } else {
                        GradientButton(
                            text =
                                when (item.relationWithMe) {
                                    5 -> {
                                        "申请中".localized
                                    }
                                    10 -> {
                                        "已加入".localized
                                    }
                                    else -> {
                                        "申请加入".localized
                                    }
                                },
                            onClick = onJoinButtonClick,
                            height = 24.dp,
                            fontSize = 12.sp,
                            minWidth = 64.dp,
                            gradientColors =
                                if (item.relationWithMe == 0) {
                                    listOf(
                                        Color(0xFFA3FF2C),
                                        Color(0xFF31FFA1),
                                    )
                                } else {
                                    listOf(
                                        Color(0xFFE9EAEF),
                                        Color(0xFFE9EAEF),
                                    )
                                },
                            textColor =
                                if (item.relationWithMe == 0) {
                                    Color(0xff111111)
                                } else {
                                    Color(0xff999999)
                                },
                            enabled = item.relationWithMe == 0,
                            paddingValues = PaddingValues(horizontal = 10.dp),
                        )
                    }
                }
            }
        },
        endContent = {},
        modifier =
            Modifier
                .fillMaxWidth()
                .click(noEffect = true, onClick = onAvatarClick)
                .background(color = WakooWhite, RoundedCornerShape(12.dp))
                .padding(horizontal = 12.dp, vertical = 8.dp),
        space = 12.dp,
    )
}

@Preview
@Composable
private fun PreviewItem() {
    val json =
        "{\"id\":248,\"name\":\"Hifun\",\"avatar_url\":\"https://media.wakooclub.com/mobileclient/android/moment/1722755474008_B2EEAB8396DE29E75463.jpeg\",\"public_id\":\"100256\",\"bulletin\":\"嘿嘿嘿嘿嘿嘿嘿嘿哈哈哈哈哈哈\",\"avatar_frame\":\"https://media.wakooclub.com/opsite/prop/icon/su7_BT4GCoX.jpg\",\"list_item_bg_img\":\"\",\"member_cnt\":5,\"rc_group_id\":\"tribe248\",\"tim_group_id\":\"tribe248\",\"relation_with_me\":0,\"sample_members\":[{\"userid\":2527,\"public_id\":\"103337\",\"nickname\":\"2800女\",\"avatar_url\":\"https://s.test.wakooclub.com/aacecZ?x-oss-process=image/format,webp\",\"gender\":1,\"age\":1,\"height\":0,\"avatar_frame\":\"\",\"medal\":null,\"medal_list\":[],\"level\":0},{\"userid\":2528,\"public_id\":\"103338\",\"nickname\":\"女2801\",\"avatar_url\":\"https://s.test.wakooclub.com/aacec0?x-oss-process=image/format,webp\",\"gender\":1,\"age\":1,\"height\":0,\"avatar_frame\":\"\",\"medal\":null,\"medal_list\":[],\"level\":0},{\"userid\":2529,\"public_id\":\"103339\",\"nickname\":\"Vange\",\"avatar_url\":\"https://s.test.wakooclub.com/aacec1?x-oss-process=image/format,webp\",\"gender\":1,\"age\":1,\"height\":0,\"avatar_frame\":\"\",\"medal\":null,\"medal_list\":[],\"level\":0},{\"userid\":2530,\"public_id\":\"103340\",\"nickname\":\"鸭鸭\",\"avatar_url\":\"https://s.test.wakooclub.com/aacec2?x-oss-process=image/format,webp\",\"gender\":1,\"age\":1,\"height\":0,\"avatar_frame\":\"\",\"medal\":null,\"medal_list\":[],\"level\":0},{\"userid\":2546,\"public_id\":\"103365\",\"nickname\":\"王2\",\"avatar_url\":\"https://s.test.wakooclub.com/aacec9?x-oss-process=image/format,webp\",\"gender\":1,\"age\":1,\"height\":0,\"avatar_frame\":\"\",\"medal\":null,\"medal_list\":[],\"level\":0}]}"
    val bean = AppJson.decodeFromString<ChatGroupSquareListBean>(json)
    WakooTheme {
        ItemContent(item = bean)
    }
}
