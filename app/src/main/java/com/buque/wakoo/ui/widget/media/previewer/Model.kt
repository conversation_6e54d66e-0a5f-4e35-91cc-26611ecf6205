package com.buque.wakoo.ui.widget.media.previewer

import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import kotlinx.serialization.Serializable

/**
 * 代表一个媒体相册，包含一组媒体项目。
 * @property items 相册中的媒体项目列表。
 */
@Serializable
data class MediaViewerAlbum(
    val items: List<MediaViewerItem>,
)

/**
 * 代表一个可显示的媒体项目（如图片）的密封接口。
 * @property placeholderImageUrl 占位图/缩略图的URL。
 * @property aspectRatio 媒体的原始宽高比。
 */
@Serializable
sealed interface MediaViewerItem {
    val placeholderImageUrl: String?

    val aspectRatio: Float

    val localFilePath: String?

    val key: String

    /**
     * 代表一个图片类型的媒体项目。
     * @property fullSizedUrl 完整尺寸图片的URL。
     */
    @Serializable
    data class Image(
        val fullSizedUrl: String?,
        override val placeholderImageUrl: String?,
        override val aspectRatio: Float,
        override val localFilePath: String? = null,
        val overrideKey: String? = null,
    ) : MediaViewerItem {
        override val key: String = overrideKey ?: this.toString()
    }

    @Serializable
    data class Video(
        val fullSizedUrl: String,
        override val placeholderImageUrl: String,
        override val aspectRatio: Float,
        override val localFilePath: String? = null,
        val overrideKey: String? = null,
    ) : MediaViewerItem {
        val playMediaItem by lazy {
            PlayMediaItem.prefixTagVideo(fullSizedUrl, "mediaViewer")
        }

        override val key: String = overrideKey ?: this.toString()
    }
}

/**
 * 传递给媒体查看器屏幕的导航参数或“Key”。
 * @property album 包含所有媒体项的相册。
 * @property initialIndex 用户点击进入查看器时的初始图片索引。
 */
@Serializable
data class MediaViewerKey(
    val album: MediaViewerAlbum,
    val initialIndex: Int,
) {
    /**
     * 方便地获取初始被点击的媒体项及其索引。
     */
    val initialItem = album.items[initialIndex]
}
