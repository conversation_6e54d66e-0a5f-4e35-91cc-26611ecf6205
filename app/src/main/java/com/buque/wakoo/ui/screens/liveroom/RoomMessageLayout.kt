package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.buque.wakoo.im_business.message.ui.entry.GiftMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.HongBaoMessageEntry
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.LiveRoomUserSystemMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.RecallMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.RichMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.RoomContent
import com.buque.wakoo.im_business.message.ui.entry.TextMsgEntry
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.widget.overScrollVertical
import com.buque.wakoo.ui.widget.rememberOverscrollFlingBehavior
import com.buque.wakoo.viewmodel.liveroom.LiveRoomViewModel

@Composable
fun BoxScope.LiveRoomMessageLayout(viewModel: LiveRoomViewModel) {
    val roomInfoState = viewModel.roomInfoState
    val isLoading by remember {
        derivedStateOf {
            viewModel.paginateState.nextLoadState.isLoading
        }
    }

    val listState = rememberLazyListState()

    viewModel.bindListState(listState)

    LazyColumn(
        modifier =
            Modifier
                .fillMaxSize()
                .overScrollVertical(),
        state = listState,
        contentPadding = PaddingValues(vertical = 16.dp, horizontal = 16.dp),
        reverseLayout = true,
        verticalArrangement = Arrangement.spacedBy(12.dp),
        flingBehavior = rememberOverscrollFlingBehavior { listState },
    ) {
        items(viewModel.messageList, contentType = { item ->
            item::class.simpleName
        }) { item ->
            val uiEntry = item.uiEntry
            when (uiEntry) {
                is RecallMsgEntry -> {
                    uiEntry.RoomContent(roomInfoState)
                }

                is TextMsgEntry -> {
                    uiEntry.RoomContent(roomInfoState)
                }

                is GiftMsgEntry -> {
                    uiEntry.RoomContent(roomInfoState)
                }

                is LiveRoomSystemMsgEntry -> {
                    uiEntry.RoomContent(roomInfoState)
                }

                is LiveRoomUserSystemMsgEntry -> {
                    uiEntry.RoomContent(roomInfoState)
                }

                is HongBaoMessageEntry -> {
                    uiEntry.RoomContent(roomInfoState) {
                        roomInfoState.sendEvent(RoomEvent.ShowHongBao(it))
                    }
                }

                is RichMsgEntry -> {
                    uiEntry.RoomContent(roomInfoState)
                }

                else -> Spacer(Modifier)
            }
        }

        if (isLoading) {
            item(key = "isLoading", contentType = "isLoading") {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center,
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = WakooGreen,
                        strokeWidth = 1.5.dp,
                    )
                }
            }
        }

        item(key = "room_system_announcement", contentType = "room_system_announcement") {
            val density = LocalDensity.current
            val fontSize =
                with(density) {
                    10.dp.toPx().toSp()
                }

            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0x1AFFFFFF), RoundedCornerShape(8.dp))
                        .padding(8.dp),
            ) {
                Text(
                    text = "官方严禁未成年人充值，严禁宣传政治、色情、暴力等内容，如有任何违法违规行为请及时向官方举报。".localized,
                    fontSize = fontSize,
                    lineHeight = fontSize * 1.3f,
                    color = Color.White.copy(alpha = 0.5f),
                )
            }
        }
    }
}
