package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.VipIcon: ImageVector
    get() {
        if (_VipIcon != null) {
            return _VipIcon!!
        }
        _VipIcon =
            ImageVector
                .Builder(
                    name = "VipIcon",
                    defaultWidth = 27.dp,
                    defaultHeight = 20.dp,
                    viewportWidth = 27f,
                    viewportHeight = 20f,
                ).apply {
                    path(fill = SolidColor(Color(0xFFFFE984))) {
                        moveTo(2f, 0f)
                        lineTo(25f, 0f)
                        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 27f, 2f)
                        lineTo(27f, 18f)
                        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 25f, 20f)
                        lineTo(2f, 20f)
                        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 0f, 18f)
                        lineTo(0f, 2f)
                        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 2f, 0f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF674E0F))) {
                        moveTo(7.371f, 14.2f)
                        lineTo(4.361f, 6.1f)
                        horizontalLineTo(6.361f)
                        lineTo(8.291f, 11.67f)
                        horizontalLineTo(8.351f)
                        lineTo(10.281f, 6.1f)
                        horizontalLineTo(12.281f)
                        lineTo(9.271f, 14.2f)
                        horizontalLineTo(7.371f)
                        close()
                        moveTo(14.857f, 6.1f)
                        verticalLineTo(14.2f)
                        horizontalLineTo(12.997f)
                        verticalLineTo(6.1f)
                        horizontalLineTo(14.857f)
                        close()
                        moveTo(19.132f, 6.1f)
                        curveTo(19.779f, 6.1f, 20.339f, 6.213f, 20.812f, 6.44f)
                        curveTo(21.292f, 6.667f, 21.662f, 6.993f, 21.922f, 7.42f)
                        curveTo(22.182f, 7.84f, 22.312f, 8.337f, 22.312f, 8.91f)
                        curveTo(22.312f, 9.477f, 22.185f, 9.973f, 21.932f, 10.4f)
                        curveTo(21.685f, 10.82f, 21.329f, 11.143f, 20.862f, 11.37f)
                        curveTo(20.402f, 11.597f, 19.859f, 11.71f, 19.232f, 11.71f)
                        horizontalLineTo(18.002f)
                        verticalLineTo(14.2f)
                        horizontalLineTo(16.142f)
                        verticalLineTo(6.1f)
                        horizontalLineTo(19.132f)
                        close()
                        moveTo(18.852f, 10.03f)
                        curveTo(19.332f, 10.03f, 19.712f, 9.93f, 19.992f, 9.73f)
                        curveTo(20.272f, 9.53f, 20.412f, 9.257f, 20.412f, 8.91f)
                        curveTo(20.412f, 8.563f, 20.272f, 8.29f, 19.992f, 8.09f)
                        curveTo(19.712f, 7.883f, 19.332f, 7.78f, 18.852f, 7.78f)
                        horizontalLineTo(18.002f)
                        verticalLineTo(10.03f)
                        horizontalLineTo(18.852f)
                        close()
                    }
                }.build()

        return _VipIcon!!
    }

@Suppress("ObjectPropertyName")
private var _VipIcon: ImageVector? = null
