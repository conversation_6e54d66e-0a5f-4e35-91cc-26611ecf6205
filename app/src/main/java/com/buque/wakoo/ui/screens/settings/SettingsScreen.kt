package com.buque.wakoo.ui.screens.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.im_business.enableAppNotification
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.CenterDialog
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.navigation.dialog.filterDoResult
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.ui.dialog.ColumnDoubleActionDialog
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SingleTextSettingsMenuItem
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.viewmodel.SettingViewModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

@Composable
fun SettingsScreen(
    onBlacklistClick: () -> Unit = {},
    onFeedbackClick: () -> Unit = {},
    onAboutAppClick: () -> Unit = {},
    onDebug: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()
    val viewModel = viewModel(SettingViewModel::class)
    val dialogController = rememberDialogController()
    val loading = LocalLoadingManager.current
    SegColorTitleScreenScaffold(
        title = "设置".localized,
    ) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            SingleTextSettingsMenuItem(
                titleText = "黑名单".localized,
                onClick = onBlacklistClick,
            )

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            SingleTextSettingsMenuItem(
                titleText = "反馈与建议".localized,
                onClick = onFeedbackClick,
            )

            HorizontalDivider(
                color = Color(0xFFE5E5E5),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = 16.dp),
            )

            SingleTextSettingsMenuItem(
                titleText = "关于我们".localized,
                onClick = onAboutAppClick,
            )

            SingleTextSettingsMenuItem(
                titleText = "应用内通知".localized,
                onClick = onAboutAppClick,
                previewContent = {
                    var enableMsg by remember {
                        mutableStateOf(enableAppNotification)
                    }
                    Switch(
                        enableMsg,
                        { isChecked ->
                            enableMsg = isChecked
                            enableAppNotification = isChecked
                        },
                        modifier = Modifier.size(32.dp, 16.dp),
                        colors =
                            SwitchDefaults.colors(
                                checkedThumbColor = Color.White,
                                checkedTrackColor = Color(0xFF38DD7A), // 选中时的绿色
                                uncheckedThumbColor = Color.White,
                                uncheckedTrackColor = Color(0xFFCCCCCC), // 未选中时的灰色
                            ),
                    )
                },
                withArrow = false
            )

            if (BuildConfig.DEBUG) {
                SingleTextSettingsMenuItem(
                    titleText = "Debug",
                    onClick = onDebug,
                )
            }

            Weight()

            SolidButton(
                text = "退出登录".localized,
                onClick = {
                    scope.launch {
                        AccountManager.logout()
                    }
                },
                modifier =
                    Modifier
                        .padding(horizontal = 28.dp)
                        .fillMaxWidth(),
            )

            SizeHeight(size = 12.dp)

            // 描边按钮
            OutlinedButton(
                text = "注销账号".localized,
                onClick = {
                    dialogController.post(WriteOffDialog())
                },
                modifier =
                    Modifier
                        .padding(horizontal = 28.dp)
                        .fillMaxWidth(),
            )

            SizeHeight(size = 20.dp)
        }
    }

    LaunchedEffect(Unit) {
        dialogController.events
            .onEach { event ->
                event.filterDoResult("writeOff") {
                    if (it.result == true) {
                        loading.show(scope) {
                            viewModel.writeOffUser().join()
                        }
                    }
                }
            }.launchIn(this)
    }
}

@Preview(showBackground = true)
@Composable
private fun SettingsPagePreview() {
    WakooTheme {
        SettingsScreen()
    }
}

@Serializable
private class WriteOffDialog : CenterDialog<Unit>() {
    @Composable
    override fun DialogScope.Content(param: Unit) {
        ColumnDoubleActionDialog(
            title = "注销账号".localized,
            content = "注销账号后我们将删除您所有相关的数据，为了防止您以外删除账号，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除。".localized,
            confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "取消".localized),
            cancelButtonConfig = DialogButtonStyles.Transparent.copy(text = "确认注销".localized),
            onCancel = {
                dismiss(true, "writeOff")
            },
            onConfirm = {
                dismiss(false, "writeOff")
            },
        )
    }
}
