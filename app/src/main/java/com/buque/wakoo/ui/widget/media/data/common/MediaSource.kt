package com.buque.wakoo.ui.widget.media.data.common

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.channels.consumeEach
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.runningFold
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 代表所有可能改变 MediaFeed 状态的内部事件。
 * 这是数据源与状态累加器（Reducer）之间的通信契约。
 */
internal sealed class MediaEvent {
    // 首次加载时使用
    object InitialLoading : MediaEvent()

    /**
     * 表示骨架数据（相册列表和带ID的占位符媒体列表）已加载完成。
     * @param allIds 完整资源Id, 和items对应。
     * @param albums 完整的相册列表。
     * @param items 包含所有媒体ID的占位符列表 (List<PlaceholderItem>)。
     */
    data class SkeletonLoaded(
        val allIds: List<String>,
        val albums: List<Album>,
        val items: List<IMediaItem>,
    ) : MediaEvent()

    // 后台全局刷新完成，携带第一页的完整数据
    data class SilentlyRefreshed(
        val feed: MediaFeed,
    ) : MediaEvent()

    // 分页加载成功，携带新一页的数据
    data class PageLoaded(
        val newItems: List<MediaItem>,
    ) : MediaEvent()

    /**
     * 表示检测到了增量数据变化。
     * @param changes 包含了新增、删除、更新的ID列表。
     */
    data class Incremental(
        val changes: MediaChanges,
    ) : MediaEvent()

    /**
     * 表示在处理过程中发生了错误。
     * @param throwable 捕获到的异常。
     */
    data class Error(
        val throwable: Throwable,
    ) : MediaEvent()
}

/**
 * 媒体数据源的高级封装。
 *
 * 该类负责将底层的、命令式的 MediaRepository 转换为一个声明式的、响应式的 Flow<MediaFeed>。
 *
 * 主要特性:
 * 1.  **事件驱动架构**: 使用内部 MediaEvent 来解耦数据获取与状态更新。
 * 2.  **ID优先，骨架先行**: 快速加载所有媒体ID并显示占位符，实现秒开效果。
 * 3.  **按需分页加载**: 基于ID锚点，由UI层驱动，只在需要时才请求媒体详情。
 * 4.  **智能的无感知刷新**: 后台刷新时，只加载到用户已看到的深度+一页，避免UI闪烁和跳回首页。
 * 5.  **轻量级增量更新**: 能够处理来自底层的增量变化，并高效更新UI状态。
 * 6.  **自包含与健壮性**: 内部管理协程和线程，对外提供简洁API。
 *
 * @param repository 底层数据仓库的平台实现。
 */
class MediaSource(
    private val repository: MediaRepository,
) {
    private val pageRequestChannel = Channel<Unit>(Channel.CONFLATED)
    private val pageSize = 100 // 定义分页大小，可根据需求调整

    /**
     * 由外部（ViewModel）调用，通知数据层加载下一页数据。
     */
    fun loadNextPage() {
        pageRequestChannel.trySend(Unit)
    }

    /**
     * 获取指定媒体类型的完整媒体数据流。
     * 这是提供给外部的唯一入口点。
     *
     * @param mediaType 要观察的媒体类型。
     * @return 一个会持续发射最新 MediaFeed 状态的 Flow。
     */
    fun getMediaFeed(mediaType: SelectorMediaType): Flow<MediaFeed> {
        // Pair 的 first 是完整的ID列表，second 是完整的MediaFeed状态。
        var stateHolder = MediaFeed()

        val eventFlow =
            callbackFlow<MediaEvent> {
                // --- 事件产生区 ---

                /**
                 * 首次加载的特定流程。
                 * 显示Loading -> 获取ID -> 显示骨架屏 -> 自动请求第一页。
                 */
                suspend fun initialLoad() {
                    trySend(MediaEvent.InitialLoading)
                    try {
                        val albums = repository.getAlbums(mediaType).getOrThrow()
                        val placeholders = repository.getAllPlaceholderMediaItems(null, mediaType).getOrThrow()
                        val allIds = placeholders.map { it.id }

                        // 更新 stateHolder 中的ID列表
                        stateHolder = stateHolder.copy(albums = albums, allIds = allIds, mediaItems = placeholders)

                        trySend(MediaEvent.SkeletonLoaded(allIds, albums, placeholders))
                        loadNextPage() // 自动触发第一页数据的加载
                    } catch (e: Exception) {
                        trySend(MediaEvent.Error(e))
                    }
                }

                /**
                 * 后台静默全局刷新的特定流程。
                 * 核心：加载到用户当前已看到的深度 + 一页预加载。
                 */
                suspend fun silentFullReload() {
                    try {
                        val currentState = stateHolder
                        val currentItemCount = currentState.anchorIdIndex + 1

                        val albums = repository.getAlbums(mediaType).getOrThrow()
                        val newAllIds = repository.getAllMediaIds(null, mediaType).getOrThrow()

                        // 计算需要加载的数量 = 当前数量 + 一页 (确保不为0)
                        val countToLoad = (currentItemCount + pageSize).coerceAtLeast(pageSize)
                        val idsToLoad = newAllIds.take(countToLoad)

                        val newItems =
                            if (idsToLoad.isNotEmpty()) {
                                repository.getMediaByIds(idsToLoad).getOrDefault(emptyList())
                            } else {
                                emptyList()
                            }

                        // 更新 stateHolder
                        stateHolder =
                            MediaFeed(albums = albums, allIds = newAllIds, mediaItems = newItems, isLoading = false)

                        // 发送刷新完成事件，携带最终的、已填充到适当深度的完整Feed
                        trySend(MediaEvent.SilentlyRefreshed(stateHolder))
                    } catch (e: Exception) {
                        trySend(MediaEvent.Error(e))
                    }
                }

                // 注册观察者以接收底层数据变化
                val observer: (MediaChanges?) -> Unit = { changes ->
                    launch(Dispatchers.IO) {
                        if (changes != null && stateHolder.mediaItems.isNotEmpty()) {
                            trySend(MediaEvent.Incremental(changes))
                        } else {
                            silentFullReload()
                        }
                    }
                }
                repository.registerObserver(observer)

                // 处理来自UI的分页请求
                launch(Dispatchers.IO) {
                    pageRequestChannel.consumeEach { anchorId ->
                        val newItems =
                            try {
                                val currentState = stateHolder
                                val startIndex = currentState.anchorIdIndex
                                if (startIndex < 0) {
                                    return@consumeEach
                                }
                                var i = 0
                                val nextPageIds =
                                    buildList {
                                        val list = currentState.mediaItems
                                        for (index in startIndex until list.size) {
                                            val item = list[index]
                                            if (item is PlaceholderMediaItem) {
                                                i++
                                                add(item.id)
                                                if (i >= 100) {
                                                    break
                                                }
                                            }
                                        }
                                    }
                                repository.getMediaByIds(nextPageIds).getOrDefault(emptyList())
                            } catch (_: Exception) {
                                emptyList()
                            }
                        trySend(MediaEvent.PageLoaded(newItems))
                    }
                }

                // 启动流程：执行首次加载
                launch(Dispatchers.IO) {
                    initialLoad()
                }

                // 当Flow被取消时，清理资源
                awaitClose {
                    repository.unregisterObserver()
                    pageRequestChannel.close()
                }
            }

        // --- 状态演进区 (Reducer) ---
        return eventFlow
            .runningFold(MediaFeed()) { _, event ->
                // 使用 stateHolder 来确保 Reducer 总能拿到最新的状态进行计算
                applyEvent(stateHolder, event, mediaType).also { newState ->
                    stateHolder = newState
                }
            }.map {
                it.copy(mediaItems = it.mediaItems.sortedByDescending { it.dateAdded })
            }.distinctUntilChanged()
    }

    /**
     * Reducer函数：根据当前状态和新事件，计算并返回下一个全新的状态。
     */
    private suspend fun applyEvent(
        currentState: MediaFeed,
        event: MediaEvent,
        mediaType: SelectorMediaType,
    ): MediaFeed =
        when (event) {
            is MediaEvent.InitialLoading -> {
                currentState.copy(isLoading = true, error = null)
            }

            is MediaEvent.SkeletonLoaded -> {
                MediaFeed(
                    albums = event.albums,
                    allIds = event.allIds, // UI显示骨架
                    mediaItems = event.items, // UI显示骨架
                    isLoading = false,
                    mediaFilling = true,
                )
            }

            is MediaEvent.SilentlyRefreshed -> {
                // 直接用后台刷新好的、带有多页数据的Feed替换当前状态
                event.feed
            }

            is MediaEvent.PageLoaded -> {
                // 收到一页新数据，将其填充到骨架列表中或追加到现有列表
                val enrichedMap = event.newItems.associateBy { it.id }

                val allIds = mutableListOf<String>()
                val newItems =
                    currentState.mediaItems.mapNotNull {
                        val item = enrichedMap[it.id]
                        if (item != null) {
                            if (item.width <= 0 || item.height <= 0 || item.sizeBytes <= 1000) {
                                null
                            } else {
                                allIds.add(it.id)
                                item
                            }
                        } else {
                            allIds.add(it.id)
                            it
                        }
                    }
                currentState.copy(allIds = allIds, mediaItems = newItems, mediaFilling = false)
            }

            is MediaEvent.Incremental -> {
                // 处理轻量级增量更新
                withContext(Dispatchers.Default) {
                    applyIncrementalChanges(currentState, event.changes, mediaType)
                }
            }

            is MediaEvent.Error -> {
                currentState.copy(isLoading = false, error = event.throwable)
            }
        }

    /**
     * 处理增量更新事件，返回一个新的MediaFeed状态。
     */
    private suspend fun applyIncrementalChanges(
        currentFeed: MediaFeed,
        changes: MediaChanges,
        mediaType: SelectorMediaType,
    ): MediaFeed {
        var newMediaItems = currentFeed.mediaItems.toMutableList()
        newMediaItems.removeAll { it.id in changes.removedIds }

        if (changes.insertedIds.isNotEmpty()) {
            val insertedItems =
                withContext(Dispatchers.IO) {
                    repository.getMediaByIds(changes.insertedIds).getOrDefault(emptyList())
                }
            newMediaItems.addAll(0, insertedItems)
        }

        val newAlbums =
            withContext(Dispatchers.IO) {
                repository.getAlbums(mediaType).getOrDefault(currentFeed.albums)
            }

        return currentFeed.copy(
            albums = newAlbums,
            mediaItems = newMediaItems.toList(),
            isLoading = false,
        )
    }
}
