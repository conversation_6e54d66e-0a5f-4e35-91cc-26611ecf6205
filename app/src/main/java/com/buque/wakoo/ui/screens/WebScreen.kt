package com.buque.wakoo.ui.screens

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import androidx.activity.compose.LocalActivity
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.core.webview.AppLinkNavigator
import com.buque.wakoo.core.webview.BaseJsBridgeEventDelegate
import com.buque.wakoo.core.webview.WebFrameInfo
import com.buque.wakoo.core.webview.buildJSBEventHandlerList
import com.buque.wakoo.core.webview.offline.OfflinePkgManager
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.inter.MsgFilter
import com.buque.wakoo.im.utils.IMUtils
import com.buque.wakoo.im.utils.WatchRecvNewCustomMessage
import com.buque.wakoo.im_business.tim.TIMEngine
import com.buque.wakoo.manager.L10nManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.widget.EffectVideoAnimationView
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.getEffectFile
import com.buque.wakoo.ui.widget.gift.YYEVAResourceFetcher
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.toLink
import com.buque.wakoo.utils.rememberPermissionLauncher
import com.buque.webview.AppBridgeWebView
import com.buque.webview.WebViewProxy
import com.buque.webview.client.IWebViewClientProxy
import com.buque.webview.getValueByUrl
import com.buque.webview.handler.BaseBridgeHandler
import com.buque.webview.handler.HandlerName
import com.buque.webview.putValueByUrl
import com.buque.webview.utils.callFailure
import com.buque.webview.utils.callSuccess
import com.kevinnzou.web.LoadingState
import com.kevinnzou.web.WebView
import com.kevinnzou.web.rememberWebViewNavigator
import com.kevinnzou.web.rememberWebViewState
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import java.io.File
import kotlin.time.Duration.Companion.seconds

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebScreen(
    route: Route.Web,
    onOpenPage: (AppNavKey) -> Unit,
    vm: WebViewModel = viewModel<WebViewModel>(),
    onFinish: () -> Unit,
) {

    val url = formatUrl(route.url)
    WebViewContent(
        url = url,
        modifier = Modifier.fillMaxSize(),
        onOpenPage = onOpenPage,
        onFinish = onFinish,
        vm = vm
    )
}

@Composable
fun formatUrl(url: String): String {
    val loadUrl = remember(url) {
        val localUrl = OfflinePkgManager.getLocalUrl(Uri.parse(url))
        localUrl ?: addLanguageToUrl(url, L10nManager.languageTag)
    }
    return loadUrl
}


class WebViewModel : ViewModel() {

    private val webViewStore = mutableMapOf<String, AppBridgeWebView?>()
    private var channelJoined: String? = null

    fun getCachedWebView(key: String) = webViewStore[key]?.takeIf {
        it.parent == null
    }

    fun putWebView(key: String, view: AppBridgeWebView) {
        webViewStore[key] = view
    }

    fun clear(key: String) {
        webViewStore[key] = null
    }

    fun joinChannel(channelName: String, callback: CallBackFunction) {
        LogUtils.d("join channel $channelName")
        if (channelName.isEmpty() || channelName == channelJoined) return
        IMUtils.launchOnUnMain {
            if (TIMEngine.joinConversation(channelName, ConversationType.RTM, true)) {
                channelJoined = channelName
                callback.callSuccess()
            } else {
                callback.callFailure()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        webViewStore.clear()
        channelJoined?.also {
            IMUtils.launchOnUnMain {
                TIMEngine.quitConversation(it, ConversationType.RTM, true)
            }
        }
    }
}

fun addLanguageToUrl(
    url: String,
    lang: String,
) = url.putValueByUrl("app-language", lang)

private fun logMessage(message: String) {
    LogUtils.d("WebView:$message")
}

@Composable
fun WebViewContent(
    url: String,
    modifier: Modifier = Modifier,
    onOpenPage: (AppNavKey) -> Unit,
    onFinish: () -> Unit = {},
    withStatus: Boolean = true,
    vm: WebViewModel = viewModel<WebViewModel>()
) {
    LaunchedEffect(Unit) {
        LogUtils.d("load url:\n$url")
    }
    val statusBars = WindowInsets.statusBars
    val navigationBars = WindowInsets.navigationBars
    val density = LocalDensity.current
    val context = LocalContext.current
    val title = url.getValueByUrl("title")
    val state = rememberWebViewState(url)
    val navigator = rememberWebViewNavigator()
    var jsbTitle by remember {
        mutableStateOf<String?>(title)
    }
    var titleVisible by rememberSaveable {
        mutableStateOf(title?.isNotEmpty() == true)
    }
    var applyStatusBarPadding by remember {
        mutableStateOf(false)
    }
    var applyNavigationBarPadding by remember {
        mutableStateOf(false)
    }
    var permissionCallback by remember {
        mutableStateOf<Pair<BaseBridgeHandler, CallBackFunction>?>(null)
    }

    val launcher =
        rememberPermissionLauncher(onDenied = {
            val (handler, callback) = permissionCallback ?: return@rememberPermissionLauncher
            handler.apply {
                callback.sendFailure(context, -1)
            }
            permissionCallback = null
        }) {
            val (handler, callback) = permissionCallback ?: return@rememberPermissionLauncher
            handler.apply {
                callback.sendSuccess(context, 0)
            }
            permissionCallback = null
        }
    var effectFile by remember {
        mutableStateOf<File?>(null)
    }
    var channelForListen by rememberSaveable { mutableStateOf("") }

    val activity = LocalActivity.current ?: return
    val scope = rememberCoroutineScope()
    val proxy =
        remember(onFinish, scope, activity, onOpenPage, vm) {
            val delegate =
                object : BaseJsBridgeEventDelegate(onFinish = onFinish, onTitleChanged = {
                    jsbTitle = it
                    titleVisible = it.isNullOrEmpty().not()
                }) {
                    override fun onPlayVideoEffect(
                        context: Context,
                        effect: String,
                    ) {
                        scope.launch {
                            getEffectFile(effect, 15.seconds)?.also {
                                effectFile = it
                                LogUtils.d("play effect $it")
                            }
                        }
                    }

                    override fun onJumpAppPage(
                        context: Context,
                        jsonObject: JsonObject,
                    ) {
                        val targetName = jsonObject.getStringOrNull("target_name").orEmpty()
                        if (AppLinkNavigator.isSupport(targetName)) {
                            scope.launch {
                                targetName.toLink()
                            }
                        } else {
                            when (targetName) {
                                "user_home" -> {
                                    runCatching {
                                        jsonObject["params"]
                                            ?.jsonObject
                                            ?.get("user_id")
                                            ?.jsonPrimitive
                                            ?.contentOrNull
                                    }.getOrNull().orEmpty().also { uid ->
                                        onOpenPage(
                                            Route.UserProfile(
                                                BasicUser.fromUid(uid),
                                            ),
                                        )
                                    }
                                }

                                "private_chat" -> {
                                    runCatching {
                                        jsonObject["params"]
                                            ?.jsonObject
                                            ?.get("user_id")
                                            ?.jsonPrimitive
                                            ?.contentOrNull
                                    }.getOrNull().orEmpty().also { uid ->
                                        onOpenPage(
                                            Route.Chat(
                                                BasicUser.fromUid(uid),
                                            ),
                                        )
                                    }
                                }

                                else -> {}
                            }
                        }
                    }

                    override fun onApiError(
                        code: Int,
                        msg: String,
                    ) {
                        when (code) {
                            -11 -> { // need charge coin
                                showToast(msg)
                                onOpenPage(Route.Recharge)
                            }

                            -12 -> { // need vip
                                showToast(msg)
                                onOpenPage(Route.Member)
                            }

                            else -> {}
                        }
                    }

                    override fun onImmersive(
                        statusBarVisible: Boolean,
                        navigationBarVisible: Boolean,
                    ) {
                        applyStatusBarPadding = statusBarVisible
                        applyNavigationBarPadding = navigationBarVisible
                    }

                    override fun onGetStatusBarHeight(context: Context): Float =
                        with(density) {
                            statusBars.getTop(density).toDp().value.also {
                                logMessage("onGetStatusBarHeight: $it")
                            }
                        }

                    override fun onGetNavigationBarHeight(context: Context): Float =
                        with(density) {
                            navigationBars.getBottom(density).toDp().value.also {
                                logMessage("onGetNavigationBarHeight: $it")
                            }
                        }

                    override fun onRequestAndroidPermission(
                        bridgeHandler: BaseBridgeHandler,
                        permissions: Array<String>?,
                        callback: CallBackFunction,
                    ) {
                        if (!permissions.isNullOrEmpty()) {
                            permissionCallback = bridgeHandler to callback
                            launcher.launch(permissions)
                        }
                    }
                }
            val eventHandlers: List<Pair<String, BridgeHandler>> = buildJSBEventHandlerList(delegate)
            val handlers = buildList {
                addAll(listOf(HandlerName.APP_TENCENT_CHANNEL to object : BaseBridgeHandler() {
                    override fun handlerV2(
                        context: Context,
                        data: JsonObject,
                        callback: CallBackFunction
                    ) {
                        val channelName = data.getStringOrNull("channel_name")
                        LogUtils.d("channel parse:$channelName")
                        channelName ?: return
                        if (channelName.isNotEmpty()) {
                            channelForListen = channelName
                        }
                        vm.joinChannel(channelName, callback)
                    }
                }))
                addAll(eventHandlers)
            }
            WebViewProxy(activity, eventHandlers = handlers, onLackCameraPermission = {
                showToast("缺少相机权限".localized)
            }, webViewStore = {
                vm.getCachedWebView(url)
            }, onWebViewCreated = {
                vm.putWebView(url, it)
            }, iWebViewClientProxy = object : IWebViewClientProxy {
                override fun shouldInterceptRequest(
                    view: WebView?,
                    request: WebResourceRequest?
                ): WebResourceResponse? {
//                    LogUtils.d("shouldInterceptRequest ${request?.url}")
                    request ?: return null
                    return OfflinePkgManager.interceptRequest(view, request?.url)
                }

                //这个方法一直没回调
                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean? {
//                    LogUtils.d("shouldOverrideUrlLoading ${url}")
                    url ?: return null
                    return OfflinePkgManager.getLocalUrl(url.toUri())?.also {
                        LogUtils.d("load local url:$it")
                        view?.loadUrl(it)
                    } != null
                }

            }, onWebViewDispose = {
                vm.clear(url)
            })
        }

    key(channelForListen) {
        if (channelForListen.isNotEmpty()) {
            WatchRecvNewCustomMessage(filter = MsgFilter(channelForListen)) {
                proxy.bridge?.callHandler("receiveRacingMessage", it.rawContent, null)
            }
        }
    }
    LaunchedEffect(navigator) {
        val bundle = state.viewState
        if (bundle == null) {
            // This is the first time load, so load the home page.
            val lowercase = url.lowercase()
            if (lowercase.endsWith(".jpg") ||
                lowercase.endsWith(".jpeg") ||
                lowercase.endsWith(
                    ".png",
                ) ||
                lowercase.endsWith(".webp")
            ) {
                navigator.loadHtml(getWrapImageUrl(url))
            } else {
                navigator.loadUrl(url)
            }
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(modifier = modifier) {
            Box(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .background(Color.White),
            ) {
                if (titleVisible) {
                    WakooTitleBar(
                        title = jsbTitle ?: state.pageTitle,
                        onBack = onFinish,
                        windowInsets = if (withStatus) TopAppBarDefaults.windowInsets else navigationBars,
                    )
                }
                val loadingState = state.loadingState

                var showProgressIndicator by remember {
                    mutableStateOf(false)
                }
                LaunchedEffect(key1 = state) {
                    snapshotFlow {
                        state.loadingState
                    }.filter {
                        it is LoadingState.Loading
                    }.collectLatest {
                        showProgressIndicator = true
                    }
                }

                if (showProgressIndicator) {
                    val progress by animateFloatAsState(
                        targetValue =
                            when (loadingState) {
                                is LoadingState.Loading -> {
                                    loadingState.progress
                                }

                                is LoadingState.Initializing -> {
                                    0f
                                }

                                else -> {
                                    1f
                                }
                            },
                        animationSpec =
                            if (state.loadingState is LoadingState.Finished) {
                                spring(visibilityThreshold = 0.01f, stiffness = 10f)
                            } else {
                                spring(visibilityThreshold = 0.01f)
                            },
                    ) {
                        if (it == 1f) {
                            showProgressIndicator = false
                        }
                    }

                    LinearProgressIndicator(
                        progress = {
                            progress
                        },
                        trackColor = Color.Transparent,
                        modifier =
                            Modifier
                                .align(Alignment.BottomCenter)
                                .fillMaxWidth(),
                    )
                }
            }

            WebView(
                state = state,
                navigator = navigator,
                modifier =
                    Modifier
                        .run {
                            val then =
                                if (applyStatusBarPadding && !titleVisible) {
                                    statusBarsPadding()
                                } else {
                                    this
                                }
                            if (applyNavigationBarPadding) {
                                then.navigationBarsPadding()
                            } else {
                                then
                            }
                        }
                        .fillMaxSize()
                        .alpha(0.99999f),
                onCreated = proxy.onCreated,
                onDispose = proxy.onDispose,
                client = proxy.client,
                chromeClient = proxy.chromeClient,
                factory = proxy.factory,
            )


        }
        val evaResourceFetcher by remember(Unit) {
            mutableStateOf(YYEVAResourceFetcher(mapOf("user" to SelfUser?.user)))
        }
        if (effectFile != null) {
            EffectVideoAnimationView(effectFile, effectFile, iEvaFetchResource = evaResourceFetcher, onComplete = {
                effectFile = null
            })
        }
    }
}

private fun getWrapImageUrl(url: String): String {
    val head =
        """
        <head>
        <style>
        *{
        margin:0px;
        }
        </style>
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        </head>
        """.trimIndent()
    val imageUrl =
        "<html>$head<body><img src='$url' style='width:100%;max-width:100%;overflow:hidden;object-fit:contain;'/></body></html>"
    return imageUrl
}

@Composable
fun WebViewDialogContent(
    webFrameInfo: WebFrameInfo,
    onOpenPage: (AppNavKey) -> Unit,
    onDismiss: () -> Unit,
) {
    val url = formatUrl(webFrameInfo.url)
    WebViewContent(
        url
            .let {
                if (Const.WEB_DEBUG) {
                    it.replace("https://apitest.wakooclub.com/", Const.LOCAL_WEB_URL)
                } else {
                    it
                }
            },
        modifier =
            Modifier
                .then(if (webFrameInfo.width == -1) Modifier.fillMaxWidth() else Modifier.width(webFrameInfo.width.dp))
                .then(if (webFrameInfo.height == -1) Modifier.fillMaxHeight() else Modifier.height(webFrameInfo.height.dp))
                .then(
                    webFrameInfo.radius?.let {
                        Modifier.clip(
                            RoundedCornerShape(
                                topStart = it.leftTop.dp,
                                topEnd = it.rightTop.dp,
                                bottomEnd = it.rightBottom.dp,
                                bottomStart = it.leftBottom.dp,
                            ),
                        )
                    } ?: Modifier,
                ),
        onOpenPage,
        onDismiss,
    )
}
