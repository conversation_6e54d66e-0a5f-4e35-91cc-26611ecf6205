package com.buque.wakoo.ui.widget

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun VoiceWaves(contentColor: Color? = null) {
    val color = contentColor ?: MaterialTheme.colorScheme.primary
    val mod =
        remember {
            Modifier
                .width(2.dp)
                .background(color, RoundedCornerShape(1.dp))
        }
    val trans = rememberInfiniteTransition(label = "wave")
    val fraction by trans.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        infiniteRepeatable(animation = tween(400, easing = FastOutLinearInEasing), repeatMode = RepeatMode.Reverse),
        label = "tr",
    )
    Row(modifier = Modifier.size(10.dp), horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.Bottom) {
        Box(modifier = mod.height(calcHeight(6, 4, fraction)))
        Box(modifier = mod.height(calcHeight(10, 4, fraction)))
        Box(modifier = mod.height(calcHeight(8, 8, fraction)))
    }
}

private fun calcHeight(
    start: Int,
    range: Int,
    fraction: Float,
): Dp {
    val t = start + range * fraction
    return if (t > 10) (10 - (t - 10)).dp else t.dp
}
