package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.VoicePublish: ImageVector
    get() {
        if (_VoicePublish != null) {
            return _VoicePublish!!
        }
        _VoicePublish = ImageVector.Builder(
            name = "VoicePublish",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(20f)
                    verticalLineToRelative(20f)
                    horizontalLineToRelative(-20f)
                    close()
                }
            ) {
            }
            group(
                clipPathData = PathData {
                    moveTo(1f, 1f)
                    horizontalLineToRelative(18f)
                    verticalLineToRelative(18f)
                    horizontalLineToRelative(-18f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFFFFFFFF))) {
                    moveTo(17.356f, 11.472f)
                    curveTo(16.672f, 14.909f, 13.637f, 17.5f, 10f, 17.5f)
                    curveTo(6.363f, 17.5f, 3.328f, 14.909f, 2.644f, 11.472f)
                    lineTo(4.115f, 11.177f)
                    curveTo(4.389f, 12.536f, 5.125f, 13.759f, 6.197f, 14.638f)
                    curveTo(7.27f, 15.516f, 8.614f, 15.996f, 10f, 15.996f)
                    curveTo(11.386f, 15.996f, 12.73f, 15.516f, 13.803f, 14.638f)
                    curveTo(14.875f, 13.759f, 15.611f, 12.536f, 15.885f, 11.177f)
                    lineTo(17.356f, 11.472f)
                    close()
                    moveTo(10f, 1.75f)
                    curveTo(10.173f, 1.75f, 10.345f, 1.762f, 10.516f, 1.786f)
                    curveTo(10.186f, 2.453f, 10f, 3.205f, 10f, 4f)
                    curveTo(10f, 6.33f, 11.593f, 8.288f, 13.75f, 8.843f)
                    verticalLineTo(10f)
                    curveTo(13.75f, 10.995f, 13.355f, 11.948f, 12.651f, 12.651f)
                    curveTo(11.948f, 13.355f, 10.995f, 13.75f, 10f, 13.75f)
                    curveTo(9.005f, 13.75f, 8.052f, 13.355f, 7.349f, 12.651f)
                    curveTo(6.645f, 11.948f, 6.25f, 10.995f, 6.25f, 10f)
                    verticalLineTo(5.5f)
                    curveTo(6.25f, 4.505f, 6.645f, 3.552f, 7.349f, 2.849f)
                    curveTo(8.052f, 2.145f, 9.005f, 1.75f, 10f, 1.75f)
                    close()
                }
                path(fill = SolidColor(Color(0xFFFFFFFF))) {
                    moveTo(15.546f, 3.246f)
                    horizontalLineTo(17.8f)
                    verticalLineTo(4.746f)
                    horizontalLineTo(15.546f)
                    verticalLineTo(7f)
                    horizontalLineTo(14.046f)
                    verticalLineTo(4.746f)
                    horizontalLineTo(11.8f)
                    verticalLineTo(3.246f)
                    horizontalLineTo(14.046f)
                    verticalLineTo(1f)
                    horizontalLineTo(15.546f)
                    verticalLineTo(3.246f)
                    close()
                }
            }
        }.build()

        return _VoicePublish!!
    }

@Suppress("ObjectPropertyName")
private var _VoicePublish: ImageVector? = null
