package com.buque.wakoo.ui.floating

import android.text.SpannedString
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.AwaitContinuation
import com.buque.wakoo.ext.click
import com.buque.wakoo.im_business.rtm.DynamicBannerInfo
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.richtext.EntryRichText
import com.buque.wakoo.ui.widget.richtext.toAnnotatedString
import com.buque.wakoo.utils.eventBus.tryToLink
import java.util.UUID

@Stable
abstract class AbsFloatingBanner(
    open val duration: Int = 8,
    open val priority: Int = 0,
    val uuid: String = UUID.randomUUID().toString(),
) {
    private val taskAwait: AwaitContinuation by lazy {
        AwaitContinuation()
    }

    @Composable
    abstract fun Render()

    suspend fun await() {
        taskAwait.suspendUntilWithTimeout(10_000L) // 默认最长动画5秒
    }

    fun resume() {
        taskAwait.resume(Unit)
    }
}

//region 通用飘屏
@Stable
data class FloatingBannerTask(
    val info: DynamicBannerInfo,
    val link: Pair<String, String>? = null,
) : AbsFloatingBanner(info.duration, info.priority) {
    @Composable
    override fun Render() {
        val aspectRatio =
            info.background?.let { it.width / it.height.toFloat() } ?: 4.9f

        Box(
            modifier =
                Modifier
                    .fillMaxWidth(0.8f)
                    .aspectRatio(aspectRatio),
        ) {
            info.background?.image?.let {
                NetworkImage(
                    it,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                )
            }

            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy((-4).dp),
                ) {
                    info.avatars.forEach {
                        NetworkImage(
                            it,
                            modifier =
                                Modifier
                                    .size(24.dp)
                                    .clip(CircleShape),
                        )
                    }
                }
                EntryRichText(
                    info.richText,
                    modifier =
                        Modifier
                            .weight(1f)
                            .basicMarquee(),
                )

                if (link != null) {
                    NetworkImage(
                        link.first,
                        modifier =
                            Modifier
                                .padding(start = 8.dp)
                                .size(48.dp, 20.dp)
                                .click(noEffect = true) {
                                    link.second.tryToLink()
                                },
                        contentScale = ContentScale.Fit,
                    )
                }
                SizeWidth(4.dp)
            }
        }
    }
}

@Composable
@Preview
fun PreviewCommonFloatingBox(modifier: Modifier = Modifier) {
    val bean =
        AppJson.decodeFromString<DynamicBannerInfo>(
            "{\"avatars\":[\"https://s.test.wakooclub.com/aaceIj\",\"https://s.test.wakooclub.com/aaceK9\"],\"rich_text\":[{\"type\":1,\"rich_text\":\"<font color='#FFD362'>&#8201;hh&#8201;</font>\",\"icon\":\"\",\"width\":0,\"height\":0,\"client_jump_link\":\"\",\"android_jump_link\":\"\",\"client_action_type\":\"\",\"border_radius\":0},{\"type\":1,\"rich_text\":\"<font color='#FFFFFF'>赠送</font>\",\"icon\":\"\",\"width\":0,\"height\":0,\"client_jump_link\":\"\",\"android_jump_link\":\"\",\"client_action_type\":\"\",\"border_radius\":0},{\"type\":1,\"rich_text\":\"<font color='#FFD362'>&#8201;华语区0000&#8201;</font>\",\"icon\":\"\",\"width\":0,\"height\":0,\"client_jump_link\":\"\",\"android_jump_link\":\"\",\"client_action_type\":\"\",\"border_radius\":0},{\"type\":1,\"rich_text\":\"<font color='#FFFFFF'>1个</font>\",\"icon\":\"\",\"width\":0,\"height\":0,\"client_jump_link\":\"\",\"android_jump_link\":\"\",\"client_action_type\":\"\",\"border_radius\":0},{\"type\":1,\"rich_text\":\"<font color='#FFD362'>&#8201;恋雨涟踪&#8201;</font>\",\"icon\":\"\",\"width\":0,\"height\":0,\"client_jump_link\":\"\",\"android_jump_link\":\"\",\"client_action_type\":\"\",\"border_radius\":0}],\"background\":{\"image\":\"https://media.wakooclub.com/opsite/floatbanner/bgimg/opa_float_screen2.png\",\"width\":327,\"height\":40},\"business_code\":\"0201000\",\"duration\":8,\"priority\":5200}",
        )
    FloatingBannerTask(bean, "www" to "wakoo://page/main").Render()
}
//endregion

//region CP官宣飘屏
@Stable
data class CPMadeFloatingBannerTask(
    val avatars: List<String> = emptyList(),
    val content: SpannedString,
    val bgImg: String,
    val link: Pair<String, String>? = null,
    override val priority: Int,
) : AbsFloatingBanner() {
    @Composable
    override fun Render() {
        val aspectRatio = 6.3f // 别问我怎么算出来的, 直接请求这个url,拿到图片宽除以高

        Box(
            modifier =
                Modifier
                    .fillMaxWidth(0.8f)
                    .aspectRatio(aspectRatio),
            contentAlignment = Alignment.Center,
        ) {
            NetworkImage(
                bgImg,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth,
            )
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy((-4).dp),
                ) {
                    avatars.forEach {
                        NetworkImage(
                            it,
                            modifier =
                                Modifier
                                    .size(24.dp)
                                    .clip(CircleShape),
                        )
                    }
                }
                Text(
                    content.toAnnotatedString(),
                    modifier =
                        Modifier
                            .weight(1f)
                            .basicMarquee(),
                )

                if (link != null) {
                    NetworkImage(
                        link.first,
                        modifier =
                            Modifier
                                .padding(start = 8.dp)
                                .size(48.dp, 20.dp)
                                .click(noEffect = true) {
                                    link.second.tryToLink()
                                },
                        contentScale = ContentScale.Fit,
                    )
                }
                SizeWidth(4.dp)
            }
        }
    }
}

//endregion
