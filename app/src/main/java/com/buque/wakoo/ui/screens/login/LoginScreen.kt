package com.buque.wakoo.ui.screens.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withLink
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import com.buque.wakoo.R
import com.buque.wakoo.manager.EnvironmentManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.Google
import com.buque.wakoo.ui.icons.MobilePhone
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooBlack
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.Weight

@Composable
fun LoginScreen(
    onQuickLogin: () -> Unit = {},
    onGoogleLogin: () -> Unit = {},
    onPhoneLogin: () -> Unit = {},
    onUserAgreementClick: () -> Unit = {},
    onPrivacyPolicyClick: () -> Unit = {},
) {
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .background(Color(0xFFF7F7F7)),
    ) {
        // 背景渐变效果
        Image(
            modifier =
                Modifier
                    .fillMaxWidth(),
            contentDescription = null,
            painter = painterResource(R.drawable.bg_login_top),
            contentScale = ContentScale.FillWidth,
        )

        // 登录按钮
        Column(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 40.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            // 快捷登录按钮
            LoginButton(
                text = "快捷登录".localized,
                backgroundColor = WakooBlack,
                textColor = WakooWhite,
                onClick = onQuickLogin,
            )

            if (EnvironmentManager.isProdRelease) {
                // Google登录按钮
                LoginButtonWithIcon(
                    text = "Google登录".localized,
                    icon = {
                        Image(
                            imageVector = WakooIcons.Google,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                        )
                    },
                    onClick = onGoogleLogin,
                )
            }

            if (!EnvironmentManager.isGoogleChannel) {
                // 手机号登录按钮
                LoginButtonWithIcon(
                    text = "手机号登录".localized,
                    icon = {
                        Image(
                            imageVector = WakooIcons.MobilePhone,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                        )
                    },
                    onClick = onPhoneLogin,
                )
            }
        }

        Weight()

        val annotatedText =
            buildAnnotatedString {
                append("登录代表已同意".localized)

                withLink(
                    LinkAnnotation.Clickable(
                        tag = "USER_AGREEMENT",
                        styles =
                            TextLinkStyles(
                                style =
                                    SpanStyle(
                                        color = Color(0xFF111111),
                                        fontWeight = FontWeight.Medium,
                                    ),
                            ),
                        linkInteractionListener = {
                            onUserAgreementClick()
                        },
                    ),
                ) {
                    append("《用户服务协议》".localized)
                }

                append("和".localized)

                withLink(
                    LinkAnnotation.Clickable(
                        tag = "PRIVACY_POLICY",
                        styles =
                            TextLinkStyles(
                                style =
                                    SpanStyle(
                                        color = Color(0xFF111111),
                                        fontWeight = FontWeight.Medium,
                                    ),
                            ),
                        linkInteractionListener = {
                            onPrivacyPolicyClick()
                        },
                    ),
                ) {
                    append("《隐私协议》".localized)
                }
            }

        val density = LocalDensity.current
        val navigationBars = WindowInsets.navigationBars
        val bottomPadding =
            navigationBars
                .asPaddingValues(density)
                .calculateBottomPadding()
                .plus(20.dp)
                .coerceAtLeast(44.dp)

        Text(
            text = annotatedText,
            style = MaterialTheme.typography.labelLarge,
            modifier =
                Modifier
                    .padding(bottom = bottomPadding)
                    .fillMaxWidth(),
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun LoginButton(
    text: String,
    backgroundColor: Color,
    textColor: Color,
    onClick: () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(44.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(backgroundColor)
                .clickable(onClick = onClick),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            color = textColor,
            style = MaterialTheme.typography.labelLarge,
            fontWeight = FontWeight.Medium,
        )
    }
}

@Composable
private fun LoginButtonWithIcon(
    text: String,
    icon: @Composable () -> Unit,
    onClick: () -> Unit,
) {
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(44.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(WakooWhite)
                .border(
                    width = 0.5.dp,
                    color = Color(0xFF999999),
                    shape = RoundedCornerShape(12.dp),
                ).clickable(onClick = onClick),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            color = WakooBlack,
            style = MaterialTheme.typography.labelLarge,
            fontWeight = FontWeight.Medium,
        )
        Box(
            modifier =
                Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 12.dp)
                    .size(20.dp),
            contentAlignment = Alignment.Center,
        ) {
            icon()
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun LoginScreenPreview() {
    WakooTheme {
        LoginScreen()
    }
}
