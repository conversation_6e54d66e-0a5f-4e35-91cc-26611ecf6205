package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.UserRelations
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ext.toastWhenError
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.RelationsKey
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.network.api.service.VoiceRoomApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.UserRelationsViewModel
import kotlinx.coroutines.launch

private val TABS: List<RelationsKey.TabContent> =
    listOf(
        RelationsKey.RecentlyChat, // 聊天
        RelationsKey.Following, // 关注
        RelationsKey.Followers, // 粉丝
    )

/**
 * 选择用户并进行操作
 *
 * @param sceneID 场景id(语音房id/群组id)
 * @param sceneType 场景类型
 * 2 私聊
 *
 * 1 部落/家族
 * 3 群聊( ps: 不会有群聊,因为这是选择用户)
 */
@Composable
fun SelectUserScreen(route: Route.SelectUser) {
    val sceneType = route.sceneType
    val sceneID = route.sceneID
    val title = route.title
    val buttonText = route.buttonText
    // Tab选项卡
    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState(initialPage = 0, pageCount = { TABS.size })
    val selectedTabIndex = pagerState.currentPage
    val lm = LocalLoadingManager.current

    val onAction =
        remember<(user: User) -> Unit> {
            { user ->
                scope.launch {
                    when (route.selectFor) {
                        Route.SelectUser.SELECT_FOR_SHARE_ROOM -> {
                            when (sceneType) {
                                2 -> {
                                    executeApiCallExpectingData {
                                        VoiceRoomApiService.instance.shareVoiceRoom(
                                            buildMap {
                                                put("share_type", sceneType.toString())
                                                put("live_house_id", sceneID)
                                                put("target_user_id", user.id)
                                            },
                                        )
                                    }.onSuccess {
                                        showToast("分享成功".localized)
                                    }
                                }

                                else -> {}
                            }
                        }

                        Route.SelectUser.SELECT_FOR_INVITE_GROUP -> {
                            lm.show(scope) {
                                GlobalRepository.chatGroupRepo
                                    .inviteJoinGroup(sceneType, user.id)
                                    .onSuccess {
                                        showToast("邀请成功".localized)
                                    }.toastWhenError()
                            }
                        }

                        else -> {}
                    }
                }
            }
        }

    SegColorTitleScreenScaffold(title, contentColor = Color.White) { pv ->
        Column(modifier = Modifier.padding(pv)) {
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier =
                    Modifier
                        .background(Color.White)
                        .padding(
                            horizontal = 30.dp,
                        ),
                divider = {},
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Box(
                            modifier =
                                Modifier
                                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                    .requiredWidth(12.dp)
                                    .height(3.dp)
                                    .background(
                                        WakooSecondarySelected,
                                        CircleShape,
                                    ),
                        )
                    }
                },
            ) {
                TABS.forEachIndexed { index, tab ->
                    Tab(
                        selected = selectedTabIndex == index,
                        selectedContentColor = WakooSecondarySelected,
                        unselectedContentColor = WakooSecondaryUnSelected,
                        onClick = {
                            scope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        content = {
                            Box(modifier = Modifier.padding(bottom = 4.dp)) {
                                tab.TabContent(selectedTabIndex == index)
                            }
                        },
                    )
                }
            }
            HorizontalPager(
                state = pagerState,
                modifier =
                    Modifier
                        .fillMaxSize(),
                beyondViewportPageCount = 1,
            ) {
                // 内容区域
                if (it == 0) {
                    val listState = rememberLazyListState()
                    val list by AppConversationManger.c2cConvFlow.collectAsStateWithLifecycle()
                    LazyColumn(
                        modifier =
                            Modifier
                                .fillMaxSize()
                                .background(Color.White),
                        state = listState,
                    ) {
                        items(list) { item ->
                            UserListItem(
                                user = item.user,
                                modifier = Modifier.padding(16.dp),
                            ) {
                                UserItemActionBox(item.user, buttonText, onAction = onAction)
                            }
                        }
                    }
                } else {
                    val viewModel = viewModel<UserRelationsViewModel>()
                    val listState = rememberLazyListState()

                    CStateListPaginateLayout<String, Int, RelationsKey, UserRelations, UserRelationsViewModel>(
                        reqKey = SelfUser?.id ?: "",
                        tabKey = TABS[it],
                        modifier = Modifier.background(Color.White),
                        listState = listState,
                        viewModel = viewModel,
                        emptyText = if (TABS[it] is RelationsKey.Followers) "暂无粉丝".localized else "暂无关注".localized,
                    ) { paginateState, list ->
                        LazyColumn(modifier = Modifier.fillMaxSize(), state = listState) {
                            items(list) { item ->
                                UserListItem(
                                    user = item.user,
                                    modifier = Modifier.padding(16.dp),
                                ) {
                                    UserItemActionBox(item.user, buttonText, onAction = onAction)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun UserItemActionBox(
    user: User,
    buttonText: String,
    onAction: (User) -> Unit,
) {
    SolidButton(
        text = buttonText,
        onClick = {
            onAction(user)
        },
        height = 32.dp,
        textColor = Color(0xFF111111),
        backgroundColor = Color(0xFF66FE6B),
        fontSize = 14.sp,
        config = ButtonStyles.Solid.copy(minWidth = 72.dp),
        paddingValues = PaddingValues(horizontal = 12.dp),
    )
}
