package com.buque.wakoo.ui.widget.media.manager

import androidx.media3.common.util.Log
import androidx.media3.common.util.UnstableApi


@androidx.annotation.OptIn(UnstableApi::class)
class MediaPlayerControllerPool(private val maxSize: Int, private val factory: MediaPlayerController.Factory) {

    private val controllerQueue = ArrayDeque<MediaPlayerController>(maxSize)

    fun acquire(): MediaPlayerController {
        var controller = controllerQueue.removeFirstOrNull()
        if (controller == null) {
            controller = factory.create()
        }
        return controller
    }

    fun release(controller: MediaPlayerController) {
        if (controllerQueue.size < maxSize) {
            controller.clear()
            controllerQueue.addLast(controller)
        } else {
            // 如果池已满，则彻底释放播放器
            controller.release()
        }
    }

    fun cleanUp() {
        for (player in controllerQueue) {
            player.release()
        }
        controllerQueue.clear()
    }
}
