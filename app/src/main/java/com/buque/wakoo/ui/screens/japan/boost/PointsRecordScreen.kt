package com.buque.wakoo.ui.screens.japan.boost

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.boost.PointsRecordItem
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.ApiClient
import com.buque.wakoo.network.api.service.BoostApi
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.ui.theme.WakooSecondaryText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.pagination.PaginateState
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.StateListPaginateLayout
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.viewmodel.BasicListPaginateViewModel
import kotlinx.serialization.json.decodeFromJsonElement

// account type
//ja diamond 21
//ja points 22

class PointsRecordViewModel(val type: Int = 22) : BasicListPaginateViewModel<Int, PointsRecordItem>() {

    private val api = ApiClient.createuserApiService<BoostApi>()

    override suspend fun loadData(
        pageKey: Int,
        pageSize: Int
    ): Result<List<PointsRecordItem>> {
        return executeApiCallExpectingData { api.getPRecords(type, pageKey.takeIf { it != 0 }) }
            .map {
                it.getOrNull("records")?.let { el ->
                    AppJson.decodeFromJsonElement<List<PointsRecordItem>>(el)
                }.orEmpty()
            }
    }

    override fun getFirstPageKey(dataKey: Any): Int = 0

    override fun getNextPageKey(
        cState: CState<List<PointsRecordItem>>,
        dataKey: Any,
        pageKey: Int?
    ): Int {
        return cState.dataOrNull?.lastOrNull()?.id ?: -1
    }

    override fun getDistinctSelector(): (PointsRecordItem) -> String {
        return {
            it.id.toString()
        }
    }

}

@Composable
fun PointsRecordScreen(title: String = "积分明细记录".localized, type: Int = 22) {
    TitleScreenScaffold(title) {
        StateListPaginateLayout<Int, PointsRecordItem, PointsRecordViewModel>(
            modifier = Modifier.padding(it),
            viewModel = viewModel(initializer = {
                PointsRecordViewModel(type)
            })
        ) { p1: PaginateState<Int>, p2: List<PointsRecordItem> ->
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
                    .navigationBarsPadding()
            ) {
                items(p2) { item ->
                    Item(item, if (type == 22) R.drawable.ic_integral else R.drawable.ic_green_diamond_straight)
                    HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFE5E5E5))
                }
            }
        }
    }
}

@Composable
private fun Item(data: PointsRecordItem, icon: Int = R.drawable.ic_integral) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 20.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(data.reason, color = WakooText, modifier = Modifier.fillMaxWidth())
            SizeHeight(8.dp)
            Text(data.formatTimeStr, color = WakooSecondaryText, fontSize = 12.sp)
        }
        Text(data.changeValue, color = WakooText, fontSize = 16.sp)
        SizeWidth(4.dp)
        Image(painter = painterResource(icon), contentDescription = "c", modifier = Modifier.size(20.dp))
    }
}
