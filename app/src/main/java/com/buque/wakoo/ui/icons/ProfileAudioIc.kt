package com.buque.wakoo.ui.icons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.ProfileAudioIc: ImageVector
    get() {
        if (_ProfileAudioIc != null) {
            return _ProfileAudioIc!!
        }
        _ProfileAudioIc =
            ImageVector
                .Builder(
                    name = "ProfileAudioIc",
                    defaultWidth = 32.dp,
                    defaultHeight = 32.dp,
                    viewportWidth = 32f,
                    viewportHeight = 32f,
                ).apply {
                    path(
                        fill = SolidColor(Color(0xFF111111)),
                        stroke = SolidColor(Color(0xFFFFFFFF)),
                        strokeLineWidth = 2f,
                    ) {
                        moveTo(16f, 4f)
                        curveTo(22.627f, 4f, 28f, 9.373f, 28f, 16f)
                        curveTo(28f, 22.627f, 22.627f, 28f, 16f, 28f)
                        curveTo(9.373f, 28f, 4f, 22.627f, 4f, 16f)
                        curveTo(4f, 9.373f, 9.373f, 4f, 16f, 4f)
                        close()
                    }
                    group(
                        clipPathData =
                            PathData {
                                moveTo(9f, 9f)
                                horizontalLineToRelative(14f)
                                verticalLineToRelative(14f)
                                horizontalLineToRelative(-14f)
                                close()
                            },
                    ) {
                        path(
                            fill =
                                Brush.linearGradient(
                                    colorStops =
                                        arrayOf(
                                            0f to Color(0xFFA3FF2C),
                                            1f to Color(0xFF31FFA1),
                                        ),
                                    start = Offset(10.782f, 15.708f),
                                    end = Offset(23f, 15.708f),
                                ),
                        ) {
                            moveTo(20.939f, 13.488f)
                            lineTo(21.083f, 13.158f)
                            curveTo(21.34f, 12.57f, 21.801f, 12.101f, 22.378f, 11.845f)
                            lineTo(22.821f, 11.648f)
                            curveTo(23.06f, 11.541f, 23.06f, 11.193f, 22.821f, 11.086f)
                            lineTo(22.402f, 10.9f)
                            curveTo(21.811f, 10.637f, 21.341f, 10.151f, 21.089f, 9.543f)
                            lineTo(20.941f, 9.186f)
                            curveTo(20.839f, 8.938f, 20.495f, 8.938f, 20.392f, 9.186f)
                            lineTo(20.245f, 9.543f)
                            curveTo(19.993f, 10.151f, 19.523f, 10.637f, 18.932f, 10.9f)
                            lineTo(18.513f, 11.086f)
                            curveTo(18.274f, 11.193f, 18.274f, 11.541f, 18.513f, 11.648f)
                            lineTo(18.956f, 11.845f)
                            curveTo(19.533f, 12.101f, 19.994f, 12.57f, 20.251f, 13.158f)
                            lineTo(20.395f, 13.488f)
                            curveTo(20.5f, 13.73f, 20.834f, 13.73f, 20.939f, 13.488f)
                            close()
                            moveTo(17.393f, 12.114f)
                            curveTo(17.553f, 12.367f, 17.771f, 12.554f, 18.049f, 12.675f)
                            lineTo(18.378f, 12.819f)
                            curveTo(18.6f, 12.916f, 18.779f, 13.051f, 18.917f, 13.226f)
                            verticalLineTo(14.833f)
                            curveTo(18.917f, 16.444f, 17.611f, 17.75f, 16f, 17.75f)
                            curveTo(14.389f, 17.75f, 13.083f, 16.444f, 13.083f, 14.833f)
                            verticalLineTo(12.5f)
                            curveTo(13.083f, 10.889f, 14.389f, 9.583f, 16f, 9.583f)
                            curveTo(16.656f, 9.583f, 17.261f, 9.8f, 17.748f, 10.165f)
                            curveTo(17.608f, 10.27f, 17.49f, 10.399f, 17.393f, 10.552f)
                            curveTo(17.242f, 10.79f, 17.167f, 11.051f, 17.167f, 11.333f)
                            curveTo(17.167f, 11.616f, 17.242f, 11.876f, 17.393f, 12.114f)
                            close()
                            moveTo(10.782f, 15.417f)
                            horizontalLineTo(11.958f)
                            curveTo(12.241f, 17.396f, 13.943f, 18.917f, 16f, 18.917f)
                            curveTo(18.057f, 18.917f, 19.759f, 17.396f, 20.042f, 15.417f)
                            horizontalLineTo(21.218f)
                            curveTo(20.949f, 17.85f, 19.017f, 19.782f, 16.583f, 20.051f)
                            verticalLineTo(22.417f)
                            horizontalLineTo(15.417f)
                            verticalLineTo(20.051f)
                            curveTo(12.983f, 19.782f, 11.051f, 17.85f, 10.782f, 15.417f)
                            close()
                        }
                    }
                }.build()

        return _ProfileAudioIc!!
    }

@Suppress("ObjectPropertyName")
private var _ProfileAudioIc: ImageVector? = null
