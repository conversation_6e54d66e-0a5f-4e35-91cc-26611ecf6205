package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.Alert: ImageVector
    get() {
        if (_Icon != null) {
            return _Icon!!
        }
        _Icon = ImageVector.Builder(
            name = "Icon",
            defaultWidth = 12.dp,
            defaultHeight = 12.dp,
            viewportWidth = 12f,
            viewportHeight = 12f
        ).apply {
            path(fill = SolidColor(Color(0xFFFFFFFF))) {
                moveTo(6f, 0f)
                curveTo(9.314f, 0f, 12f, 2.686f, 12f, 6f)
                curveTo(12f, 9.314f, 9.314f, 12f, 6f, 12f)
                curveTo(2.686f, 12f, 0f, 9.314f, 0f, 6f)
                curveTo(0f, 2.686f, 2.686f, 0f, 6f, 0f)
                close()
                moveTo(6f, 8.625f)
                curveTo(5.586f, 8.625f, 5.25f, 8.961f, 5.25f, 9.375f)
                curveTo(5.25f, 9.789f, 5.586f, 10.125f, 6f, 10.125f)
                curveTo(6.414f, 10.125f, 6.75f, 9.789f, 6.75f, 9.375f)
                curveTo(6.75f, 8.961f, 6.414f, 8.625f, 6f, 8.625f)
                close()
                moveTo(6f, 1.875f)
                curveTo(5.586f, 1.875f, 5.25f, 2.211f, 5.25f, 2.625f)
                verticalLineTo(7.125f)
                curveTo(5.25f, 7.539f, 5.586f, 7.875f, 6f, 7.875f)
                curveTo(6.414f, 7.875f, 6.75f, 7.539f, 6.75f, 7.125f)
                verticalLineTo(2.625f)
                curveTo(6.75f, 2.211f, 6.414f, 1.875f, 6f, 1.875f)
                close()
            }
        }.build()

        return _Icon!!
    }

@Suppress("ObjectPropertyName")
private var _Icon: ImageVector? = null
