package com.buque.wakoo.ui.screens.chatgroup.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import com.buque.wakoo.R
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.chatgroup.ChatGroupMember
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.ChatGroupRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.easyPostBottomPanel
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.navigation.rememberLauncherForResult
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.screens.chatgroup.panel.EditGroupInfoPanel
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.selector.MediaSelectorResult
import com.buque.wakoo.ui.widget.state.CState
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.ui.widget.state.dataOrNull
import com.buque.wakoo.utils.upload.CosTransferHelper
import com.buque.wakoo.utils.upload.TransferResult
import com.buque.wakoo.utils.upload.UploadUtils
import com.buque.wakoo.viewmodel.chatgroup.ChatGroupSettingsViewModel

@Composable
fun GroupSettingsScreen(
    chatGroupId: String,
    viewModel: ChatGroupSettingsViewModel,
) {
    LaunchedEffect(viewModel) {
        if (viewModel.state.value.dataOrNull == null) {
            viewModel.refreshState()
        }
    }
    val nav = LocalAppNavController.current
    val rootNav = LocalAppNavController.root
    val lm = LocalLoadingManager.current
    val scope = rememberCoroutineScope()

    val launcher =
        rootNav.rememberLauncherForResult<Route.MediaSelector, MediaSelectorResult> { result ->
            if (result.list.isNotEmpty()) {
                lm.show(scope) {
                    val uri = result.list.single().uriString.toUri()
                    val path =
                        UploadUtils.generateOSSPath(WakooApplication.instance, uri, UploadUtils.DEFAULT_AVATAR_PATH)
                    val cosTransferHelper = CosTransferHelper()
                    val currentTaskWrapper = cosTransferHelper.upload(cosPath = path, uri = uri)
                    val result = currentTaskWrapper.await()
                    when (result) {
                        is TransferResult.Failure -> {
                            showToast(result.exception.toString())
                        }

                        is TransferResult.Success -> {
                            viewModel
                                .update(avatar = result.url)
                                .onSuccess {
                                    showToast("修改成功".localized)
                                }
                        }
                    }
                }
            }
        }

    val cState = viewModel.state.value
    val dialogController = rememberDialogController()

    fun exitGroup(isOwner: Boolean) {
        lm.show(scope) {
            viewModel
                .exit(isOwner)
                .onSuccess {
                    if (isOwner) {
                        showToast("已成功解散群组".localized)
                    } else {
                        showToast("已退出群组".localized)
                    }
                    rootNav.pop()
                }
        }
    }

    GroupSettingsContent(cState, onChangeAvatar = {
        launcher.launch(Route.MediaSelector())
    }, onChangeAdmin = {
        nav.push(ChatGroupRoute.ChatGroupAdminSetting(chatGroupId))
    }, onKickMember = {
        val targetRoles =
            if (it.iAmOwner) {
                "${ChatGroupMember.ROLE_ADMIN},${ChatGroupMember.ROLE_COMMON}"
            } else {
                ChatGroupMember.ROLE_COMMON.toString()
            }
        nav.push(ChatGroupRoute.KickOutMember(it.id, targetRoles))
    }, onChangeName = { groupName ->
        dialogController.easyPostBottomPanel {
            EditGroupInfoPanel(
                title = "修改群组名称".localized,
                content = groupName,
                placeholder = "请输入群组名称".localized,
                maxLength = 20,
                textFieldHeight = 100.dp,
            ) { newName ->
                lm.show(scope) {
                    viewModel
                        .update(name = newName)
                        .onSuccess {
                            showToast("修改成功".localized)
                            dismiss()
                        }
                }
            }
        }
    }, onChangeBuilt = { text ->
        dialogController.easyPostBottomPanel {
            EditGroupInfoPanel(
                title = "修改群组简介".localized,
                content = text,
                placeholder = "请输入群组简介".localized,
                maxLength = 500,
                textFieldHeight = 168.dp,
            ) { newBulletin ->
                lm.show(scope) {
                    viewModel
                        .update(bulletin = newBulletin)
                        .onSuccess {
                            showToast("修改成功".localized)
                            dismiss()
                        }
                }
            }
        }
    }, onDisturbChange = { dis ->
        lm.show(scope) {
            viewModel
                .update(enableDontDisturb = dis)
        }
    }, onReport = {
        rootNav.push(Route.Report(7, viewModel.groupId))
    }, onExit = { iAmOwner ->
        dialogController.easyPost {
            val title = if (iAmOwner) "确定要解散群组吗".localized else "确定要退出群组吗".localized
            SimpleDoubleActionDialog(
                title,
                cancelButtonConfig = DialogButtonStyles.Primary.copy("确定".localized),
                confirmButtonConfig = DialogButtonStyles.Secondary.copy("取消".localized),
                onCancel = {
                    // 退出
                    dismiss()
                    exitGroup(iAmOwner)
                },
            ) {
                dismiss()
            }
        }
    })
}

@Composable
fun GroupSettingsContent(
    state: CState<ChatGroupBean>,
    onRefresh: OnAction = {},
    onChangeAvatar: OnAction = {},
    onChangeName: OnDataCallback<String> = {},
    onChangeBuilt: OnDataCallback<String> = {},
    onChangeAdmin: OnAction = {},
    onKickMember: OnDataCallback<ChatGroupBean> = {},
    onDisturbChange: (Boolean) -> Unit = {},
    onReport: OnAction = {},
    onExit: OnDataCallback<Boolean> = {},
) {
    TitleScreenScaffold("群组设置".localized, containerColor = Color.White) { pd ->
        CStateLayout(state, onRetry = onRefresh) { group ->
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(pd),
            ) {
                if (group.iAmOwner) {
                    SettingsRowWithImage(
                        "群组头像".localized,
                        group.avatarUrl,
                        onClick = onChangeAvatar,
                    )
                    HorizontalDivider(
                        color = Color(0xFFE0E0E0),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )
                    // 群组名称
                    SettingsRowWithText("群组名称".localized, value = group.name, onClick = {
                        onChangeName(group.name)
                    })

                    HorizontalDivider(
                        color = Color(0xFFE0E0E0),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )
                }

                if (group.iAmOwner || group.iAmAdmin) {
                    // 群组介绍
                    SettingsRowWithText(
                        title = "群组介绍".localized,
                        value = group.bulletin,
                        modifier = Modifier.fillMaxWidth(),
                        onClick = {
                            onChangeBuilt(group.bulletin)
                        },
                    )
                }

                if (group.iAmOwner) {
                    HorizontalDivider(
                        color = Color(0xFFE0E0E0),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )

                    // 管理员设置
                    SettingsRowWithText(
                        title = "管理员设置".localized,
                        value = "最多设置3名".localized,
                        modifier = Modifier.fillMaxWidth(),
                        onClick = onChangeAdmin,
                    )
                }

                if (group.iAmOwner || group.iAmAdmin) {
                    HorizontalDivider(
                        color = Color(0xFFE0E0E0),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )

                    // 踢出成员
                    SettingsRow(
                        title = "踢出成员".localized,
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        onKickMember(group)
                    }

                    HorizontalDivider(
                        color = Color(0xFFE0E0E0),
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(horizontal = 16.dp),
                    )
                }

                // 消息免打扰
                SettingsRowWithSwitch(
                    title = "消息免打扰".localized,
                    checked = group.iEnableDontDisturb,
                    onCheckedChange = onDisturbChange,
                    modifier = Modifier.fillMaxWidth(),
                )

                HorizontalDivider(
                    color = Color(0xFFE0E0E0),
                    thickness = 0.5.dp,
                    modifier = Modifier.padding(horizontal = 16.dp),
                )

                // 举报
                SettingsRow(
                    title = "举报".localized,
                    modifier = Modifier.fillMaxWidth(),
                    onClick = onReport,
                )

                Spacer(modifier = Modifier.weight(1f)) // 将按钮推到底部

                // 解散群组按钮
                Button(
                    onClick = {
                        onExit(group.iAmOwner)
                    },
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 30.dp, vertical = 20.dp)
                            .height(48.dp),
                    shape = RoundedCornerShape(24.dp), // 圆角
                    colors =
                        ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFE9EAEF), // 按钮背景色
                            contentColor = Color(0xFF999999), // 按钮文字颜色
                        ),
                    elevation = ButtonDefaults.buttonElevation(defaultElevation = 0.dp), // 取消阴影
                ) {
                    Text(
                        text = if (group.iAmOwner) "解散群组".localized else "退出群组".localized,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                    )
                }
            }
        }
    }
}

@Composable
fun SettingsRow(
    title: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Row(
        modifier =
            modifier
                .background(Color.White)
                .height(56.dp)
                .clickable(onClick = onClick)
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            color = Color(0xFF111111),
        )
        Spacer(modifier = Modifier.weight(1f))
        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = Color(0xFF999999), // 箭头颜色
        )
    }
}

@Composable
fun SettingsRowWithText(
    title: String,
    value: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Row(
        modifier =
            modifier
                .background(Color.White)
                .height(56.dp)
                .clickable(onClick = onClick)
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            color = Color.Black,
        )
        Spacer(modifier = Modifier.weight(1f))
        Text(
            text = value,
            fontSize = 14.sp,
            color = Color(0xFF999999),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.widthIn(max = 180.dp), // 限制文字宽度防止过长
        )
        Spacer(modifier = Modifier.width(8.dp))
        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = Color(0xFF999999),
        )
    }
}

@Composable
fun SettingsRowWithImage(
    title: String,
    imageUrl: String, // 使用Int作为drawable资源ID
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Row(
        modifier =
            modifier
                .background(Color.White)
                .height(72.dp) // 为头像留出更多高度
                .clickable(onClick = onClick)
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            color = Color.Black,
        )
        Spacer(modifier = Modifier.weight(1f))
        // 使用NetworkImage显示头像
        NetworkImage(
            imageUrl,
            modifier =
                Modifier
                    .size(40.dp)
                    .clip(CircleShape),
        )
        Spacer(modifier = Modifier.width(8.dp))
        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = Color(0xFF999999),
        )
    }
}

@Composable
fun SettingsRowWithSwitch(
    title: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier =
            modifier
                .background(Color.White)
                .height(56.dp)
                .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            color = Color.Black,
        )
        Spacer(modifier = Modifier.weight(1f))
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            modifier = Modifier.size(32.dp, 16.dp),
            colors =
                SwitchDefaults.colors(
                    checkedThumbColor = Color.White,
                    checkedTrackColor = Color(0xFF38DD7A), // 选中时的绿色
                    uncheckedThumbColor = Color.White,
                    uncheckedTrackColor = Color(0xFFCCCCCC), // 未选中时的灰色
                ),
        )
    }
}

@Preview(showBackground = true, widthDp = 375) // 预览尺寸设置为375dp以模拟设计稿
@Composable
fun DefaultPreview0() {
    WakooTheme {
        val group =
            ChatGroupBean(
                "123456",
                relationWithMe = ChatGroupMember.ROLE_OWNER,
                name = "群聊名称哈哈哈",
                bulletin = "介绍介绍介绍介绍介绍介绍介...",
                iEnableDontDisturb = true,
            )
        GroupSettingsContent(CState.Success(group))
    }
}

@Preview(showBackground = true, widthDp = 375) // 预览尺寸设置为375dp以模拟设计稿
@Composable
fun DefaultPreview1() {
    WakooTheme {
        val group =
            ChatGroupBean(
                "123456",
                name = "群聊名称哈哈哈",
                iAmAdmin = true,
                bulletin = "介绍介绍介绍介绍介绍介绍介...",
            )
        GroupSettingsContent(CState.Success(group))
    }
}

@Preview(showBackground = true, widthDp = 375) // 预览尺寸设置为375dp以模拟设计稿
@Composable
fun DefaultPreview() {
    WakooTheme {
        val group = ChatGroupBean("123456", name = "群聊名称哈哈哈", bulletin = "介绍介绍介绍介绍介绍介绍介...")
        GroupSettingsContent(CState.Success(group))
    }
}
