package com.buque.wakoo.ui.screens.profile

import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AudioRecordManager
import com.buque.wakoo.manager.RecordingState
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.network.api.bean.VoicePublishConfig
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.screens.voice.RecordingSection
import com.buque.wakoo.ui.screens.voice.VoiceState
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.PermissionUtils
import com.buque.wakoo.viewmodel.ProfileVoiceEditViewModel
import com.buque.wakoo.ui.widget.AutoSizeText
import kotlin.math.roundToInt

@Composable
fun ProfileVoiceEditScreen(modifier: Modifier = Modifier) {
    val viewModel = viewModel<ProfileVoiceEditViewModel>()

    // 当从返回栈退出的时候才清空录音缓存
    DisposableEffect(Unit) {
        onDispose {
            MediaPlayerManager.releaseIf {
                it.tag.startsWith("profile-publish-voice-")
            }
            AudioRecordManager.singletonInstance.cleanup()
        }
    }

    val scope = rememberCoroutineScope()
    val loadingManager = LocalLoadingManager.current
    val rootNavController = LocalAppNavController.root

    //region 录音相关

    val audioRecordManager = viewModel.audioRecordManager

    // 监听录音管理器的状态变化
    val recordingState by audioRecordManager.recordingState.collectAsStateWithLifecycle()
    // 主要内容区域
    val audioLauncher =
        rememberLauncherForActivityResult(PermissionUtils.Contracts) { granted ->
            if (granted) {
                audioRecordManager.startRecording(
                    maxDurationMillis = 15 * 1000L,
                    minDurationMillis = 5 * 1000L,
                )
            } else {
                showToast("无法获取录音权限，请到系统设置中手动授予录音权限".localized)
            }
        }

    val voiceState by remember {
        derivedStateOf {
            val rState = recordingState
            when (rState) {
                is RecordingState.Recording -> {
                    VoiceState.Recording(rState)
                }

                is RecordingState.Completed -> {
                    VoiceState.Playing(
                        playItem =
                            PlayMediaItem.prefixTagAudio(
                                url = rState.finalOutputFile.absolutePath,
                                prefix = "publish-voice-",
                            ),
                        recorder = rState,
                    )
                }

                else -> {
                    VoiceState.Idle
                }
            }
        }
    }
    //endregion

    SegColorTitleScreenScaffold(title = "我的声音名片".localized) { pv ->
        Column(
            modifier =
                Modifier
                    .background(
                        color = Color.White,
                    ).padding(pv)
                    .fillMaxSize(),
        ) {
            Column(
                modifier =
                    Modifier
                        .padding(horizontal = 16.dp)
                        .padding(top = 20.dp)
                        .fillMaxWidth()
                        .background(
                            brush = Brush.linearGradient(listOf(Color(0xffa0ff65), Color(0xff47ff8a))),
                            shape = RoundedCornerShape(18.dp),
                        ).border(6.dp, Color(0xb2ffffff), RoundedCornerShape(18.dp)),
            ) {
                Text(
                    "你可以自由发挥\n也可以诵读以下文字".localized,
                    fontFamily = FontFamily.MI_SANS,
                    fontSize = 22.sp,
                    lineHeight = 30.sp,
                    fontWeight = FontWeight.W900,
                    color = Color(0xff54AC53),
                    modifier = Modifier.padding(vertical = 20.dp, horizontal = 24.dp),
                )

                val social by viewModel.voiceSocial

                Column(
                    modifier =
                        Modifier
                            .padding(horizontal = 10.dp)
                            .padding(bottom = 18.dp)
                            .fillMaxWidth()
                            .background(
                                Color(0x8A0D8348),
                                shape = RoundedCornerShape(16.dp),
                            ).padding(vertical = 26.dp, horizontal = 16.dp),
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween,
                    ) {
                        Text("邀你朗读".localized, fontSize = 14.sp, lineHeight = 23.sp, color = Color.White)
                        Text(
                            "换一个".localized,
                            modifier =
                                Modifier.noEffectClick {
                                    viewModel.refresh()
                                },
                            fontSize = 14.sp,
                            lineHeight = 23.sp,
                            color = Color.White,
                        )
                    }
                    SizeHeight(24.dp)
                    AutoSizeText(
                        social,
                        lineHeight = 23.sp,
                        color = Color.White,
                        modifier = Modifier.fillMaxWidth(),
                        fontSize = 14.sp,
                        minTextSize = 12.sp,
                        maxTextSize = 14.sp,
                        maxLines = 5,
                    )
                }
            }
            SizeHeight(64.dp)
            // 录制声音内容区域
            RecordingSection(
                config =
                    VoicePublishConfig(
                        soundMinDuration = 5,
                        soundMaxDuration = 15,
                    ),
                voiceState = voiceState,
                onStartRecording = {
                    audioLauncher.launch(Manifest.permission.RECORD_AUDIO)
                },
                onStopRecording = {
                    audioRecordManager.stopRecording()
                },
                onStartPlaying = {
                    MediaPlayerManager.play(it.playItem)
                },
                onStopPlaying = {
                    MediaPlayerManager.pause(it.playItem)
                },
                onReRecord = {
                    MediaPlayerManager.release(it.playItem)
                    try {
                        it.recorder.finalOutputFile.delete()
                    } catch (e: Exception) {
                        LogUtils.e(
                            throwable = e,
                            message = "删除文件失败",
                        )
                    } finally {
                        audioRecordManager.reset()
                    }
                },
                onConfirm = { manager ->
                    loadingManager.show(scope) {
                        val publishRet =
                            viewModel.publish(
                                manager.recorder.finalOutputFile.absolutePath,
                                (manager.recorder.duration / 1000f).roundToInt(),
                            )
                        if (publishRet) { // 上传成功
                            showToast("上传成功".localized)
                            rootNavController.pop()
                        }
                    }
                },
            )
        }
    }
}

@Composable
@Preview
private fun PreviewProfileVoice() {
    ProfileVoiceEditScreen()
}
