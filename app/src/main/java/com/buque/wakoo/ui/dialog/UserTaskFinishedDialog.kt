package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.ui.widget.GradientVerticalButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.richtext.EntryRichText

/**
 * 主播完成任务弹窗
 */
@Composable
fun UserTaskFinishedDialog(
    title: String,
    desc: String,
    btnText: String,
    richDesc: List<RichItem> = listOf(),
    onClick: OnAction = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1.2f)
            .paint(painter = painterResource(R.drawable.ic_hqu_task_finished_bg), contentScale = ContentScale.FillWidth),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.fillMaxHeight(0.38f))

        SizeHeight(21.dp)
        //标题
        Text(title, fontSize = 20.sp, color = Color(0xff061c20), lineHeight = 20.sp, fontWeight = FontWeight.W600)
        SizeHeight(16.dp)
        //内容
        if (richDesc.isNotEmpty()) {
            EntryRichText(richDesc, fontSize = 20.sp, color = Color(0xff061c20), lineHeight = 20.sp, fontWeight = FontWeight.W600)
        } else {
            Text(desc, fontSize = 14.sp, color = Color(0xff061c20), lineHeight = 14.sp, fontWeight = FontWeight.W600)
        }
        Weight(1f)
        //按钮
        GradientVerticalButton(
            onClick = onClick,
            text = btnText,
            minWidth = 160.dp,
            height = 36.dp,
            textColor = Color.White,
            gradientColors = listOf(Color(0xffffa8b4), Color(0xffff2b64))
        )
        SizeHeight(20.dp)
    }
}

@Composable
@Preview
private fun PreviewCommonH5Dialog2() {
    UserTaskFinishedDialog(
        "恭喜聊天任务完成", "恭喜获得了xxxx积分", "领取现金"
    )
}