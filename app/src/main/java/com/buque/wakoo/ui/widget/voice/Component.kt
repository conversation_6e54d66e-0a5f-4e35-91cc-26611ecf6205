package com.buque.wakoo.ui.widget.voice

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.VoiceCardItem
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.ext.by
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.bean.VoiceTag
import com.buque.wakoo.ui.icons.Delete
import com.buque.wakoo.ui.icons.Favorite
import com.buque.wakoo.ui.icons.Favorited
import com.buque.wakoo.ui.icons.Like
import com.buque.wakoo.ui.icons.Liked
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AudioWaveAnimation
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.Rotatable
import com.buque.wakoo.ui.widget.RotationState
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TextTagChip
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.quoteFrame

@Composable
fun ColumnScope.VoiceOwnerContent(
    user: BasicUser,
    isRotatable: Boolean = false,
    textColor: Color = Color(0xFF111111),
    onUserClick: () -> Unit = {},
) {
    Rotatable(if (isRotatable) RotationState.PLAYING else RotationState.STOPPED) {
        AvatarNetworkImage(
            user = user,
            size = 90.dp,
            modifier =
                Modifier
                    .border(
                        width = 4.dp,
                        color = WakooWhite,
                        shape = CircleShape,
                    ),
        )
    }

    SizeHeight(12.dp)

    Text(
        text = user.name,
        style = MaterialTheme.typography.titleSmall,
        color = textColor,
    )

    SizeHeight(8.dp)

    GenderAgeTag(user = user)
}

@Composable
fun VoiceTagContent(
    tags: List<VoiceTag>,
    modifier: Modifier = Modifier,
) {
    FlowRow(
        modifier =
            modifier
                .fillMaxWidth()
                .horizontalScroll(state = rememberScrollState()),
        horizontalArrangement =
            Arrangement.spacedBy(
                space = 8.dp,
                alignment = Alignment.CenterHorizontally,
            ),
    ) {
        for (tag in tags) {
            TextTagChip(text = "#${tag.name}")
        }
    }
}

@Composable
fun VoiceTextContent(
    text: String,
    modifier: Modifier = Modifier,
    textColor: Color = Color(0xFF666666),
    quoteColor: Color = Color(0xFF111111),
    enabledScroll: Boolean = false,
) {
    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .quoteFrame(color = quoteColor)
                .padding(
                    horizontal = 32.dp,
                    vertical = 28.dp,
                ).then(
                    Modifier.verticalScroll(rememberScrollState()) to
                        Modifier by
                        enabledScroll,
                ),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            color = textColor,
            style = MaterialTheme.typography.bodyMedium,
            lineHeight = 20.sp,
        )
    }
}

@Composable
fun VoiceAnimatedStateArea(
    isPlaying: Boolean,
    duration: Int,
    icon: @Composable () -> Unit,
    onVoiceClick: () -> Unit,
    modifier: Modifier = Modifier,
    rotationState: RotationState = if (isPlaying) RotationState.PLAYING else RotationState.STOPPED,
) {
    Row(
        modifier = modifier.noEffectClick(onClick = onVoiceClick),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(9.dp),
    ) {
        Rotatable(rotationState) { icon() }

        AudioWaveAnimation(
            isPlaying = isPlaying,
            modifier =
                Modifier
                    .weight(1f)
                    .height(24.dp),
            barColor = Color(0xFF111111), // 类似 Spotify 的绿色
        )

        Text(
            text = "${duration}s",
            modifier = Modifier.widthIn(min = 25.dp),
            fontFamily = FontFamily.MI_SANS,
            style = MaterialTheme.typography.bodyMedium,
            lineHeight = 24.sp,
            color = Color(0xFF111111),
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
fun VoiceActionArea(
    item: VoiceCardItem,
    modifier: Modifier = Modifier,
    onToggleLike: () -> Unit = {},
    onToggleFavorite: () -> Unit = {},
    onReport: () -> Unit = {},
    onDelete: () -> Unit = {},
) {
    Row(
        modifier =
            modifier
                .fillMaxWidth()
                .height(48.dp)
                .background(
                    color = Color(0xFFF8F8F8),
                    shape =
                        RoundedCornerShape(
                            bottomStart = 16.dp,
                            bottomEnd = 16.dp,
                        ),
                ).padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        VoiceActionItem(
            text = item.likeCount.toString(),
            imageVector = if (item.isLike) WakooIcons.Liked else WakooIcons.Like,
            onClick = onToggleLike,
            enabled = !item.user.isSelf,
        )

        SizeWidth(24.dp)

        VoiceActionItem(
            text = item.favoriteCount.toString(),
            imageVector = if (item.isFavorite) WakooIcons.Favorited else WakooIcons.Favorite,
            onClick = onToggleFavorite,
            enabled = !item.user.isSelf,
        )

        Weight()

        if (item.user.isSelf) {
            VoiceActionItem(
                text = "删除".localized,
                imageVector = WakooIcons.Delete,
                onClick = onDelete,
            )
        } else {
            VoiceActionItem(
                text = "举报".localized,
                imageVector = WakooIcons.Report,
                onClick = onReport,
            )
        }

        SizeWidth(24.dp)
        Text(
            text = item.formatCreateTime,
            style = MaterialTheme.typography.labelLarge,
            color = Color(0xFF999999),
        )
    }
}

@Composable
fun VoiceActionItem(
    text: String,
    imageVector: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    Row(
        modifier = modifier.noEffectClick(enabled = enabled, onClick = onClick),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Image(
            imageVector = imageVector,
            contentDescription = null,
            modifier = Modifier.size(16.dp),
        )
        SizeWidth(4.dp)
        Text(
            text = text,
            style = MaterialTheme.typography.labelLarge,
            color = Color(0xFF999999),
        )
    }
}
