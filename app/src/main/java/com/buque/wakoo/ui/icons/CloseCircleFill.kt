package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val WakooIcons.CloseCircleFill: ImageVector
    get() {
        if (_CloseCircleFill != null) {
            return _CloseCircleFill!!
        }
        _CloseCircleFill = ImageVector.Builder(
            name = "CloseCircleFill",
            defaultWidth = 24.dp,
            defaultHeight = 24.dp,
            viewportWidth = 24f,
            viewportHeight = 24f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(24f)
                    verticalLineToRelative(24f)
                    horizontalLineToRelative(-24f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFF111111))) {
                    moveTo(12f, 12.002f)
                    moveToRelative(-6.857f, 0f)
                    arcToRelative(6.857f, 6.857f, 0f, isMoreThanHalf = true, isPositiveArc = true, 13.714f, 0f)
                    arcToRelative(6.857f, 6.857f, 0f, isMoreThanHalf = true, isPositiveArc = true, -13.714f, 0f)
                }
                path(fill = SolidColor(Color(0xFFFFFFFF))) {
                    moveTo(12f, 22f)
                    curveTo(6.477f, 22f, 2f, 17.523f, 2f, 12f)
                    curveTo(2f, 6.477f, 6.477f, 2f, 12f, 2f)
                    curveTo(17.523f, 2f, 22f, 6.477f, 22f, 12f)
                    curveTo(22f, 17.523f, 17.523f, 22f, 12f, 22f)
                    close()
                    moveTo(12f, 10.586f)
                    lineTo(9.172f, 7.757f)
                    lineTo(7.757f, 9.172f)
                    lineTo(10.586f, 12f)
                    lineTo(7.757f, 14.828f)
                    lineTo(9.172f, 16.243f)
                    lineTo(12f, 13.414f)
                    lineTo(14.828f, 16.243f)
                    lineTo(16.243f, 14.828f)
                    lineTo(13.414f, 12f)
                    lineTo(16.243f, 9.172f)
                    lineTo(14.828f, 7.757f)
                    lineTo(12f, 10.586f)
                    close()
                }
            }
        }.build()

        return _CloseCircleFill!!
    }

@Suppress("ObjectPropertyName")
private var _CloseCircleFill: ImageVector? = null

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.CloseCircleFill,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((24.0).dp)
                        .height((24.0).dp),
            )
        }
    }
}
