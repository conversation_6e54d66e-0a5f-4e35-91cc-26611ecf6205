package com.buque.wakoo.ui.screens.liveroom.panel

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.buque.wakoo.bean.UserPages
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.screens.liveroom.LiveRoomInfoState
import com.buque.wakoo.ui.screens.liveroom.RoomEvent
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.UserListItem
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.liveroom.RoomOnlineListViewModel

@Composable
fun DialogScope.LiveRoomOnlinePanel(
    roomInfoState: LiveRoomInfoState,
    modifier: Modifier = Modifier,
) {
    BottomPanelScaffold(
        title = "房间在线用户".localized,
        useClose = true,
        modifier = modifier.fillMaxHeight(0.7f),
        backgroundColor = WakooWhite,
        contentPadding = 0.dp,
        bottomPadding = 0.dp,
    ) {
        val listState = rememberLazyListState()
        CStateListPaginateLayout<String, Int, UserPages, RoomOnlineListViewModel>(
            reqKey = roomInfoState.id,
            modifier = Modifier.fillMaxSize(),
            listState = listState,
            autoRefresh = true,
        ) { paginateState, list ->
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                state = listState,
                contentPadding = PaddingValues(bottom = 36.dp),
            ) {
                items(list) { item ->
                    UserListItem(
                        user = item.user,
                        modifier = Modifier.padding(16.dp),
                        startContent = {
                            AvatarNetworkImage(
                                user = item.user,
                                size = 48.dp,
                                onClick = {
                                    roomInfoState.sendEvent(
                                        RoomEvent.PanelDialog {
                                            LiveRoomUserInfoPanel(item.user, roomInfoState)
                                        },
                                    )
                                },
                            )
                        },
                    )
                }
            }
        }
    }
}
