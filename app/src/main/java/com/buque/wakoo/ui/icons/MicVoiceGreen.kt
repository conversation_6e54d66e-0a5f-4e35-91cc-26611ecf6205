package com.buque.wakoo.ui.icons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.MicVoiceGreen: ImageVector
    get() {
        if (_MicVoiceGreen != null) {
            return _MicVoiceGreen!!
        }
        _MicVoiceGreen = ImageVector.Builder(
            name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            defaultWidth = 16.dp,
            defaultHeight = 16.dp,
            viewportWidth = 16f,
            viewportHeight = 16f
        ).apply {
            path(
                fill = Brush.linearGradient(
                    colorStops = arrayOf(
                        0f to Color(0xFFA3FF2C),
                        1f to Color(0xFF31FFA1)
                    ),
                    start = Offset(2.036f, 7.667f),
                    end = Offset(16f, 7.667f)
                )
            ) {
                moveTo(13.644f, 5.129f)
                lineTo(13.809f, 4.752f)
                curveTo(14.102f, 4.08f, 14.63f, 3.544f, 15.288f, 3.251f)
                lineTo(15.795f, 3.026f)
                curveTo(16.068f, 2.904f, 16.068f, 2.506f, 15.795f, 2.384f)
                lineTo(15.316f, 2.171f)
                curveTo(14.641f, 1.871f, 14.104f, 1.316f, 13.816f, 0.621f)
                lineTo(13.647f, 0.213f)
                curveTo(13.529f, -0.071f, 13.137f, -0.071f, 13.019f, 0.213f)
                lineTo(12.851f, 0.621f)
                curveTo(12.563f, 1.316f, 12.026f, 1.871f, 11.35f, 2.171f)
                lineTo(10.872f, 2.384f)
                curveTo(10.598f, 2.506f, 10.598f, 2.904f, 10.872f, 3.026f)
                lineTo(11.378f, 3.251f)
                curveTo(12.037f, 3.544f, 12.565f, 4.08f, 12.858f, 4.752f)
                lineTo(13.022f, 5.129f)
                curveTo(13.142f, 5.405f, 13.524f, 5.405f, 13.644f, 5.129f)
                close()
                moveTo(9.591f, 3.559f)
                curveTo(9.774f, 3.848f, 10.024f, 4.062f, 10.341f, 4.2f)
                lineTo(10.718f, 4.365f)
                curveTo(10.971f, 4.475f, 11.176f, 4.63f, 11.333f, 4.83f)
                verticalLineTo(6.667f)
                curveTo(11.333f, 8.508f, 9.841f, 10f, 8f, 10f)
                curveTo(6.159f, 10f, 4.666f, 8.508f, 4.666f, 6.667f)
                verticalLineTo(4f)
                curveTo(4.666f, 2.159f, 6.159f, 0.667f, 8f, 0.667f)
                curveTo(8.749f, 0.667f, 9.441f, 0.914f, 9.998f, 1.331f)
                curveTo(9.838f, 1.451f, 9.702f, 1.599f, 9.591f, 1.774f)
                curveTo(9.419f, 2.046f, 9.333f, 2.344f, 9.333f, 2.667f)
                curveTo(9.333f, 2.99f, 9.419f, 3.287f, 9.591f, 3.559f)
                close()
                moveTo(2.036f, 7.333f)
                horizontalLineTo(3.38f)
                curveTo(3.704f, 9.595f, 5.649f, 11.333f, 8f, 11.333f)
                curveTo(10.351f, 11.333f, 12.296f, 9.595f, 12.619f, 7.333f)
                horizontalLineTo(13.963f)
                curveTo(13.656f, 10.114f, 11.448f, 12.323f, 8.667f, 12.63f)
                verticalLineTo(15.333f)
                horizontalLineTo(7.333f)
                verticalLineTo(12.63f)
                curveTo(4.552f, 12.323f, 2.344f, 10.114f, 2.036f, 7.333f)
                close()
            }
        }.build()

        return _MicVoiceGreen!!
    }

@Suppress("ObjectPropertyName")
private var _MicVoiceGreen: ImageVector? = null
