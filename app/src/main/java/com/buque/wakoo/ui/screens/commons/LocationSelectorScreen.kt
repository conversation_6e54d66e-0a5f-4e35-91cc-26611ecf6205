package com.buque.wakoo.ui.screens.commons

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.LocationManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.StandardListItemScaffold

@Composable
fun LocationSelectorScreen(
    resultKey: String,
    modifier: Modifier = Modifier,
) {
    val locationInfo by LocationManager.locationFlow.collectAsStateWithLifecycle()
    val controller = LocalAppNavController.root

    SegColorTitleScreenScaffold(title = "地理位置显示".localized) { pv ->
        Column(
            modifier =
                Modifier
                    .background(color = Color.White)
                    .padding(pv),
        ) {
            LocationItem("不显示地理位置".localized, "", Icons.Default.Check) {
                controller.setResult(resultKey, it)
                controller.popIs<Route.LocationSelector>()
            }
            HorizontalDivider()
            LocationItem("显示国家/地区及城市".localized, locationInfo?.getFullAddress() ?: "", Icons.AutoMirrored.Filled.KeyboardArrowRight) {
                controller.setResult(resultKey, it)
                controller.popIs<Route.LocationSelector>()
            }
            HorizontalDivider()
            LocationItem("仅显示国家/地区".localized, locationInfo?.country ?: "", Icons.AutoMirrored.Filled.KeyboardArrowRight) {
                controller.setResult(resultKey, it)
                controller.popIs<Route.LocationSelector>()
            }
            HorizontalDivider()
            LocationItem("仅显示城市".localized, locationInfo?.city ?: "", Icons.AutoMirrored.Filled.KeyboardArrowRight) {
                controller.setResult(resultKey, it)
                controller.popIs<Route.LocationSelector>()
            }
        }
    }
}

@Composable
private fun LocationItem(
    title: String,
    content: String,
    imageVector: ImageVector,
    onClick: (content: String) -> Unit,
) {
    StandardListItemScaffold(
        startContent = {
            Text(title, fontSize = 14.sp, lineHeight = 14.sp, color = Color(0xff111111))
        },
        centerContent = {
            Text(
                content,
                fontSize = 14.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.End,
            )
        },
        endContent = {
            Image(imageVector, contentDescription = null)
        },
        modifier =
            Modifier
                .fillMaxWidth()
                .height(48.dp)
                .click {
                    onClick(content)
                }.padding(horizontal = 16.dp),
    )
}

@Composable
@Preview
private fun PreviewLocationScreen() {
    LocationSelectorScreen("")
}
