package com.buque.wakoo.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.user.ISocialInfo
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.ArrowRight
import com.buque.wakoo.ui.icons.WakooIcons

@Composable
fun AudioStateBar(
    user: ISocialInfo,
    modifier: Modifier = Modifier,
    openRoom: (Int) -> Unit = {},
) {
    val c = MaterialTheme.colorScheme.primary
    val roomId = user.roomId?.takeIf { user.isInRoom }
    if (roomId != null) {
        Row(
            modifier =
                modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(6.dp))
                    .click {
                        openRoom(roomId)
                    }.background(Color(0xFFE8FFE9))
                    .padding(16.dp, 12.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            VoiceWaves(contentColor = Color(0xff3EEE44))
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                "语音派对中,快去跟TA聊聊吧~".localized,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = c,
            )
            Weight(1f)
            Icon(
                WakooIcons.ArrowRight,
                contentDescription = "",
                modifier = Modifier.size(12.dp),
                tint = c,
            )
        }
    }
}
