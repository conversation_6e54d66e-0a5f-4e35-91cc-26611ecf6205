package com.buque.wakoo.ui.widget.state

import androidx.compose.runtime.MutableState
import com.buque.wakoo.network.ApiResponse
import com.buque.wakoo.network.executeApiOrThrow
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

/**
 * 通用的任务调用执行器，用于处理初次加载和下拉刷新。
 * 它封装了从“加载中”到“成功”或“失败”的完整状态流转逻辑。
 *
 * @param updateState 状态更新器。
 * @param isRefresh 标记本次调用是否为下拉刷新。
 * @param call 一个挂起函数，代表任务。
 * @param transform 把call的值转成另一个真实数据类型，这在接口中可能用到。
 */
suspend fun <T, S> executeStatefulCallX(
    updateState: ((CState<T>) -> CState<T>) -> Unit,
    isRefresh: Boolean = false,
    call: suspend () -> S,
    transform: (S) -> T,
) {
    // 1. 进入加载状态
    updateState {
        if (isRefresh && it is CState.Success) {
            // 如果已是成功状态，则认为是“刷新”，保持旧数据，更新 isRefreshing 标志
            it.copy(
                isRefreshing = true,
                throwable = it.throwable,
            )
        } else {
            // 否则是初次加载
            CState.Loading(isRefreshing = isRefresh, throwable = it.errorOrNull)
        }
    }

    // 2. 执行API调用
    try {
        val data =
            with(Dispatchers.IO) {
                transform(call())
            }
        // 3. 成功，更新为 Success 状态
        updateState {
            CState.Success(
                data = data,
                isRefreshing = false, // 加载/刷新结束
            )
        }
    } catch (e: Exception) {
        // 4. 失败，根据当前状态决定是阻塞性错误还是非阻塞性错误
        updateState {
            if (it is CState.Success) {
                // 刷新失败，保持旧数据，通过 throwable 提示错误（非阻塞）
                it.copy(
                    isRefreshing = false,
                    throwable = e,
                )
            } else {
                // 初次加载失败，进入 Error 状态（阻塞）
                CState.Error(throwable = e)
            }
        }
        if (e is CancellationException) {
            throw e
        }
    }
}

/**
 * 通用的任务调用执行器，用于处理初次加载和下拉刷新。
 * 它封装了从“加载中”到“成功”或“失败”的完整状态流转逻辑。
 *
 * @param updateState 状态更新器。
 * @param isRefresh 标记本次调用是否为下拉刷新。
 * @param call 一个挂起函数，代表任务。
 * @param transform 把call的值转成另一个真实数据类型，这在接口中可能用到。
 */
suspend fun <T> executeStatefulCall(
    updateState: ((CState<T>) -> CState<T>) -> Unit,
    isRefresh: Boolean = false,
    call: suspend () -> T,
    transform: (T) -> T = { it },
) {
    executeStatefulCallX<T, T>(
        updateState = updateState,
        isRefresh = isRefresh,
        call = call,
        transform = transform,
    )
}

suspend fun <T> executeStatefulCall(
    getState: () -> CState<T>,
    setState: (CState<T>) -> Unit,
    isRefresh: Boolean = false,
    call: suspend () -> T,
    transform: (T) -> T = { it },
) {
    executeStatefulCall<T>(
        updateState = { action ->
            setState(action(getState()))
        },
        isRefresh = isRefresh,
        call = call,
        transform = transform,
    )
}

suspend fun <T> executeStatefulCall(
    mutableState: MutableState<CState<T>>,
    isRefresh: Boolean = false,
    call: suspend () -> T,
    transform: (T) -> T = { it },
) {
    executeStatefulCall<T>(
        getState = {
            mutableState.value
        },
        setState = {
            mutableState.value = it
        },
        isRefresh = isRefresh,
        call = call,
        transform = transform,
    )
}

/**
 * 通用的任务调用执行器，用于处理初次加载和下拉刷新。
 * 它封装了从“加载中”到“成功”或“失败”的完整状态流转逻辑。
 *
 * @param T API成功返回的数据类型。
 * @param stateFlow 持有UI状态的 StateFlow。
 * @param isRefresh 标记本次调用是否为下拉刷新。
 * @param call 一个挂起函数，代表任务。
 */
suspend fun <T> executeStatefulCall(
    stateFlow: MutableStateFlow<CState<T>>,
    isRefresh: Boolean = false,
    call: suspend () -> T,
) {
    executeStatefulCall(
        updateState = stateFlow::update,
        isRefresh = isRefresh,
        call = call,
    )
}

/**
 * 通用的API调用执行器，用于处理初次加载和下拉刷新。
 * 它封装了从“加载中”到“成功”或“失败”的完整状态流转逻辑。
 *
 * @param T API成功返回的数据类型。
 * @param updateState 状态更新器。
 * @param isRefresh 标记本次调用是否为下拉刷新。
 * @param apiCall 一个挂起函数，代表实际的网络请求。
 */
suspend fun <T, S> executeStatefulApiCall(
    updateState: ((CState<T>) -> CState<T>) -> Unit,
    isRefresh: Boolean = false,
    apiCall: suspend () -> ApiResponse<S>,
    transform: (S) -> T,
) {
    executeStatefulCallX<T, S>(
        updateState = updateState,
        isRefresh = isRefresh,
        call = {
            executeApiOrThrow(true, apiCall).getOrThrow() as S
        },
        transform = transform,
    )
}

suspend fun <T, S> executeStatefulApiCall(
    getState: () -> CState<T>,
    setState: (CState<T>) -> Unit,
    isRefresh: Boolean = false,
    apiCall: suspend () -> ApiResponse<S>,
    transform: (S) -> T,
) {
    executeStatefulApiCall<T, S>(
        updateState = { action ->
            setState(action(getState()))
        },
        isRefresh = isRefresh,
        apiCall = apiCall,
        transform = transform,
    )
}

suspend fun <T, S> executeStatefulApiCall(
    mutableState: MutableState<CState<T>>,
    isRefresh: Boolean = false,
    apiCall: suspend () -> ApiResponse<S>,
    transform: (S) -> T,
) {
    executeStatefulApiCall<T, S>(
        getState = {
            mutableState.value
        },
        setState = {
            mutableState.value = it
        },
        isRefresh = isRefresh,
        apiCall = apiCall,
        transform = transform,
    )
}

/**
 * 通用的API调用执行器，用于处理初次加载和下拉刷新。
 * 它封装了从“加载中”到“成功”或“失败”的完整状态流转逻辑。
 *
 * @param T API成功返回的数据类型。
 * @param stateFlow 持有UI状态的 StateFlow。
 * @param isRefresh 标记本次调用是否为下拉刷新。
 * @param apiCall 一个挂起函数，代表实际的网络请求。
 */
suspend fun <T, S> executeStatefulApiCall(
    stateFlow: MutableStateFlow<CState<T>>,
    isRefresh: Boolean = false,
    apiCall: suspend () -> ApiResponse<S>,
    transform: (S) -> T,
) {
    executeStatefulApiCall(
        updateState = stateFlow::update,
        isRefresh = isRefresh,
        apiCall = apiCall,
        transform = transform,
    )
}
