package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.BlockEffect: ImageVector
    get() {
        if (_BlockEffect != null) {
            return _BlockEffect!!
        }
        _BlockEffect = ImageVector.Builder(
            name = "BlockEffect",
            defaultWidth = 24.dp,
            defaultHeight = 24.dp,
            viewportWidth = 24f,
            viewportHeight = 24f
        ).apply {
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(21.678f, 20.263f)
                lineTo(20.264f, 21.678f)
                lineTo(16.021f, 17.435f)
                lineTo(17.435f, 16.021f)
                lineTo(21.678f, 20.263f)
                close()
                moveTo(20.313f, 12.033f)
                curveTo(20.553f, 12.261f, 20.564f, 12.641f, 20.336f, 12.882f)
                curveTo(20.285f, 12.936f, 20.225f, 12.979f, 20.158f, 13.011f)
                lineTo(15.508f, 15.224f)
                curveTo(15.384f, 15.283f, 15.283f, 15.384f, 15.224f, 15.508f)
                lineTo(13.012f, 20.158f)
                curveTo(12.87f, 20.457f, 12.511f, 20.584f, 12.212f, 20.441f)
                curveTo(12.145f, 20.41f, 12.085f, 20.366f, 12.034f, 20.313f)
                lineTo(9.349f, 17.479f)
                lineTo(10.764f, 16.064f)
                lineTo(12.082f, 17.455f)
                lineTo(13.418f, 14.649f)
                curveTo(13.675f, 14.109f, 14.109f, 13.675f, 14.649f, 13.418f)
                lineTo(17.456f, 12.082f)
                lineTo(16.064f, 10.763f)
                lineTo(17.479f, 9.349f)
                lineTo(20.313f, 12.033f)
                close()
                moveTo(14.843f, 2.579f)
                curveTo(15.134f, 2.421f, 15.498f, 2.528f, 15.656f, 2.819f)
                curveTo(15.692f, 2.884f, 15.715f, 2.955f, 15.725f, 3.028f)
                lineTo(16.004f, 5.166f)
                lineTo(14.22f, 6.95f)
                lineTo(14.006f, 5.312f)
                lineTo(11.276f, 6.797f)
                curveTo(10.751f, 7.082f, 10.144f, 7.179f, 9.557f, 7.069f)
                lineTo(6.5f, 6.5f)
                lineTo(7.07f, 9.556f)
                curveTo(7.179f, 10.143f, 7.082f, 10.75f, 6.797f, 11.274f)
                lineTo(5.312f, 14.006f)
                lineTo(6.95f, 14.22f)
                lineTo(5.166f, 16.003f)
                lineTo(3.029f, 15.724f)
                curveTo(2.7f, 15.681f, 2.468f, 15.38f, 2.511f, 15.052f)
                curveTo(2.52f, 14.979f, 2.544f, 14.908f, 2.579f, 14.843f)
                lineTo(5.04f, 10.319f)
                curveTo(5.106f, 10.198f, 5.129f, 10.057f, 5.104f, 9.922f)
                lineTo(4.16f, 4.859f)
                curveTo(4.1f, 4.534f, 4.315f, 4.221f, 4.641f, 4.16f)
                curveTo(4.713f, 4.147f, 4.788f, 4.147f, 4.861f, 4.16f)
                lineTo(9.923f, 5.104f)
                curveTo(10.059f, 5.129f, 10.199f, 5.106f, 10.319f, 5.04f)
                lineTo(14.843f, 2.579f)
                close()
            }
            path(fill = SolidColor(Color(0xFF111111))) {
                moveTo(18.363f, 4.222f)
                lineTo(19.778f, 5.636f)
                lineTo(5.635f, 19.778f)
                lineTo(4.221f, 18.364f)
                lineTo(18.363f, 4.222f)
                close()
            }
        }.build()

        return _BlockEffect!!
    }

@Suppress("ObjectPropertyName")
private var _BlockEffect: ImageVector? = null
