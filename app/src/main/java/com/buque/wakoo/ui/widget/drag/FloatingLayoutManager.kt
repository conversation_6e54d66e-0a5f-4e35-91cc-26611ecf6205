package com.buque.wakoo.ui.widget.drag

import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionOnScreen
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import com.buque.wakoo.ui.widget.drag.DraggableFloatingState.Companion.DraggableFloatingStateSaver
import com.buque.wakoo.ui.widget.drag.FloatingLayoutManagerState.Companion.FloatingLayoutManagerStateSaver
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

@Stable
class FloatingLayoutManagerState(
    var scope: CoroutineScope,
    var collisionSpacing: Float,
) {
    companion object {
        val FloatingLayoutManagerStateSaver =
            Saver<FloatingLayoutManagerState, Any>(
                save = { managerState ->
                    // 我们只保存每个子 state 的 saved list，不保存 manager 自己的状态
                    managerState.states.map {
                        with(DraggableFloatingStateSaver) {
                            save(it)
                        }
                    }
                },
                restore = { savedList ->
                    val restoredStates =
                        (savedList as List<List<Any?>>).mapNotNull {
                            DraggableFloatingStateSaver.restore(it)
                        }
                    // 创建一个新的 manager state，并填入恢复的子 states
                    FloatingLayoutManagerState(
                        scope = CoroutineScope(Dispatchers.Main.immediate), // 临时 scope
                        collisionSpacing = 0f, // 这个值会在 remember 中被重设
                    ).apply {
                        states.addAll(restoredStates)
                    }
                },
            )
    }

    internal val states = mutableStateListOf<DraggableFloatingState>()

    fun onDragEnd(state: DraggableFloatingState) {
        if (state.isVisible) {
            resolveCollisions(state)
        }
    }

    fun onVisibilityChanged(state: DraggableFloatingState) {
        if (state.isVisible) {
            resolveCollisions(state)
        }
    }

    private var job: Job? = null

    private fun resolveCollisions(activeState: DraggableFloatingState) {
        job?.cancel()
        job =
            scope.launch {
                var targetOffset = activeState.getWindowFinalTargetOffset()
                val otherVisibleStates = states.filter { it != activeState && it.isVisible }
                val otherTargetRects =
                    otherVisibleStates.map {
                        Rect(it.getWindowFinalTargetOffset(), it.componentSize.toSize())
                    }

                val activeRect = Rect(targetOffset, activeState.componentSize.toSize())

                val isOverlapping =
                    otherTargetRects.any { otherRect ->
                        activeRect.inflate(collisionSpacing).overlaps(otherRect)
                    }

                if (isOverlapping) {
                    // *** 核心修正：调用新的、带约束的 findAvailablePosition ***
                    val newPosition =
                        findAvailablePosition(
                            activeState = activeState,
                            initialTarget = targetOffset,
                            otherTargetRects = otherTargetRects,
                        )

                    if (newPosition != null) {
                        targetOffset = newPosition
                    }
                }

                activeState.animateToOffset(targetOffset)
            }
    }

    /**
     * 寻找可用位置的全新实现，能够感知贴边状态。
     */
    private fun findAvailablePosition(
        activeState: DraggableFloatingState,
        initialTarget: Offset,
        otherTargetRects: List<Rect>,
    ): Offset? {
        // --- 情况一：如果组件需要贴边 ---
        if (activeState.isStickyToEdge) {
            // 我们只在垂直方向上搜索，水平方向（X坐标）是固定的
            val targetX = initialTarget.x // 保持贴边的X坐标不变

            // 搜索策略：从当前Y坐标开始，交替向上和向下搜索
            val searchStep = 10f // 每次移动10个像素
            for (i in 0..100) { // 限制搜索次数，防止无限循环
                val dy = i * searchStep

                // 1. 尝试向下移动
                val downwardPos = activeState.getWindowCoercedOffset(Offset(targetX, initialTarget.y + dy))
                if (isPositionAvailable(downwardPos, activeState, otherTargetRects)) {
                    return downwardPos
                }

                // 2. 尝试向上移动
                val upwardPos = activeState.getWindowCoercedOffset(Offset(targetX, initialTarget.y - dy))
                if (isPositionAvailable(upwardPos, activeState, otherTargetRects)) {
                    return upwardPos
                }
            }
        } else {
            // 使用我们之前的“螺旋搜索”策略，因为它适用于自由浮动的组件
            val step = (collisionSpacing + 20).toInt()
            val maxRadius = (activeState.parentBounds.width + activeState.parentBounds.height).div(2).toInt()

            if (isPositionAvailable(initialTarget, activeState, otherTargetRects)) {
                return initialTarget
            }

            for (radius in step..maxRadius step step) {
                for (angle in 0 until 360 step 15) {
                    val rad = Math.toRadians(angle.toDouble())
                    val testX = initialTarget.x + (radius * kotlin.math.cos(rad)).toFloat()
                    val testY = initialTarget.y + (radius * kotlin.math.sin(rad)).toFloat()
                    val testPosition = activeState.getWindowCoercedOffset(Offset(testX, testY))

                    if (isPositionAvailable(testPosition, activeState, otherTargetRects)) {
                        return testPosition
                    }
                }
            }
        }

        // 如果所有策略都失败，返回 null
        return null
    }

    private fun isPositionAvailable(
        position: Offset,
        activeState: DraggableFloatingState,
        otherTargetRects: List<Rect>,
    ): Boolean {
        val testRect = Rect(position, activeState.componentSize.toSize())
        if (!activeState.parentBounds.contains(testRect)) {
            return false
        }
        return !otherTargetRects.any { otherRect ->
            testRect.inflate(collisionSpacing).overlaps(otherRect)
        }
    }

    private fun Rect.contains(innerRect: Rect): Boolean =
        this.contains(innerRect.topLeft) &&
            this.contains(innerRect.topRight) &&
            this.contains(innerRect.bottomLeft) &&
            this.contains(innerRect.bottomRight)
}

@Composable
fun rememberFloatingLayoutManagerState(collisionSpacing: Dp = 10.dp): FloatingLayoutManagerState {
    val scope = rememberCoroutineScope()
    val collisionSpacingPx = with(LocalDensity.current) { collisionSpacing.toPx() }

    val state =
        rememberSaveable(saver = FloatingLayoutManagerStateSaver) {
            FloatingLayoutManagerState(scope, collisionSpacingPx)
        }

    // 恢复后，注入最新的 scope 和参数
    LaunchedEffect(scope, collisionSpacingPx, state) {
        state.scope = scope
        state.collisionSpacing = collisionSpacingPx
    }

    return state
}

interface FloatingLayoutScope {
    @Composable
    fun DraggableItem(
        state: DraggableFloatingState,
        modifier: Modifier = Modifier,
        content: @Composable () -> Unit,
    )
}

private class FloatingLayoutScopeImpl(
    private val managerState: FloatingLayoutManagerState,
) : FloatingLayoutScope {
    var parentSize by mutableStateOf(Size.Zero)
    var parentPosition by mutableStateOf(Offset.Zero)

    @Composable
    override fun DraggableItem(
        state: DraggableFloatingState,
        modifier: Modifier,
        content: @Composable () -> Unit,
    ) {
        DisposableEffect(state) {
            state.managerState = managerState
            managerState.states.add(state)
            onDispose {
                if (state.managerState === managerState) {
                    state.managerState = null
                }
                managerState.states.remove(state)
            }
        }

        LaunchedEffect(state.isVisible) {
            managerState.onVisibilityChanged(state)
        }

        // *** 这是最核心的修复：将 parentSize 从管理器同步到子 State ***
        LaunchedEffect(parentSize, parentPosition) {
            state.setBounds(parent = Rect(parentPosition, parentSize))
        }

        DraggableFloatingItem(
            state = state,
            modifier = modifier,
            onDragEnd = { managerState.onDragEnd(state) },
        ) {
            content()
        }
    }
}

@Composable
fun FloatingLayoutManager(
    modifier: Modifier = Modifier,
    state: FloatingLayoutManagerState = LocalFloatingLayoutManagerState.current,
    content: @Composable FloatingLayoutScope.() -> Unit,
) {
    val scope =
        remember(state) {
            FloatingLayoutScopeImpl(state)
        }
    BoxWithConstraints(
        modifier =
            modifier
                .fillMaxSize()
                .onGloballyPositioned {
                    scope.parentPosition = it.positionOnScreen()
                },
    ) {
        val parentSize = Size(constraints.maxWidth.toFloat(), constraints.maxHeight.toFloat())
        scope.parentSize = parentSize
        scope.content()
    }
}

val LocalFloatingLayoutManagerState =
    staticCompositionLocalOf<FloatingLayoutManagerState> {
        error("No FloatingLayoutManagerState provided")
    }
