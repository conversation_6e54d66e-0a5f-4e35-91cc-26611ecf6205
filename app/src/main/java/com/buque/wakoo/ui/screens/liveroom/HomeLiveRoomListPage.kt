package com.buque.wakoo.ui.screens.liveroom

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.MyLiveRoomInfo
import com.buque.wakoo.bean.RecommendLiveRoom
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.AccountManager.selfUser
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.ui.icons.FiSrGlassCheers
import com.buque.wakoo.ui.icons.RecommendRoomJoin
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.widget.AudioWaveAnimation
import com.buque.wakoo.ui.widget.AutoSizeText
import com.buque.wakoo.ui.widget.CommonBanner
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.roundedParallelogram
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.liveroom.LiveRoomRecommendViewModel

@Composable
fun HomeLiveRoomListPage(
    modifier: Modifier = Modifier,
    onJoinRoom: (room: BasicRoomInfo) -> Unit = {},
    onCreateLiveRoom: () -> Unit = {},
) {
    Box(modifier = modifier.fillMaxSize()) {
        val listState = rememberLazyListState()
        val viewModel = viewModel<LiveRoomRecommendViewModel>()

        CStateListPaginateLayout<Any, Int, RecommendLiveRoom, LiveRoomRecommendViewModel>(
            reqKey = "",
            viewModel = viewModel,
            listState = listState,
            emptyText = "暂无派对".localized,
            emptyId = R.drawable.ic_empty_for_all,
            modifier = Modifier.background(color = Color(0xffF7F7F7)),
            wrapperBox = { content ->
                Column {
                    CommonBanner(
                        202,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                    )
                    content()
                }
            },
        ) { paginateState, list ->
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                items(list) { room ->
                    LiveRoomItem(room, onJoinRoom = onJoinRoom)
                }

                item {
                    SizeHeight(108.dp)
                }
            }
        }

        RoomOperationButton(
            modifier =
                Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 24.dp),
            onCreateLiveRoom = onCreateLiveRoom,
            onJoinMyLiveRoom = {
                onJoinRoom(
                    BasicRoomInfo(
                        id = it.roomId,
                        publicId = it.publicId,
                        title = it.roomName,
                        owner = selfUser?.toRoomUser(),
                        roomMode = LiveRoomMode.UnKnown(-1),
                        micMode = LiveMicMode.UnKnown(-1),
                        notice = null,
                        desc = null,
                        background = null,
                        tagIds = null,
                    ),
                )
            },
        )
    }
}

@Composable
private fun LiveRoomItem(
    room: RecommendLiveRoom,
    onJoinRoom: (room: BasicRoomInfo) -> Unit = {},
) {
    Row(
        modifier =
            Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .background(
                    brush = Brush.verticalGradient(listOf(Color(0xffffffff), Color(0xffffffff), Color(0xfff0ffe8))),
                    shape = RoundedCornerShape(topStart = 20.dp, topEnd = 10.dp, bottomStart = 5.dp, bottomEnd = 20.dp),
                ).noEffectClick {
                    onJoinRoom(room.toBasicRoomInfo())
                }.padding(start = 12.dp, end = 20.dp, top = 18.dp, bottom = 24.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 主播头像
        AvatarNetworkImage(
            user = room.owner,
            size = 64.dp,
            shape = CircleShape,
            border =
                BorderStroke(
                    width = 2.dp,
                    color = Color(0xFF66FE6B),
                ),
        )
        Spacer(modifier = Modifier.width(12.dp))
        Column(modifier = Modifier.weight(1f)) {
            // 房间名
            Text(
                text = room.title,
                color = Color(0xFF111111),
                fontSize = 14.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                fontWeight = FontWeight.SemiBold,
                lineHeight = 24.sp,
            )
            Spacer(modifier = Modifier.height(8.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                // 成员头像列表+人数气泡
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy((-4).dp),
                ) {
                    room.recommendUserList.take(3).forEachIndexed { idx, user ->
                        Box(
                            modifier =
                                Modifier
                                    .size(20.dp)
                                    .border(
                                        width = 1.dp,
                                        color = Color.White,
                                        shape = CircleShape,
                                    ).clip(CircleShape),
                        ) {
                            NetworkImage(
                                data = user.avatarUrl,
                                modifier = Modifier.fillMaxSize(),
                            )
                        }
                    }
                    Box(
                        modifier =
                            Modifier
                                .size(20.dp)
                                .background(
                                    color = Color(0xFFEAFFF2),
                                    shape = CircleShape,
                                ).border(
                                    width = 1.dp,
                                    color = Color.White,
                                    shape = CircleShape,
                                ),
                        contentAlignment = Alignment.Center,
                    ) {
                        AutoSizeText(
                            text = "+${room.inRoomUserCnt}",
                            color = Color(0xFF31FFA1),
                            lineHeight = 15.sp,
                            fontSize = 10.sp,
                        )
                    }


                }
                if (room.hasRedPacket) {
                    SizeWidth(4.dp)
                    Image(
                        painterResource(R.drawable.ic_hongbao),
                        contentDescription = "hb",
                        modifier = Modifier.size(15.dp, 20.dp),
                        contentScale = ContentScale.FillHeight,
                    )
                }
                if (room.roomMode == LiveRoomMode.PKMode.value) {
                    SizeWidth(3.dp)
                    Text(
                        "PK模式".localized,
                        modifier = Modifier
                            .roundedParallelogram(
                                8f, brush = Brush.horizontalGradient(
                                    listOf(Color(0xff6FC3FF), Color(0xffF772FF))
                                ), Color.Transparent, 0.dp, cornerRadius = 16f
                            ).padding(horizontal = 10.dp, vertical = 3.dp),
                        fontSize = 11.sp, color = Color.White, lineHeight = 15.sp
                    )
                }
            }
        }
        Spacer(modifier = Modifier.width(12.dp))
        // 右上角10人派对中
        Column(horizontalAlignment = Alignment.End) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                AudioWaveAnimation(
                    isPlaying = true,
                    fromCenter = false,
                    modifier =
                        Modifier
                            .size(12.dp),
                    brush = Brush.verticalGradient(listOf(Color(0xffa3ff2c), Color(0xff31ffa1))),
                    barSpacing = 1.5.dp,
                    radius = 3.dp,
                )
                Spacer(modifier = Modifier.width(2.dp))
                Text(
                    text = "%s人派对中".localizedFormat(room.inRoomUserCnt),
                    color = Color(0xFFB6B6B6),
                    fontSize = 13.sp,
                )
            }
            SizeHeight(20.dp)
            Row(
                modifier =
                    Modifier
                        .paint(
                            painter = painterResource(R.drawable.ic_live_room_join_bg),
                            contentScale = ContentScale.FillWidth,
                        ).padding(vertical = 7.dp, horizontal = 13.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
            ) {
                Image(WakooIcons.RecommendRoomJoin, contentDescription = "", modifier = Modifier.size(13.dp))
                SizeWidth(3.dp)
                Text(
                    "加入".localized,
                    fontSize = 12.sp,
                    lineHeight = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xff66fe6b),
                )
            }
        }
    }
}

@Composable
private fun RoomOperationButton(
    modifier: Modifier = Modifier,
    onCreateLiveRoom: () -> Unit = {},
    onJoinMyLiveRoom: (MyLiveRoomInfo) -> Unit = {},
) {
    val selfUser = LocalSelfUserProvider.current
    val myRoom = selfUser.myLiveRoom

    if (myRoom == null && !selfUser.canCreateLiveRoom) {
        return
    }
    Row(
        modifier =
            modifier
                .paint(painter = painterResource(R.drawable.ic_operation_gradient_bg), contentScale = ContentScale.FillBounds)
                .click(noEffect = true) {
                    if (myRoom != null) {
                        onJoinMyLiveRoom(myRoom)
                    } else {
                        onCreateLiveRoom()
                    }
                }.padding(horizontal = 48.dp, vertical = 24.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Image(WakooIcons.FiSrGlassCheers, contentDescription = "", modifier = Modifier.size(18.dp))
        SizeWidth(5.dp)
        Text(
            if (myRoom == null) {
                "创建派对".localized
            } else {
                "我的派对".localized
            },
            fontSize = 14.sp,
            color = Color(0xff111111),
            lineHeight = 16.sp,
            fontWeight = FontWeight.W900,
            fontFamily = FontFamily.MI_SANS,
        )
    }
}

@Composable
@Preview(showBackground = true)
private fun PreviewRecommendLiveRoom() {
    val room =
        AppJson.decodeFromString<RecommendLiveRoom>(
            "{\"id\":394,\"title\":\"苹果13测试机创建的电台苹果13测试机\",\"room_mode\":5,\"owner\":{\"userid\":4422,\"nickname\":\"苹果13test测\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceFo?x-oss-process=image/format,webp\"},\"recommend_user_list\":[{\"userid\":4422,\"nickname\":\"苹果13test测\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceFo?x-oss-process=image/format,webp\",\"gender\":1,\"age\":20,\"attractive_tags\":[],\"using_mic\":false,\"in_room\":false},{\"userid\":4422,\"nickname\":\"苹果13test测\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceFo?x-oss-process=image/format,webp\",\"gender\":1,\"age\":20,\"attractive_tags\":[],\"using_mic\":false,\"in_room\":false},{\"userid\":4422,\"nickname\":\"苹果13test测\",\"avatar_url\":\"https://s.test.wakooclub.com/aaceFo?x-oss-process=image/format,webp\",\"gender\":1,\"age\":20,\"attractive_tags\":[],\"using_mic\":false,\"in_room\":false}],\"has_red_packet\":false,\"has_ongoing_coin_pk_game\":false,\"in_room_user_cnt\":6}",
        )

    Column {
        Text(
            "PK模式".localized,
            modifier =
                Modifier
                    .roundedParallelogram(
                        8f,
                        brush =
                            Brush.horizontalGradient(
                                listOf(Color(0xff6FC3FF), Color(0xffF772FF)),
                            ),
                        Color.Transparent,
                        0.dp,
                        cornerRadius = 12f,
                    ).padding(horizontal = 10.dp, vertical = 3.dp),
        )

        LiveRoomItem(room)

        RoomOperationButton()

        SizeHeight(10.dp)
    }
}
