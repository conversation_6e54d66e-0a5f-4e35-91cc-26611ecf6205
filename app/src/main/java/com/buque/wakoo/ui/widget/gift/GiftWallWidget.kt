package com.buque.wakoo.ui.widget.gift

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.bean.GiftWall
import com.buque.wakoo.bean.GiftWallSummaryBean
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.GiftWallItemBG
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.RecyclePoolUtils
import com.buque.wakoo.viewmodel.GiftWallViewModel
import kotlinx.coroutines.launch
import kotlin.random.Random

//region bean

sealed interface GiftWallItem {
    val type: Int

    val span: Int
}

class SpaceItem(
    val height: Int,
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 0
}

data class TopRadiusItem(
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 1
}

data class BottomRadiusItem(
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 2
}

class SpanItem(
    override val span: Int,
    val isStart: Boolean = false,
    val isFirstLine: Boolean = false,
) : GiftWallItem {
    override val type: Int
        get() = 3
}

class CategoryTitleItem(
    val title: String,
    val lightCount: Int = 0,
    val totalCount: Int = 0,
    val seriesId: Int = -1,
    override val span: Int,
) : GiftWallItem {
    override val type: Int
        get() = 4

    fun updateLightCount(newCount: Int): CategoryTitleItem = CategoryTitleItem(title, lightCount + newCount, totalCount, seriesId, span)
}

data class GiftItem(
    val gift: GiftWall.GiftWrapper.Gift,
    val count: Int,
    val direction: Int = -1,
    val seriesId: Int = -1,
    val isFirstLine: Boolean = false,
) : GiftWallItem {
    fun updateLightCount(newCount: Int): GiftItem = GiftItem(gift, count + newCount, direction, seriesId)

    override val type: Int
        get() = 5

    override val span: Int
        get() = 2
}

data class CategoryGiftWall(
    val category: String,
    val items: List<GiftWallItem>,
    val lightCount: Int = 0,
    val totalCount: Int = 0,
)

data class NewCategoryGiftWall(
    val category: String,
    val items: List<SeriesItem>,
    val lightCount: Int = 0,
    val totalCount: Int = 0,
) {
    data class SeriesItem(
        val seriesName: String,
        val items: List<GiftItem>,
        val lightCount: Int = 0,
        val totalCount: Int = 0,
    )
}
//endregion

data class GiftWallContainerStyle(
    val backgroundColor: Color = Color(0xFFFFF3F6),
    val activeTitleColor: Color = Color(0xFF1D2129),
    val inactiveTitleColor: Color = Color(0xFF86909C),
    val activeCountColor: Color = activeTitleColor,
    val inactiveCountColor: Color = inactiveTitleColor,
    @DrawableRes
    val activeItemBgImg: Int = -1,
    @DrawableRes
    val inactiveItemBgImg: Int = -1,
    val categoryTitleColor: Color = Color(0xFFFFB71A),
    val paddingValues: PaddingValues = PaddingValues(horizontal = 3.dp, vertical = 5.dp),
    val marginValues: PaddingValues = PaddingValues(0.dp),
    val itemHeight: Int = 0,
    val bottomPadding: Int = 0,
)

//region 礼物墙老UI

@Composable
private fun GiftWallPageScaffold(
    summary: GiftWallSummaryBean,
    modifier: Modifier = Modifier,
    contentWidget: @Composable (GiftWallSummaryBean.Tab) -> Unit = {},
) {
//    TotalGiftStatisticWidget(summary.starCnt, summary.totalCnt)

    val pageState =
        rememberPagerState {
            summary.tabs.size
        }
    val selectedTabIndex = pageState.currentPage
    val scope = rememberCoroutineScope()

    Column(modifier = modifier) {
        ScrollableTabRow(
            selectedTabIndex = selectedTabIndex,
            modifier = Modifier.heightIn(min = 48.dp),
            edgePadding = 0.dp,
            divider = {},
            containerColor = Color.Transparent,
            indicator = { tabPositions ->
                if (selectedTabIndex < tabPositions.size) {
                    Box(
                        modifier =
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                .requiredWidth(12.dp)
                                .height(3.dp)
                                .background(
                                    WakooSecondarySelected,
                                    CircleShape,
                                ),
                    )
                }
            },
        ) {
            summary.tabs.forEachIndexed { index, tab ->
                Tab(
                    selected = selectedTabIndex == index,
                    selectedContentColor = WakooSecondarySelected,
                    unselectedContentColor = WakooSecondaryUnSelected,
                    onClick = {
                        scope.launch {
                            pageState.animateScrollToPage(index)
                        }
                    },
                    content = {
                        Box(
                            modifier = Modifier.padding(bottom = 4.dp),
                            contentAlignment = Alignment.Center,
                        ) {
                            Text(
                                text = tab.name,
                                modifier = Modifier.padding(horizontal = 18.dp),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = if (selectedTabIndex == index) FontWeight.SemiBold else FontWeight.Normal,
                            )
                        }
                    },
                )
            }
        }

        HorizontalPager(pageState, modifier = Modifier.weight(1f)) {
            contentWidget(summary.tabs[it])
        }
    }
}

/**
 * 礼物墙布局
 *
 * @param list 礼物墙转换后的数据
 * @param colors 礼物墙布局渐变颜色
 * @param borderColor 礼物墙的边框颜色
 */
@Composable
private fun GiftWallPageContent(
    list: List<GiftWallItem>,
    colors: List<Color> = listOf(Color(0xFFE9FFE2), Color(0xfff9fff9)),
    borderColor: Color = Color(0xFFB5FFDD),
    giftItemBgColor: Color = Color.White,
    onGiftClicked: (GiftItem) -> Unit = {},
) {
    val gradientSecondColor =
        remember {
            getColorFromGradient(colors, position = 0.2f)
        }

    LazyVerticalGrid(
        columns = GridCells.Fixed(8),
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(horizontal = 16.dp),
    ) {
        itemsIndexed(list, span = { index, it ->
            GridItemSpan(it.span)
        }) { index, it ->
            when (it) {
                is SpaceItem -> {
                    SpacerItem(it.height)
                }

                is TopRadiusItem -> {
                    Spacer(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(12.dp)
                                .background(
                                    brush = Brush.verticalGradient(listOf(colors.first(), gradientSecondColor)),
                                    RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
                                ).border(
                                    0.8.dp,
                                    color = borderColor,
                                    mode = 2,
                                    radius = 12.dp,
                                ),
                    )
                }

                is BottomRadiusItem -> {
                    Spacer(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(12.dp)
                                .background(
                                    color = colors.last(),
                                    RoundedCornerShape(bottomStart = 12.dp, bottomEnd = 12.dp),
                                ).border(
                                    0.8.dp,
                                    color = borderColor,
                                    mode = 4,
                                    radius = 12.dp,
                                ),
                    )
                }

                is SpanItem -> {
                    SpanItemWidget(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .background(
                                    brush =
                                        Brush.verticalGradient(
                                            listOf(
                                                if (it.isFirstLine) gradientSecondColor else colors.last(),
                                                colors.last(),
                                            ),
                                        ),
                                ).border(
                                    0.8.dp,
                                    color = borderColor,
                                    mode = if (it.isStart) 1 else 3,
                                ),
                    )
                }

                is CategoryTitleItem -> {
                    if (it.title.isNotBlank()) {
                        Box(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .border(
                                        0.8.dp,
                                        color = borderColor,
                                        mode = 5,
                                    ).background(
                                        brush = Brush.verticalGradient(listOf(colors.first(), gradientSecondColor)),
                                    ).padding(vertical = 6.dp),
                            contentAlignment = Alignment.BottomCenter,
                        ) {
                            Image(painterResource(R.drawable.ic_giftwall_series_bg), contentDescription = "")
                            Text(
                                text =
                                    buildAnnotatedString {
                                        append(it.title)
                                        append("(")
                                        append(it.lightCount.toString())
                                        append("/")
                                        append(it.totalCount.toString())
                                        append(")")
                                    },
                                color = Color(0xFF111111),
                                fontSize = 11.sp,
                                textAlign = TextAlign.Center,
                                lineHeight = 15.sp,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(bottom = 2.dp),
                            )
                        }
                    }
                }

                is GiftItem -> {
                    GiftUIItem(
                        it.gift,
                        it.count,
                        iconBgColor = giftItemBgColor,
                        modifier =
                            Modifier
                                .background(
                                    brush =
                                        Brush.verticalGradient(
                                            listOf(
                                                if (it.isFirstLine) gradientSecondColor else colors.last(),
                                                colors.last(),
                                            ),
                                        ),
                                ).border(
                                    if (it.direction < 0) 0.dp else 0.8.dp,
                                    color = borderColor,
                                    mode = it.direction,
                                ).click(
                                    noEffect = true,
                                    onClick =
                                        remember(it.gift.id) {
                                            {
                                                onGiftClicked(it)
                                            }
                                        },
                                ),
                    )
                }
            }
        }
    }
}

@Composable
private fun GiftUIItem(
    gift: GiftWall.GiftWrapper.Gift,
    count: Int,
    modifier: Modifier = Modifier,
    iconBgColor: Color,
) {
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(horizontal = 3.dp, vertical = 5.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val colorFilter =
            if (count > 0) {
                null
            } else {
                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
            }

        NetworkImage(
            gift.icon,
            modifier =
                Modifier
                    .size(64.dp, 74.dp)
                    .background(color = iconBgColor, shape = RoundedCornerShape(10.dp)),
            contentScale = ContentScale.Inside,
            colorFilter = colorFilter,
        )

        Text(
            text = gift.name,
            modifier =
                Modifier
                    .padding(top = 5.dp)
                    .height(18.dp),
            color = Color(0xFF222222),
            fontSize = 11.sp,
            lineHeight = 22.sp,
            fontWeight = FontWeight.SemiBold,
            maxLines = 1,
        )
        Text(
            text = "x$count",
            modifier =
                Modifier
                    .padding(top = 2.dp)
                    .height(16.dp),
            color = Color(0xFFC8CCD7),
            fontSize = 12.sp,
            lineHeight = 22.sp,
            fontWeight = FontWeight.W900,
            fontFamily = FontFamily.MI_SANS,
            maxLines = 1,
        )
    }
}

@Composable
private fun SpacerItem(height: Int) {
    Spacer(
        modifier =
            Modifier
                .fillMaxWidth()
                .height(height.dp),
    )
}

@Composable
private fun SpanItemWidget(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier.padding(horizontal = 3.dp, vertical = 5.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(74.dp))
        Text(
            text = "礼物".localized,
            modifier =
                Modifier
                    .padding(top = 5.dp)
                    .height(18.dp),
            color = Color.Transparent,
            fontSize = 12.sp,
            maxLines = 1,
        )
        Text(
            text = "x0",
            modifier =
                Modifier
                    .padding(top = 2.dp)
                    .height(16.dp),
            color = Color.Transparent,
            fontSize = 10.sp,
            maxLines = 1,
        )
    }
}
//endregion

//region 新的页面UI

@Composable
fun UserGiftWallTab(
    sceneId: String,
    sceneType: Int? = null,
    refreshFlag: Int = 0,
) {
    val viewModel =
        viewModel<GiftWallViewModel>(initializer = {
            GiftWallViewModel(
                sceneType, // 如果是礼物面板点进来的, 就是4私聊, 否则全是6个人主页来的
                sceneId.toInt(),
                true,
            )
        })

    val giftWallSummary by viewModel.giftWallSummary.collectAsStateWithLifecycle()

    UserGiftWallTabScaffold(giftWallSummary) {
        val state by viewModel.getState(it, 4).collectAsStateWithLifecycle()
        when (state) {
            is GiftWallViewModel.State.LoadSucceedState -> {
                (state as? GiftWallViewModel.State.LoadSucceedState)?.let {
                    val item = it.list.find { it is CategoryTitleItem } as? CategoryTitleItem
                    val withBox = !(item?.title.isNullOrBlank())

                    UserGiftWallTabContent(it.list)
                }
            }

            GiftWallViewModel.State.LoadingState -> {
                Box(
                    modifier =
                        Modifier
                            .fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    CircularProgressIndicator()
                }
            }

            GiftWallViewModel.State.LoadFailed -> {
                Box(
                    modifier =
                        Modifier
                            .fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Text("加载失败".localized)
                }
            }

            else -> {
            }
        }
    }
}

@Composable
fun UserGiftWallTabScaffold(
    summary: GiftWallSummaryBean,
    contentWidget: @Composable (GiftWallSummaryBean.Tab) -> Unit = {},
) {
    Box(
        modifier =
            Modifier
                .clip(RoundedCornerShape(16.dp))
                .background(color = Color(0xfff9fff9))
                .border(1.dp, color = Color(0xffb5ffdd), RoundedCornerShape(16.dp)),
    ) {
        Spacer(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .height(115.dp)
                    .background(
                        brush = Brush.verticalGradient(listOf(Color(0xffecffe6), Color(0xfff9fff9))),
                    ),
        )

        val pageState =
            rememberPagerState {
                summary.tabs.size
            }
        val selectedTabIndex = pageState.currentPage
        val scope = rememberCoroutineScope()

        Column {
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.padding(horizontal = 30.dp, vertical = 15.dp),
                divider = {},
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Image(
                            painter = painterResource(R.drawable.ic_gift_wall_tab_bg),
                            contentDescription = null,
                            modifier = Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex]),
                        )
                    }
                },
            ) {
                summary.tabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = selectedTabIndex == index,
                        selectedContentColor = Color(0xff54ab6f),
                        unselectedContentColor = Color(0x8054ab6f),
                        onClick = {
                            scope.launch {
                                pageState.animateScrollToPage(index)
                            }
                        },
                        content = {
                            Text(
                                text = tab.name,
                                modifier = Modifier.padding(horizontal = 18.dp),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = if (selectedTabIndex == index) FontWeight.SemiBold else FontWeight.Normal,
                            )
                        },
                    )
                }
            }

            HorizontalPager(pageState, modifier = Modifier.weight(1f)) {
                contentWidget(summary.tabs[it])
            }
        }
    }
}

@Composable
fun UserGiftWallTabContent(
    list: List<GiftWallItem>,
    onGiftClicked: (GiftItem) -> Unit = {},
) {
    val realList by remember {
        derivedStateOf {
            list.filter {
                it is GiftItem || it is CategoryTitleItem || it is SpanItem
            }
        }
    }
    LazyVerticalGrid(
        GridCells.Fixed(8),
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(horizontal = 4.dp, vertical = 14.dp),
    ) {
        itemsIndexed(realList, span = { index, item ->
            GridItemSpan(item.span)
        }) { index, it ->
            when (it) {
                is CategoryTitleItem -> {
                    if (it.title.isNotBlank()) {
                        Row(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 6.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                        ) {
                            Image(painterResource(R.drawable.ic_gift_wall_start_left), contentDescription = "")
                            Text(
                                text =
                                    buildAnnotatedString {
                                        append(it.title)
                                        append("(")
                                        append(it.lightCount.toString())
                                        append("/")
                                        append(it.totalCount.toString())
                                        append(")")
                                    },
                                color = Color(0xff54ab6f),
                                fontSize = 11.sp,
                                textAlign = TextAlign.Center,
                                lineHeight = 15.sp,
                                fontWeight = FontWeight.Medium,
                            )
                            Image(painterResource(R.drawable.ic_gift_wall_start_right), contentDescription = "")
                        }
                    }
                }

                is GiftItem -> {
                    Column(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 3.dp, vertical = 5.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        val colorFilter =
                            if (it.count > 0) {
                                null
                            } else {
                                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
                            }

                        Box {
                            Image(
                                WakooIcons.GiftWallItemBG,
                                contentDescription = null,
                                modifier = Modifier.fillMaxWidth().aspectRatio(0.84375f),
                            )
                            NetworkImage(
                                it.gift.icon,
                                modifier =
                                    Modifier
                                        .align(Alignment.BottomCenter)
                                        .padding(bottom = 5.dp)
                                        .fillMaxWidth(0.8f)
                                        .aspectRatio(1f),
                                contentScale = ContentScale.Inside,
                                colorFilter = colorFilter,
                            )
                        }

                        Text(
                            text = it.gift.name,
                            modifier =
                                Modifier
                                    .padding(top = 5.dp)
                                    .height(18.dp),
                            color = Color(0xFF222222),
                            fontSize = 11.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.SemiBold,
                            maxLines = 1,
                        )
                        Text(
                            text = "x${it.count}",
                            modifier =
                                Modifier
                                    .padding(top = 2.dp)
                                    .height(16.dp),
                            color = Color(0xFFC8CCD7),
                            fontSize = 12.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.W900,
                            fontFamily = FontFamily.MI_SANS,
                            maxLines = 1,
                        )
                    }
                }

                is SpanItem -> {
                    SpanItemWidget(modifier = Modifier.fillMaxWidth())
                }

                else -> {
                }
            }
        }
    }
}

//endregion

@Preview(showBackground = true)
@Composable
fun PreviewGiftWallPage() {
    val giftBean =
        GiftWall.GiftWrapper(
            gift = GiftWall.GiftWrapper.Gift(t = 0, id = 311, name = "测试礼物"),
            count = Random.nextInt(0, 2),
        )
    val list =
        mutableListOf<GiftWall.SeriesGift>()
            .apply {
                for (j in 0..2) {
                    add(
                        GiftWall.SeriesGift(
                            1,
                            seriesName = "测试礼物$j",
                            gifts =
                                buildList {
                                    val listSize = Random.nextInt(3, 10)
                                    for (i in 0 until listSize) {
                                        add(GiftWall.GiftWrapper(giftBean.gift, Random.nextInt(0, 2)))
                                    }
                                },
                        ),
                    )
                }
            }
    val items = GiftWallViewModel.convertGiftWall(list, 5)
    UserGiftWallTabScaffold(
        GiftWallSummaryBean(
            tabs =
                listOf(
                    GiftWallSummaryBean.Tab(t = 0, name = "盲盒礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                    GiftWallSummaryBean.Tab(t = 1, name = "普通礼物"),
                ),
        ),
    ) {
        UserGiftWallTabContent(items) {
        }
    }
}

//region 工具类 , 目前只有这里用到了,所以只放在这里

fun getColorFromGradient(
    colors: List<Color>,
    stops: List<Float>? =
        colors.mapIndexed { index, color ->
            (1f / colors.size) * index
        },
    position: Float, // 0f - 1f
): Color {
    val validPosition = position.coerceIn(0f, 1f)
    val computedStops =
        stops?.takeIf { it.size == colors.size }
            ?: List(colors.size) { i -> if (i.toFloat() / (colors.size - 1) < 1f) i.toFloat() / (colors.size - 1) else 1f }

    // 边界检查
    if (validPosition <= computedStops.first()) return colors.first()
    if (validPosition >= computedStops.last()) return colors.last()

    // 查找插值区间
    val index = computedStops.indexOfFirst { it >= validPosition } - 1
    val startStop = computedStops[index]
    val endStop = computedStops[index + 1]
    val intervalFraction = (validPosition - startStop) / (endStop - startStop)

    return lerp(colors[index], colors[index + 1], intervalFraction)
}

class ComposePath {
    companion object {
        fun obtain(): Path {
            val thiz = Path::class.java.toString()
            if (!RecyclePoolUtils.hasgister(thiz)) {
                RecyclePoolUtils.register(
                    thiz,
                    object : RecyclePoolUtils.InstanceCallback<Path> {
                        override fun newInstance(): Path = Path()

                        override fun reset(data: Path) {
                            data.reset()
                        }
                    },
                )
            }
            return RecyclePoolUtils.obtain()
        }

        inline fun doRun(func: (Path) -> Unit) {
            val obtain = obtain()
            func(obtain)
            RecyclePoolUtils.recycle(obtain)
        }
    }
}

/**
 * @param borderWidth
 * @param brush
 * @param radius
 * @param mode 0 不是任何一遍 1左边 2上边 3右边 4下边 5左右 6上下
 */
private fun Modifier.border(
    borderWidth: Dp = 1.dp,
    brush: Brush,
    radius: Dp = 0.dp,
    mode: Int = 0,
) = this.drawBehind {
    if (borderWidth == 0.dp || mode <= 0) {
        return@drawBehind
    }

    val strokeWidth = borderWidth.toPx()
    val width = size.width
    val height = size.height
    ComposePath.doRun {
        it.apply {
            when (mode) {
                2 -> {
                    moveTo(0f, radius.toPx())
                    quadraticTo(
                        0f,
                        0f,
                        radius.toPx(),
                        0f,
                    )
                    // 上边线 + topEndRadius
                    lineTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                }

                4 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                1 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                }

                3 -> {
                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                5 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )

                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                6 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                else -> {
                }
            }
        }
        drawPath(
            path = it,
            brush = brush,
            style = Stroke(strokeWidth),
        )
    }
}

/**
 * @param borderWidth
 * @param brush
 * @param radius
 * @param mode 0 不是任何一遍 1左边 2上边 3右边 4下边 5左右 6上下
 */
private fun Modifier.border(
    borderWidth: Dp = 1.dp,
    color: Color,
    radius: Dp = 0.dp,
    mode: Int = 0,
) = this.drawBehind {
    if (borderWidth == 0.dp || mode <= 0) {
        return@drawBehind
    }

    val strokeWidth = borderWidth.toPx()
    val width = size.width
    val height = size.height

    ComposePath.doRun {
        it.apply {
            when (mode) {
                2 -> {
                    moveTo(0f, radius.toPx())
                    quadraticTo(
                        0f,
                        0f,
                        radius.toPx(),
                        0f,
                    )
                    // 上边线 + topEndRadius
                    lineTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                }

                4 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                1 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                }

                3 -> {
                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                5 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f,
                        0f,
                        0f,
                        radius.toPx(),
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )

                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width,
                        0f,
                        width,
                        radius.toPx(),
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width,
                        height,
                        width - radius.toPx(),
                        height,
                    )
                }

                6 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f,
                        height,
                        radius.toPx(),
                        height,
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width,
                        height,
                        width,
                        height - radius.toPx(),
                    )
                }

                else -> {
                }
            }
        }

        drawPath(
            path = it,
            color = color,
            style = Stroke(strokeWidth),
        )
    }
}

//endregion
