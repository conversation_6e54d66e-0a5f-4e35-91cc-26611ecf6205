package com.buque.wakoo.ui.screens.messages.notification

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.R
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooGreen
import com.buque.wakoo.ui.theme.WakooRed
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth

/**
 * 通知/消息列表项组件
 *
 * @param avatar 头像资源ID
 * @param name 发送者名称
 * @param message 消息内容
 * @param time 时间
 * @param unreadCount 未读消息数，为null则不显示
 * @param isOfficial 是否是官方消息
 */
@Composable
fun NotificationListItem(
    avatar: Int,
    name: String,
    message: String,
    time: String,
    unreadCount: Int? = null,
    isOfficial: Boolean = false,
) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.Top,
    ) {
        // 头像
        Image(
            painter = painterResource(id = avatar),
            contentDescription = "Avatar of $name",
            modifier =
                Modifier
                    .size(48.dp)
                    .clip(CircleShape),
        )

        SizeWidth(6.dp)

        // 消息内容
        Column(
            modifier =
                Modifier
                    .weight(1f)
                    .padding(top = 3.5.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = name,
                    style =
                        MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Normal,
                            fontSize = MaterialTheme.typography.titleMedium.fontSize,
                        ),
                )

                if (isOfficial) {
                    SizeWidth(4.dp)
                    Box(
                        modifier =
                            Modifier
                                .background(
                                    color = WakooGreen,
                                    shape = RoundedCornerShape(4.dp),
                                ).padding(horizontal = 10.dp, vertical = 5.dp),
                        contentAlignment = Alignment.Center,
                    ) {
                        Text(
                            text = "官方".localized,
                            style =
                                MaterialTheme.typography.labelSmall.copy(
                                    color = Color.White,
                                    fontWeight = FontWeight.Medium,
                                ),
                        )
                    }
                }
            }

            SizeHeight(9.dp)

            Text(
                text = message,
                style =
                    MaterialTheme.typography.labelSmall.copy(
                        color = WakooGrayText,
                    ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }

        SizeWidth(8.dp)

        // 右侧时间和未读数
        Column(
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.padding(top = 6.dp),
        ) {
            Text(
                text = time,
                style =
                    MaterialTheme.typography.labelSmall.copy(
                        color = WakooGrayText,
                    ),
            )

            unreadCount?.let {
                Box(
                    modifier =
                        Modifier
                            .background(
                                color = WakooRed,
                                shape = CircleShape,
                            ).padding(horizontal = 4.dp, vertical = if (it >= 99) 0.dp else 0.5.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = if (it >= 99) "99+" else it.toString(),
                        color = Color.White,
                        style =
                            MaterialTheme.typography.labelSmall.copy(
                                fontWeight = FontWeight.Medium,
                            ),
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun NotificationListItemPreview() {
    WakooTheme {
        Column {
            NotificationListItem(
                avatar = R.drawable.ic_app_logo,
                name = "Wakoo官方通知",
                message = "我在这里等了好久，你终于出现啦啦啦啦啦啦啦啦啦...",
                time = "1分钟前",
                unreadCount = 99,
                isOfficial = true,
            )

            NotificationListItem(
                avatar = R.drawable.ic_app_logo,
                name = "幼儿园搬花",
                message = "我在这里等了好久，你终于出现啦！",
                time = "1分钟前",
                unreadCount = 2,
            )

            NotificationListItem(
                avatar = R.drawable.ic_app_logo,
                name = "幼儿园搬花",
                message = "我在这里等了好久，你终于出现啦！",
                time = "1分钟前",
            )
        }
    }
}
