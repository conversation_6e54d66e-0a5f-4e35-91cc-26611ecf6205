package com.buque.wakoo.ui.screens.messages.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.bean.BuddyZone
import com.buque.wakoo.bean.message.BBoxInfo
import com.buque.wakoo.bean.message.appendWithStyle
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.CpRelationInfo
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.ui.icons.BrokeUp
import com.buque.wakoo.ui.icons.Close
import com.buque.wakoo.ui.icons.QuestionLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.icons.Wing
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooSecondaryText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.widget.DashedDivider
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.DateTimeUtils.formatTimeWithHours
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.tryToLink
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

private val PINK = Color(0xFFFE669E)
private val PINK_LIGHT = Color(0xFFFFDFF0)

@Composable
fun BuddyCard(
    message: String,
    buttonText: String,
    modifier: Modifier = Modifier,
    bgRes: Int = R.drawable.bg_buddy_roo,
    height: Dp = 182.dp,
    spacing: Dp = 16.dp,
    onClick: OnAction = {},
) {
    Column(
        modifier =
            modifier
                .size(295.dp, height)
                .paint(painterResource(bgRes), contentScale = ContentScale.FillBounds)
                .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Bottom,
    ) {
        Text(message, color = PINK, textAlign = TextAlign.Center, lineHeight = 18.sp)
        SizeHeight(spacing)
        PinkButton(buttonText, onClick = onClick)
    }
}

@Composable
fun PinkButton(
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    paddingValues: PaddingValues = PaddingValues(horizontal = 26.dp),
    maxHeight: Dp = 36.dp,
    onClick: () -> Unit = {},
) {
    SolidButton(
        text,
        onClick = onClick,
        backgroundColor = if (enabled) PINK else Color(0xFFFFDFF0),
        fontSize = 14.sp,
        enabled = enabled,
        textColor = if (enabled) Color.White else Color(0xFFCB3585),
        modifier = modifier.heightIn(max = maxHeight),
        paddingValues = paddingValues,
    )
}

@Composable
fun CPCard(
    leftUser: User,
    rightUser: User,
    cpValue: String,
    daysDesc: String,
    modifier: Modifier = Modifier,
    cardBackground: String? = null,
    hideCPRank: Boolean = false,
    onGotoCPRank: OnAction = {},
) {
    Box(
        modifier
            .fillMaxWidth()
            .aspectRatio(343 / 100f)
            .then(
                if (cardBackground.isNullOrBlank()) {
                    Modifier
                        .background(Brush.verticalGradient(listOf(Color(0xFFFFDFEF), Color.White)), RoundedCornerShape(12.dp))
                        .border(
                            1.5.dp,
                            Brush.verticalGradient(listOf(Color(0xFFFFC4E3), Color(0xFFFFE3F2))),
                            RoundedCornerShape(12.dp),
                        )
                } else {
                    Modifier
                },
            ),
    ) {
        if (!cardBackground.isNullOrEmpty()) {
            NetworkImage(cardBackground, modifier = Modifier.fillMaxSize(), contentScale = ContentScale.FillBounds)
        }
        Row(modifier = Modifier.fillMaxSize(), verticalAlignment = Alignment.CenterVertically) {
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                SizeHeight(20.dp)
                AvatarNetworkImage(leftUser, modifier = Modifier.size(48.dp))
                SizeHeight(8.dp)
                Text(
                    leftUser.name,
                    fontSize = 12.sp,
                    modifier =
                        Modifier
                            .widthIn(max = 120.dp)
                            .padding(horizontal = 2.dp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                SizeHeight(8.dp)
            }

            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Column(
                    modifier =
                        Modifier
                            .size(74.dp, 50.dp)
                            .paint(painterResource(R.drawable.icon_heart), contentScale = ContentScale.FillBounds),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    SizeHeight(12.dp)
                    Text("CP值".localized, color = Color.White, fontSize = 12.sp)
                    SizeHeight(4.dp)
                    BasicText(
                        cpValue,
                        style = TextStyle(fontWeight = FontWeight.Medium, color = Color.White),
                        maxLines = 1,
                        autoSize = TextAutoSize.StepBased(minFontSize = 6.sp, maxFontSize = 11.sp),
                    )
                }
                Text(daysDesc, fontSize = 11.sp, color = PINK)
            }

            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                SizeHeight(20.dp)
                AvatarNetworkImage(rightUser, modifier = Modifier.size(48.dp))
                SizeHeight(8.dp)
                Text(
                    rightUser.name,
                    fontSize = 12.sp,
                    modifier =
                        Modifier
                            .widthIn(max = 120.dp)
                            .padding(horizontal = 2.dp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                SizeHeight(8.dp)
            }
        }

//        if (!hideCPRank) {
        val shape =
            remember {
                RoundedCornerShape(topEnd = 12.dp, bottomStart = 12.dp)
            }
        Row(
            modifier =
                Modifier
                    .clip(shape)
                    .clickable(onClick = onGotoCPRank)
                    .background(PINK, shape)
                    .padding(4.dp)
                    .align(Alignment.TopEnd),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text("CP榜单".localized, color = Color.White, fontSize = 12.sp)
            Icon(Icons.Default.KeyboardArrowRight, "arr", modifier = Modifier.size(14.dp), tint = Color.White)
        }
//        }
    }
}

@Composable
fun TargetUserCPCard(
    targetUser: User,
    cpInfo: CpRelationInfo,
    modifier: Modifier = Modifier,
    onlyPublicCP: Boolean = true,
) {
    val leftUser: User = targetUser
    val rightUser: User? =
        if (onlyPublicCP) {
            cpInfo.publicCp
        } else {
            cpInfo.cp
        }

    if (rightUser != null) {
        val extra = cpInfo.cpExtraInfo
        CPCard(
            leftUser = leftUser,
            rightUser = rightUser,
            modifier = modifier,
            daysDesc = extra.togatherDays,
            cpValue = extra.cpValue.toString(),
            hideCPRank = cpInfo.hideCpPublicBillboard,
            cardBackground = cpInfo.cpCardBackground,
            onGotoCPRank = {
                cpInfo.cpPublicBillboardUrl.tryToLink()
            },
        )
    }
}

@Composable
fun PublishCpMedal(
    publicCp: User,
    publicCpMedalUrl: String,
    modifier: Modifier = Modifier.size(64.dp, 18.dp),
) {
    Box(
        modifier = modifier,
    ) {
        NetworkImage(
            data = publicCpMedalUrl,
            modifier = Modifier.fillMaxSize(),
        )
        AvatarNetworkImage(
            user = publicCp,
            size = Dp.Unspecified,
            enabled = false,
            modifier =
                Modifier
                    .padding(1.5.dp)
                    .fillMaxHeight()
                    .aspectRatio(1f),
        )
    }
}

@Preview
@Composable
private fun PreviewBuddyCardCPZone() {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(Color(0xFFEEEEEE)),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        BuddyCard("恭喜你们成功结成CP，解锁CP空间", "查看CP空间")
        SizeHeight(20.dp)
        BuddyCard(
            "对方赠送了CP表白礼物，希望和你结成线上虚拟情侣，邀请24小时后失效",
            "查看邀请",
            bgRes = R.drawable.bg_buddy_invite,
            spacing = 12.dp,
        )

        SizeHeight(20.dp)
        Box(modifier = Modifier.width(295.dp)) {
            BuddyCard(
                "恋爱君温馨提示：你们关系升级的很快呢！点击下方按钮可与TA结成线上虚拟情侣，解锁更多亲密互动玩法",
                "去组CP",
                modifier = Modifier.padding(top = 16.dp),
                bgRes = R.drawable.bg_buddy_msg,
                height = 170.dp,
                spacing = 12.dp,
            )
            Box(
                modifier =
                    Modifier
                        .size(88.dp, 48.dp)
                        .align(Alignment.TopCenter),
            ) {
                AvatarNetworkImage(BasicUser.sampleBoy, modifier = Modifier.size(48.dp))
                AvatarNetworkImage(
                    BasicUser.sampleGirl,
                    modifier =
                        Modifier
                            .size(48.dp)
                            .align(Alignment.CenterEnd),
                )
            }
        }

        CPCard(BasicUser.sampleBoy, BasicUser.sampleGirl, "2200", daysDesc = "在一起120天", modifier = Modifier.padding(16.dp))
    }
}

@Composable
fun BuddyZone(
    targetUser: User,
    cpInfo: CpRelationInfo,
    modifier: Modifier = Modifier,
    onPublicCP: OnAction = {},
    onUpLevel: OnAction = {},
    onDestroyCP: OnAction = {},
    onShowRule: OnDataCallback<String> = {},
    onDoTask: OnDataCallback<BuddyZone.DailyTaskInfo.TaskInfo> = {},
) {
    val levelInfo = cpInfo.cpExtraInfo.levelInfo
    val mod =
        Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(8.dp))
            .padding(8.dp, 12.dp)
    val scrollState = rememberScrollState()
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        SizeHeight(8.dp)
        TargetUserCPCard(targetUser, cpInfo, onlyPublicCP = !targetUser.isSelf)
        val zone = cpInfo.cpZone ?: return
        val cpTask = zone.dailyTaskInfo
        val self = LocalSelfUserProvider.current
        val isChallenge = cpTask.ongoingActivity != null && cpTask.ongoingActivity.activityBonusList.isNotEmpty()
        Column(
            modifier =
                Modifier
                    .padding(top = 8.dp)
                    .background(Color(0xFFFFE7F3), RoundedCornerShape(12.dp))
                    .padding(8.dp, 16.dp),
        ) {
            Row(modifier = Modifier.fillMaxWidth()) {
                DashedDivider(
                    modifier =
                        Modifier
                            .weight(1f)
                            .align(Alignment.Bottom),
                    color = PINK,
                    thickness = 2.dp,
                )
                SizeWidth(4.dp)
                Image(
                    rememberVectorPainter(WakooIcons.Wing),
                    "ic",
                    modifier =
                        Modifier
                            .align(Alignment.Bottom)
                            .graphicsLayer(
                                scaleX = -1f,
                            ),
                )
                Text(
                    if (isChallenge) cpTask.ongoingActivity.activityName else "CP空间".localized,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(horizontal = 8.dp),
                )
                Image(rememberVectorPainter(WakooIcons.Wing), "ic", modifier = Modifier.align(Alignment.Bottom))
                SizeWidth(4.dp)
                DashedDivider(
                    modifier =
                        Modifier
                            .weight(1f)
                            .align(Alignment.Bottom),
                    color = PINK,
                    thickness = 2.dp,
                )
            }
            SizeHeight(12.dp)

            if (self.isCN) {
                // 官宣等级
                Row(
                    modifier = mod,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text("CP官宣等级：".localized, color = WakooText)
                    if (levelInfo.level > 0) {
                        NetworkImage(
                            levelInfo.wakooNormalImgUrl,
                            modifier = Modifier.size(84.dp, 24.dp),
                        )
                        Weight(1f)
                        if (levelInfo.hasNextLevel) {
                            SizeWidth(4.dp)
                            PinkButton(
                                "升级".localized,
                                onClick = onUpLevel,
                                modifier = Modifier.widthIn(min = 68.dp),
                                paddingValues = PaddingValues(horizontal = 15.dp),
                                maxHeight = 28.dp,
                            )
                        }
                    } else {
                        Text("暂无等级".localized, fontSize = 12.sp, color = WakooGrayText)
                        Weight(1f)
                        PinkButton(
                            "去官宣".localized,
                            onClick = onPublicCP,
                            modifier = Modifier.widthIn(min = 68.dp),
                            paddingValues = PaddingValues(horizontal = 15.dp),
                            maxHeight = 28.dp,
                        )
                    }
                }
            }
//        活动
            val act = cpTask.ongoingActivity
            if (act != null) {
                SizeHeight(8.dp)
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        act.activityDesc,
                        fontSize = 11.sp,
                        modifier = Modifier.widthIn(max = 180.dp),
                        lineHeight = 16.sp,
                        color = Color(0xFF804458),
                    )

                    LazyVerticalGrid(
                        GridCells.Fixed(3),
                        modifier =
                            Modifier
                                .padding(vertical = 8.dp)
                                .heightIn(max = 800.dp)
                                .wrapContentHeight(),
                    ) {
                        items(act.activityBonusList) { item ->
                            Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                                Box(
                                    modifier =
                                        Modifier
                                            .size(80.dp)
                                            .background(
                                                Brush.radialGradient(listOf(Color.White.copy(alpha = 0.25f), Color(0xFFFFC0E2))),
                                                RoundedCornerShape(12.dp),
                                            )
                                            .padding(4.dp),
                                ) {
                                    NetworkImage(
                                        item.icon,
                                        contentScale = ContentScale.FillWidth,
                                        modifier =
                                            Modifier
                                                .fillMaxSize()
                                                .clip(RoundedCornerShape(10.dp)),
                                    )
                                }
                                SizeHeight(8.dp)
                                Text(
                                    item.text,
                                    fontSize = 12.sp,
                                    color = Color(0xFF804458),
                                    modifier =
                                        Modifier
                                            .fillMaxSize()
                                            .widthIn(max = 80.dp)
                                            .padding(horizontal = 12.dp),
                                    textAlign = TextAlign.Center,
                                )
                            }
                        }
                    }
                }
            }

            // 7日恋爱热度
            if (!isChallenge) {
                SizeHeight(8.dp)
                Column(modifier = mod) {
                    Row(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text("7日恋爱热度：".localized, color = WakooText)
                        Text(
                            "${zone.hotDegree}°C",
                            fontSize = 16.sp,
                            color = PINK,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.weight(1f),
                        )
                        Icon(
                            WakooIcons.QuestionLine,
                            modifier =
                                Modifier
                                    .size(24.dp)
                                    .click(onClick = {
                                        onShowRule(zone.cpRuleDesc)
                                    }),
                            contentDescription = "qu",
                            tint = WakooSecondaryText,
                        )
                    }
                    Text(zone.hotDegreeHint, color = PINK, fontSize = 12.sp, lineHeight = 16.sp)
                    SizeHeight(4.dp)
                    LinearProgressIndicator(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(12.dp),
                        progress = { 1f * zone.hotDegree / zone.hotDegreeGoal },
                        strokeCap = StrokeCap.Round,
                        gapSize = 0.dp,
                        color = PINK,
                        drawStopIndicator = {},
                        trackColor = Color(0xFFFFE7F3),
                    )
                    SizeHeight(4.dp)

                    Row(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                    ) {
                        Text("0°C", color = WakooText)
                        Weight()
                        Text("${zone.hotDegreeGoal}°C", color = WakooText)
                    }
                }
            }

            // 任务
            SizeHeight(8.dp)
            Column(modifier = mod) {
                // title
                if (isChallenge) {
                    Column(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(top = 4.dp, bottom = 20.dp),
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(cpTask.dailyTaskTitle, fontSize = 16.sp, color = WakooText, fontWeight = FontWeight.Bold)
                            SizeHeight(10.dp)
                            if (!cpTask.dailyTaskFinished) {
                                SizeWidth(4.dp)
                                Text(
                                    "今日未完成".localized,
                                    modifier =
                                        Modifier
                                            .background(Color(0xFFFFDFF0), RoundedCornerShape(4.dp))
                                            .padding(6.dp, 3.dp),
                                    fontSize = 12.sp,
                                    color = PINK,
                                )
                            }
                            Weight()
                            val deadline = cpTask.taskEndTimestamp.times(1000L)

                            var countDownTime by remember {
                                mutableStateOf(emptyList<String>())
                            }
                            LaunchedEffect(
                                key1 = cpTask.version,
                                key2 = cpTask.dailyTaskFinished,
                                key3 = cpTask.ongoingActivity,
                            ) {
                                if (cpTask.dailyTaskFinished || cpTask.ongoingActivity == null) {
                                    countDownTime = emptyList()
                                    return@LaunchedEffect
                                }
                                while (isActive) {
                                    delay(1000)
                                    val now = System.currentTimeMillis()
                                    if (now >= deadline) {
                                        countDownTime = emptyList()
                                        break
                                    } else {
                                        countDownTime =
                                            formatTimeWithHours(deadline.minus(now))
                                                .also {
                                                    LogUtils.d("count down:$it")
                                                }.split(":")
                                    }
                                }
                            }
                            if (countDownTime.size == 3) {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    countDownTime.forEachIndexed { index, item ->
                                        Text(
                                            item,
                                            fontSize = 14.sp,
                                            color = PINK,
                                            textAlign = TextAlign.Center,
                                            lineHeight = 20.sp,
                                            modifier =
                                                Modifier
                                                    .width(20.dp)
                                                    .aspectRatio(1f)
                                                    .background(PINK_LIGHT, RoundedCornerShape(3.dp)),
                                        )
                                        if (index < countDownTime.lastIndex) {
                                            Text(":", fontSize = 12.sp, color = PINK, fontWeight = FontWeight.Bold)
                                        }
                                    }
                                }
                            }
                        }
                        SizeHeight(8.dp)
                        Text(cpTask.dailyTaskNote, fontSize = 12.sp, color = PINK)
                    }
                } else {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            cpTask.dailyTaskTitle,
                            fontSize = 16.sp,
                            color = WakooText,
                            fontWeight = FontWeight.Bold,
                            modifier =
                                Modifier.paint(
                                    painterResource(R.drawable.ic_pnik_line),
                                    contentScale = ContentScale.Inside,
                                ),
                        )
                        SizeHeight(8.dp)
                        Text(cpTask.dailyTaskNote, fontSize = 12.sp, color = PINK)
                        SizeHeight(16.dp)
                    }
                }

                cpTask.taskInfos.forEach { item: BuddyZone.DailyTaskInfo.TaskInfo ->
                    HorizontalDivider(modifier = Modifier.fillMaxWidth(), thickness = 0.5.dp, color = Color(0xFFE5E5E5))
                    Row(
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(vertical = 20.dp),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(item.taskName, color = WakooText)
                            SizeHeight(8.dp)
                            Text(
                                buildAnnotatedString {
                                    if (item.taskProgress.isNotEmpty()) {
                                        appendWithStyle(item.taskProgress + " ", SpanStyle(color = WakooGrayText))
                                    }
                                    appendWithStyle(item.taskBonus, SpanStyle(color = PINK))
                                },
                                fontSize = 12.sp,
                            )
                        }

                        PinkButton(
                            item.taskBtnLabel,
                            paddingValues = PaddingValues(horizontal = 8.dp),
                            enabled = !item.taskIsFinished,
                            modifier =
                                Modifier
                                    .widthIn(min = 68.dp)
                                    .height(28.dp),
                            onClick = {
                                onDoTask(item)
                            },
                        )
                    }
                }
            }
        }

        Row(
            modifier =
                Modifier
                    .padding(top = 16.dp, bottom = 32.dp)
                    .clip(RoundedCornerShape(50))
                    .click(onClick = onDestroyCP)
                    .background(Color(0xFFEEEEEE), RoundedCornerShape(50))
                    .padding(10.dp)
                    .widthIn(min = 150.dp),
            horizontalArrangement = Arrangement.Center,
        ) {
            Image(rememberVectorPainter(WakooIcons.BrokeUp), contentDescription = "icon")
            SizeWidth(4.dp)
            Text("解除CP".localized, color = Color(0xFFCCCCCC), fontSize = 14.sp)
        }
    }
}

@Composable
fun BBoxDialogContent(
    leftUser: User,
    rightUser: User,
    message: String,
    buttonText: String,
    giftDesc: String,
    onClick: OnAction = {},
) {
    Box(modifier = Modifier.size(311.dp, (294 + 16).dp)) {
        Image(
            painterResource(R.drawable.bg_bbox_dialog),
            modifier =
                Modifier
                    .padding(top = 16.dp)
                    .fillMaxSize(),
            contentDescription = "bg",
            contentScale = ContentScale.FillBounds,
        )
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Box(modifier = Modifier.size((56 + 64).dp, 64.dp)) {
                AvatarNetworkImage(
                    leftUser,
                    modifier =
                        Modifier
                            .border(1.dp, Color.White, CircleShape),
                    size = 64.dp,
                )
                AvatarNetworkImage(
                    rightUser,
                    modifier =
                        Modifier
                            .align(Alignment.CenterEnd)
                            .border(1.dp, Color.White, CircleShape),
                    size = 64.dp,
                )
            }
            SizeHeight(12.dp)
            Text(message, color = WakooText, textAlign = TextAlign.Center)

            Weight()
            Column(
                modifier =
                    Modifier
                        .widthIn(min = 190.dp)
                        .heightIn(min = 44.dp)
                        .clip(RoundedCornerShape(50))
                        .click(onClick = onClick)
                        .background(Color.White, RoundedCornerShape(50))
                        .padding(horizontal = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
            ) {
                Text(buttonText, fontSize = 16.sp, color = PINK, fontWeight = FontWeight.Medium)
                SizeHeight(1.dp)
                Text(giftDesc, fontSize = 12.sp, color = PINK)
            }
            SizeHeight(20.dp)
        }
    }
}

@Preview
@Composable
private fun BBoxPreview() {
    BBoxDialogContent(
        BasicUser.sampleBoy,
        BasicUser.sampleGirl,
        "恭喜【无敌小霸王】和【幼儿园搬花】获得1个CP盲盒！",
        "开盲盒送给CP",
        "999钻石",
    )
}

@Composable
fun BBoxPendant(
    desc: AnnotatedString, modifier: Modifier = Modifier,
    onClick: OnAction = {}, onClose: OnAction = {}
) {
    Box(
        modifier = modifier
            .size(68.dp, 90.dp)
            .paint(painterResource(R.drawable.bg_bbox_pendant), contentScale = ContentScale.FillBounds)
            .click(onClick = onClick)
            .padding(3.dp)
            .padding(bottom = 2.dp)
    ) {
        Icon(
            WakooIcons.Close,
            contentDescription = "",
            tint = Color.White,
            modifier = Modifier
                .size(10.dp)
                .align(Alignment.TopEnd)
                .clip(CircleShape)
                .click(onClick = onClose)
                .background(Color(0x66000000))
                .padding(2.dp)
        )
        Text(
            desc,
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            fontSize = 9.sp, fontFamily = FontFamily.Monospace,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold
        )
    }
}

@Preview
@Composable
private fun BBoxPendantPreview() {
    BBoxPendant(buildAnnotatedString {
        append("与CP共同挂麦00:01s后可获得")
    }) { }
}

@Composable
fun BlindBoxPendant(bBoxInfo: BBoxInfo, modifier: Modifier = Modifier) {
    val desc = if (bBoxInfo.filled) {
        remember(bBoxInfo.completeDesc) {
            buildAnnotatedString {
                appendWithStyle(bBoxInfo.completeDesc, SpanStyle(color = Color(0xFF132A6E)))
            }
        }
    } else {
        var annotatedString by remember { mutableStateOf(buildAnnotatedString {  }) }

        fun formatSec(sec: Long): String {
            if (sec <= 0L) return "00:00"
            return "${sec / 60}".padStart(2, '0') + ":" + "${sec % 60}".padStart(2, '0')
        }

        fun buildText(time:String): AnnotatedString{
            val text = "与CP共同挂麦%s后可获得".localizedFormat(time)
            return buildAnnotatedString {
                listOf(text.substringBefore(time), time, text.substringAfter(time))
                    .filter { it.isNotEmpty() }
                    .forEach { item ->
                        val color = if (item == time) {
                            PINK
                        } else {
                            Color(0xFF132A6E)
                        }
                        appendWithStyle(item, SpanStyle(color = color))
                    }
            }
        }

        var passedSec by remember(bBoxInfo.remainSeconds) { mutableIntStateOf(0) }
        LaunchedEffect(bBoxInfo.status, bBoxInfo.remainSeconds) {
            var time = formatSec(bBoxInfo.remainSeconds)
            annotatedString = buildText(time)
            if (bBoxInfo.isProgress) {
                while (true) {
                    delay(1000)
                    passedSec += 1
                    val remainSec = bBoxInfo.remainSeconds - passedSec
                    time = formatSec(remainSec)
                    annotatedString = buildText(time)
                    if (time == "00:00") break
                }
            }
        }
        annotatedString
    }
    BBoxPendant(desc, onClick = {
        bBoxInfo.link.tryToLink()
    }, onClose = {
        AppConfigManager.hideBlindBox()
    })
}