package com.buque.wakoo.ui.widget.media.data.common

import kotlinx.serialization.Serializable

/**
 * 描述媒体库变化的精细化数据类。
 * 用于实现高效的增量更新。
 *
 * @property removedIds 被删除的媒体ID列表。
 * @property insertedIds 新增的媒体ID列表。
 * @property updatedIds 被更新的媒体ID列表。
 */
data class MediaChanges(
    val removedIds: List<String>,
    val insertedIds: List<String>,
    val updatedIds: List<String>,
)

@Serializable
sealed interface IMediaItem {
    val id: String
    val dateAdded: Long
}

/**
 * 媒体项的通用密封类，定义了所有媒体类型共有的属性。
 * 使用 sealed class 便于在处理时通过 when 表达式进行详尽的类型判断。
 *
 * @property albumId 相册的唯一ID。
 * @property id 媒体在设备上的唯一标识符 (Android: MediaStore ID, iOS: PHAsset localIdentifier)。
 * @property uriString 媒体内容的URI字符串表示。
 *                     在Android上是 Uri.toString() 的结果，在iOS上是 PHAsset.localIdentifier。
 * @property displayName 媒体的显示名称，通常是文件名。
 * @property sizeBytes 文件大小，单位为字节 (Bytes)。
 * @property dateAdded 媒体被添加到系统相册的时间戳 (秒)。
 * @property width 媒体的宽度（像素）。
 * @property height 媒体的高度（像素）。
 * @property mimeType MIME类型, 例如 "image/jpeg", "image/png"。
 */
@Serializable
sealed interface MediaItem : IMediaItem {
    val albumId: String
    val uriString: String
    val displayName: String
    val sizeBytes: Long
    val width: Int
    val height: Int
    val mimeType: String

    val aspectRatio
        get() = if (width > 0 && height > 0) width.toFloat().div(height) else 1f
}

/**
 * 图片数据模型
 * @param mimeType 图片的MIME类型, 例如 "image/jpeg", "image/png"。
 */
@Serializable
data class ImageItem(
    override val id: String, // 某些情况下id可能就是uriString
    override val albumId: String,
    override val uriString: String,
    override val displayName: String,
    override val sizeBytes: Long,
    override val dateAdded: Long,
    override val width: Int,
    override val height: Int,
    override val mimeType: String,
) : MediaItem

/**
 * 视频数据模型
 * @param durationMillis 视频的时长，单位为毫秒。
 */
@Serializable
data class VideoItem(
    override val id: String, // 某些情况下id可能就是uriString
    override val albumId: String,
    override val uriString: String,
    override val displayName: String,
    override val sizeBytes: Long,
    override val dateAdded: Long,
    override val width: Int,
    override val height: Int,
    override val mimeType: String,
    val durationMillis: Long,
) : MediaItem

/**
 * 相册/文件夹数据模型
 *
 * @property id 相册的唯一ID (Android: BUCKET_ID, iOS: PHAssetCollection localIdentifier)。
 * @property name 相册的显示名称。
 * @property coverUriString coverUriString 封面媒体的URI字符串。
 * @property count 该相册下的媒体总数。
 */
data class Album(
    val id: String,
    val name: String,
    val coverUriString: String?,
    val count: Int,
)
