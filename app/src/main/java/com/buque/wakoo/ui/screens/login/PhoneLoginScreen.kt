package com.buque.wakoo.ui.screens.login

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooBlack
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooLightGrayBg
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TextFieldPadding
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

private class PhoneNumberVisualTransformation : VisualTransformation {
    override fun filter(text: AnnotatedString): TransformedText {
        // text.text 是原始的、无格式的数字字符串，例如 "18888888888"
        val trimmed = if (text.text.length >= 11) text.text.substring(0..10) else text.text

        // 构建带格式的 AnnotatedString
        val formattedString =
            AnnotatedString
                .Builder()
                .apply {
                    for (i in trimmed.indices) {
                        append(trimmed[i])
                        if (i == 2 || i == 6) {
                            append(" ") // 在第3位和第7位数字后添加空格
                        }
                    }
                }.toAnnotatedString()

        // OffsetMapping 是关键，它告诉 Compose 如何在原始文本和转换后文本之间映射光标位置
        val offsetMapping =
            object : OffsetMapping {
                override fun originalToTransformed(offset: Int): Int {
                    // originalToTransformed: 原始 -> 转换后
                    // 计算在原始光标位置前有多少个空格
                    if (offset <= 2) return offset
                    if (offset <= 6) return offset + 1
                    return offset + 2
                }

                override fun transformedToOriginal(offset: Int): Int {
                    // transformedToOriginal: 转换后 -> 原始
                    // 计算在转换后光标位置前有多少个空格
                    if (offset <= 3) return offset
                    if (offset <= 8) return offset - 1
                    return offset - 2
                }
            }

        return TransformedText(formattedString, offsetMapping)
    }
}

@Composable
fun PhoneLoginScreen(
    modifier: Modifier = Modifier,
    onSendVerifyCode: (String) -> Unit = {},
    onLogin: (String, String) -> Unit = { _, _ -> },
) {
    var phoneNumber by rememberSaveable { mutableStateOf("") }
    var verifyCode by rememberSaveable { mutableStateOf("") }
    var countdownSeconds by rememberSaveable { mutableIntStateOf(0) }

    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    LaunchedEffect(Unit) {
        focusRequester.requestFocus() // 请求焦点
        keyboardController?.show() // 请求显示键盘
    }

    fun callLogin() {
        if (phoneNumber.length >= 11 && verifyCode.length >= 6) {
            onLogin(phoneNumber, verifyCode)
        } else {
            showToast("请输入完整手机号和验证码".localized)
        }
    }

    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(WakooWhite)
                .paint(
                    painter = painterResource(id = R.drawable.bg_common_top),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter,
                ),
    ) {
        TitleScreenScaffold(
            title = "",
            containerColor = Color.Transparent,
        ) { paddingValues ->

            ImeButtonScaffold(
                buttonContent = {
                    // 底部按钮
                    GradientButton(
                        text = "下一步".localized,
                        height = 56.dp,
                        enabled = phoneNumber.length >= 11 && verifyCode.length >= 6,
                        onClick = ::callLogin,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .align(Alignment.BottomCenter)
                                .padding(horizontal = 28.dp),
                    )
                },
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                buttonModifier =
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFFFFFFF))
                        .padding(
                            top = 20.dp,
                            bottom = 15.dp,
                        ),
                buttonContentMinimumPadding = 224.dp,
            ) {
                // 主要内容
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 28.dp)
                            .padding(top = 10.dp),
                ) {
                    // 标题
                    Text(
                        text = "手机号登录".localized,
                        color = WakooBlack,
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp,
                        style = MaterialTheme.typography.titleLarge,
                        fontFamily = FontFamily.MI_SANS,
                    )

                    SizeHeight(12.dp)

                    Text(
                        text = "未注册的手机号验证后将自动注册".localized,
                        color = WakooGrayText,
                        fontSize = 14.sp,
                    )

                    SizeHeight(60.dp)

                    // 昵称输入
                    Text(
                        text = "手机号".localized,
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    val focusManager = LocalFocusManager.current

                    AppTextField(
                        value = phoneNumber,
                        onValueChange = { newText ->
                            val filteredText = newText.filter { it.isDigit() }
                            if (filteredText.length <= 11) {
                                phoneNumber = filteredText
                            }
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .focusRequester(focusRequester),
                        maxLength = 11,
                        showLengthTip = false,
                        shape = CircleShape,
                        placeholder = "请输入手机号".localized,
                        textStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFF111111),
                                lineHeight = 21.sp,
                            ),
                        placeholderStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 21.sp,
                                fontWeight = FontWeight.Normal,
                            ),
                        prefix = {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    text = "CN+86",
                                    fontSize = 16.sp,
                                    color = WakooText,
                                    fontWeight = FontWeight.Normal,
                                )
                                SizeWidth(8.dp)
                                VerticalDivider(
                                    color = Color(0xFFB6B6B6),
                                    thickness = 1.dp,
                                    modifier = Modifier.height(16.dp),
                                )
                                SizeWidth(5.dp)
                            }
                        },
                        backgroundColor = WakooLightGrayBg,
                        singleLine = true,
                        visualTransformation = PhoneNumberVisualTransformation(),
                        keyboardOptions =
                            KeyboardOptions(
                                keyboardType = KeyboardType.Number,
                                imeAction = ImeAction.Next,
                            ),
                        keyboardActions =
                            KeyboardActions(
                                onNext = {
                                    if (phoneNumber.length >= 11) {
                                        focusManager.moveFocus(FocusDirection.Down)
                                        onSendVerifyCode(phoneNumber)
                                        verifyCode = ""
                                        countdownSeconds = 60
                                    } else {
                                        showToast("请输入完整手机号".localized)
                                    }
                                },
                            ),
                        contentPadding =
                            PaddingValues(
                                start = 24.dp,
                                end = 24.dp,
                                top = TextFieldPadding,
                                bottom = TextFieldPadding,
                            ),
                    )

                    SizeHeight(16.dp)

                    Text(
                        text = "验证码".localized,
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    AppTextField(
                        value = verifyCode,
                        onValueChange = { newText ->
                            val filteredText = newText.filter { it.isDigit() }
                            if (filteredText.length <= 6) {
                                verifyCode = filteredText
                            }
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp),
                        maxLength = 6,
                        showLengthTip = false,
                        shape = CircleShape,
                        placeholder = "请输入验证码".localized,
                        textStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFF111111),
                                lineHeight = 21.sp,
                            ),
                        placeholderStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 21.sp,
                                fontWeight = FontWeight.Normal,
                            ),
                        suffix = {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                SizeWidth(5.dp)
                                VerticalDivider(
                                    color = Color(0xFFB6B6B6),
                                    thickness = 1.dp,
                                    modifier = Modifier.height(16.dp),
                                )
                                SizeWidth(8.dp)

                                val isCountingDown = countdownSeconds > 0
                                val sendCodeText = if (isCountingDown) "${countdownSeconds}s" else "获取验证码".localized
                                val isSendCodeButtonEnabled = phoneNumber.length >= 11 && !isCountingDown
                                val sendCodeTextColor =
                                    if (isSendCodeButtonEnabled) {
                                        Color(0xFF15ABFF)
                                    } else {
                                        Color(0xFFB6B6B6)
                                    }

                                LaunchedEffect(countdownSeconds > 0) {
                                    while (isActive) {
                                        delay(1000)
                                        countdownSeconds--
                                    }
                                }

                                Text(
                                    text = sendCodeText,
                                    fontSize = 16.sp,
                                    color = sendCodeTextColor,
                                    fontWeight = FontWeight.Normal,
                                    modifier =
                                        Modifier
                                            .widthIn(min = 80.dp)
                                            .noEffectClick(enabled = isSendCodeButtonEnabled) {
                                                if (phoneNumber.length >= 11) {
                                                    focusManager.moveFocus(FocusDirection.Down)
                                                    onSendVerifyCode(phoneNumber)
                                                    verifyCode = ""
                                                    countdownSeconds = 60
                                                }
                                            },
                                    textAlign = TextAlign.Center,
                                )
                            }
                        },
                        backgroundColor = WakooLightGrayBg,
                        singleLine = true,
                        keyboardOptions =
                            KeyboardOptions(
                                keyboardType = KeyboardType.Number,
                                imeAction = ImeAction.Done,
                            ),
                        keyboardActions =
                            KeyboardActions(
                                onDone = {
                                    callLogin()
                                },
                            ),
                        contentPadding =
                            PaddingValues(
                                start = 24.dp,
                                end = 24.dp,
                                top = TextFieldPadding,
                                bottom = TextFieldPadding,
                            ),
                    )

                    SizeHeight(16.dp)
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PhoneLoginScreenPreview() {
    WakooTheme {
        PhoneLoginScreen()
    }
}
