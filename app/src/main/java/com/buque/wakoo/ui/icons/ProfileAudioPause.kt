package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.ProfileAudioPause: ImageVector
    get() {
        if (_ProfileAudioPause != null) {
            return _ProfileAudioPause!!
        }
        _ProfileAudioPause =
            ImageVector
                .Builder(
                    name = "ProfileAudioPause",
                    defaultWidth = 32.dp,
                    defaultHeight = 32.dp,
                    viewportWidth = 32f,
                    viewportHeight = 32f,
                ).apply {
                    path(
                        fill = SolidColor(Color(0xFF111111)),
                        stroke = SolidColor(Color(0xFFFFFFFF)),
                        strokeLineWidth = 2f,
                    ) {
                        moveTo(16f, 4f)
                        curveTo(22.627f, 4f, 28f, 9.373f, 28f, 16f)
                        curveTo(28f, 22.627f, 22.627f, 28f, 16f, 28f)
                        curveTo(9.373f, 28f, 4f, 22.627f, 4f, 16f)
                        curveTo(4f, 9.373f, 9.373f, 4f, 16f, 4f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF66FE6B))) {
                        moveTo(12.505f, 12f)
                        lineTo(14.172f, 12f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.672f, 12.5f)
                        lineTo(14.672f, 19.5f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.172f, 20f)
                        lineTo(12.505f, 20f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 12.005f, 19.5f)
                        lineTo(12.005f, 12.5f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 12.505f, 12f)
                        close()
                    }
                    path(fill = SolidColor(Color(0xFF66FE6B))) {
                        moveTo(17.857f, 12f)
                        lineTo(19.524f, 12f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 20.024f, 12.5f)
                        lineTo(20.024f, 19.5f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 19.524f, 20f)
                        lineTo(17.857f, 20f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 17.357f, 19.5f)
                        lineTo(17.357f, 12.5f)
                        arcTo(0.5f, 0.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 17.857f, 12f)
                        close()
                    }
                }.build()

        return _ProfileAudioPause!!
    }

@Suppress("ObjectPropertyName")
private var _ProfileAudioPause: ImageVector? = null
