package com.buque.wakoo.ui.screens.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.BuildConfig
import com.buque.wakoo.R
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SingleTextSettingsMenuItem
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.TowTextsSettingsMenuItem
import com.buque.wakoo.utils.AppUtils

/**
 * 关于我们页面
 * 显示应用信息、版本信息和相关协议链接
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutAppScreen(
    onUserAgreementClick: () -> Unit = {},
    onPrivacyPolicyClick: () -> Unit = {},
    currentVersion: String = BuildConfig.VERSION_NAME,
) {
    var hasNewVersion by rememberSaveable {
        mutableStateOf(false)
    }

    var newestVersion by rememberSaveable {
        mutableStateOf("无新版本".localized)
    }

    var refreshKey by remember {
        mutableIntStateOf(0)
    }

    LaunchedEffect(refreshKey) {
        AppConfigManager.getSystemConfig().onSuccess {
            hasNewVersion = it.hasNewVersion
            if (it.hasNewVersion) {
                newestVersion = it.androidNewestVersion
            } else if (refreshKey > 0) {
                showToast("当前已是最新版本".localized)
            }
        }
    }

    val context = LocalContext.current

    SegColorTitleScreenScaffold(title = "关于我们".localized) { paddingValues ->
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .background(Color(0xFFF7F7F7))
                    .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            SizeHeight(40.dp)

            // Logo和版本信息区域
            AppLogoSection(version = currentVersion)

            SizeHeight(30.dp)

            // 设置选项列表

            TowTextsSettingsMenuItem(
                titleText = "用户服务协议".localized,
                previewText = "",
                onClick = onUserAgreementClick,
            )

            TowTextsSettingsMenuItem(
                titleText = "隐私协议".localized,
                previewText = "",
                onClick = onPrivacyPolicyClick,
            )

            TowTextsSettingsMenuItem(
                titleText = "当前版本".localized,
                previewText = currentVersion,
                onClick = {},
            )

            SingleTextSettingsMenuItem(
                titleText = "新版检测".localized,
                onClick = {
                    if (hasNewVersion) {
                        AppUtils.openAppInPlayStore(context)
                    } else {
                        refreshKey++
                    }
                },
            ) {
                BadgedBox(badge = {
                    if (hasNewVersion) {
                        Badge(modifier = Modifier.offset(x = 8.dp))
                    }
                }) {
                    Text(
                        text = newestVersion,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF999999),
                    )
                }
            }

            SizeHeight(40.dp)
        }
    }
}

/**
 * 应用Logo和版本信息区域
 */
@Composable
private fun AppLogoSection(version: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        // Logo图标
        Image(
            painter = painterResource(id = R.drawable.ic_app_logo),
            contentDescription = "Wakoo Logo",
            modifier =
                Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(16.dp)),
            contentScale = ContentScale.Fit,
        )

        // 版本号
        Text(
            text = "v $version",
            fontSize = 12.sp,
            color = Color(0xFF999999),
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AboutUsPagePreview() {
    WakooTheme {
        AboutAppScreen()
    }
}

@Preview(showBackground = true)
@Composable
private fun AboutUsPageWithNewVersionPreview() {
    WakooTheme {
        AboutAppScreen(
            currentVersion = "1.0.1",
        )
    }
}
