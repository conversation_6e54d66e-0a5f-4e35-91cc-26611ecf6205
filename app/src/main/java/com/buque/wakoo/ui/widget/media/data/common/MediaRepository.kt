package com.buque.wakoo.ui.widget.media.data.common

/**
 * 媒体数据仓库接口定义。
 * 这是数据层的核心抽象，定义了所有与媒体库交互的方法。
 * 实现这个接口的类将负责处理特定平台（Android/iOS）的数据获取逻辑。
 */
interface MediaRepository {
    /**
     * 异步获取所有相册/文件夹列表。
     *
     * @param mediaType 用于筛选和统计相册内媒体数量的类型。
     * @return 返回一个包含 [Result] 的列表，成功时包含 [Album] 列表，失败时包含异常。
     */
    suspend fun getAlbums(mediaType: SelectorMediaType): Result<List<Album>>

    /**
     * [推荐用于简单场景] 异步分页获取媒体列表。
     * 这是一个传统的、命令式的分页方法。对于需要自动更新和更复杂UI的场景，
     * 强烈推荐使用 `MediaSource` 提供的响应式Flow。
     *
     * @param page 页码，从 0 开始。
     * @param pageSize 每页加载的数量。
     * @param albumId 可选的相册ID。如果为 null 或等于 [ALL_MEDIA_ALBUM_ID]，则获取所有媒体。
     * @param mediaType 要获取的媒体类型。
     * @return 返回一个包含 [Result] 的列表，成功时包含 [MediaItem] 列表，失败时包含异常。
     */
    suspend fun getMedia(
        page: Int,
        pageSize: Int,
        albumId: String? = null,
        mediaType: SelectorMediaType,
    ): Result<List<MediaItem>>

    /**
     * 高效地异步获取指定相册下所有媒体的ID列表。
     * 这个方法应该只查询ID列，以保证极高的速度。
     *
     * @param albumId 可选的相册ID。如果为 null，则获取所有媒体的ID。
     * @param mediaType 要获取的媒体类型。
     * @return 返回一个包含 Result 的列表，成功时包含ID的String列表。
     */
    suspend fun getAllMediaIds(
        albumId: String?,
        mediaType: SelectorMediaType,
    ): Result<List<String>>

    suspend fun getAllPlaceholderMediaItems(
        albumId: String?,
        mediaType: SelectorMediaType,
    ): Result<List<PlaceholderMediaItem>>

    /**
     * 根据一串ID，批量获取完整的媒体信息。
     *
     * @param ids 要查询的媒体ID列表。
     * @return 返回包含 Result 的列表，成功时包含完整的 [MediaItem] 列表。
     */
    suspend fun getMediaByIds(ids: List<String>): Result<List<MediaItem>>

    /**
     * 注册一个观察者，用于监听系统媒体库的变化。
     * 当有新照片、截图或视频被创建/删除时，会触发回调。
     *
     * @param observer 数据变化时调用的回调函数。
     */
    fun registerObserver(observer: (MediaChanges?) -> Unit)

    /**
     * 注销之前注册的媒体库变化观察者。
     * 在组件销毁时必须调用此方法，以防止内存泄漏。
     */
    fun unregisterObserver()

    companion object {
        /**
         * 用于代表“所有媒体”这个特殊相册的ID。
         */
        const val ALL_MEDIA_ALBUM_ID = "com.cmp.picker.ALL_MEDIA"
    }
}
