package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.RecommendRoomJoin: ImageVector
    get() {
        if (_RecommendRoomJoin != null) {
            return _RecommendRoomJoin!!
        }
        _RecommendRoomJoin = ImageVector.Builder(
            name = "RecommendRoomJoin",
            defaultWidth = 13.dp,
            defaultHeight = 13.dp,
            viewportWidth = 13f,
            viewportHeight = 13f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(13f)
                    verticalLineToRelative(13f)
                    horizontalLineToRelative(-13f)
                    close()
                }
            ) {
                path(
                    stroke = SolidColor(Color(0xFF66FE6B)),
                    strokeLineWidth = 2f,
                    strokeLineCap = StrokeCap.Round
                ) {
                    moveTo(6.9f, 3.102f)
                    lineTo(5.1f, 10.896f)
                }
                path(
                    stroke = SolidColor(Color(0xFF66FE6B)),
                    strokeLineWidth = 2f,
                    strokeLineCap = StrokeCap.Round
                ) {
                    moveTo(2f, 7f)
                    horizontalLineTo(6f)
                }
                path(
                    stroke = SolidColor(Color(0xFF66FE6B)),
                    strokeLineWidth = 2f,
                    strokeLineCap = StrokeCap.Round
                ) {
                    moveTo(9f, 7f)
                    horizontalLineTo(11f)
                }
            }
        }.build()

        return _RecommendRoomJoin!!
    }

@Suppress("ObjectPropertyName")
private var _RecommendRoomJoin: ImageVector? = null
