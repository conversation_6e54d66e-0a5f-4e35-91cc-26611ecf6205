package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.PencilEdit: ImageVector
    get() {
        if (_Edit2Fill != null) {
            return _Edit2Fill!!
        }
        _Edit2Fill = ImageVector.Builder(
            name = "Edit2Fill",
            defaultWidth = 14.dp,
            defaultHeight = 14.dp,
            viewportWidth = 14f,
            viewportHeight = 14f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(14f)
                    verticalLineToRelative(14f)
                    horizontalLineToRelative(-14f)
                    close()
                }
            ) {
                path(fill = SolidColor(Color(0xFF111111))) {
                    moveTo(5.392f, 11.082f)
                    horizontalLineTo(12.25f)
                    verticalLineTo(12.249f)
                    horizontalLineTo(1.75f)
                    verticalLineTo(9.774f)
                    lineTo(7.525f, 3.999f)
                    lineTo(10f, 6.474f)
                    lineTo(5.391f, 11.082f)
                    horizontalLineTo(5.392f)
                    close()
                    moveTo(8.349f, 3.174f)
                    lineTo(9.587f, 1.936f)
                    curveTo(9.696f, 1.827f, 9.845f, 1.766f, 10f, 1.766f)
                    curveTo(10.154f, 1.766f, 10.302f, 1.827f, 10.412f, 1.936f)
                    lineTo(12.062f, 3.587f)
                    curveTo(12.172f, 3.696f, 12.233f, 3.844f, 12.233f, 3.999f)
                    curveTo(12.233f, 4.154f, 12.172f, 4.302f, 12.062f, 4.411f)
                    lineTo(10.824f, 5.649f)
                    lineTo(8.35f, 3.174f)
                    horizontalLineTo(8.349f)
                    close()
                }
            }
        }.build()

        return _Edit2Fill!!
    }

@Suppress("ObjectPropertyName")
private var _Edit2Fill: ImageVector? = null
