package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.CheckFill: ImageVector
    get() {
        if (_Check2Fill != null) {
            return _Check2Fill!!
        }
        _Check2Fill = ImageVector.Builder(
            name = "Check2Fill",
            defaultWidth = 16.dp,
            defaultHeight = 16.dp,
            viewportWidth = 16f,
            viewportHeight = 16f
        ).apply {
            path(
                fill = SolidColor(Color(0xFF66FE6B)),
                pathFillType = PathFillType.EvenOdd
            ) {
                moveTo(12.637f, 2.095f)
                curveTo(12.785f, 2.007f, 12.961f, 1.978f, 13.129f, 2.017f)
                curveTo(13.297f, 2.055f, 13.444f, 2.157f, 13.538f, 2.301f)
                lineTo(14.198f, 3.308f)
                curveTo(14.284f, 3.44f, 14.321f, 3.597f, 14.302f, 3.753f)
                curveTo(14.283f, 3.909f, 14.21f, 4.054f, 14.095f, 4.161f)
                lineTo(14.093f, 4.163f)
                lineTo(14.084f, 4.172f)
                lineTo(14.046f, 4.207f)
                lineTo(13.896f, 4.351f)
                curveTo(13.066f, 5.156f, 12.261f, 5.986f, 11.482f, 6.841f)
                curveTo(10.018f, 8.452f, 8.278f, 10.56f, 7.108f, 12.605f)
                curveTo(6.781f, 13.176f, 5.983f, 13.299f, 5.51f, 12.806f)
                lineTo(1.186f, 8.314f)
                curveTo(1.124f, 8.25f, 1.076f, 8.173f, 1.044f, 8.09f)
                curveTo(1.012f, 8.007f, 0.997f, 7.918f, 1f, 7.828f)
                curveTo(1.004f, 7.739f, 1.025f, 7.651f, 1.062f, 7.57f)
                curveTo(1.1f, 7.489f, 1.154f, 7.417f, 1.22f, 7.357f)
                lineTo(2.527f, 6.178f)
                curveTo(2.642f, 6.075f, 2.789f, 6.014f, 2.943f, 6.007f)
                curveTo(3.098f, 6f, 3.25f, 6.047f, 3.374f, 6.14f)
                lineTo(5.58f, 7.794f)
                curveTo(9.026f, 4.396f, 10.98f, 3.092f, 12.637f, 2.095f)
                close()
            }
        }.build()

        return _Check2Fill!!
    }

@Suppress("ObjectPropertyName")
private var _Check2Fill: ImageVector? = null
