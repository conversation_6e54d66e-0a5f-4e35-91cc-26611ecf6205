package com.buque.wakoo.ui.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.icons.VoicePublish
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth

@Composable
fun DynamicPublishPanel(
    onVoiceContent: () -> Unit = {}, onMomentContent: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color.White, shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 14.dp, vertical = 24.dp), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标题
        Text(
            text = "选择你想发布的生活记录类型".localized, fontSize = 16.sp, fontWeight = FontWeight.Medium, color = Color(0xFF666666), textAlign = TextAlign.Center
        )

        SizeHeight(36.dp)

        // 按钮区域
        Row(
            modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 声音内容按钮
            Row(
                modifier = Modifier
                    .weight(1f)
                    .background(color = Color(0xff111111), shape = CircleShape)
                    .padding(horizontal = 20.dp, vertical = 8.dp)
                    .click(onClick = onVoiceContent),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(WakooIcons.VoicePublish, contentDescription = null)
                SizeWidth(2.dp)
                Text("声音内容".localized, fontSize = 16.sp, lineHeight = 22.sp, color = Color.White)
            }
            Row(
                modifier = Modifier
                    .weight(1f)
                    .background(color = Color(0xff111111), shape = CircleShape)
                    .padding(horizontal = 20.dp, vertical = 8.dp)
                    .click(onClick = onMomentContent),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(WakooIcons.VoicePublish, contentDescription = null)
                SizeWidth(2.dp)
                Text("瞬间动态".localized, fontSize = 16.sp, lineHeight = 22.sp, color = Color.White)
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewDynamicPublishPanel() {
    DynamicPublishPanel()
}
