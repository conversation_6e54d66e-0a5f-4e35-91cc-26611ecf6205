package com.buque.wakoo.ui.widget.wheelPicker.demo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.wheelPicker.DateWheelPicker
import com.buque.wakoo.ui.widget.wheelPicker.WheelPickerUtils
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.minus
import kotlinx.datetime.todayIn
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

/**
 * 增强版日期选择器示例
 * 展示循环滚动和日期范围限制功能
 */
@Composable
fun EnhancedDatePickerExample() {
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp),
    ) {
        Text(
            text = "增强版日期选择器示例",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
        )

        // 基础日期选择器
        BasicDatePickerExample()

        // 限制到今天的日期选择器
        DatePickerToTodayExample()

        // 带循环滚动的日期选择器
        InfiniteDatePickerExample()

        // 完全自定义的日期选择器
        CustomDatePickerExample()
    }
}

/**
 * 基础日期选择器示例
 */
@OptIn(ExperimentalTime::class)
@Composable
private fun BasicDatePickerExample() {
    var selectedDate by remember {
        mutableStateOf(Clock.System.todayIn(TimeZone.currentSystemDefault()))
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "基础日期选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            DateWheelPicker(
                selectedDate = selectedDate,
                onDateChanged = { selectedDate = it },
                yearRange = (2020..2030),
                itemHeight = 48.dp,
                visibleItemsCount = 5,
            )

            Text(
                text = "选中日期: $selectedDate",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 限制到今天的日期选择器示例
 */
@Composable
private fun DatePickerToTodayExample() {
    var selectedDate by remember {
        mutableStateOf(LocalDate(2040, 1, 1))
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "限制到今天的日期选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            Text(
                text = "只能选择今天及之前的日期",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            DateWheelPicker(
                selectedDate = selectedDate,
                onDateChanged = { selectedDate = it },
                yearRange = (2020..2030),
                enabledDateRange = WheelPickerUtils.createDateRangeToToday(2020..2030),
                itemHeight = 48.dp,
                visibleItemsCount = 5,
            )

            Text(
                text = "选中日期: $selectedDate",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 带循环滚动的日期选择器示例
 */
@OptIn(ExperimentalTime::class)
@Composable
private fun InfiniteDatePickerExample() {
    var selectedDate by remember {
        mutableStateOf(Clock.System.todayIn(TimeZone.currentSystemDefault()))
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "循环滚动日期选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            Text(
                text = "月份和日期支持循环滚动，日期固定显示31天（不存在的日期会变灰）",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            DateWheelPicker(
                selectedDate = selectedDate,
                onDateChanged = { selectedDate = it },
                yearRange = (2020..2030),
                enabledDateRange = WheelPickerUtils.createDateRangeToToday(2020..2030),
                isMonthInfinite = true,
                isDayInfinite = true,
                itemHeight = 48.dp,
                visibleItemsCount = 5,
            )

            Text(
                text = "选中日期: $selectedDate",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 完全自定义的日期选择器示例
 */
@OptIn(ExperimentalTime::class)
@Composable
private fun CustomDatePickerExample() {
    var selectedDate by remember {
        mutableStateOf(Clock.System.todayIn(TimeZone.currentSystemDefault()))
    }

    // 自定义日期范围：只能选择最近30天
    val customDateRange =
        remember {
            val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
            val thirtyDaysAgo = today.minus(30, DateTimeUnit.DAY)
            thirtyDaysAgo..today
        }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "自定义范围日期选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            Text(
                text = "只能选择最近30天，月份循环滚动，日期固定31天",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )

            DateWheelPicker(
                selectedDate = selectedDate,
                onDateChanged = { selectedDate = it },
                yearRange = (2020..2030),
                enabledDateRange = customDateRange,
                isMonthInfinite = true,
                isDayInfinite = true,
                itemHeight = 48.dp,
                visibleItemsCount = 7,
            )

            Text(
                text = "选中日期: $selectedDate",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )

            Text(
                text = "可用范围: ${customDateRange.start} 到 ${customDateRange.endInclusive}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun EnhancedDatePickerExamplePreview() {
    WakooTheme {
        EnhancedDatePickerExample()
    }
}
