package com.buque.wakoo.ui.dialog

import androidx.activity.compose.LocalActivity
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.FirstRechargeGood
import com.buque.wakoo.bean.FirstRechargeInfo
import com.buque.wakoo.bean.ReChargeItem
import com.buque.wakoo.bean.SubItem
import com.buque.wakoo.consts.Pay
import com.buque.wakoo.consts.Pay.CALL_TYPE_GOOGLE_SDK
import com.buque.wakoo.core.pay.AppPayCoreKit
import com.buque.wakoo.core.pay.PayRequest
import com.buque.wakoo.ext.click
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.utils.datapoints.Analytics
import kotlinx.coroutines.launch

@Composable
fun DialogScope.FirstRechargeDialog(info: FirstRechargeInfo, position: Int) {
    val act = LocalActivity.current
    val scope = rememberCoroutineScope()
    val lm = LocalLoadingManager.current

    FirstRechargeDialogWrapper(info) { productId, fkLink ->
        Analytics.trace(
            Analytics.Event.NEWBIE_PACKAGE_POPUP_BUY_BTN_CLICK, mapOf(
                "position" to position
            )
        )
        lm.show(scope) {
            if (act != null) {
                AppPayCoreKit.buy(
                    act,
                    PayRequest(
                        CALL_TYPE_GOOGLE_SDK,
                        productId,
                        fkLink,
                        orderType = Pay.ORDER_TYPE_FIRST_RECHARGE,
                    ),
                )
            }
            dismiss()
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            appCoroutineScope.launch {
                AppConfigManager.fetchPendantConfig()
            }
        }
    }
}

@Composable
fun FirstRechargeDialogWrapper(
    info: FirstRechargeInfo,
    onBuy: (productId: String, fkLink: String) -> Unit = { _, _ -> },
) {
//    val isPlayChannel = EnvironmentManager.isGoogleChannel
    // 当前选中的付款方式
//    var selectedFkOption by rememberSaveableRefWithPrevious<FirstRechargeGood?>(info.goods) { prev ->
//        if (isPlayChannel) {
//            mutableStateOf(info.goods.find { it.fkChannel == 1 })
//        } else {
//            if (prev == null) {
//                mutableStateOf(info.goods.firstOrNull())
//            } else {
//                mutableStateOf(
//                    info.goods.find {
//                        it.fkChannel == prev.fkChannel
//                    } ?: info.goods.firstOrNull(),
//                )
//            }
//        }
//    }

    // 当前选中的付款方式对应的商品
    val selectedMemberOption by remember {
        mutableStateOf(info.items.firstOrNull())
    }

    Box(
        contentAlignment = Alignment.BottomCenter,
        modifier = Modifier.wrapContentSize(),
    ) {
        Spacer(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .matchParentSize()
                    .align(Alignment.BottomCenter)
                    .padding(top = 100.dp)
                    .background(
                        brush =
                            Brush.verticalGradient(
                                colors = listOf(Color(0xffFF6753), Color(0xffFFD632)),
                            ),
                        shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
                    ),
        )
        Column {
            NetworkImage(
                R.drawable.ic_first_recharge_header,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(2.5f),
            )
            Column(
                modifier =
                    Modifier
                        .offset(y = -20.dp)
                        .padding(horizontal = 7.dp)
                        .fillMaxWidth()
                        .background(
                            color = Color(0xffFFFAE4),
                            shape = RoundedCornerShape(24.dp),
                        )
                        .border(
                            7.dp,
                            color = Color(0xffFFF394),
                            shape = RoundedCornerShape(24.dp),
                        )
                        .padding(horizontal = 6.dp, vertical = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    buildAnnotatedString {
                        if (info.payPriceLabel.isNotBlank() && info.bonusPriceLabel.isNotBlank()) {
                            append("充".localized)
                            withStyle(
                                SpanStyle(color = Color(0xffFF7318), fontSize = 20.sp, fontFamily = FontFamily.MI_SANS),
                            ) {
                                append(info.payPriceLabel)
                            }
                            append("获得".localized)
                            withStyle(
                                SpanStyle(color = Color(0xffFF7318), fontSize = 20.sp, fontFamily = FontFamily.MI_SANS),
                            ) {
                                append(info.bonusPriceLabel)
                            }
                            append("礼包".localized)
                        }
                    },
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xff7a4016),
                    modifier = Modifier.padding(top = 12.dp),
                )

                selectedMemberOption?.apply {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(4),
                        modifier = Modifier.padding(vertical = 8.dp, horizontal = 12.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally),
                    ) {
                        if (topItem != null) {
                            item {
                                GoodItem(topItem)
                            }
                        }
                        items(subItems) {
                            GoodItem(it)
                        }
                    }
                }
            }

            SizeHeight(7.dp)
            BuyButton(selectedMemberOption, onBuy = onBuy)
        }
    }
}

//region 内部组件
@Composable
private fun FkItem(
    good: FirstRechargeGood,
    selected: Boolean,
    onClick: () -> Unit,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
) {
    Row(
        modifier =
            Modifier
                .height(36.dp)
                .background(if (selected) Color(0xFFFF3541) else Color.White, RoundedCornerShape(6.dp))
                .run {
                    if (selected) {
                        border(1.dp, Color(0xFFFFBBC6), RoundedCornerShape(6.dp))
                    } else {
                        this
                    }
                }
                .selectable(
                    selected = selected,
                    onClick = onClick,
                    enabled = true,
                    role = Role.RadioButton,
                    interactionSource = interactionSource,
                    indication = null,
                )
                .padding(horizontal = 6.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
//        Image(painter = good.icon, contentDescription = null)
        NetworkImage(good.icon, modifier = Modifier.size(24.dp))
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = good.name,
            fontSize = 12.sp,
            color = if (selected) Color(0xFFFFFFFF) else Color(0xFF7A4016),
        )
    }
}

@Composable
private fun GoodItem(item: SubItem) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Column(
            modifier =
                Modifier
                    .clip(RoundedCornerShape(12.dp))
                    .background(color = Color.White, shape = RoundedCornerShape(12.dp)),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            SizeHeight(6.dp)
            NetworkImage(
                item.icon,
                modifier =
                    Modifier
                        .padding(horizontal = 6.dp)
                        .size(64.dp),
                contentScale = ContentScale.FillWidth,
            )

            if (item.bonus_label.isNotBlank()) {
                Text(
                    item.bonus_label,
                    modifier =
                        Modifier
                            .align(Alignment.End)
                            .widthIn(min = 56.dp)
                            .background(
                                brush =
                                    Brush.horizontalGradient(
                                        listOf(
                                            Color(0xFFA3FF2C),
                                            Color(0xFF31FFA1),
                                        ),
                                    ),
                                shape = RoundedCornerShape(topStart = 8.dp, bottomEnd = 12.dp),
                            )
                            .padding(horizontal = 8.dp, vertical = 2.dp),
                    fontSize = 10.sp,
                    color = Color(0xff111111),
                    lineHeight = 10.sp,
                    textAlign = TextAlign.Center,
                )
            } else {
                Spacer(modifier = Modifier.height(6.dp))
            }
        }
        SizeHeight(8.dp)

        Text(
            buildAnnotatedString {
                append(item.text)
                withStyle(
                    SpanStyle(
                        color = Color(0xFFFF3650),
                    ),
                ) {
                    append("\n")
                    append(item.duration_label)
                }
            },
            textAlign = TextAlign.Center,
            fontSize = 12.sp,
            lineHeight = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xff7a4016),
        )
    }
}

@Composable
private fun BuyButton(
    selectedMemberOption: ReChargeItem?,
    onBuy: (productId: String, fkLink: String) -> Unit = { _, _ -> },
) {
    val buttonTransition = rememberInfiniteTransition(label = "button_anim")
    val buttonTraction by buttonTransition.animateFloat(
        initialValue = 1f,
        targetValue = 0.9f,
        animationSpec =
            infiniteRepeatable(
                tween(800, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse,
            ),
        label = "button_animation",
    )

    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.BottomCenter) {
        Box(
            modifier =
                Modifier
                    .padding(bottom = 42.dp)
                    .scale(buttonTraction)
                    .paint(painter = painterResource(id = R.drawable.ic_first_recharge_buy_bg))
                    .click(noEffect = true) {
                        selectedMemberOption?.also {
                            onBuy(it.productId, it.fkLink)
                        }
                    },
        ) {
            Text(
                text =
                    buildAnnotatedString {
                        append("立即购买".localized)
                        if (selectedMemberOption != null) {
                            withStyle(
                                SpanStyle(fontSize = 16.sp),
                            ) {
                                val str = selectedMemberOption.transformPrice.replace(" ", "")
                                if (str.isNotBlank()) {
                                    append(" ")
                                    append(str)
                                }
                            }
                        }
                    },
                fontSize = 20.sp,
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.align(Alignment.Center),
            )
        }

        NetworkImage(
            R.drawable.ic_first_recharge_hand,
            modifier =
                Modifier
                    .padding(end = 48.dp, bottom = 4.dp)
                    .size(72.dp, 80.dp)
                    .align(Alignment.BottomEnd),
            contentScale = ContentScale.FillWidth,
        )
    }
}
//endregion

@Preview(widthDp = 375)
@Composable
private fun FirstRechargeDialogPreview() {
    val bean =
        AppJson.decodeFromString<FirstRechargeInfo>(
            "{\"items\":[],\"cost_num\":\"0.99\",\"value_num\":\"80\",\"unit\":\"\"}",
        )
    FirstRechargeDialogWrapper(bean)
}
