package com.buque.wakoo.ui.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val WakooIcons.MicLine: ImageVector
    get() {
        if (_MicLine != null) {
            return _MicLine!!
        }
        _MicLine =
            ImageVector
                .Builder(
                    name = "<PERSON><PERSON><PERSON><PERSON>",
                    defaultWidth = 24.dp,
                    defaultHeight = 24.dp,
                    viewportWidth = 24f,
                    viewportHeight = 24f,
                ).apply {
                    path(fill = SolidColor(Color(0xFF111111))) {
                        moveTo(12f, 3f)
                        curveTo(10.343f, 3f, 9f, 4.343f, 9f, 6f)
                        verticalLineTo(10f)
                        curveTo(9f, 11.657f, 10.343f, 13f, 12f, 13f)
                        curveTo(13.657f, 13f, 15f, 11.657f, 15f, 10f)
                        verticalLineTo(6f)
                        curveTo(15f, 4.343f, 13.657f, 3f, 12f, 3f)
                        close()
                        moveTo(12f, 1f)
                        curveTo(14.761f, 1f, 17f, 3.239f, 17f, 6f)
                        verticalLineTo(10f)
                        curveTo(17f, 12.761f, 14.761f, 15f, 12f, 15f)
                        curveTo(9.238f, 15f, 7f, 12.761f, 7f, 10f)
                        verticalLineTo(6f)
                        curveTo(7f, 3.239f, 9.238f, 1f, 12f, 1f)
                        close()
                        moveTo(3.055f, 11f)
                        horizontalLineTo(5.071f)
                        curveTo(5.556f, 14.392f, 8.473f, 17f, 12f, 17f)
                        curveTo(15.526f, 17f, 18.444f, 14.392f, 18.929f, 11f)
                        horizontalLineTo(20.945f)
                        curveTo(20.484f, 15.172f, 17.171f, 18.484f, 13f, 18.945f)
                        verticalLineTo(23f)
                        horizontalLineTo(11f)
                        verticalLineTo(18.945f)
                        curveTo(6.828f, 18.484f, 3.516f, 15.172f, 3.055f, 11f)
                        close()
                    }
                }.build()

        return _MicLine!!
    }

@Suppress("ObjectPropertyName")
private var _MicLine: ImageVector? = null
