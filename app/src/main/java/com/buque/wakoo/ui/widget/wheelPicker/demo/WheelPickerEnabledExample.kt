package com.buque.wakoo.ui.widget.wheelPicker.demo

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.ui.widget.wheelPicker.WheelPicker
import com.buque.wakoo.ui.widget.wheelPicker.rememberWheelPickerState
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.daysUntil
import kotlinx.datetime.number
import kotlinx.datetime.plus
import kotlinx.datetime.todayIn
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

/**
 * 带有可用性检查的WheelPicker示例
 * 演示如何限制某些选项不可选择
 */
@Composable
fun WheelPickerEnabledExample() {
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(32.dp),
    ) {
        Text(
            text = "可用性检查示例",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
        )

        // 日期选择器示例 - 只能选择今天及之前的日期
        DatePickerWithEnabledExample()

        // 数字选择器示例 - 只能选择偶数
        NumberPickerWithEnabledExample()
    }
}

/**
 * 日期选择器示例 - 只能选择今天及之前的日期
 */
@OptIn(ExperimentalTime::class)
@Composable
private fun DatePickerWithEnabledExample() {
    var selectedDate by remember {
        mutableStateOf(Clock.System.todayIn(TimeZone.currentSystemDefault()))
    }

    val today = Clock.System.todayIn(TimeZone.currentSystemDefault())

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "日期选择器（只能选择今天及之前）",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            // 年份选择器
            val years = remember { (2020..2030).toList() }
            val yearState =
                rememberWheelPickerState(
                    initialIndex = years.indexOf(selectedDate.year).coerceAtLeast(0),
                    itemCount = years.size,
                    isItemEnabled = { index ->
                        val year = years.getOrNull(index) ?: today.year
                        year <= today.year
                    },
                )

            // 月份选择器
            val months = remember { (1..12).toList() }
            val monthState =
                rememberWheelPickerState(
                    initialIndex = selectedDate.month.number - 1,
                    itemCount = months.size,
                    isItemEnabled = { index ->
                        val month = months.getOrNull(index) ?: today.month.number
                        val year = years.getOrNull(yearState.snappedIndex) ?: today.year
                        if (year < today.year) {
                            true // 过去的年份，所有月份都可选
                        } else {
                            month <= today.month.number // 当前年份，只能选择当前月份及之前
                        }
                    },
                )

            // 日期选择器
            val currentYear = years.getOrNull(yearState.snappedIndex) ?: today.year
            val currentMonth = months.getOrNull(monthState.snappedIndex) ?: today.month.number
            val days =
                remember(currentYear, currentMonth) {
                    val daysInMonth =
                        LocalDate(currentYear, currentMonth, 1).daysUntil(
                            LocalDate(currentYear, currentMonth, 1).plus(1, DateTimeUnit.MONTH),
                        )
                    (1..daysInMonth).toList()
                }

            val dayState =
                rememberWheelPickerState(
                    initialIndex = selectedDate.day - 1,
                    itemCount = days.size,
                    isItemEnabled = { index ->
                        val day = days.getOrNull(index) ?: today.day
                        val testDate = LocalDate(currentYear, currentMonth, day)
                        testDate <= today
                    },
                )

            // 监听选择变化
            LaunchedEffect(yearState.snappedIndex, monthState.snappedIndex, dayState.snappedIndex) {
                val year = years.getOrNull(yearState.snappedIndex) ?: today.year
                val month = months.getOrNull(monthState.snappedIndex) ?: today.month.number
                val day = days.getOrNull(dayState.snappedIndex) ?: today.day
                selectedDate = LocalDate(year, month, day)
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                // 年份
                WheelPicker(
                    items = years,
                    state = yearState,
                    modifier = Modifier.weight(1f),
                    visibleItemsCount = 5,
                    itemHeight = 48.dp,
                    itemContent = { year, isSelected, isEnabled ->
                        Text(
                            text = "${year}年",
                            style = MaterialTheme.typography.bodyLarge,
                            color =
                                when {
                                    !isEnabled -> Color.Gray.copy(alpha = 0.3f)
                                    isSelected -> MaterialTheme.colorScheme.primary
                                    else -> MaterialTheme.colorScheme.onSurface
                                },
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        )
                    },
                )

                // 月份
                WheelPicker(
                    items = months,
                    state = monthState,
                    modifier = Modifier.weight(1f),
                    visibleItemsCount = 5,
                    itemHeight = 48.dp,
                    itemContent = { month, isSelected, isEnabled ->
                        Text(
                            text = "${month}月",
                            style = MaterialTheme.typography.bodyLarge,
                            color =
                                when {
                                    !isEnabled -> Color.Gray.copy(alpha = 0.3f)
                                    isSelected -> MaterialTheme.colorScheme.primary
                                    else -> MaterialTheme.colorScheme.onSurface
                                },
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        )
                    },
                )

                // 日期
                WheelPicker(
                    items = days,
                    state = dayState,
                    modifier = Modifier.weight(1f),
                    visibleItemsCount = 5,
                    itemHeight = 48.dp,
                    itemContent = { day, isSelected, isEnabled ->
                        Text(
                            text = "${day}日",
                            style = MaterialTheme.typography.bodyLarge,
                            color =
                                when {
                                    !isEnabled -> Color.Gray.copy(alpha = 0.3f)
                                    isSelected -> MaterialTheme.colorScheme.primary
                                    else -> MaterialTheme.colorScheme.onSurface
                                },
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        )
                    },
                )
            }

            Text(
                text = "选中日期: $selectedDate",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 数字选择器示例 - 只能选择偶数
 */
@Composable
private fun NumberPickerWithEnabledExample() {
    var selectedNumber by remember { mutableIntStateOf(0) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "数字选择器（只能选择偶数）",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            val numbers = remember { (0..20).toList() }
            val state =
                rememberWheelPickerState(
                    initialIndex = 0,
                    itemCount = numbers.size,
                    isItemEnabled = { index ->
                        val number = numbers.getOrNull(index) ?: 0
                        number % 2 == 0 // 只有偶数可选
                    },
                )

            // 监听选择变化
            LaunchedEffect(state.snappedIndex) {
                selectedNumber = numbers.getOrNull(state.snappedIndex) ?: 0
            }

            WheelPicker(
                items = numbers,
                state = state,
                modifier = Modifier.fillMaxWidth(),
                visibleItemsCount = 7,
                itemHeight = 48.dp,
                itemContent = { number, isSelected, isEnabled ->
                    Text(
                        text = number.toString(),
                        style = MaterialTheme.typography.bodyLarge,
                        color =
                            when {
                                !isEnabled -> Color.Gray.copy(alpha = 0.3f)
                                isSelected -> Color.Red
                                else -> MaterialTheme.colorScheme.onSurface
                            },
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    )
                },
            )

            Text(
                text = "选中数字: $selectedNumber",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )

            Text(
                text = "提示: 奇数不可选择，滚动到奇数会自动跳转到最近的偶数",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun WheelPickerEnabledExamplePreview() {
    MaterialTheme {
        WheelPickerEnabledExample()
    }
}
