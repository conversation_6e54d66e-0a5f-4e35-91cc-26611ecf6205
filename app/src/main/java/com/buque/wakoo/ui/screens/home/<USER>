package com.buque.wakoo.ui.screens.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.lerp
import androidx.compose.ui.zIndex
import com.buque.wakoo.R
import com.buque.wakoo.bean.BasicRoomInfo
import com.buque.wakoo.bean.PageInfo
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.screens.chatgroup.screen.GroupSquarePage
import com.buque.wakoo.ui.screens.home.child.RecommendUserListPage
import com.buque.wakoo.ui.screens.liveroom.HomeLiveRoomListPage
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooSelected
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooUnSelected
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.rememberSelectionFractions
import com.buque.wakoo.ui.widget.state.CStateLayout
import kotlinx.coroutines.launch

@Composable
fun DiscoverPage(
    modifier: Modifier = Modifier,
    onCreateLiveRoom: () -> Unit = {},
    onJoinRoom: (room: BasicRoomInfo) -> Unit = {},
    toNavigate: (AppNavKey) -> Unit = {},
) {
    val scope = rememberCoroutineScope()

    val cStateConfig by AppConfigManager.rememberUIConfigState(true)

    CStateLayout(
        state = cStateConfig,
        modifier =
            modifier
                .fillMaxSize()
                .background(color = Color(0xfff7f7f7)),
        useScrollableLayout = false,
        onRetry = {
            scope.launch {
                AppConfigManager.getUIConfig(false)
            }
        },
        emptyCheckProvider = {
            it.data.disCoveryTabs.isEmpty()
        },
        emptyButton = "刷新数据".localized,
        onEmptyClick = {
            scope.launch {
                AppConfigManager.getUIConfig(false)
            }
        },
    ) {
        val tabs = it.disCoveryTabs
        val pagerState = rememberPagerState(
            initialPage = if (it.landingPage.pageType == PageInfo.DISCOVER)
                tabs.indexOfFirst { tab -> tab.t == it.landingPage.subPage }.takeIf { i -> i >= 0 } ?: 0
            else 0, pageCount = { tabs.size })
        val selectedTabIndex = pagerState.currentPage

        val selectionFractions by rememberSelectionFractions(pagerState)

        val tabWidths = remember { mutableStateMapOf<Int, Float>() }

        val extraPadding by with(LocalDensity.current) {
            remember {
                derivedStateOf {
                    tabs
                        .foldIndexed(0f) { index, acc, _ ->
                            val scale = selectionFractions.getOrElse(index) { 1f } * 0.2f
                            val width = tabWidths.getOrElse(index) { 0f }
                            acc + width * scale
                        }.toDp()
                }
            }
        }

        Column(
            modifier =
                modifier
                    .fillMaxSize()
                    .statusBarsPadding(),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                ScrollableTabRow(
                    selectedTabIndex = selectedTabIndex,
                    modifier = Modifier.weight(1f),
                    divider = {},
                    edgePadding = 24.dp,
                    containerColor = Color.Transparent,
                    indicator = { tabPositions ->
                        if (selectedTabIndex < tabPositions.size) {
                            Box(
                                modifier =
                                    Modifier
                                        .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                        .fillMaxWidth(),
                            ) {
                                Image(
                                    modifier =
                                        Modifier
                                            .size(
                                                width = 75.dp,
                                                height = 10.6.dp,
                                            )
                                            .offset(
                                                x = (-3).dp,
                                                y = 0.5.dp,
                                            ),
                                    contentDescription = null,
                                    painter = painterResource(R.drawable.ic_tab_diacator_four_font),
                                    contentScale = ContentScale.Fit,
                                )
                            }
                        }
                    },
                ) {
                    tabs.forEachIndexed { index, tab ->
                        val fraction by remember {
                            derivedStateOf {
                                selectionFractions.getOrElse(index) { 1f }
                            }
                        }
                        var textWidth by remember {
                            mutableFloatStateOf(0f)
                        }
                        Tab(
                            selected = selectedTabIndex == index,
                            modifier =
                                Modifier
                                    .zIndex(1f)
                                    .graphicsLayer {
                                        if (index > 0) {
                                            val totalTranslation =
                                                run {
                                                    var temp = 0f
                                                    tabs.forEachIndexed { i, _ ->
                                                        if (i < index) {
                                                            val scale = selectionFractions.getOrElse(i) { 1f } * 0.2f
                                                            temp += (tabWidths[i] ?: 0f) * scale
                                                        } else {
                                                            return@forEachIndexed
                                                        }
                                                    }
                                                    temp
                                                }
                                            translationX = totalTranslation
                                        }
                                    },
                            selectedContentColor = WakooSelected,
                            unselectedContentColor = WakooUnSelected,
                            interactionSource = remember { NoIndicationInteractionSource() },
                            onClick = {
                                scope.launch {
                                    pagerState.animateScrollToPage(index)
                                }
                            },
                            content = {
                                val animatedColor =
                                    lerp(
                                        start = WakooUnSelected,
                                        stop = WakooSelected,
                                        fraction = fraction,
                                    )
                                Box(
                                    modifier =
                                        Modifier.padding(
                                            top = 5.dp,
                                            end = 20.dp,
                                        ),
                                ) {
                                    Text(
                                        text = tab.name,
                                        color = animatedColor,
                                        modifier =
                                            Modifier.graphicsLayer {
                                                textWidth = size.width
                                                tabWidths[index] = textWidth
                                                val scale =
                                                    lerp(
                                                        1f,
                                                        1.2f,
                                                        fraction,
                                                    )
                                                scaleX = scale
                                                scaleY = scale
                                                transformOrigin =
                                                    TransformOrigin(
                                                        0f,
                                                        1f,
                                                    )
                                            },
                                        fontFamily = FontFamily.MI_SANS,
                                        fontSize = 16.sp,
                                    )
                                }
                            },
                        )
                    }
                    if (extraPadding.value > 0) {
                        SizeWidth(extraPadding)
                    }
                }

                SizeHeight(42.dp)
            }

            HorizontalPager(
                state = pagerState,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
                beyondViewportPageCount = 1,
            ) { page ->
                val tab = tabs[page]
                when (tab.t) {
                    1 -> {
                        HomeLiveRoomListPage(
                            onCreateLiveRoom = onCreateLiveRoom,
                            onJoinRoom = onJoinRoom,
                        )
                    }

                    2 -> {
                        RecommendUserListPage(toNavigate = toNavigate)
                    }

                    3 -> {
                        GroupSquarePage(
                            toChatGroupDetail = {
                                toNavigate(Route.ChatGroupDetail(it))
                            },
                            toMineGroup = { id ->
                                toNavigate(Route.ChatGroup(id))
                            },
                            bottomPadding = 108.dp,
                        )
                    }

                    else -> {
                        Box(contentAlignment = Alignment.Center) {
                            Text("当前版本暂不支持".localized)
                        }
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun DiscoverPagePreview() {
    WakooTheme {
        DiscoverPage()
    }
}
