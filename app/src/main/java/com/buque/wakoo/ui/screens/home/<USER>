package com.buque.wakoo.ui.screens.home

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.lerp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.R
import com.buque.wakoo.app.Const
import com.buque.wakoo.app.DevicesKV
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.noRippleCombinedClickable
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.VoiceListTab
import com.buque.wakoo.ui.icons.MicVoiceGreen
import com.buque.wakoo.ui.icons.Search
import com.buque.wakoo.ui.icons.TabIndicator
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.voice.VoiceVerticalListPager
import com.buque.wakoo.ui.theme.WakooSelected
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooUnSelected
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.rememberSelectionFractions
import com.buque.wakoo.utils.eventBus.AppEvent
import com.buque.wakoo.utils.eventBus.EventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

private val TABS: List<VoiceListTab.ISquare> =
    listOf(
        VoiceListTab.Recommend,
        VoiceListTab.Follow,
    )

private const val PAGE_TAG_PREFIX = "square-tab-"

@Composable
fun SquareTabPage(
    toSearchScreen: () -> Unit = {},
    toPublishVoice: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState(pageCount = { TABS.size })
    val selectedTabIndex = pagerState.currentPage

    val selectionFractions by rememberSelectionFractions(pagerState)

    val tabWidths = remember { mutableStateMapOf<Int, Float>() }

    val extraPadding by with(LocalDensity.current) {
        remember {
            derivedStateOf {
                TABS
                    .foldIndexed(0f) { index, acc, _ ->
                        val scale = selectionFractions.getOrElse(index) { 1f } * 0.2f
                        val width = tabWidths.getOrElse(index) { 0f }
                        acc + width * scale
                    }.toDp()
            }
        }
    }

    if (!LocalInspectionMode.current) {
        var firstEnter by remember {
            mutableStateOf(DevicesKV.getBoolean(Const.KVKey.FIRST_ENTER_VOICE_FEED, true))
        }

        if (firstEnter) {
            LaunchedEffect(Unit) {
                DevicesKV.putBoolean(Const.KVKey.FIRST_ENTER_VOICE_FEED, false)
                delay(5000)
                firstEnter = false
            }

            Dialog(onDismissRequest = {
                firstEnter = false
            }) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Image(
                        painter = painterResource(R.drawable.bg_voice_feed_guide),
                        contentDescription = null,
                        modifier = Modifier.height(223.dp),
                        contentScale = ContentScale.FillHeight,
                    )
                    SizeHeight(12.dp)
                    Text(text = "上下滑动可以切换声音".localized, fontSize = 16.sp, color = WakooWhite)
                }
            }
        }
    }

    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .statusBarsPadding(),
    ) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            ScrollableTabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.weight(1f),
                divider = {},
                edgePadding = 0.dp,
                containerColor = Color.Transparent,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Box(
                            modifier =
                                Modifier
                                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                    .fillMaxWidth(),
                        ) {
                            Image(
                                modifier =
                                    Modifier
                                        .size(
                                            width = 48.dp,
                                            height = 10.6.dp,
                                        )
                                        .offset(
                                            x = (-3).dp,
                                            y = 0.5.dp,
                                        ),
                                contentDescription = null,
                                imageVector = WakooIcons.TabIndicator,
                            )
                        }
                    }
                },
            ) {
                TABS.forEachIndexed { index, tab ->
                    val fraction by remember {
                        derivedStateOf {
                            selectionFractions.getOrElse(index) { 1f }
                        }
                    }
                    var textWidth by remember {
                        mutableFloatStateOf(0f)
                    }
                    Tab(
                        selected = selectedTabIndex == index,
                        modifier =
                            Modifier
                                .zIndex(1f)
                                .graphicsLayer {
                                    if (index > 0) {
                                        val totalTranslation =
                                            run {
                                                var temp = 0f
                                                TABS.forEachIndexed { i, _ ->
                                                    if (i < index) {
                                                        val scale =
                                                            selectionFractions.getOrElse(i) { 1f } * 0.2f
                                                        temp += (tabWidths[i] ?: 0f) * scale
                                                    } else {
                                                        return@forEachIndexed
                                                    }
                                                }
                                                temp
                                            }
                                        translationX = totalTranslation
                                    }
                                },
                        selectedContentColor = WakooSelected,
                        unselectedContentColor = WakooUnSelected,
                        interactionSource = remember { NoIndicationInteractionSource() },
                        onClick = {},
                        content = {
                            val animatedColor =
                                lerp(
                                    start = WakooUnSelected,
                                    stop = WakooSelected,
                                    fraction = fraction,
                                )
                            Box(
                                modifier =
                                    Modifier.padding(
                                        top = 5.dp,
                                        end = 20.dp,
                                    ),
                            ) {
                                tab.TabContent(
                                    selected = selectedTabIndex == index,
                                    modifier =
                                        Modifier
                                            .noRippleCombinedClickable(
                                                onDoubleClick = {
                                                    if (selectedTabIndex == index) {
                                                        scope.launch {
                                                            EventBus.send(AppEvent.Refresh("square"))
                                                        }
                                                    }
                                                },
                                                onClick = {
                                                    scope.launch {
                                                        pagerState.animateScrollToPage(index)
                                                    }
                                                },
                                            )
                                            .graphicsLayer {
                                                textWidth = size.width
                                                tabWidths[index] = textWidth
                                                val scale =
                                                    lerp(
                                                        1f,
                                                        1.2f,
                                                        fraction,
                                                    )
                                                scaleX = scale
                                                scaleY = scale
                                                transformOrigin =
                                                    TransformOrigin(
                                                        0f,
                                                        1f,
                                                    )
                                            },
                                    color = animatedColor,
                                )
                            }
                        },
                    )
                }
                if (extraPadding.value > 0) {
                    SizeWidth(extraPadding)
                }
            }
            val uiConfig by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()

            AnimatedVisibility(uiConfig.searchBtnExists) {
                IconButton(
                    onClick = toSearchScreen,
                    modifier = Modifier.size(24.dp),
                ) {
                    Icon(WakooIcons.Search, contentDescription = "search everything")
                }
            }
            AnimatedVisibility(uiConfig.showPublishVoice) {
                Row(
                    modifier =
                        Modifier
                            .height(28.dp)
                            .background(color = Color(0xff111111), shape = CircleShape)
                            .click(noEffect = true, onClick = toPublishVoice)
                            .padding(horizontal = 10.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        imageVector = WakooIcons.MicVoiceGreen,
                        contentDescription = "发布",
                        modifier = Modifier.size(16.dp),
                    )
                    Spacer(Modifier.width(1.dp))
                    Text(
                        "发布".localized,
                        fontSize = 12.sp,
                        lineHeight = 12.sp,
                        style =
                            TextStyle(
                                brush =
                                    Brush.horizontalGradient(
                                        colors = listOf(Color(0xFFA3FF2C), Color(0xFF31FFA1)),
                                    ),
                            ),
                    )
                }
            }
        }

        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(Unit) {
            val observer =
                LifecycleEventObserver { source, event ->
                    if (event == Lifecycle.Event.ON_STOP) {
                        MediaPlayerManager.pauseIf {
                            it.tag.startsWith(PAGE_TAG_PREFIX)
                        }
                    }
                }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                // Composable 离开组合时执行清理
                MediaPlayerManager.releaseIf {
                    it.tag.startsWith(PAGE_TAG_PREFIX)
                }
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .weight(1f),
            beyondViewportPageCount = 1,
        ) { page ->
            val tab = TABS[page]
            LaunchedEffect(Unit) {
                snapshotFlow {
                    pagerState.currentPage != page
                }.filter { it }.collectLatest {
                    MediaPlayerManager.releaseIf {
                        it.tag.startsWith("$PAGE_TAG_PREFIX$tab")
                    }
                }
            }

            if (pagerState.currentPage == 0) {

                DisposableEffect(Unit) {
                    val observer =
                        LifecycleEventObserver { source, event ->
                            LiveRoomManager.inFeedPage = event.targetState.isAtLeast(Lifecycle.State.RESUMED)
                        }

                    lifecycleOwner.lifecycle.addObserver(observer)

                    onDispose {
                        LiveRoomManager.inFeedPage = false
                        lifecycleOwner.lifecycle.removeObserver(observer)
                    }
                }
            }


            VoiceVerticalListPager(
                tagPrefix = PAGE_TAG_PREFIX,
                tab = tab,
                indexInParent = page,
                parentPagerState = pagerState,
                showBanner = tab == VoiceListTab.Recommend,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun SquareTabPagePreview() {
    WakooTheme {
        SquareTabPage()
    }
}
