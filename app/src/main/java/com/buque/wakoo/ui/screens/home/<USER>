package com.buque.wakoo.ui.screens.home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Badge
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.formatUnreadCount
import com.buque.wakoo.im_business.conversation.AppConversationManger
import com.buque.wakoo.im_business.conversation.C2CConversation
import com.buque.wakoo.im_business.conversation.TribeConversation
import com.buque.wakoo.navigation.MessageTab
import com.buque.wakoo.ui.screens.messages.chat.ConversationPage
import com.buque.wakoo.ui.screens.messages.notification.NotificationInfoScreen
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.AdaptiveScrollableTabRow
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.adaptiveTabIndicatorOffset
import kotlinx.coroutines.launch

private val TABS: List<MessageTab> =
    listOf(
        MessageTab.IMMessage, // 关注
        MessageTab.Notification, // 粉丝
    )

@Composable
fun MessagePage(
    toC2CChat: (user: User) -> Unit = {},
    toChatGroup: (id: String?) -> Unit = {},
) {
    val pagerState = rememberPagerState(pageCount = { TABS.size })
    val selectedTabIndex = pagerState.currentPage
    val scope = rememberCoroutineScope()

    SegColorTitleScreenScaffold(
        "",
        topBar = {
            TopAppBar(
                title = {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center,
                    ) {
                        AdaptiveScrollableTabRow(
                            selectedTabIndex = selectedTabIndex,
                            tabSpacing = 20.dp,
                            indicator = { tabPositions ->
                                if (selectedTabIndex < tabPositions.size) {
                                    Box(
                                        modifier =
                                            Modifier
                                                .adaptiveTabIndicatorOffset(tabPositions[selectedTabIndex])
                                                .requiredWidth(12.dp)
                                                .height(3.dp)
                                                .background(
                                                    WakooSecondarySelected,
                                                    CircleShape,
                                                ),
                                    )
                                }
                            },
                        ) {
                            TABS.forEachIndexed { index, tab ->
                                Tab(
                                    selected = selectedTabIndex == index,
                                    selectedContentColor = WakooSecondarySelected,
                                    unselectedContentColor = WakooSecondaryUnSelected,
                                    onClick = {
                                        scope.launch {
                                            pagerState.animateScrollToPage(index)
                                        }
                                    },
                                    interactionSource = remember { NoIndicationInteractionSource() },
                                    content = {
                                        Box(
                                            modifier =
                                                Modifier
                                                    .padding(bottom = 4.dp),
                                            contentAlignment = Alignment.Center,
                                        ) {
                                            tab.TabContent(
                                                selectedTabIndex == index,
                                                modifier = Modifier.padding(horizontal = 18.dp),
                                            )
                                            if (index == 0) {
                                                val unReadCount by AppConversationManger.unReadCountFlow.collectAsStateWithLifecycle()
                                                if (unReadCount > 0) {
                                                    Badge(modifier = Modifier.align(Alignment.TopEnd)) {
                                                        Text(
                                                            text = unReadCount.formatUnreadCount(99),
                                                            style =
                                                                TextStyle(
                                                                    fontSize = 11.sp,
                                                                    lineHeight = 16.sp,
                                                                    fontWeight = FontWeight.Medium,
                                                                    color = Color.White,
                                                                ),
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    },
                                )
                            }
                        }
                    }
                },
            )
        },
        contentWindowInsets = WindowInsets.statusBars,
    ) { pv ->
        HorizontalPager(pagerState, modifier = Modifier.padding(pv)) { index ->
            val page = TABS[index]
            when (page) {
                MessageTab.IMMessage -> {
                    ConversationPage(true) {
                        when (it) {
                            is C2CConversation -> {
                                toC2CChat(it.user)
                            }

                            is TribeConversation -> {
                                if (it is TribeConversation.Empty) {
                                    toChatGroup(null)
                                } else if (it is TribeConversation.Instance) {
                                    toChatGroup(it.group.id)
                                }
                            }
                        }
                    }
                }

                MessageTab.Notification -> {
                    NotificationInfoScreen()
                }
            }
        }
    }
}
