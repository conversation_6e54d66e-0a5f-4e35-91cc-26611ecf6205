package com.buque.wakoo.ui.widget.state

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.isSpecified
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SolidButton

object StateComponent {
    @Composable
    fun Custom(
        imageContent: @Composable ColumnScope.() -> Unit,
        textContent: @Composable ColumnScope.() -> Unit,
        modifier: Modifier = Modifier,
        verticalSpacing: Dp = 16.dp,
        buttonContent: @Composable (ColumnScope.() -> Unit)? = null,
    ) {
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            imageContent()
            SizeHeight(verticalSpacing)
            textContent()
            if (buttonContent != null) {
                SizeHeight(verticalSpacing * 0.5f) // 按钮与文本间距稍大
                buttonContent()
            }
        }
    }

    @Composable
    fun Empty(
        modifier: Modifier = Modifier,
        text: String = "暂无数据".localized,
        emptyId: Int = R.drawable.ic_empty_for_all,
        textColor: Color = Color(0xFFB6B6B6),
        verticalSpacing: Dp = 16.dp,
        buttonText: String? = null,
        onClick: () -> Unit = {},
    ) {
        Custom(
            imageContent = {
                StateComponentDefaults.SImage(id = emptyId, contentDescription = "empty")
            },
            textContent = {
                StateComponentDefaults.SText(text = text, color = textColor)
            },
            modifier = modifier,
            verticalSpacing = verticalSpacing,
            buttonContent =
                if (!buttonText.isNullOrBlank()) {
                    {
                        SolidButton(
                            text = buttonText,
                            onClick = onClick,
                            fontSize = 14.sp,
                            height = 40.dp,
                            paddingValues = PaddingValues(horizontal = 30.dp),
                        )
                    }
                } else {
                    null
                },
        )
    }

    @Composable
    fun Error(
        text: String,
        modifier: Modifier = Modifier,
        errorId: Int = R.drawable.ic_error_for_all,
        textColor: Color = Color(0xFFB6B6B6),
        verticalSpacing: Dp = 16.dp,
        buttonText: String? = "重试".localized,
        onClick: () -> Unit,
    ) {
        Custom(
            imageContent = {
                StateComponentDefaults.SImage(id = errorId, contentDescription = "error")
            },
            textContent = {
                StateComponentDefaults.SText(text = text, color = textColor)
            },
            modifier = modifier,
            verticalSpacing = verticalSpacing,
            buttonContent =
                if (!buttonText.isNullOrBlank()) {
                    {
                        SolidButton(
                            text = buttonText,
                            onClick = onClick,
                            fontSize = 14.sp,
                            height = 40.dp,
                            paddingValues = PaddingValues(horizontal = 30.dp),
                        )
                    }
                } else {
                    null
                },
        )
    }

    @Composable
    fun NetworkError(
        modifier: Modifier = Modifier,
        text: String = "网络错误".localized,
        errorId: Int = R.drawable.ic_error_for_network,
        textColor: Color = Color(0xFFB6B6B6),
        verticalSpacing: Dp = 16.dp,
        buttonText: String? = "重试".localized,
        onClick: () -> Unit,
    ) {
        Error(
            text = text,
            modifier = modifier,
            errorId = errorId,
            textColor = textColor,
            verticalSpacing = verticalSpacing,
            buttonText = buttonText,
            onClick = onClick,
        )
    }

    /**
     * 默认的加载中视图
     */
    @Composable
    fun Loading(modifier: Modifier = Modifier) {
        CircularProgressIndicator(modifier = modifier)
    }
}

//
// /**
// * 包含 [StateComponent] 默认实现的工厂对象。
// * 允许轻松地创建和自定义状态组件的默认部分。
// */
object StateComponentDefaults {
    /**
     * [StateComponent] 的默认图片/图标。
     *
     * @param id 要显示的 Painter。
     * @param contentDescription 无障碍功能的内容描述。
     * @param modifier 应用于图标的 Modifier。
     * @param size 图标的大小。
     * @param tint 图标的着色。默认为半透明的 onSurface 颜色。
     */
    @Composable
    fun SImage(
        id: Int,
        modifier: Modifier = Modifier,
        contentDescription: String? = null,
        size: Dp = 120.dp,
        tint: Color = Color.Unspecified,
    ) {
        androidx.compose.foundation.Image(
            painter = painterResource(id),
            contentDescription = contentDescription,
            modifier = modifier.size(size),
            colorFilter =
                tint.takeIf { it.isSpecified }?.let {
                    ColorFilter.tint(tint)
                },
        )
    }

    /**
     * [StateComponent] 的默认图片/图标。
     *
     * @param painter 要显示的 Painter。
     * @param contentDescription 无障碍功能的内容描述。
     * @param modifier 应用于图标的 Modifier。
     * @param size 图标的大小。
     * @param tint 图标的着色。使用 Color.Unspecified 将使用原始颜色。
     */
    @Composable
    fun SImage(
        painter: Painter,
        modifier: Modifier = Modifier,
        contentDescription: String? = null,
        size: Dp = 120.dp,
        tint: Color = Color.Unspecified,
    ) {
        androidx.compose.foundation.Image(
            painter = painter,
            contentDescription = contentDescription,
            modifier = modifier.size(size),
            colorFilter =
                tint.takeIf { it.isSpecified }?.let {
                    ColorFilter.tint(tint)
                },
        )
    }

    /**
     * [StateComponent] 的默认图片/图标。
     *
     * @param imageVector 要显示的 imageVector。
     * @param contentDescription 无障碍功能的内容描述。
     * @param modifier 应用于图标的 Modifier。
     * @param size 图标的大小。
     * @param tint 图标的着色。使用 Color.Unspecified 将使用原始颜色。
     */
    @Composable
    fun SImage(
        imageVector: ImageVector,
        modifier: Modifier = Modifier,
        contentDescription: String? = null,
        size: Dp = 120.dp,
        tint: Color = Color.Unspecified,
    ) {
        androidx.compose.foundation.Image(
            imageVector = imageVector,
            contentDescription = contentDescription,
            modifier = modifier.size(size),
            colorFilter =
                tint.takeIf { it.isSpecified }?.let {
                    ColorFilter.tint(tint)
                },
        )
    }

    /**
     * [StateComponent] 的默认文本。
     *
     * @param text 要显示的字符串。
     * @param modifier 应用于文本的 Modifier。
     * @param color 文本颜色。
     * @param fontSize 字体大小
     * @param style 文本样式。
     */
    @Composable
    fun SText(
        text: String,
        modifier: Modifier = Modifier,
        color: Color = Color(0xFFB6B6B6),
        fontSize: TextUnit = TextUnit.Unspecified,
        style: TextStyle = MaterialTheme.typography.labelLarge,
    ) {
        androidx.compose.material3.Text(
            text = text,
            color = color,
            style = MaterialTheme.typography.labelLarge,
            fontSize = fontSize,
            textAlign = TextAlign.Center
        )
    }
}

// --- 预览和使用示例 ---

/**
 * 示例1：使用默认样式的 StateComponent
 */
@Preview(
    showBackground = true,
    name = "Empty State Preview",
)
@Composable
private fun EmptyStateComponentPreview() {
    MaterialTheme {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            StateComponent.Empty()
        }
    }
}

@Preview(
    showBackground = true,
    name = "Error State Preview",
)
@Composable
private fun ErrorStateComponentPreview() {
    MaterialTheme {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            StateComponent.Error(text = "请求错误", errorId = R.drawable.ic_error_for_all) {
            }
        }
    }
}

@Preview(
    showBackground = true,
    name = "Error State Preview",
)
@Composable
private fun NetworkErrorStateComponentPreview() {
    MaterialTheme {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            StateComponent.Error(text = "网络错误", errorId = R.drawable.ic_error_for_network) {
            }
        }
    }
}
