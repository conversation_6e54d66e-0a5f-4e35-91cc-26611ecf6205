package com.buque.wakoo.ui.widget.media.selector

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import androidx.compose.runtime.mutableStateListOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.ui.widget.media.data.AndroidMediaRepository
import com.buque.wakoo.ui.widget.media.data.common.IMediaItem
import com.buque.wakoo.ui.widget.media.data.common.ImageItem
import com.buque.wakoo.ui.widget.media.data.common.MediaFeed
import com.buque.wakoo.ui.widget.media.data.common.MediaItem
import com.buque.wakoo.ui.widget.media.data.common.MediaSource
import com.buque.wakoo.ui.widget.media.data.common.SelectorMediaType
import com.buque.wakoo.ui.widget.media.data.common.VideoItem
import com.buque.wakoo.viewmodel.BaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.withContext
import java.io.FileNotFoundException

class MediaListViewModel(
    private val mediaType: SelectorMediaType,
    private val maxSelectCount: Int,
    private val selectedItem: List<MediaItem>?,
) : BaseViewModel() {
    class Factory(
        private val mediaType: SelectorMediaType,
        private val maxSelectCount: Int,
        private val selectedItem: List<MediaItem>?,
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T = MediaListViewModel(mediaType, maxSelectCount, selectedItem) as T
    }

    private val androidMediaRepository = AndroidMediaRepository(WakooApplication.instance, viewModelScope)

    private val mediaSource = MediaSource(androidMediaRepository)

    private val tempSelectedId = selectedItem?.map { it.id }?.toMutableSet()

    private val selectedIdList = mutableStateListOf<String>()

    val selectedIds: List<String> get() = selectedIdList

    val mediaFeedFlow by lazy {
        mediaSource
            .getMediaFeed(mediaType)
            .onEach {
                val selectedSize = selectedIdList.size
                var tempList = mutableListOf<String>()
                it.mediaItems.forEach { item ->
                    if (selectedIdList.contains(item.id)) {
                        tempList.add(item.id)
                        if (tempList.size == selectedSize && tempSelectedId.isNullOrEmpty()) {
                            return@onEach
                        }
                    } else if (!tempSelectedId.isNullOrEmpty() && tempSelectedId.contains(item.id)) {
                        changeSelected(item)
                        tempSelectedId.remove(item.id)
                        if (tempList.size == selectedSize && tempSelectedId.isEmpty()) {
                            return@onEach
                        }
                    }
                }
                if (tempList.size != selectedSize) {
                    selectedIdList.clear()
                    selectedIdList.addAll(tempList)
                }
            }.stateIn(viewModelScope, SharingStarted.Eagerly, MediaFeed())
    }

    val selectedItems: List<MediaItem>
        get() =
            mediaFeedFlow.value.mediaItems.mapNotNull {
                if (it is MediaItem && selectedIdList.contains(it.id)) {
                    it
                } else {
                    null
                }
            }

    fun refresh() {
        androidMediaRepository.refresh()
    }

    fun loadMoreMediaInfo() {
        mediaSource.loadNextPage()
    }

    fun changeSelected(item: IMediaItem) {
        if (selectedIdList.any { it == item.id }) {
            selectedIdList.removeIf {
                it == item.id
            }
        } else {
            if (maxSelectCount == 1 && selectedIdList.size == 1) {
                selectedIdList[0] = item.id
            } else if (selectedIdList.size < maxSelectCount) {
                selectedIdList.add(item.id)
            }
        }
    }

    /**
     * 公开给 UI 的入口函数，用于启动媒体信息的加载过程。
     *
     * @param context Context 对象，用于访问 ContentResolver。
     * @param uris 从照片选择器返回的 URI 列表。
     */
    suspend fun loadMediaItems(
        context: Context,
        uris: List<Uri>,
    ): List<MediaItem> =
        if (selectedItem.isNullOrEmpty()) {
            uris.take(maxSelectCount).mapNotNull { uri ->
                getMediaItemFromUri(context, uri)
            }
        } else {
            buildList {
                addAll(selectedItem)
                addAll(
                    uris
                        .filter { uri ->
                            !selectedItem.any { it.uriString == uri.toString() }
                        }.mapNotNull { uri ->
                            getMediaItemFromUri(context, uri)
                        },
                )
            }.take(maxSelectCount)
        }

    /**
     * 核心处理函数，尝试从给定的 URI 中提取完整的媒体元数据。
     * 它采用“首先尝试高效查询，失败则优雅降级”的策略。
     *
     * @param context Context 对象。
     * @param uri 要处理的单个媒体 URI。
     * @return 一个填充了数据的 [MediaItem] 对象，如果处理失败则返回 null。
     */
    private suspend fun getMediaItemFromUri(
        context: Context,
        uri: Uri,
    ): MediaItem? =
        withContext(Dispatchers.IO) {
            // 使用 withContext(Dispatchers.IO) 确保所有操作都在后台 IO 线程上执行。
            val contentResolver = context.contentResolver

            // 定义我们需要从 MediaStore 查询的字段列。
            // 这是最高效的获取元数据的方式。
            val projection =
                arrayOf(
                    MediaStore.MediaColumns._ID,
                    MediaStore.MediaColumns.DISPLAY_NAME,
                    MediaStore.MediaColumns.MIME_TYPE,
                    MediaStore.MediaColumns.SIZE,
                    MediaStore.MediaColumns.DATE_ADDED,
                    MediaStore.MediaColumns.WIDTH,
                    MediaStore.MediaColumns.HEIGHT,
                )

            try {
                // 直接查询返回的 Uri。如果该 Uri 来自 MediaStore，此查询会成功。
                contentResolver
                    .query(
                        uri,
                        projection,
                        null, // selection
                        null, // selectionArgs
                        null, // sortOrder
                    )?.use { cursor ->
                        // .use 会自动关闭 cursor
                        if (cursor.moveToFirst()) {
                            // 从 Cursor 中提取数据
                            val id = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns._ID))
                            val displayName =
                                cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME))
                            val mimeType =
                                cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.MIME_TYPE))
                            val size = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.SIZE))
                            val dateAdded =
                                AndroidMediaRepository.normalizeTimestampToMillis(
                                    cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DATE_ADDED)),
                                )
                            var width = cursor.getInt(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.WIDTH))
                            var height = cursor.getInt(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.HEIGHT))

                            // 健壮性检查：对于某些视频文件，MediaStore 可能不直接提供 width/height。
                            // 如果从 cursor 获取的尺寸为 0，则回退到使用 MediaMetadataRetriever 获取。
                            if (width == 0 || height == 0) {
                                val dimensions =
                                    if (mimeType.startsWith("video/")) {
                                        AndroidMediaRepository.getVideoDimensions(context, uri)
                                    } else {
                                        AndroidMediaRepository.getImageDimensions(context, uri)
                                    }
                                width = dimensions.first
                                height = dimensions.second
                            }

                            // 根据 MIME 类型创建对应的 `ImageItem` 或 `VideoItem`
                            return@withContext if (mimeType.startsWith("video/")) {
                                val duration = AndroidMediaRepository.getVideoDuration(context, uri)
                                VideoItem(
                                    id = if (id == 0L) uri.toString() else id.toString(),
                                    albumId = "", // 按要求为空
                                    uriString = uri.toString(),
                                    displayName = displayName ?: "", // 按要求为空，但我们能拿到
                                    sizeBytes = size,
                                    dateAdded = dateAdded, // 按要求为空，但我们能拿到
                                    width = width,
                                    height = height,
                                    mimeType = mimeType,
                                    durationMillis = duration,
                                )
                            } else {
                                ImageItem(
                                    id = if (id == 0L) uri.toString() else id.toString(),
                                    albumId = "", // 按要求为空
                                    uriString = uri.toString(),
                                    displayName = displayName ?: "", // 按要求为空，但我们能拿到
                                    sizeBytes = size,
                                    dateAdded = dateAdded, // 按要求为空，但我们能拿到
                                    width = width,
                                    height = height,
                                    mimeType = mimeType,
                                )
                            }
                        }
                    }
            } catch (e: Exception) {
                // 如果查询失败（例如，URI 来自云端、或不是一个标准的 content:// URI），
                // 打印错误日志并调用回退方法。
                e.printStackTrace()
                return@withContext getMediaItemFromUriFallback(context, uri)
            }

            // 如果 `query` 返回 null 或者 Cursor 为空，也调用回退方法。
            return@withContext getMediaItemFromUriFallback(context, uri)
        }

    /**
     * 当直接查询 Uri 失败时的回退方法。
     * 这种方法无法获取 MediaStore ID、dateAdded 等数据库字段，但能获取基础信息。
     * 对于处理来自云端（如Google Photos）或第三方应用的文件至关重要。
     *
     * @param context Context 对象。
     * @param uri 要处理的媒体 URI。
     * @return 一个填充了基础数据的 [MediaItem] 对象，或 null。
     */
    private fun getMediaItemFromUriFallback(
        context: Context,
        uri: Uri,
    ): MediaItem? {
        val contentResolver = context.contentResolver
        val mimeType = contentResolver.getType(uri) ?: return null // 如果无法确定类型，则放弃处理

        // 尝试通过 openFileDescriptor 获取文件大小，这比读取整个流更高效。
        var sizeBytes: Long = -1
        try {
            contentResolver.openFileDescriptor(uri, "r")?.use {
                sizeBytes = it.statSize
            }
        } catch (e: FileNotFoundException) {
            // 某些 URI 可能不支持此操作，忽略错误。
            e.printStackTrace()
        }

        return when {
            mimeType.startsWith("video/") -> {
                val dimensions = AndroidMediaRepository.getVideoDimensions(context, uri)
                val duration = AndroidMediaRepository.getVideoDuration(context, uri)
                VideoItem(
                    id = uri.toString(), // 在无法获取真实 ID 时，使用 URI 字符串作为唯一标识
                    albumId = "",
                    uriString = uri.toString(),
                    displayName = "",
                    sizeBytes = sizeBytes,
                    dateAdded = 0L,
                    width = dimensions.first,
                    height = dimensions.second,
                    mimeType = mimeType,
                    durationMillis = duration,
                )
            }

            mimeType.startsWith("image/") -> {
                val dimensions = AndroidMediaRepository.getImageDimensions(context, uri)
                ImageItem(
                    id = uri.toString(), // 使用 URI 字符串作为唯一标识
                    albumId = "",
                    uriString = uri.toString(),
                    displayName = "",
                    sizeBytes = sizeBytes,
                    dateAdded = 0L,
                    width = dimensions.first,
                    height = dimensions.second,
                    mimeType = mimeType,
                )
            }

            else -> null // 不支持的媒体类型
        }
    }
}
