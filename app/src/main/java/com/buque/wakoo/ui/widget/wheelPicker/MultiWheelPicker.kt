package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 通用多级联动滚轮选择器组件
 * 支持任意级数的WheelPicker，每级数据、选中项、可用项、联动逻辑均可自定义
 */
@Composable
fun <T> MultiWheelPicker(
    columns: List<PickerColumn<T>>,
    onValueChange: (selected: List<Int>) -> Unit,
    modifier: Modifier = Modifier,
    space: Dp = 0.dp,
    itemHeight: Dp = WheelPickerDefaults.DefaultItemHeight,
    visibleItemsCount: Int = WheelPickerDefaults.DefaultVisibleItemsCount,
    itemTextStyle: TextStyle = WheelPickerDefaults.defaultTextStyle(),
    selectedTextColor: Color = WheelPickerDefaults.defaultSelectedTextColor(),
    unselectedTextColor: Color = WheelPickerDefaults.defaultUnselectedTextColor(),
    unEnabledTextColor: Color = WheelPickerDefaults.defaultUnEnabledTextColor(),
    dividerConfig: WheelPickerDefaults.DividerConfig = WheelPickerDefaults.defaultDividerConfig(),
    effect3DConfig: WheelPickerDefaults.Effect3DConfig = WheelPickerDefaults.default3DEffectConfig(),
) {
    // 用于驱动每一级的数据
    val itemsList = remember { mutableStateListOf<List<T>>() }
    val selectedIndices = remember { mutableStateListOf<Int>() }
    // 初始化或同步数据
    if (itemsList.size != columns.size || selectedIndices.size != columns.size) {
        itemsList.clear()
        itemsList.addAll(columns.map { it.items })
        selectedIndices.clear()
        selectedIndices.addAll(columns.map { it.initialIndex })
    } else {
        // 保证每级数据和columns同步
        columns.forEachIndexed { idx, col ->
            if (itemsList[idx] !== col.items) {
                itemsList[idx] = col.items
                if (selectedIndices[idx] >= col.items.size) {
                    selectedIndices[idx] = 0
                }
            }
        }
    }
    // 记录每一级的WheelPickerState
    val states =
        columns.mapIndexed { idx, col ->
            rememberWheelPickerState(
                initialIndex = col.initialIndex,
                itemCount = col.items.size,
                isInfinite = col.isInfinite,
                isItemEnabled = col.isItemEnabled ?: { true },
            )
        }
    // 保证state和数据同步
    columns.indices.map { idx ->
        LaunchedEffect(itemsList[idx].size) {
            states[idx].updateData(itemsList[idx].size, false)
        }
    }
    // 联动逻辑：当上一级选中项变化时，刷新下一级及其后所有级数据
    columns.forEachIndexed { idx, col ->
        if (idx > 0 && col.onParentChanged != null) {
            val parentIndex = selectedIndices[idx - 1]
            LaunchedEffect(parentIndex, itemsList[idx - 1]) {
                val newItems = col.onParentChanged.invoke(parentIndex)
                if (itemsList[idx] != newItems) {
                    // 递归重置下游所有级别
                    for (i in idx until columns.size) {
                        val items = if (i == idx) newItems else columns[i].onParentChanged?.invoke(0) ?: columns[i].items
                        itemsList[i] = items
                        selectedIndices[i] = 0
                        states[i].animateScrollToItem(0)
                    }
                }
            }
        }
    }
    // 监听每一级的选中项变化，回调所有选中值
    columns.forEachIndexed { idx, _ ->
        val snappedIndex = states[idx].snappedIndex
        LaunchedEffect(snappedIndex, itemsList[idx]) {
            if (selectedIndices[idx] != snappedIndex) {
                selectedIndices[idx] = snappedIndex
            }
            onValueChange(selectedIndices.toList())
        }
    }
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(space),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        columns.forEachIndexed { idx, col ->
            WheelPicker(
                items = itemsList[idx],
                state = states[idx],
                // 修复：weight只能在RowScope下使用，需放在最前面
                modifier =
                    Modifier
                        .weight(col.weight)
                        .then(col.modifier),
                visibleItemsCount = visibleItemsCount,
                itemHeight = itemHeight,
                itemTextStyle = itemTextStyle,
                selectedTextColor = selectedTextColor,
                unselectedTextColor = unselectedTextColor,
                unEnabledTextColor = unEnabledTextColor,
                dividerConfig = dividerConfig,
                effect3DConfig = effect3DConfig,
                isInfinite = col.isInfinite,
                itemContent = { item, isSelected, isEnabled ->
                    col.itemContent(item, isSelected, isEnabled)
                },
            )
        }
    }
}

/**
 * 每一级的配置
 */
data class PickerColumn<T>(
    val items: List<T>,
    val initialIndex: Int = 0,
    val isInfinite: Boolean = false,
    val isItemEnabled: ((Int) -> Boolean)? = null,
    val modifier: Modifier = Modifier,
    val weight: Float = 1f,
    val convert: ((T) -> String)? = null,
    val itemContent: @Composable (item: T, isSelected: Boolean, isEnabled: Boolean) -> Unit = { item, isSelected, isEnabled ->
        Text(
            text = (convert?.invoke(item)) ?: item.toString(),
            color =
                when {
                    !isEnabled -> WheelPickerDefaults.defaultUnEnabledTextColor()
                    isSelected -> WheelPickerDefaults.defaultSelectedTextColor()
                    else -> WheelPickerDefaults.defaultUnselectedTextColor()
                },
            style = WheelPickerDefaults.defaultTextStyle(),
        )
    },
    /**
     * 父级选中项变化时，返回新数据
     * 参数为父级选中索引
     */
    val onParentChanged: ((parentIndex: Int) -> List<T>)? = null,
)

/**
 *  @time 2024/7/25
 *  <AUTHOR>
 *  @package com.qyqy.cupid.widgets.wheel.core
 */
interface WheelData {
    val uniqueId: String

    fun children(): List<WheelData>? = null
}
