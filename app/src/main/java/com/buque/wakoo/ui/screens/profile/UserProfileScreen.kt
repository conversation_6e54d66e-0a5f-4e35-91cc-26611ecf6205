package com.buque.wakoo.ui.screens.profile

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.bean.MediaInfo
import com.buque.wakoo.bean.UIConfig
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.CpRelationInfo
import com.buque.wakoo.bean.user.LocalSelfUserProvider
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.UserProfileInfo
import com.buque.wakoo.bean.user.isSelf
import com.buque.wakoo.ext.LaunchOnceEffect
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.network.api.bean.SoundBrand
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.DynamicPublishPanel
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.LocationHollow
import com.buque.wakoo.ui.icons.PencilEdit
import com.buque.wakoo.ui.icons.ProfileAudioIc
import com.buque.wakoo.ui.icons.ProfileAudioPause
import com.buque.wakoo.ui.icons.ProfileAudioPlay
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.messages.chat.TargetUserCPCard
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.widget.AudioWaveAnimation
import com.buque.wakoo.ui.widget.ExpLevelWidget
import com.buque.wakoo.ui.widget.GenderAgeTag
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.LoopHorizontalPager
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.UserLevelWidget
import com.buque.wakoo.ui.widget.VipCrownTag
import com.buque.wakoo.ui.widget.coordinatorlayout.CustomCollapsibleHeader
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleHeaderState
import com.buque.wakoo.ui.widget.coordinatorlayout.rememberCustomCollapsibleScrollBehavior
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import com.buque.wakoo.viewmodel.UserProfileViewModel
import kotlinx.coroutines.launch

private const val PAGE_TAG_PREFIX = "profile-tab-"

@Composable
fun UserProfileScreen(
    user: User,
    toEditUserInfo: () -> Unit = {},
    toSettings: () -> Unit = {},
    toUserRelations: (Int) -> Unit = {},
    toNavigation: (AppNavKey) -> Unit = {},
) {
    val headerState = rememberCustomCollapsibleHeaderState()

    val selfInfo = LocalSelfUserProvider.current
    val viewModel =
        if (user.id == selfInfo.id) {
            viewModel<UserProfileViewModel>(initializer = {
                UserProfileViewModel(UserProfileInfo.fromSelfUserInfo(selfInfo))
            })
        } else {
            viewModel<UserProfileViewModel>(initializer = {
                UserProfileViewModel(user)
            })
        }

    val userProfileInfo by viewModel.userProfileInfoFlow.collectAsStateWithLifecycle()
    val coroutineScope = rememberCoroutineScope()
    val rootNavController = LocalAppNavController.root
    val uiConfig by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()

    DisposableEffect(Unit) {
        onDispose {
            MediaPlayerManager.releaseIf {
                it.tag.startsWith("soundBrand-")
            }
        }
    }

    val scrollBehavior =
        rememberCustomCollapsibleScrollBehavior(
            state = headerState.topAppBarState,
            enableSnap = true,
        )

    val refreshEnable by remember(headerState) {
        derivedStateOf {
            headerState.collapsedFraction <= 0.0f
        }
    }

    val dialogController = rememberDialogController()
    val loadingManager = LocalLoadingManager.current

    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        Scaffold(
            modifier =
                Modifier
                    .fillMaxSize()
                    .nestedScroll(scrollBehavior.nestedScrollConnection),
            containerColor = Color.Transparent,
            topBar = {
                Box {
                    CustomCollapsibleHeader(
                        scrollBehavior = scrollBehavior,
                        state = headerState,
                        collapsedContent = {
                            Spacer(modifier = Modifier.height(TopAppBarDefaults.TopAppBarExpandedHeight.plus(14.dp)))
                        },
                        expandedContent = {
                            Column {
                                // 个人资料区域
                                UserProfileTopSection(
                                    user = userProfileInfo.user,
                                    modifier = Modifier.fillMaxWidth(),
                                    cpRelationInfo = if (userProfileInfo.isCN) userProfileInfo.cpRelationInfo else null,
                                    albums = userProfileInfo.albums,
                                    soundBrand = userProfileInfo.soundBrand,
                                    toNavigation = toNavigation,
                                    uiConfig = uiConfig,
                                    onClickEditButton = {
                                        dialogController.easyPost {
                                            SimpleDoubleActionDialog(
                                                "是否要删除或者修改声音签名".localized,
                                                cancelButtonConfig = DialogButtonStyles.Secondary.copy(text = "删除".localized),
                                                confirmButtonConfig = DialogButtonStyles.Primary.copy(text = "修改".localized),
                                                onCancel = {
                                                    dismiss()
                                                    loadingManager.show(coroutineScope) {
                                                        viewModel.removeVoiceIntro()
                                                    }
                                                },
                                                onConfirm = {
                                                    dismiss()
                                                    toNavigation(Route.UserProfileVoiceEdit)
                                                },
                                            )
                                        }
                                    },
                                )

                                SizeHeight(14.dp)

                                UserProfileBasicSection(
                                    userProfileInfo = userProfileInfo,
                                    modifier = Modifier.padding(horizontal = 16.dp),
                                    toUserRelations = toUserRelations,
                                )

                                if (!uiConfig.partnerHasEscaped && !userProfileInfo.isSelf && userProfileInfo.cpRelationInfo != null) {
                                    TargetUserCPCard(
                                        targetUser = userProfileInfo.user,
                                        cpInfo = userProfileInfo.cpRelationInfo!!,
                                        modifier =
                                            Modifier
                                                .fillMaxWidth()
                                                .padding(horizontal = 16.dp)
                                                .padding(top = 20.dp),
                                    )
                                }

                                HorizontalDivider(
                                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 24.dp),
                                    color = Color(0xffEFEFEF),
                                    thickness = 1.dp,
                                )
                            }
                        },
                    )

                    TopAppBar(
                        user = user,
                        toSettings = toSettings,
                        toEditUserInfo = toEditUserInfo,
                        showBack = true,
                        onReport = {
                            rootNavController.push(Route.Report(1, user.id))
                        },
                        onBlacked = {
                            coroutineScope.launch {
                                viewModel.updateBlackState(true)
                            }
                        },
                    )
                }
            },
            contentWindowInsets = WindowInsets(),
        ) { paddingValues ->
            BoxWithConstraints(
                modifier =
                    Modifier
                        .padding(paddingValues)
                        .fillMaxSize(),
            ) {
                val maxheight = maxHeight
                Column(
                    modifier =
                        Modifier
                            .height(maxheight)
                            .verticalScroll(rememberScrollState())
                            .nestedScroll(scrollBehavior.nestedScrollConnection),
                ) {
                    // 底部tab
                    UserProfilePageForProfile(
                        userProfileInfo = userProfileInfo,
                        tagPrefix = PAGE_TAG_PREFIX,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .weight(1f)
                                .padding(bottom = if (user.isSelf) 0.dp else 56.dp),
                        refreshEnable = refreshEnable,
                        onPublishDynamic = {
                            dialogController.easyPost {
                                DynamicPublishPanel(
                                    onVoiceContent = {
                                        dismiss()
                                        toNavigation(Route.VoicePublish)
                                    },
                                    onMomentContent = {
                                        dismiss()
                                        toNavigation(Route.MomentPublish)
                                    },
                                )
                            }
                        },
                        onNavigateTo = toNavigation,
                    )
                }
            }
        }

        if (!user.isSelf) {
            Row(
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .padding(
                            start = 16.dp,
                            bottom =
                                WindowInsets.navigationBars
                                    .asPaddingValues()
                                    .calculateBottomPadding()
                                    .plus(10.dp),
                            end = 16.dp,
                        ),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                AnimatedVisibility(
                    visible = !userProfileInfo.withRelationInfo.isEmpty,
                    modifier = Modifier.weight(1f),
                ) {
                    if (userProfileInfo.withRelationInfo.isFollowed) {
                        SolidButton(
                            text = "已关注".localized,
                            onClick = {
                                viewModel.toggleFollowState()
                            },
                            modifier = Modifier.fillMaxWidth(),
                            backgroundColor = Color(0xFF111111),
                            textColor = Color(0xFF66FE6B),
                        )
                    } else {
                        OutlinedButton(
                            text = "关注".localized,
                            onClick = {
                                viewModel.toggleFollowState()
                            },
                            modifier = Modifier.fillMaxWidth(),
                            backgroundColor = Color.White,
                        )
                    }
                }

                GradientButton(
                    text = "私信".localized,
                    onClick = {
                        rootNavController.push(Route.Chat(userProfileInfo.toBasic()))
                    },
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }

    dialogController.RenderDialogs(Unit)
}

@Composable
private fun PublicCPBox(
    cpRelationInfo: CpRelationInfo?,
    modifier: Modifier = Modifier,
) {
    val cpInfo = cpRelationInfo ?: return
    val publicCP = cpInfo.publicCp ?: return
    Box(modifier = modifier.size(64.dp, 74.dp)) {
        AvatarNetworkImage(
            publicCP,
            modifier =
                Modifier
                    .size(64.dp)
                    .border(1.dp, Color.White, CircleShape),
        )
        if (cpInfo.cpExtraInfo.levelInfo.wakooNormalImgUrl
                .isNotBlank()
        ) {
            NetworkImage(
                cpInfo.cpExtraInfo.levelInfo.wakooNormalImgUrl,
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .height(20.dp)
                        .align(Alignment.BottomCenter),
                contentScale = ContentScale.Inside,
            )
        }
    }
}

/**
 * 用户个人资料区域
 */
@Composable
private fun UserProfileTopSection(
    user: User,
    modifier: Modifier = Modifier,
    cpRelationInfo: CpRelationInfo? = null,
    albums: List<MediaInfo>? = null,
    soundBrand: SoundBrand? = null,
    toNavigation: (AppNavKey) -> Unit = {},
    onClickEditButton: () -> Unit = {},
    uiConfig: UIConfig,
) {
    val canEditVoice = user.id.isSelf && user.type == 1
    val scope = rememberCoroutineScope()
    Box(
        modifier =
            Modifier
                .fillMaxWidth()
                .aspectRatio(375f / 358),
    ) {
        val pageState = rememberPagerState { (albums?.size ?: 1).coerceAtLeast(1) }
        LoopHorizontalPager(
            state = pageState,
            interval = 2500,
            modifier = Modifier.fillMaxSize(),
        ) {
            if (albums.isNullOrEmpty()) {
                NetworkImage(
                    user.avatar,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                )
            } else {
                NetworkImage(
                    data = albums[it].mediaUrl,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                )
            }
        }

        if (!uiConfig.partnerHasEscaped && cpRelationInfo != null) {
            PublicCPBox(
                cpRelationInfo,
                modifier =
                    Modifier
                        .align(Alignment.BottomEnd)
                        .padding(bottom = 100.dp, end = 16.dp),
            )
        }

        Box(
            modifier =
                modifier
                    .align(Alignment.BottomCenter)
                    .height(90.dp),
        ) {
            Spacer(
                modifier =
                    Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .height(40.dp)
                        .background(
                            color = Color.White,
                            shape =
                                RoundedCornerShape(
                                    topStart = 24.dp,
                                    topEnd = 24.dp,
                                ),
                        ),
            )
            AvatarNetworkImage(
                user,
                enabled = false,
                modifier =
                    Modifier
                        .align(Alignment.BottomStart)
                        .padding(start = 16.dp)
                        .border(1.dp, color = Color.White, CircleShape),
                size = 72.dp,
            )

            if (canEditVoice || !soundBrand?.sourceUrl.isNullOrBlank()) {
                val playItem =
                    remember(soundBrand?.sourceUrl) {
                        if (!soundBrand?.sourceUrl.isNullOrBlank()) {
                            PlayMediaItem.prefixTagAudio(soundBrand.sourceUrl, "soundBrand-${user.id}")
                        } else {
                            null
                        }
                    }

                Row(
                    modifier =
                        Modifier
                            .align(Alignment.BottomStart)
                            .padding(start = 65.dp)
                            .background(color = Color(0xffDFFFE0), shape = CircleShape)
                            .noEffectClick {
                                if (canEditVoice && playItem == null) {
                                    toNavigation(Route.UserProfileVoiceEdit)
                                } else if (playItem != null) {
                                    MediaPlayerManager.toggle(playItem)
                                }
                            },
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    if (playItem == null) {
                        Image(WakooIcons.ProfileAudioIc, contentDescription = null, modifier = Modifier.size(32.dp))
                    } else {
                        Image(
                            if (playItem.isActuallyPlaying) {
                                WakooIcons.ProfileAudioPause
                            } else {
                                WakooIcons.ProfileAudioPlay
                            },
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                        )
                    }

                    if (playItem != null) {
                        AudioWaveAnimation(
                            isPlaying = playItem.isActuallyPlaying,
                            modifier = Modifier.size(64.dp, 12.dp),
                            barWidth = 2.dp,
                            barSpacing = 1.5.dp,
                            barColor = Color(0xFF111111), // 类似 Spotify 的绿色
                            minHeightFraction = 0.2f,
                            maxHeightFraction = 0.8f,
                            radius = 3.dp,
                        )

                        if (!user.id.isSelf) {
                            LaunchOnceEffect {
                                MediaPlayerManager.toggle(playItem)
                            }
                        }
                    }

                    SizeWidth(4.dp)

                    Text(
                        buildAnnotatedString {
                            if (!soundBrand?.sourceUrl.isNullOrBlank()) {
                                append("${soundBrand.duration}s")
                            } else if (canEditVoice) {
                                append("添加语音签名".localized)
                            }
                        },
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.W600,
                    )

                    if (canEditVoice) {
                        SizeWidth(2.dp)
                        Image(
                            WakooIcons.PencilEdit,
                            contentDescription = null,
                            modifier =
                                Modifier.click(noEffect = true, onClick = {
                                    if (playItem == null) {
                                        toNavigation(Route.UserProfileVoiceEdit)
                                    } else {
                                        onClickEditButton()
                                    }
                                }),
                        )
                    }
                    SizeWidth(8.dp)
                }
            }

            if (!albums.isNullOrEmpty()) {
                val state = rememberLazyListState()
                LaunchedEffect(pageState.currentPage) {
                    state.scrollToItem(pageState.currentPage)
                }
                LazyRow(
                    modifier =
                        Modifier
                            .align(alignment = Alignment.TopEnd)
                            .fillMaxWidth(0.4f),
                    state = state,
                    horizontalArrangement = Arrangement.End,
                ) {
                    itemsIndexed(albums) { index, it ->
                        NetworkImage(
                            data = it.mediaUrl,
                            modifier =
                                Modifier
                                    .padding(end = 6.dp)
                                    .size(38.dp)
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(Color(0xff999999))
                                    .click {
                                        scope.launch {
                                            pageState.scrollToPage(index)
                                        }
                                    },
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun UserProfileBasicSection(
    userProfileInfo: UserProfileInfo,
    modifier: Modifier = Modifier,
    toUserRelations: (Int) -> Unit = {},
) {
    val user = userProfileInfo.user
    Column(modifier = modifier.fillMaxWidth()) {
        FlowRow(
            horizontalArrangement = Arrangement.spacedBy(2.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp),
        ) {
            Text(
                text = user.name,
                style = MaterialTheme.typography.titleSmall,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            // 性别年龄标签
            GenderAgeTag(user = user)

            // VIP标签
            if (user.isVip) {
                VipCrownTag()
            }
            if (userProfileInfo.isJP) {
                ExpLevelWidget(userProfileInfo)
            } else {
                UserLevelWidget(userProfileInfo.level)
            }
        }

        SizeHeight(12.dp)
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text("ID:${user.publishId}", color = Color(0xff999999), fontSize = 12.sp, lineHeight = 12.sp)
            SizeWidth(18.dp)
            if (userProfileInfo.regionLabel.isNotBlank()) {
                Image(WakooIcons.LocationHollow, contentDescription = null)
                Text(userProfileInfo.regionLabel, color = Color(0xff999999), fontSize = 12.sp, lineHeight = 12.sp)
            }
        }
        SizeHeight(16.dp)
        Row {
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            fontFamily = FontFamily.MI_SANS,
                            fontWeight = FontWeight.W900,
                            color = Color(0xff111111),
                            fontSize = 16.sp,
                        ),
                    ) {
                        append(userProfileInfo.followersCount.toString())
                    }
                    append("粉丝".localized)
                },
                textAlign = TextAlign.Center,
                fontSize = 11.sp,
                color = Color(0xff999999),
                fontWeight = FontWeight.W400,
                modifier =
                    Modifier.noEffectClick {
                        toUserRelations(0)
                    },
            )
            SizeWidth(16.dp)
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            fontFamily = FontFamily.MI_SANS,
                            fontWeight = FontWeight.W900,
                            color = Color(0xff111111),
                            fontSize = 16.sp,
                        ),
                    ) {
                        append(userProfileInfo.followingCount.toString())
                    }
                    append("关注".localized)
                },
                textAlign = TextAlign.Center,
                fontSize = 11.sp,
                color = Color(0xff999999),
                fontWeight = FontWeight.W400,
                modifier =
                    Modifier.noEffectClick {
                        toUserRelations(1)
                    },
            )
            SizeWidth(16.dp)
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            fontFamily = FontFamily.MI_SANS,
                            fontWeight = FontWeight.W900,
                            color = Color(0xff111111),
                            fontSize = 16.sp,
                        ),
                    ) {
                        append(userProfileInfo.friendCount.toString())
                    }
                    append("好友".localized)
                },
                textAlign = TextAlign.Center,
                fontSize = 11.sp,
                color = Color(0xff999999),
                fontWeight = FontWeight.W400,
                modifier =
                    Modifier.noEffectClick {
                        toUserRelations(2)
                    },
            )
        }
    }
}

@Composable
@Preview
private fun PreviewUserProfileScreen() {
    WakooTheme { UserProfileScreen(BasicUser.sampleGirl) }
}
