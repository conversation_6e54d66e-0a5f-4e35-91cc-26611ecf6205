package com.buque.wakoo.ui.widget.drag

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.spring
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.round
import androidx.compose.ui.unit.toIntSize
import com.buque.wakoo.ui.widget.drag.DraggableFloatingState.Companion.DraggableFloatingStateSaver
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Stable
class DraggableFloatingState constructor(
    // 初始定位参数
    private val initialAlignment: Alignment,
    private val initialOffset: Offset,
    // 行为开关
    initialIsVisible: Boolean,
    initialIsDraggable: Boolean,
    initialIsStickyToEdge: Boolean,
    val allowDragOutOfBounds: Boolean,
    // 其他参数
    private val stickyEdgeMargin: Float,
    internal var scope: CoroutineScope,
    restoredCurrentOffset: Offset? = null,
) {
    companion object {
        // DraggableFloatingState 的 Saver
        val DraggableFloatingStateSaver: Saver<DraggableFloatingState, List<Any?>> =
            Saver(
                save = { state ->
                    listOf(
                        // 构造函数参数
                        state.initialAlignment.toPair(), // Alignment 没有默认 Saver，我们自定义转换
                        state.initialOffset.x, // 分开保存 x, y
                        state.initialOffset.y,
                        state.isVisible,
                        state.isDraggable,
                        state.isStickyToEdge,
                        state.allowDragOutOfBounds,
                        state.stickyEdgeMargin,
                        // 运行时状态
                        state.offset.value.x,
                        state.offset.value.y,
                        state.isInitialized,
                    )
                },
                restore = { list ->
                    val restoredCurrentOffset = Offset(list[8] as Float, list[9] as Float)

                    DraggableFloatingState(
                        initialAlignment = alignmentFromPair(list[0] as Pair<Float, Float>),
                        initialOffset = Offset(list[1] as Float, list[2] as Float),
                        initialIsVisible = list[3] as Boolean,
                        initialIsDraggable = list[4] as Boolean,
                        initialIsStickyToEdge = list[5] as Boolean,
                        allowDragOutOfBounds = list[6] as Boolean,
                        stickyEdgeMargin = list[7] as Float,
                        scope = CoroutineScope(Dispatchers.Main.immediate),
                        restoredCurrentOffset = restoredCurrentOffset,
                    ).apply {
                        // 恢复运行时状态
                        this.isInitialized = list[10] as Boolean
                    }
                },
            )
    }

    // --- 核心状态 ---
    private val offset = Animatable(restoredCurrentOffset ?: Offset.Zero, Offset.VectorConverter)

    var isVisible by mutableStateOf(initialIsVisible)
    var isDraggable by mutableStateOf(initialIsDraggable)

    // --- isStickyToEdge 的正确实现 ---
    private var _isStickyToEdge by mutableStateOf(initialIsStickyToEdge)
    var isStickyToEdge: Boolean
        get() = _isStickyToEdge
        set(value) {
            val oldValue = _isStickyToEdge
            if (oldValue != value) {
                _isStickyToEdge = value
                if (!oldValue) { // 从关到开，自动执行贴边动画
                    animateToFinalOffset()
                }
            }
        }

    // --- 尺寸状态 ---
    var parentBounds by mutableStateOf(Rect.Zero)
        private set
    var componentSize by mutableStateOf(IntSize.Zero)
        private set

    // --- 内部状态 ---
    var isInitialized by mutableStateOf(false)
        private set

    var managerState: FloatingLayoutManagerState? = null

    val isStuckToLeft: Boolean
        get() {
            if (parentBounds.width == 0f) return true // 默认靠左
            return offset.value.x + componentSize.width / 2f <= parentBounds.width / 2f
        }

    var isBeingDragged by mutableStateOf(false)
        private set

    fun getCurrentOffset() = offset.value.round()

    private var job: Job? = null

    fun setBounds(parent: Rect) {
        val needsInitialization = !isInitialized && !parentBounds.isEmpty && componentSize.width > 0
        val parentSizeChanged = this.parentBounds != parent

        this.parentBounds = parent

        // 只有在尺寸变化时才更新边界，避免不必要的协程启动
        if (parentSizeChanged) {
            updateBounds()
        }

        if (needsInitialization) {
            initializePosition()
            isInitialized = true
        } else if (isInitialized && parentSizeChanged) {
            // 父或子组件尺寸变化，需要重新约束当前位置
            job?.cancel()
            job =
                scope.launch {
                    offset.snapTo(getCoercedOffset(offset.value))
                    delay(150)
                    managerState?.onVisibilityChanged(this@DraggableFloatingState)
                }
        }
    }

    // --- 尺寸更新与初始化 ---
    fun setSizes(component: IntSize) {
        val needsInitialization = !isInitialized && !parentBounds.isEmpty && component.width > 0
        val componentSizeChanged = this.componentSize != component

        this.componentSize = component

        // 只有在尺寸变化时才更新边界，避免不必要的协程启动
        if (componentSizeChanged) {
            updateBounds()
        }

        if (needsInitialization) {
            initializePosition()
            isInitialized = true
        } else if (isInitialized && componentSizeChanged) {
            // 父或子组件尺寸变化，需要重新约束当前位置
            job?.cancel()
            job =
                scope.launch {
                    offset.snapTo(getCoercedOffset(offset.value))
                    delay(150)
                    managerState?.onVisibilityChanged(this@DraggableFloatingState)
                }
        }
    }

    private fun updateBounds() {
        scope.launch {
            if (allowDragOutOfBounds) {
                offset.updateBounds(null, null)
            } else {
                if (!parentBounds.isEmpty && componentSize.width > 0 && componentSize.height > 0) {
                    val lowerBound = Offset.Zero
                    val upperBound =
                        Offset(
                            (parentBounds.width - componentSize.width),
                            (parentBounds.height - componentSize.height),
                        )
                    if (upperBound.x >= 0 && upperBound.y >= 0) {
                        offset.updateBounds(lowerBound, upperBound)
                    }
                }
            }
        }
    }

    private fun initializePosition() {
        scope.launch {
            offset.snapTo(getFinalTargetOffset(isInitialPhase = true))
            managerState?.onVisibilityChanged(this@DraggableFloatingState)
        }
    }

    // --- 拖动逻辑 ---
    fun drag(dragAmount: Offset) {
        if (!isDraggable) return
        isBeingDragged = true // 拖动开始
        scope.launch {
            // 直接 snapTo，让 Animatable 根据已设置的 bounds 自动处理越界
            offset.snapTo(offset.value + dragAmount)
        }
    }

    // 我们需要一个 dragStart 方法
    fun dragStart() {
        if (!isDraggable) return
        isBeingDragged = true
    }

    // 拖动结束时调用
    fun dragEnd() {
        isBeingDragged = false
    }

    fun stickToEdge() {
        if (isStickyToEdge) {
            scope.launch {
                offset.animateTo(getFinalTargetOffset(isInitialPhase = false))
            }
        }
    }

    internal suspend fun animateToOffset(targetOffset: Offset) {
        val finalCoercedOffset = getCoercedOffset(targetOffset - parentBounds.topLeft)
        offset.animateTo(finalCoercedOffset, spring())
    }

    internal fun getWindowFinalTargetOffset(): Offset = getFinalTargetOffset(false) + parentBounds.topLeft

    internal fun getWindowCoercedOffset(offset: Offset): Offset = getCoercedOffset(offset - parentBounds.topLeft) + parentBounds.topLeft

    // --- 动画与目标位置计算 ---
    private fun getFinalTargetOffset(isInitialPhase: Boolean = false): Offset {
        if (parentBounds.isEmpty || componentSize == IntSize.Zero) return Offset.Zero

        val baseOffset =
            if (isInitialPhase) {
                val alignmentPosition = initialAlignment.align(componentSize, parentBounds.size.toIntSize(), LayoutDirection.Ltr)
                Offset(alignmentPosition.x.toFloat(), alignmentPosition.y.toFloat()) + initialOffset
            } else {
                offset.value
            }

        // 1. 确定一个必须在界内的“安全”基准位置
        val safeBaseOffset = getCoercedOffset(baseOffset)

        // 2. 在“安全”基准上，应用贴边逻辑
        if (isStickyToEdge) {
            val isTargetingLeft =
                if (isInitialPhase) {
                    initialAlignment
                        .align(
                            IntSize.Zero,
                            IntSize(parentBounds.width.toInt(), 0),
                            LayoutDirection.Ltr,
                        ).x < parentBounds.width / 2
                } else {
                    safeBaseOffset.x + componentSize.width / 2f <= parentBounds.width / 2f
                }

            val targetX =
                if (isTargetingLeft) {
                    stickyEdgeMargin
                } else {
                    (parentBounds.width - componentSize.width) - stickyEdgeMargin
                }

            return getCoercedOffset(Offset(targetX, safeBaseOffset.y))
        }

        return safeBaseOffset
    }

    private fun getCoercedOffset(offset: Offset): Offset {
        if (parentBounds.width == 0f || componentSize.width == 0) return offset
        return if (isStickyToEdge) {
            Offset(
                x = offset.x.coerceIn(0f + stickyEdgeMargin, (parentBounds.width - componentSize.width) - stickyEdgeMargin),
                y = offset.y.coerceIn(0f, (parentBounds.height - componentSize.height)),
            )
        } else {
            Offset(
                x = offset.x.coerceIn(0f, (parentBounds.width - componentSize.width)),
                y = offset.y.coerceIn(0f, (parentBounds.height - componentSize.height)),
            )
        }
    }

    private fun animateToFinalOffset() {
        isBeingDragged = false // 确保动画开始时也已结束拖动状态
        managerState?.onVisibilityChanged(this@DraggableFloatingState)
    }
}

@Composable
fun rememberDraggableFloatingState(
    initialAlignment: Alignment = Alignment.Center,
    initialOffsetDp: DpOffset = DpOffset.Zero,
    initialIsVisible: Boolean = true,
    initialIsDraggable: Boolean = true,
    initialIsStickyToEdge: Boolean = false,
    allowDragOutOfBounds: Boolean = false,
    stickyEdgeMarginDp: Dp = 10.dp,
): DraggableFloatingState {
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    val stickyEdgeMargin = with(density) { stickyEdgeMarginDp.toPx() }
    val initialOffset = with(density) { Offset(initialOffsetDp.x.toPx(), initialOffsetDp.y.toPx()) }
    // 使用 keys 确保在配置不同时创建新实例，这是最健壮的做法
    val state =
        rememberSaveable(
            saver = DraggableFloatingStateSaver,
        ) {
            DraggableFloatingState(
                scope = scope,
                initialAlignment = initialAlignment,
                initialOffset = initialOffset,
                initialIsVisible = initialIsVisible,
                initialIsDraggable = initialIsDraggable,
                initialIsStickyToEdge = initialIsStickyToEdge,
                allowDragOutOfBounds = allowDragOutOfBounds,
                stickyEdgeMargin = stickyEdgeMargin,
            )
        }

    LaunchedEffect(scope, state) {
        state.scope = scope
    }
    return state
}

// Alignment 的自定义转换辅助函数
private fun Alignment.toPair(): Pair<Float, Float> =
    when (this) {
        Alignment.TopStart -> -1.0f to -1.0f
        Alignment.TopCenter -> 0.0f to -1.0f
        Alignment.TopEnd -> 1.0f to -1.0f
        Alignment.CenterStart -> -1.0f to 0.0f
        Alignment.Center -> 0.0f to 0.0f
        Alignment.CenterEnd -> 1.0f to 0.0f
        Alignment.BottomStart -> -1.0f to 1.0f
        Alignment.BottomCenter -> 0.0f to 1.0f
        Alignment.BottomEnd -> 1.0f to 1.0f
        else -> 0.0f to 0.0f // Fallback for other Alignments
    }

private fun alignmentFromPair(pair: Pair<Float, Float>): Alignment =
    when (pair) {
        -1.0f to -1.0f -> Alignment.TopStart
        0.0f to -1.0f -> Alignment.TopCenter
        1.0f to -1.0f -> Alignment.TopEnd
        -1.0f to 0.0f -> Alignment.CenterStart
        0.0f to 0.0f -> Alignment.Center
        1.0f to 0.0f -> Alignment.CenterEnd
        -1.0f to 1.0f -> Alignment.BottomStart
        0.0f to 1.0f -> Alignment.BottomCenter
        1.0f to 1.0f -> Alignment.BottomEnd
        else -> Alignment.Center
    }
