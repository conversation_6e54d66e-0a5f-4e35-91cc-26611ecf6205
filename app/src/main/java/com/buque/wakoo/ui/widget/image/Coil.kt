package com.buque.wakoo.ui.widget.image

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.isSpecified
import coil3.compose.AsyncImage
import coil3.compose.SubcomposeAsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.RootNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.icons.ImageError
import com.buque.wakoo.ui.icons.ImagePlaceholder
import com.buque.wakoo.ui.icons.WakooIcons

/**
 * 为Compose加载网络图片
 */
@Composable
fun NetworkImage(
    data: Any?,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop,
    placeholder: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImagePlaceholder,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    error: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImageError,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    preview: Painter? = placeholder,
    crossfade: Boolean = true,
    memoryCacheKey: String? = null,
    colorFilter: ColorFilter? = null,
) {
    if (LocalInspectionMode.current) {
        Image(
            painter =
                when (data) {
                    is Int -> painterResource(data)
                    is Painter -> data
                    else -> preview ?: painterResource(id = R.drawable.ic_app_logo)
                },
            contentDescription = null,
            modifier = modifier,
            contentScale = contentScale,
        )
        return
    }
    val context = LocalContext.current

    AsyncImage(
        model =
            ImageRequest
                .Builder(context)
                .data(data)
                .memoryCacheKey(memoryCacheKey)
                .crossfade(crossfade)
                .build(),
        contentDescription = null,
        contentScale = contentScale,
        modifier = modifier,
        placeholder = placeholder,
        error = error,
        colorFilter = colorFilter,
    )
}

@Composable
fun SquareNetworkImage(
    data: Any?,
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    shape: Shape = RectangleShape,
    contentScale: ContentScale = ContentScale.Crop,
    placeholder: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImagePlaceholder,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    error: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImageError,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    preview: Painter? = placeholder,
    crossfade: Boolean = true,
) {
    NetworkImage(
        data = data,
        modifier =
            Modifier
                .size(size)
                .clip(shape)
                .then(modifier),
        contentScale = contentScale,
        placeholder = placeholder,
        error = error,
        preview = preview ?: painterResource(id = R.drawable.ic_app_logo),
        crossfade = crossfade,
    )
}

/**
 * 为Compose加载圆形头像图片
 */
@Composable
fun AvatarNetworkImage(
    user: User,
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    shape: Shape = CircleShape,
    border: BorderStroke? = null,
    contentScale: ContentScale = ContentScale.Crop,
    placeholder: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImagePlaceholder,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    error: Painter? =
        rememberConstrainedCenteredVectorPainter(
            imageVector = WakooIcons.ImageError,
            backgroundColor = Color(0xFFE9EAEF),
        ),
    preview: Painter? = placeholder,
    crossfade: Boolean = true,
    enabled: Boolean = true,
    onClick: (RootNavController) -> Unit = {
        it.push(Route.UserProfile(user.toBasic()))
    },
) {
    if (LocalInspectionMode.current) {
        Image(
            painter = preview ?: painterResource(id = R.drawable.ic_app_logo),
            contentDescription = null,
            modifier =
                modifier
                    .size(size)
                    .run {
                        if (border != null) {
                            border(border, shape)
                        } else {
                            this
                        }
                    }.clip(shape),
            contentScale = contentScale,
        )
        return
    }
    val rootNavController = LocalAppNavController.root
    NetworkImage(
        data = user.avatar,
        modifier =
            modifier
                .run {
                    if (border != null) {
                        border(border, shape)
                    } else {
                        this
                    }.then(
                        if (size.isSpecified) {
                            size(size)
                        } else {
                            this
                        },
                    )
                }.clip(shape)
                .run {
                    if (enabled) {
                        clickable {
                            onClick(rootNavController)
                        }
                    } else {
                        this
                    }
                },
        contentScale = contentScale,
        placeholder = placeholder,
        error = error,
        preview = preview ?: painterResource(id = R.drawable.ic_app_logo),
        crossfade = crossfade,
    )
}

/**
 * 创建带加载状态的图片
 */
@Composable
fun LoadingNetworkImage(
    url: Any?,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Crop,
    loading: @Composable () -> Unit,
    error: @Composable () -> Unit,
) {
    val context = LocalContext.current

    SubcomposeAsyncImage(
        model =
            ImageRequest
                .Builder(context)
                .data(url)
                .crossfade(true)
                .build(),
        contentDescription = null,
        contentScale = contentScale,
        modifier = modifier,
        loading = {
            loading()
        },
        error = {
            error()
        },
    )
}
