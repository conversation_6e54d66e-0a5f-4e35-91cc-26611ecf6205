package com.buque.wakoo.ui.screens.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.emptyContent
import com.buque.wakoo.bean.InputEdit
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.bean.user.SocialInfo
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.UserProfileInfo
import com.buque.wakoo.bean.user.isSelf
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.conditional
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.AudioPlayerManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.navigation.UserProfileTab
import com.buque.wakoo.navigation.VoiceListTab
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.navigation.dialog.rememberDialogController
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.ui.dialog.DialogButtonStyles
import com.buque.wakoo.ui.dialog.LongTextDialog
import com.buque.wakoo.ui.dialog.SimpleDoubleActionDialog
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.AddRound
import com.buque.wakoo.ui.icons.ArrowRight
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.PencilEdit
import com.buque.wakoo.ui.icons.Report
import com.buque.wakoo.ui.icons.Settings
import com.buque.wakoo.ui.icons.UserForbidLine
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.screens.messages.chat.BuddyZone
import com.buque.wakoo.ui.screens.voice.VoiceLazyListPage
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooSecondaryUnSelected
import com.buque.wakoo.ui.widget.AdaptiveScrollableTabRow
import com.buque.wakoo.ui.widget.NoIndicationInteractionSource
import com.buque.wakoo.ui.widget.OutlinedButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.SpaceListItemScaffold
import com.buque.wakoo.ui.widget.WakooTitleBar
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.adaptiveTabIndicatorOffset
import com.buque.wakoo.ui.widget.gift.UserGiftWallTab
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.image.SquareNetworkImage
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.popup.BubbleShape
import com.buque.wakoo.ui.widget.state.CStateLayout
import com.buque.wakoo.utils.eventBus.toLink
import com.buque.wakoo.viewmodel.BuddyZoneViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

@Composable
fun UserProfilePageForMine(
    selfUserInfo: SelfUserInfo,
    tagPrefix: String,
    modifier: Modifier = Modifier,
    refreshEnable: Boolean = true,
    onPublishDynamic: () -> Unit = {},
    onNavigateTo: (AppNavKey) -> Unit = {},
) {
    val user = selfUserInfo.user
    val config by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()
    val tabs =
        remember(user.id, selfUserInfo.showCpZone, selfUserInfo.showCpZone && config.partnerHasEscaped) {
            buildList {
                if (selfUserInfo.showCpZone && !config.partnerHasEscaped) {
                    add(UserProfileTab.CPTab)
                }
                add(UserProfileTab.ProfileTab)
                add(UserProfileTab.VoiceTab)
                if (config.showGiftWall) {
                    add(UserProfileTab.GiftWallTab)
                }
            }
        }
    Column(
        modifier = modifier,
    ) {
        // Tab选项卡
        val scope = rememberCoroutineScope()
        val pagerState = rememberPagerState(pageCount = { tabs.size }, initialPage = 0)
        val selectedTabIndex = pagerState.currentPage

        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxWidth(),
        ) {
            AdaptiveScrollableTabRow(
                selectedTabIndex = selectedTabIndex,
                edgePadding = 8.dp,
                tabSpacing = 20.dp,
                indicator = { tabPositions ->
                    if (selectedTabIndex < tabPositions.size) {
                        Box(
                            modifier =
                                Modifier
                                    .adaptiveTabIndicatorOffset(tabPositions[selectedTabIndex])
                                    .requiredWidth(12.dp)
                                    .height(3.dp)
                                    .background(WakooSecondarySelected, CircleShape),
                        )
                    }
                },
            ) {
                tabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = selectedTabIndex == index,
                        selectedContentColor = WakooSecondarySelected,
                        unselectedContentColor = WakooSecondaryUnSelected,
                        onClick = { scope.launch { pagerState.animateScrollToPage(index) } },
                        interactionSource = remember { NoIndicationInteractionSource() },
                        content = {
                            Box(
                                modifier = Modifier.padding(bottom = 4.dp),
                            ) {
                                tab.TabContent(selectedTabIndex == index)
                            }
                        },
                    )
                }
            }
        }

        val lifecycleOwner = LocalLifecycleOwner.current

        var giftWallRefreshFlag by rememberSaveable {
            mutableIntStateOf(1)
        }

        DisposableEffect(Unit) {
            val observer =
                LifecycleEventObserver { source, event ->
                    if (event == Lifecycle.Event.ON_STOP) {
                        AudioPlayerManager.releaseIf {
                            it.toString().startsWith(tagPrefix)
                        }
                    }
                }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                // Composable 离开组合时执行清理
                AudioPlayerManager.releaseIf {
                    it.toString().startsWith(tagPrefix)
                }
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
        ) { page ->
            val tab = tabs[page]
            when (tab) {
                UserProfileTab.CPTab -> {
                    ProfileCPTab(selfUserInfo.user)
                }

                UserProfileTab.ProfileTab -> {
                    val userProfileInfo =
                        remember(selfUserInfo) {
                            UserProfileInfo.fromSelfUserInfo(selfUserInfo)
                        }
                    UserInformationTab(
                        userProfileInfo = userProfileInfo,
                        onNavigateTo = onNavigateTo,
                        onPublishDynamic = onPublishDynamic,
                    )
                }

                UserProfileTab.VoiceTab -> {
                    UserVoiceCardTab(user, tagPrefix, refreshEnable = refreshEnable)
                }

                UserProfileTab.GiftWallTab -> {
                    Column(
                        modifier =
                            Modifier
                                .padding(16.dp)
                                .fillMaxSize(),
                    ) {
                        UserGiftWallTab(user.id, refreshFlag = giftWallRefreshFlag)
                    }
                }
            }
        }
    }
}

/**
 * 为个人资料中心准备的
 */
@Composable
fun UserProfilePageForProfile(
    userProfileInfo: UserProfileInfo,
    tagPrefix: String,
    modifier: Modifier = Modifier,
    refreshEnable: Boolean = true,
    onPublishDynamic: () -> Unit = {},
    onNavigateTo: (AppNavKey) -> Unit = {},
) {
    val user = userProfileInfo.user
    val config by AppConfigManager.uiConfigFlow.collectAsStateWithLifecycle()
    val tabs =
        remember(user.id, config.showGiftWall, userProfileInfo.showCpZone && config.partnerHasEscaped) {
            buildList {
                if (userProfileInfo.showCpZone && !config.partnerHasEscaped) {
                    add(UserProfileTab.CPTab)
                }
                add(UserProfileTab.ProfileTab)
                add(UserProfileTab.VoiceTab)
                if (config.showGiftWall) {
                    add(UserProfileTab.GiftWallTab)
                }
            }
        }
    Column(
        modifier = modifier,
    ) {
        // Tab选项卡
        val scope = rememberCoroutineScope()
        val pagerState = rememberPagerState(pageCount = { tabs.size }, initialPage = 0)
        val selectedTabIndex = pagerState.currentPage

        ScrollableTabRow(
            selectedTabIndex = selectedTabIndex,
            modifier = Modifier.fillMaxWidth(),
            divider = {},
            containerColor = Color.Transparent,
            edgePadding = 8.dp,
            indicator = { tabPositions ->
                if (selectedTabIndex < tabPositions.size) {
                    Box(
                        modifier =
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                .requiredWidth(12.dp)
                                .height(3.dp)
                                .background(WakooSecondarySelected, CircleShape),
                    )
                }
            },
        ) {
            tabs.forEachIndexed { index, tab ->
                Tab(
                    selected = selectedTabIndex == index,
                    selectedContentColor = WakooSecondarySelected,
                    unselectedContentColor = WakooSecondaryUnSelected,
                    onClick = { scope.launch { pagerState.animateScrollToPage(index) } },
                    content = {
                        Box(
                            modifier = Modifier.padding(bottom = 4.dp),
                        ) {
                            tab.TabContent(
                                selectedTabIndex == index,
                                modifier = Modifier.padding(horizontal = 8.dp),
                            )
                        }
                    },
                )
            }
        }

        val lifecycleOwner = LocalLifecycleOwner.current

        var giftWallRefreshFlag by rememberSaveable {
            mutableIntStateOf(1)
        }

        DisposableEffect(Unit) {
            val observer =
                LifecycleEventObserver { source, event ->
                    if (event == Lifecycle.Event.ON_STOP) {
                        AudioPlayerManager.releaseIf {
                            it.toString().startsWith(tagPrefix)
                        }
                    }
                }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                // Composable 离开组合时执行清理
                AudioPlayerManager.releaseIf {
                    it.toString().startsWith(tagPrefix)
                }
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
        ) { page ->
            val tab = tabs[page]
            when (tab) {
                UserProfileTab.CPTab -> {
                    ProfileCPTab(user)
                }

                UserProfileTab.ProfileTab -> {
                    UserInformationTab(
                        userProfileInfo = userProfileInfo,
                        onNavigateTo = onNavigateTo,
                        onPublishDynamic = onPublishDynamic,
                        moduleBgColor = Color(0xfff8f8f8),
                    )
                }

                UserProfileTab.VoiceTab -> {
                    UserVoiceCardTab(user, tagPrefix, refreshEnable = refreshEnable)
                }

                UserProfileTab.GiftWallTab -> {
                    Column(
                        modifier =
                            Modifier
                                .padding(16.dp)
                                .fillMaxSize(),
                    ) {
                        UserGiftWallTab(user.id, refreshFlag = giftWallRefreshFlag)
                    }
                }
            }
        }
    }
}

@Composable
fun ProfileCPTab(
    targetUser: User,
    modifier: Modifier = Modifier,
) {
    val lm = LocalLoadingManager.current
    val dc = rememberDialogController()
    var isProcessing by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()
    val viewModel = viewModel<BuddyZoneViewModel>()
    val state = viewModel.cpInfoFlow.collectAsStateWithLifecycle().value

    CStateLayout(
        state = state,
    ) { cpInfo ->
        Column(
            modifier =
                modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 16.dp),
        ) {
            BuddyZone(
                targetUser = targetUser,
                cpInfo = cpInfo,
                onDoTask = { task ->
                    scope.launch {
                        if (isProcessing) return@launch
                        isProcessing = true
                        GlobalRepository.taskRepository
                            .doTask(task.taskId)
                            .onSuccess { result ->
                                if (result.clientJumpLink.isNotEmpty()) {
                                    result.clientJumpLink.toLink()
                                } else {
                                    viewModel.refresh()
                                }
                                showToast(result.toast)
                            }
                        isProcessing = false
                    }
                },
                onPublicCP = {
                    scope.launch {
                        cpInfo.defaultCpRuleUrl.toLink()
                    }
                },
                onUpLevel = {
                    scope.launch {
                        cpInfo.defaultCpRuleUrl.toLink()
                    }
                },
                onShowRule = { message ->
                    dc.easyPost {
                        LongTextDialog(
                            title = "温馨提示".localized,
                            content = message,
                            buttonContent = {
                                SolidButton(
                                    text = "确定".localized,
                                    onClick = {
                                        dismiss()
                                    },
                                    modifier =
                                        Modifier
                                            .padding(vertical = 20.dp)
                                            .align(Alignment.CenterHorizontally),
                                    height = 36.dp,
                                    minWidth = 160.dp,
                                )
                            },
                        )
                    }
                },
                onDestroyCP = {
                    dc.easyPost {
                        SimpleDoubleActionDialog(
                            "人海茫茫，相遇不易\n确定要解除CP吗？".localized,
                            cancelButtonConfig = DialogButtonStyles.Red.copy(text = "狠心解除".localized),
                            confirmButtonConfig = DialogButtonStyles.Secondary.copy("再想想".localized),
                            onCancel = {
                                lm.show(null) {
                                    GlobalRepository.relationRepo.breakUpBuddy().onSuccess {
                                        viewModel.refresh()
                                    }
                                }
                                dismiss()
                            },
                            onConfirm = {
                                dismiss()
                            },
                        )
                    }
                },
            )
        }
    }
}

//region 个人资料子tab

// 声音tab组件
@Composable
private fun UserVoiceCardTab(
    user: User,
    tagPrefix: String,
    refreshEnable: Boolean = true,
    pagerContentPadding: PaddingValues = PaddingValues.Zero,
    onNavigateTo: (AppNavKey) -> Unit = {},
) {
    val tabs: List<VoiceListTab.IUser> =
        remember(user.id) {
            listOf(
                VoiceListTab.MyPublish(user.id),
                VoiceListTab.Like(user.id),
                VoiceListTab.Favorite(user.id),
            )
        }
    Column {
        // Tab选项卡
        val scope = rememberCoroutineScope()
        val pagerState = rememberPagerState(pageCount = { tabs.size })
        val selectedTabIndex = pagerState.currentPage

        // 胶囊背景容器
        TabRow(
            selectedTabIndex = selectedTabIndex,
            modifier =
                Modifier
                    .padding(horizontal = 50.dp, vertical = 10.dp)
                    .background(color = Color(0xFFF5F6FA), shape = CircleShape)
                    .height(30.dp)
                    .fillMaxWidth(),
            divider = {},
            containerColor = Color.Transparent,
            indicator = { tabPositions ->
                if (selectedTabIndex < tabPositions.size) {
                    Box(
                        modifier =
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                .padding(4.dp)
                                .fillMaxSize()
                                .background(color = Color.White, shape = CircleShape)
                                .zIndex(-1f),
                    )
                }
            },
        ) {
            tabs.forEachIndexed { index, tab ->
                Tab(
                    selected = selectedTabIndex == index,
                    selectedContentColor = Color(0xFF111111), // 选中文字颜色
                    unselectedContentColor = Color(0xFF999999), // 未选中文字颜色
                    interactionSource = remember { NoIndicationInteractionSource() },
                    onClick = { scope.launch { pagerState.animateScrollToPage(index) } },
                    content = {
                        Box(modifier = Modifier.height(30.dp), contentAlignment = Alignment.Center) {
                            tab.TabContent(
                                selectedTabIndex == index,
                                style = TextStyle(fontSize = 11.sp),
                            )
                        }
                    },
                )
            }
        }

        val lifecycleOwner = LocalLifecycleOwner.current

        DisposableEffect(Unit) {
            val observer =
                LifecycleEventObserver { source, event ->
                    if (event == Lifecycle.Event.ON_STOP) {
                        MediaPlayerManager.pauseIf {
                            it.tag.startsWith(tagPrefix)
                        }
                    }
                }

            lifecycleOwner.lifecycle.addObserver(observer)

            onDispose {
                // Composable 离开组合时执行清理
                MediaPlayerManager.releaseIf {
                    it.tag.startsWith(tagPrefix)
                }
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
            beyondViewportPageCount = 1,
            userScrollEnabled = false,
        ) { page ->
            val tab = tabs[page]

            LaunchedEffect(Unit) {
                snapshotFlow {
                    pagerState.currentPage != page
                }.filter { it }.collectLatest {
                    MediaPlayerManager.releaseIf {
                        it.tag.startsWith("$tagPrefix$tab")
                    }
                }
            }

            // 内容区域
            VoiceLazyListPage(
                user = user,
                tagPrefix = tagPrefix,
                tab = tab,
                indexInParent = page,
                parentPagerState = pagerState,
                refreshEnable = refreshEnable,
                pagerContentPadding = pagerContentPadding,
            )
        }
    }
}

@Composable
private fun UserInformationTab(
    userProfileInfo: UserProfileInfo,
    onPublishDynamic: () -> Unit = {},
    onNavigateTo: (AppNavKey) -> Unit = {},
    moduleBgColor: Color = Color.White,
) {
    val user = userProfileInfo.user
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
    ) {
        // 作品统计
        item {
            Row(modifier = Modifier.padding(bottom = 24.dp)) {
                Column(
                    modifier =
                        Modifier
                            .weight(
                                1f,
                            ).paint(
                                painter = painterResource(R.drawable.ic_publish_count_bg),
                                contentScale = ContentScale.FillWidth,
                            ).padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.Center,
                ) {
                    Text("发布作品数".localized, color = Color(0xffD1A04B), fontSize = 10.sp, lineHeight = 14.sp)
                    SizeHeight(4.dp)
                    Text(
                        userProfileInfo.soundCount.toString(),
                        color = Color(0xffD1A04B),
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        fontFamily = FontFamily.MI_SANS,
                    )
                }
                SizeWidth(13.dp)
                Column(
                    modifier =
                        Modifier
                            .weight(
                                1f,
                            ).paint(
                                painter = painterResource(R.drawable.ic_like_count_bg),
                                contentScale = ContentScale.FillWidth,
                            ).padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.Center,
                ) {
                    Text("获得点赞".localized, color = Color(0xff64C4D3), fontSize = 10.sp, lineHeight = 14.sp)
                    SizeHeight(4.dp)
                    Text(
                        userProfileInfo.soundLikeCount.toString(),
                        color = Color(0xff64C4D3),
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        fontFamily = FontFamily.MI_SANS,
                    )
                }
            }
        }

        // 自我介绍(必须存在的, UI区分 日区/华语区)
        item {
            ProfileModule(
                "自我介绍".localized,
                backGroundColor =
                    if (userProfileInfo.intro.isEmpty() &&
                        userProfileInfo.isCN &&
                        user.sIsSelf
                    ) {
                        moduleBgColor
                    } else {
                        Color.Unspecified
                    },
                headerRightWidget = {
                    if (user.isSelf) {
                        Image(
                            WakooIcons.PencilEdit,
                            contentDescription = null,
                            modifier =
                                Modifier
                                    .size(18.dp)
                                    .click(noEffect = true, onClick = { onNavigateTo(Route.EditUserInfo) })
                                    .padding(2.dp),
                        )
                    }
                },
                bottomDp = 8.dp,
                desc = userProfileInfo.intro.ifEmpty { (if (user.isSelf) "点击按钮添加自我介绍".localized else "TA还没有设置自我介绍".localized) },
            ) {
                if (userProfileInfo.isCN && user.isSelf) {
                    if (userProfileInfo.intro.isEmpty()) {
                        SolidButton(
                            "添加介绍".localized,
                            height = 28.dp,
                            fontSize = 14.sp,
                            onClick = {
                                onNavigateTo(
                                    Route.EditTextUserInfo(
                                        InputEdit.createShortIntroEdit(
                                            user.intro,
                                        ),
                                    ),
                                )
                            },
                            modifier = Modifier.align(alignment = Alignment.CenterHorizontally),
                        )
                    }
                }
            }
        }

        // 相册
        if (userProfileInfo.isJP) {
            item {
                val albums = userProfileInfo.albums ?: listOf()
                ProfileModule(
                    if (albums.isEmpty()) "相册".localized else "${"相册".localized}(${albums.size})",
                    backGroundColor = Color.Unspecified,
                    desc = if (user.sIsSelf) "" else (if (albums.isEmpty()) "TA还没有发布过相片".localized else ""),
                    bottomDp = 8.dp,
                ) {
                    // 是自己的相册 / 或者相册不是空的
                    if (user.sIsSelf || albums.isNotEmpty()) {
                        LazyRow(
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .background(color = Color.White, shape = RoundedCornerShape(12.dp)),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            contentPadding = PaddingValues(8.dp),
                        ) {
                            if (user.sIsSelf) {
                                item {
                                    Box(modifier = Modifier.aspectRatio(1f)) {
                                        Image(
                                            WakooIcons.AddRound,
                                            contentDescription = null,
                                            modifier =
                                                Modifier
                                                    .size(56.dp)
                                                    .background(color = Color(0xffE9EAEF), shape = RoundedCornerShape(8.dp))
                                                    .click {
                                                        onNavigateTo(Route.UserAlbum)
                                                    }.padding(16.dp),
                                        )
                                    }
                                }
                            }

                            items(albums) {
                                NetworkImage(
                                    it.mediaUrl,
                                    modifier =
                                        Modifier
                                            .size(56.dp)
                                            .clip(RoundedCornerShape(8.dp)),
                                )
                            }
                        }
                    }
                }
            }
        }

        // 基础信息
        if (userProfileInfo.isJP) {
            item {
                FlowRow(verticalArrangement = Arrangement.spacedBy(20.dp), modifier = Modifier.padding(bottom = 20.dp)) {
                    Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            "基本情报".localized,
                            fontWeight = FontWeight.Medium,
                            fontSize = 13.sp,
                            lineHeight = 14.sp,
                            color = Color(0xff111111),
                        )
                        Weight(1f)
                        if (user.id.isSelf) {
                            Image(
                                WakooIcons.PencilEdit,
                                contentDescription = null,
                                modifier =
                                    Modifier
                                        .size(18.dp)
                                        .click(noEffect = true, onClick = { onNavigateTo(Route.EditUserInfo) })
                                        .padding(2.dp),
                            )
                        }
                    }
                    userProfileInfo.getNativeProfileList().forEach { values ->
                        Text(
                            buildAnnotatedString {
                                append(values.first)
                                append("：")
                                withStyle(
                                    SpanStyle(
                                        color = Color(0xff1d2129),
                                    ),
                                ) {
                                    append(values.second)
                                }
                            },
                            fontSize = 14.sp,
                            lineHeight = 14.sp,
                            fontWeight = FontWeight.W400,
                            color = Color(0xffb6b6b6),
                            modifier = Modifier.widthIn(min = 156.dp),
                        )
                    }
                }
            }
        }

        // 加入的群组(必须存在的,UI 不区分 日区/华语区)
        if (userProfileInfo.group != null || user.sIsSelf) {
            item {
                ProfileModule(
                    "我的群组".localized,
                    backGroundColor =
                        if (userProfileInfo.group == null) {
                            moduleBgColor
                        } else {
                            Color.Unspecified
                        },
                    bottomDp = 8.dp,
                    desc = if (userProfileInfo.group != null) "" else "暂未加入群组".localized,
                ) {
                    val group = userProfileInfo.group
                    if (group != null) {
                        SpaceListItemScaffold(
                            startContent = {
                                SquareNetworkImage(
                                    modifier =
                                        Modifier.clickable(onClick = {
                                            onNavigateTo(Route.ChatGroupDetail(group.id))
                                        }),
                                    data = group.avatarUrl,
                                    size = 80.dp,
                                    shape = RoundedCornerShape(8.dp),
                                )
                            },
                            centerContent = {
                                Column(verticalArrangement = Arrangement.spacedBy(5.dp)) {
                                    Text(
                                        text = group.name,
                                        style = MaterialTheme.typography.titleSmall,
                                        color = WakooSecondarySelected,
                                        lineHeight = 20.sp,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                    )

                                    Text(
                                        text = "${group.memberCnt}人",
                                        style = MaterialTheme.typography.labelLarge,
                                        color = WakooSecondaryUnSelected,
                                        lineHeight = 16.sp,
                                        maxLines = 2,
                                        overflow = TextOverflow.Ellipsis,
                                    )
                                }
                            },
                            endContent = {
                                Image(
                                    WakooIcons.ArrowRight,
                                    contentDescription = null,
                                    modifier = Modifier.align(alignment = Alignment.CenterVertically),
                                )
                            },
                            modifier =
                                Modifier
                                    .fillMaxWidth()
                                    .background(color = moduleBgColor, RoundedCornerShape(12.dp))
                                    .click {
                                        if (user.sIsSelf) {
                                            onNavigateTo(Route.ChatGroup(group.id))
                                        } else {
                                            onNavigateTo(Route.ChatGroupDetail(group.id))
                                        }
                                    }.padding(8.dp),
                            space = 12.dp,
                        )
                    } else {
                        SolidButton("群组广场".localized, height = 28.dp, fontSize = 14.sp, onClick = {
                            onNavigateTo(Route.ChatGroupSquare)
                        }, modifier = Modifier.align(alignment = Alignment.CenterHorizontally))
                    }
                }
            }
        }

        // 生态动态(瞬间)
        if (userProfileInfo.isCN) {
            item {
                val moments = userProfileInfo.moments ?: listOf()
                ProfileModule(
                    if (moments.isEmpty()) "生活记录".localized else "${"生活记录".localized}(${userProfileInfo.momentTotalCount})",
                    backGroundColor = if (moments.isEmpty() && user.sIsSelf) moduleBgColor else Color.Unspecified,
                    headerRightWidget = {
                        Row(
                            modifier = Modifier.weight(1f),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            if (user.sIsSelf) {
                                Image(
                                    WakooIcons.PencilEdit,
                                    contentDescription = null,
                                    modifier =
                                        Modifier
                                            .size(18.dp)
                                            .click(noEffect = true, onClick = onPublishDynamic)
                                            .padding(2.dp),
                                )
                            }
                            Weight(1f)
                            Row(
                                modifier =
                                    Modifier
                                        .click(noEffect = true) {
                                            onNavigateTo(Route.MomentList(user.id))
                                        }.padding(2.dp),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Text("更多".localized, fontSize = 12.sp, lineHeight = 14.sp, color = Color(0xffb6b6b6))
                                Image(WakooIcons.ArrowRight, contentDescription = null, modifier = Modifier.size(14.dp))
                            }
                        }
                    },
                    desc = if (moments.isEmpty()) "暂无任何记录, 去发布生活记录内容吧".localized else "",
                ) {
                    if (moments.isEmpty() && user.sIsSelf) {
                        SolidButton("立即发布".localized, height = 28.dp, fontSize = 14.sp, onClick = {
                            onNavigateTo(Route.MomentPublish)
                        }, modifier = Modifier.align(alignment = Alignment.CenterHorizontally))
                    } else {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            moments.take(5).forEach { item ->
                                NetworkImage(
                                    item.mediaUrl,
                                    modifier =
                                        Modifier
                                            .size(60.dp)
                                            .clip(RoundedCornerShape(8.dp)),
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
//endregion

//region widgets
@Composable
private fun ProfileModule(
    title: String,
    desc: String = "",
    backGroundColor: Color = Color.Unspecified,
    headerRightWidget: @Composable RowScope.() -> Unit = {},
    bottomDp: Dp = 0.dp,
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier =
            Modifier
                .padding(bottom = bottomDp)
                .fillMaxWidth()
                .conditional(backGroundColor != Color.Unspecified) {
                    background(color = backGroundColor, shape = RoundedCornerShape(12.dp)).padding(12.dp)
                },
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(title, fontWeight = FontWeight.Medium, fontSize = 13.sp, lineHeight = 14.sp, color = Color(0xff111111))
            headerRightWidget()
        }
        SizeHeight(12.dp)
        if (desc.isNotEmpty()) {
            Text(desc, fontSize = 14.sp, lineHeight = 12.sp, color = Color(0xff999999))
            SizeHeight(20.dp)
        }
        content()
    }
}

/** 顶部应用栏 */
@Composable
fun TopAppBar(
    user: User,
    toSettings: () -> Unit,
    toEditUserInfo: () -> Unit,
    modifier: Modifier = Modifier,
    showBack: Boolean = false,
    onReport: () -> Unit = {},
    onBlacked: () -> Unit = {},
) {
    WakooTitleBar(
        title = null,
        modifier = modifier,
        navigationIcon = if (showBack) WakooTitleBarDefaults.backIconNavigation() else emptyContent,
    ) {
        if (user.isSelf) {
            // 编辑资料按钮
            OutlinedButton(
                text = "编辑资料".localized,
                onClick = toEditUserInfo,
                height = 28.dp,
                borderWidth = 1f,
                fontSize = 12.sp,
                paddingValues = PaddingValues(horizontal = 8.dp),
            )

            SizeWidth(15.dp)

            // 设置按钮
            WakooTitleBarDefaults.IconButtonAction(
                imageVector = WakooIcons.Settings,
                onClick = toSettings,
                modifier = Modifier.size(24.dp),
            )

            SizeWidth(10.dp)
        } else {
            Box {
                // 设置按钮
                var expanded by rememberSaveable { mutableStateOf(false) }

                WakooTitleBarDefaults.IconButtonAction(
                    imageVector = WakooIcons.More,
                    onClick = {
                        expanded = true
                    },
                )

                val bubbleShape =
                    remember {
                        BubbleShape(arrowPositionBias = 0.85f)
                    }

                DropdownMenu(
                    expanded = expanded, // 菜单的展开状态
                    onDismissRequest = { expanded = false }, // 点击菜单外部或按返回键时关闭菜单
                    shape = bubbleShape,
                    offset = DpOffset((-6).dp, (-12).dp),
                    tonalElevation = 0.dp,
                    shadowElevation = 0.dp,
                    containerColor = Color.White,
                ) {
                    // 菜单项
                    Row(
                        modifier =
                            Modifier
                                .clickable(onClick = {
                                    onReport()
                                    expanded = false
                                })
                                .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        Icon(
                            imageVector = WakooIcons.Report,
                            modifier = Modifier.size(20.dp, 20.dp),
                            contentDescription = null,
                            tint = Color(0xFF111111),
                        )
                        Text(
                            text = "举报".localized,
                            modifier = Modifier.weight(1f),
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }

                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        thickness = 0.5.dp,
                        color = Color(0xFFE5E5E5),
                    )

                    Row(
                        modifier =
                            Modifier
                                .clickable(onClick = {
                                    onBlacked()
                                    expanded = false
                                })
                                .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                    ) {
                        Icon(
                            imageVector = WakooIcons.UserForbidLine,
                            modifier = Modifier.size(20.dp, 20.dp),
                            contentDescription = null,
                            tint = Color(0xFF111111),
                        )
                        Text(
                            text = "拉黑".localized,
                            modifier = Modifier.weight(1f),
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }
        }
    }
}

/** 粉丝关注信息区域 */
@Composable
fun FollowInfoSection(
    socialInfo: SocialInfo,
    toUserRelations: (Int) -> Unit = {},
) {
    Row(
        modifier =
            Modifier
                .padding(horizontal = 64.dp)
                .fillMaxWidth()
                .height(intrinsicSize = IntrinsicSize.Min),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 关注
        Column(
            modifier = Modifier.noEffectClick(onClick = { toUserRelations(0) }),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = socialInfo.followingCount.toString(),
                style = MaterialTheme.typography.titleLarge,
                fontFamily = FontFamily.MI_SANS,
                color = Color(0xFF111111),
            )
            Text(
                text = "关注".localized,
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF999999),
            )
        }

        // 分隔线
        VerticalDivider(
            color = Color(0xFFE5E5E5),
            thickness = 0.5.dp,
            modifier = Modifier.fillMaxHeight(0.7f),
        )

        // 粉丝
        Column(
            modifier = Modifier.noEffectClick(onClick = { toUserRelations(1) }),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = socialInfo.followersCount.toString(),
                style = MaterialTheme.typography.titleLarge,
                fontFamily = FontFamily.MI_SANS,
                color = Color(0xFF111111),
            )
            Text(
                text = "粉丝".localized,
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF999999),
            )
        }

        // 分隔线
        VerticalDivider(
            color = Color(0xFFE5E5E5),
            thickness = 0.5.dp,
            modifier = Modifier.fillMaxHeight(0.7f),
        )

        // 粉丝
        Column(
            modifier = Modifier.noEffectClick(onClick = { toUserRelations(2) }),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                text = socialInfo.friendCount.toString(),
                style = MaterialTheme.typography.titleLarge,
                fontFamily = FontFamily.MI_SANS,
                color = Color(0xFF111111),
            )
            Text(
                text = "好友".localized,
                style = MaterialTheme.typography.labelLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF999999),
            )
        }
    }
}

//endregion
