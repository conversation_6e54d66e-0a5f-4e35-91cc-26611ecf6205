package com.buque.wakoo.ui.dialog

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.outlined.Star
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.R
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.navigation.dialog.easyPost
import com.buque.wakoo.network.api.service.SettingsApiService
import com.buque.wakoo.network.executeApiCall
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.CloseCircle
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooText
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.SolidButton
import com.google.android.play.core.review.ReviewInfo
import com.google.android.play.core.review.ReviewManagerFactory

@Composable
fun DialogScope.AppRatingContent(onRateNowClick: (Int) -> Unit) {
    val context = LocalContext.current
    val activity = LocalActivity.current

    val manager =
        remember(context) {
            ReviewManagerFactory.create(context)
        }

    val reviewInfoState =
        remember(manager) {
            mutableStateOf<ReviewInfo?>(null)
        }

    if (activity != null) {
        LaunchedEffect(manager) {
            val request = manager.requestReviewFlow()
            request.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    // We got the ReviewInfo object
                    reviewInfoState.value = task.result
                }
            }
        }
    }

    AppRatingDialogContent(
        ::dismiss,
    ) { selectedStars: Int ->
        onRateNowClick(selectedStars)
        dismiss()
        if (selectedStars <= 3) {
            dialogController.easyPost {
                val scope = rememberCoroutineScope()
                val loading = LocalLoadingManager.current
                FeedbackDialogContent(selectedStars, ::dismiss) { content ->
                    loading.show(scope) {
                        executeApiCall {
                            SettingsApiService.instance.postFeedback(
                                buildMap {
                                    put("content", content)
                                },
                            )
                        }.onSuccess {
                            dismiss()
                        }
                    }
                }
            }
        } else {
            val reviewInfo = reviewInfoState.value
            if (reviewInfo != null && activity != null) {
                val flow = manager.launchReviewFlow(activity, reviewInfo)
                flow.addOnCompleteListener { _ ->
                    // The flow has finished. The API does not indicate whether the user
                    // reviewed or not, or even whether the review dialog was shown. Thus, no
                    // matter the result, we continue our app flow.
                }
            }
        }
    }
}

/**
 * 应用评分弹窗
 *
 * @param onDismissRequest 当弹窗被取消时调用
 * @param onRateNowClick 当点击“立即评价”时调用，传入用户选择的星级
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AppRatingDialogContent(
    onDismissRequest: () -> Unit,
    onRateNowClick: (Int) -> Unit,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier =
                Modifier
                    .fillMaxWidth()
                    .background(WakooWhite, RoundedCornerShape(8.dp)),
        ) {
            Image(
                painter = painterResource(R.drawable.bg_app_rating),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth,
            )

            SizeHeight(20.dp)

            Text(
                text = "邀请你为wakoo评分".localized,
                style = MaterialTheme.typography.titleSmall.copy(fontWeight = FontWeight.Medium),
                color = WakooText,
                textAlign = TextAlign.Center,
            )

            SizeHeight(4.dp)

            Text(
                text = "点击下方可直接进行评价".localized,
                style = MaterialTheme.typography.labelLarge,
                color = WakooGrayText,
                textAlign = TextAlign.Center,
            )

            SizeHeight(20.dp)

            var selectedStars by remember { mutableIntStateOf(0) }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // 5颗星评分
                for (i in 1..5) {
                    Icon(
                        imageVector = if (i <= selectedStars) Icons.Filled.Star else Icons.Outlined.Star,
                        contentDescription = "Star $i",
                        tint = if (i <= selectedStars) Color(0xFFFFD235) else Color(0xFFE9EAEF),
                        modifier =
                            Modifier
                                .size(36.dp)
                                .noEffectClick {
                                    selectedStars = i
                                },
                    )
                }
            }

            SizeHeight(20.dp)

            SolidButton(
                text = "确认".localized,
                onClick = { onRateNowClick(selectedStars) },
                height = 36.dp,
                minWidth = 113.dp,
                enabled = selectedStars > 0, // 至少选择一颗星才能点击
            )

            SizeHeight(20.dp)
        }

        SizeHeight(8.dp)

        IconButton(onClick = onDismissRequest) {
            Icon(
                imageVector = WakooIcons.CloseCircle,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = WakooWhite,
            )
        }
    }
}

/**
 * 低分反馈弹窗 (当用户给出低分时显示)
 *
 * @param onDismissRequest 当弹窗被取消时调用
 * @param onSubmitFeedback 当点击“提交反馈”时调用，传入用户输入的反馈内容
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun FeedbackDialogContent(
    selectedStars: Int,
    onDismissRequest: () -> Unit,
    onSubmitFeedback: (String) -> Unit,
) {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(WakooWhite, RoundedCornerShape(8.dp))
                .padding(horizontal = 16.dp, vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = "评分成功！".localized,
            style = MaterialTheme.typography.titleSmall.copy(fontWeight = FontWeight.Medium),
            color = WakooText,
            textAlign = TextAlign.Center,
        )
        SizeHeight(18.dp)

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 5颗星评分
            for (i in 1..5) {
                Icon(
                    imageVector = if (i <= selectedStars) Icons.Filled.Star else Icons.Outlined.Star,
                    contentDescription = "Star $i",
                    tint = if (i <= selectedStars) Color(0xFFFFD235) else Color(0xFFE9EAEF),
                    modifier = Modifier.size(36.dp),
                )
            }
        }

        SizeHeight(18.dp)

        val focusRequester = remember { FocusRequester() }

        LaunchedEffect(Unit) {
            focusRequester.requestFocus() // 请求焦点
        }

        var feedbackText by remember { mutableStateOf("") }

        AppTextField(
            value = feedbackText,
            onValueChange = {
                feedbackText = it
            },
            modifier =
                Modifier
                    .focusRequester(focusRequester)
                    .fillMaxWidth()
                    .height(154.dp),
            showLengthTip = false,
            keyboardOptions =
                KeyboardOptions(
                    imeAction = ImeAction.Done,
                ),
            keyboardActions =
                KeyboardActions(
                    onDone = {
                    },
                ),
            backgroundColor = Color(0xFFF8F8F8),
            placeholder = "非常期待您的意见反馈，wakoo产品开发团队将会用心查看每一条反馈，并不断优化我们的产品。".localized,
        )

        SizeHeight(20.dp)

        Row {
            SolidButton(
                text = "以后再说".localized,
                onClick = onDismissRequest,
                modifier = Modifier.weight(1f),
                height = 36.dp,
                backgroundColor = Color(0xFFEFEFEF),
                textColor = Color(0xFF999999),
            )

            SizeWidth(20.dp)

            SolidButton(
                text = "提交反馈".localized,
                onClick = { onSubmitFeedback(feedbackText) },
                modifier = Modifier.weight(1f),
                height = 36.dp,
                enabled = feedbackText.isNotBlank(),
            )
        }
    }
}

// 预览函数
@Preview(showBackground = true)
@Composable
private fun PreviewAppRatingDialog() {
    WakooTheme {
        Box(modifier = Modifier.padding(20.dp)) {
            AppRatingDialogContent(
                onDismissRequest = {},
                onRateNowClick = { stars -> println("Rated $stars stars") },
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewLowScoreFeedbackDialog() {
    WakooTheme {
        Box(modifier = Modifier.padding(20.dp)) {
            FeedbackDialogContent(
                selectedStars = 3,
                onDismissRequest = {},
                onSubmitFeedback = { feedback -> println("Feedback: $feedback") },
            )
        }
    }
}
