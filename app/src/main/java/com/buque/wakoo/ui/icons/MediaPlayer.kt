package com.buque.wakoo.ui.icons

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val WakooIcons.MediaPlayer: ImageVector
    get() {
        if (_MediaPlayer != null) {
            return _MediaPlayer!!
        }
        _MediaPlayer = ImageVector.Builder(
            name = "MediaPlayer",
            defaultWidth = 80.dp,
            defaultHeight = 80.dp,
            viewportWidth = 80f,
            viewportHeight = 80f
        ).apply {
            group(
                clipPathData = PathData {
                    moveTo(0f, 0f)
                    horizontalLineToRelative(80f)
                    verticalLineToRelative(80f)
                    horizontalLineToRelative(-80f)
                    close()
                }
            ) {
                path(
                    fill = SolidColor(Color(0xFF111111)),
                    fillAlpha = 0.3f,
                    strokeAlpha = 0.3f
                ) {
                    moveTo(40f, 40f)
                    moveToRelative(-32f, 0f)
                    arcToRelative(32f, 32f, 0f, isMoreThanHalf = true, isPositiveArc = true, 64f, 0f)
                    arcToRelative(32f, 32f, 0f, isMoreThanHalf = true, isPositiveArc = true, -64f, 0f)
                }
                path(
                    stroke = SolidColor(Color.White),
                    strokeLineWidth = 4f
                ) {
                    moveTo(40f, 40f)
                    moveToRelative(-34f, 0f)
                    arcToRelative(34f, 34f, 0f, isMoreThanHalf = true, isPositiveArc = true, 68f, 0f)
                    arcToRelative(34f, 34f, 0f, isMoreThanHalf = true, isPositiveArc = true, -68f, 0f)
                }
                path(fill = SolidColor(Color.White)) {
                    moveTo(34.051f, 25.859f)
                    lineTo(53.297f, 38.687f)
                    curveTo(53.513f, 38.831f, 53.691f, 39.026f, 53.813f, 39.255f)
                    curveTo(53.936f, 39.484f, 54f, 39.74f, 54f, 40f)
                    curveTo(54f, 40.26f, 53.936f, 40.516f, 53.813f, 40.745f)
                    curveTo(53.691f, 40.974f, 53.513f, 41.17f, 53.297f, 41.314f)
                    lineTo(34.047f, 54.141f)
                    curveTo(33.81f, 54.299f, 33.534f, 54.389f, 33.25f, 54.402f)
                    curveTo(32.965f, 54.416f, 32.683f, 54.352f, 32.432f, 54.218f)
                    curveTo(32.18f, 54.083f, 31.97f, 53.884f, 31.823f, 53.64f)
                    curveTo(31.677f, 53.396f, 31.599f, 53.117f, 31.598f, 52.832f)
                    verticalLineTo(27.169f)
                    curveTo(31.598f, 26.883f, 31.676f, 26.603f, 31.823f, 26.359f)
                    curveTo(31.97f, 26.114f, 32.181f, 25.914f, 32.432f, 25.779f)
                    curveTo(32.684f, 25.645f, 32.968f, 25.581f, 33.253f, 25.596f)
                    curveTo(33.538f, 25.61f, 33.814f, 25.701f, 34.051f, 25.859f)
                    close()
                }
            }
        }.build()

        return _MediaPlayer!!
    }

@Suppress("ObjectPropertyName")
private var _MediaPlayer: ImageVector? = null

@Preview
@Composable
private fun IconPreview() {
    com.buque.wakoo.ui.theme.WakooTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                imageVector = com.buque.wakoo.ui.icons.WakooIcons.MediaPlayer,
                contentDescription = null,
                modifier =
                    Modifier
                        .width((40.0).dp)
                        .height((40.0).dp),
            )
        }
    }
}
