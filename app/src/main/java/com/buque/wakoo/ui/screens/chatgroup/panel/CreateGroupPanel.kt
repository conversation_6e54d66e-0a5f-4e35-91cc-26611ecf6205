package com.buque.wakoo.ui.screens.chatgroup.panel

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.app.OnDataCallback
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.dialog.DialogController
import com.buque.wakoo.navigation.dialog.DialogScope
import com.buque.wakoo.ui.dialog.BottomPanelScaffold
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooSecondarySelected
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.NetworkImage

@Composable
fun DialogScope.CreateGroupPanel(
    inputTitle: String,
    onNameChanged: OnDataCallback<String>,
    groupAvatar: String?,
    onChangeAvatar: OnAction,
    onCreate: OnAction,
) {
    val createCallback by rememberUpdatedState(onCreate)
    BottomPanelScaffold(
        title = "创建兴趣群组".localized,
        useClose = true,
        modifier =
            Modifier
                .windowInsetsPadding(WindowInsets.ime)
                .fillMaxWidth(),
        backgroundColor = WakooWhite,
    ) {
        ImeButtonScaffold(
            modifier = Modifier.fillMaxWidth(),
            buttonModifier =
                Modifier
                    .fillMaxWidth()
                    .background(WakooWhite)
                    .padding(
                        top = 20.dp,
                        bottom = 24.dp,
                    ),
            weightFill = false,
            buttonContent = {
                val nextEnable by remember(inputTitle, groupAvatar) {
                    derivedStateOf {
                        inputTitle.isNotBlank() && groupAvatar?.isNotBlank() == true
                    }
                }

                Box {
                    GradientButton(
                        text = "创建群组".localized,
                        onClick = createCallback,
                        modifier = Modifier.padding(top = 4.dp),
                        enabled = nextEnable,
                        minWidth = 220.dp,
                        height = 44.dp,
                        paddingValues = PaddingValues(horizontal = 20.dp),
                    )

                    Text(
                        "会员权益".localized,
                        modifier =
                            Modifier
                                .align(Alignment.TopEnd)
                                .background(
                                    color = Color(0xffFFE984),
                                    shape = RoundedCornerShape(2.dp),
                                ).padding(horizontal = 5.dp, vertical = 4.dp),
                        color = Color(0xff674E0F),
                        fontSize = 10.sp,
                        fontFamily = FontFamily.MI_SANS,
                        fontWeight = FontWeight.W900,
                    )
                }
            },
        ) {
            Column {
                Box(
                    modifier =
                        Modifier
                            .align(Alignment.CenterHorizontally)
                            .size(80.dp)
                            .background(Color(0xFFF8F8F8), RoundedCornerShape(8.dp))
                            .clickable(onClick = onChangeAvatar),
                ) {
                    if (groupAvatar == null) {
                        Image(
                            painter = painterResource(R.drawable.ic_avatar_placeholder),
                            contentDescription = "icon",
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.FillBounds,
                        )
                    } else {
                        NetworkImage(
                            data = groupAvatar,
                            modifier = Modifier.fillMaxSize(),
                        )
                    }
                }

                SizeHeight(24.dp)

                Text(
                    text = "群组名称".localized,
                    style = MaterialTheme.typography.bodyMedium,
                    color = WakooSecondarySelected,
                )

                SizeHeight(10.dp)

                AppTextField(
                    value = inputTitle,
                    onValueChange = onNameChanged,
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                    placeholder = "为你的群组取一个名字吧".localized,
                    maxLength = 20,
                    singleLine = true,
                    showLengthTip = false,
                    backgroundColor = Color(0xFFF8F8F8),
                    contentPadding = PaddingValues(start = 12.dp, top = 10.dp, end = 60.dp, bottom = 10.dp),
                    keyboardOptions =
                        KeyboardOptions(
                            imeAction = ImeAction.Done,
                        ),
                    keyboardActions =
                        KeyboardActions(
                            onDone = {
                                if (inputTitle.isNotBlank() && groupAvatar?.isNotBlank() == true) {
                                    onCreate()
                                }
                            },
                        ),
                )

                SizeHeight(10.dp)
            }
        }
    }
}

@Preview
@Composable
private fun PreviewCreateGroupPanel() {
    WakooTheme {
        DialogController.preview.apply {
            CreateGroupPanel("", {}, "", {}, {})
        }
    }
}
