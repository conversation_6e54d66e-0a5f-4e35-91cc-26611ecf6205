package com.buque.wakoo.ui.screens.moments

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.buque.wakoo.R
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.bean.MomentItem
import com.buque.wakoo.ext.click
import com.buque.wakoo.ext.getDaysBetweenTimestamps
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.ui.dialog.loading.LocalLoadingManager
import com.buque.wakoo.ui.icons.Location
import com.buque.wakoo.ui.icons.More
import com.buque.wakoo.ui.icons.ThumbUp
import com.buque.wakoo.ui.icons.ThumbUpDone
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.widget.ExpandableText
import com.buque.wakoo.ui.widget.SegColorTitleScreenScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.VerticalGrid
import com.buque.wakoo.ui.widget.Weight
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.previewer.MediaPreviewState
import com.buque.wakoo.ui.widget.media.previewer.TransitionOverlay
import com.buque.wakoo.ui.widget.media.previewer.rememberPreviewState
import com.buque.wakoo.ui.widget.media.previewer.showMediaInfoPreviewer
import com.buque.wakoo.ui.widget.popup.BubbleShape
import com.buque.wakoo.ui.widget.state.CStateListPaginateLayout
import com.buque.wakoo.viewmodel.UserMomentListViewModel
import me.saket.telephoto.zoomable.rememberZoomablePeekOverlayState
import me.saket.telephoto.zoomable.zoomablePeekOverlay
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

@Composable
fun UserMomentListScreen(
    userId: String,
    modifier: Modifier = Modifier,
) {
    val viewModel =
        viewModel<UserMomentListViewModel>(initializer = {
            UserMomentListViewModel(userId)
        })
    val listState = rememberLazyListState()
    val isSelf by
        remember {
            derivedStateOf {
                userId == SelfUser?.id
            }
        }

    val scope = rememberCoroutineScope()
    val loadingManager = LocalLoadingManager.current

    val density = LocalDensity.current
    val previewState = rememberPreviewState()

    // 转场覆盖层，负责在转场期间显示动画元素和全屏查看器。
    TransitionOverlay(state = previewState)

    SegColorTitleScreenScaffold(title = "生活记录".localized) { pv ->
        CStateListPaginateLayout<Any, Int, MomentItem, UserMomentListViewModel>(
            "",
            viewModel = viewModel,
            listState = listState,
            emptyText = "暂无记录".localized,
            emptyId = R.drawable.ic_empty_for_all,
            modifier = Modifier.padding(pv),
        ) { _, list ->
            LazyColumn(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .background(color = Color.White),
            ) {
                items(list) { item ->
                    MomentItemContent(item, isSelf, previewState, onThumbClick = {
                        viewModel.thumbUpOrNotMoment(item)
                    }, onRemoveMoment = {
                        loadingManager.show(scope) {
                            viewModel.deleteMoment(item)
                        }
                    }) { index ->
                        previewState.showMediaInfoPreviewer(
                            mediaInfos =
                                list.flatMap {
                                    it.images.onEach { info ->
                                        info.parentId = it.id.toString()
                                    }
                                },
                            info = item.images[index],
                            radius =
                                with(density) {
                                    12.dp.toPx()
                                },
                            applyIndexKey = true,
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MomentItemContent(
    item: MomentItem,
    isSelf: Boolean = false,
    previewState: MediaPreviewState,
    onThumbClick: () -> Unit = {},
    onRemoveMoment: () -> Unit = {},
    onPreview: (Int) -> Unit = {},
) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        // 左侧时间列
        Column(
            modifier = Modifier.width(60.dp),
        ) {
            Text(
                text = getDateText(item.createTime),
                fontSize = 11.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xff999999),
            )
            Text(
                text = getTimeText(item.createTime),
                fontSize = 12.sp,
                color = Color(0xFF999999),
            )
        }

        // 右侧内容列
        Column(
            modifier = Modifier.weight(1f),
        ) {
            ExpandableText(
                text = item.text,
                expandableText = "查看全文".localized,
                collapsedMaxLines = 2,
                style =
                    TextStyle(
                        color = Color(0xff111111),
                        fontSize = 14.sp,
                        lineHeight = 21.sp,
                    ),
                expandableTextColor = Color(0xff4ADD4F),
            )

            SizeHeight(10.dp)
            VerticalGrid(
                columns =
                    if (item.images.size == 1) {
                        1
                    } else if (item.images.size < 4) {
                        2
                    } else {
                        3
                    },
                horizontalSpace = 10.dp,
                verticalSpace = 10.dp,
            ) {
                item.images.forEachIndexed { index, info ->
                    NetworkImage(
                        data = info.mediaUrl,
                        modifier =
                            Modifier
                                .then(
                                    if (item.images.size == 1) {
                                        Modifier.fillMaxWidth(0.7f)
                                    } else {
                                        Modifier.fillMaxWidth()
                                    },
                                ).aspectRatio(
                                    if (item.images.size == 1) {
                                        info.aspectRatio
                                    } else {
                                        1f
                                    },
                                ).clip(RoundedCornerShape(12.dp))
                                .clickable {
                                    onPreview(index)
                                }.run {
                                    with(previewState) {
                                        registerGridItem("${item.id}-${info.mediaUrl}")
                                            .zoomablePeekOverlay(rememberZoomablePeekOverlayState())
                                    }
                                },
                        contentScale = ContentScale.Crop,
                    )
                }
            }

            SizeHeight(16.dp)

            // 底部：位置信息和交互按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // 左侧位置信息
                if (item.locationLabel.isNotEmpty()) {
                    Icon(
                        imageVector = WakooIcons.Location,
                        contentDescription = null,
                        modifier = Modifier.size(14.dp),
                    )
                    Text(
                        text = item.locationLabel,
                        fontSize = 12.sp,
                        color = Color(0xFF999999),
                    )
                }

                Weight(1f)

                // 点赞按钮
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    modifier = Modifier.click(onClick = onThumbClick),
                ) {
                    Image(
                        imageVector = if (item.iHaveLiked) WakooIcons.ThumbUpDone else WakooIcons.ThumbUp,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                    )
                    Text(
                        text = "(${item.likeCnt})",
                        fontSize = 12.sp,
                        color = Color(0xFF999999),
                    )
                }
                SizeWidth(12.dp)

                if (isSelf) {
                    Box {
                        var expanded by rememberSaveable { mutableStateOf(false) }
                        // 更多选项按钮
                        Icon(
                            imageVector = WakooIcons.More,
                            contentDescription = "更多选项".localized,
                            modifier =
                                Modifier
                                    .size(20.dp)
                                    .clickable {
                                        expanded = true
                                    },
                        )

                        val bubbleShape =
                            remember {
                                BubbleShape(arrowPositionBias = 0.85f)
                            }

                        // 弹出菜单主体
                        DropdownMenu(
                            expanded = expanded, // 菜单的展开状态
                            onDismissRequest = { expanded = false }, // 点击菜单外部或按返回键时关闭菜单
                            shape = bubbleShape,
                            offset = DpOffset(6.dp, 0.dp),
                            tonalElevation = 0.dp,
                            shadowElevation = 0.dp,
                            containerColor = Color(0x80111111),
                        ) {
                            // 菜单项

                            Column {
                                SizeHeight(6.5.dp)
                                Text(
                                    text = "删除".localized,
                                    color = Color.White,
                                    fontSize = 14.sp,
                                    textAlign = TextAlign.Center,
                                    modifier =
                                        Modifier
                                            .click {
                                                expanded = false
                                                onRemoveMoment()
                                            }.padding(horizontal = 16.dp),
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

// 获取日期文本
private fun getDateText(createTime: Long): AnnotatedString {
    val days = System.currentTimeMillis().getDaysBetweenTimestamps(createTime * 1000)

    return buildAnnotatedString {
        val style = SpanStyle(fontSize = 14.sp, color = Color(0xff111111), fontWeight = FontWeight.SemiBold)
        if (days == 0L) {
            withStyle(style) {
                append("今天".localized)
            }
        } else if (days == 1L) {
            withStyle(style) {
                append("昨天".localized)
            }
        } else {
            val calendar = Calendar.getInstance()
            calendar.time = Date(createTime * 1000)
            append(calendar.get(Calendar.DAY_OF_MONTH).toString())
            append("/")
            withStyle(style) {
                append("%s月".localizedFormat(calendar.get(Calendar.MONTH) + 1))
            }
        }
    }
}

// 获取时间文本
private fun getTimeText(createTime: Long): String {
    val date = Date(createTime * 1000)
    val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    return timeFormat.format(date)
}

@Composable
@Preview
private fun MomentItemPreview() {
    val item =
        AppJson.decodeFromString<MomentItem>(
            "{\"id\":601,\"text\":\"啊的说法撒旦法撒旦法阿斯蒂芬阿斯蒂芬阿斯蒂芬阿斯蒂芬as啊的说法撒旦法撒旦法阿斯蒂芬阿斯蒂芬阿斯蒂芬阿斯蒂芬as啊的说法撒旦法撒旦法阿斯蒂芬阿斯蒂芬阿斯蒂芬阿斯蒂芬as\",\"images\":[{\"url\":\"https://media.wakooclub.com//mobileclient/android/avatar/20250717/1752735177969_358_1000000183.png\",\"width\":236,\"height\":238,\"size\":78903,\"isVideo\":false},{\"url\":\"https://media.wakooclub.com//mobileclient/android/avatar/20250717/1752735177969_358_1000000183.png\",\"width\":236,\"height\":238,\"size\":78903,\"isVideo\":false}],\"video\":null,\"video_audite_status\":0,\"location_label\":\"\",\"like_cnt\":0,\"create_time\":1752235182,\"i_have_liked\":false,\"topics\":[],\"type\":\"moment\"}",
        )
    MomentItemContent(item, previewState = rememberPreviewState())
}
