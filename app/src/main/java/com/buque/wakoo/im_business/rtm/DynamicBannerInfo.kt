package com.buque.wakoo.im_business.rtm

import com.buque.wakoo.bean.RichItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DynamicBannerInfo(
    val avatars: List<String> = emptyList(),
    @SerialName("rich_text") val richText: List<RichItem> = emptyList(),
    val background: BannerBackground? = null,
    // 业务代码
    @SerialName("business_code")
    val businessCode: String = "",
    val duration: Int = 8,
    val priority: Int = 0,
)

@Serializable
data class BannerBackground(
    val image: String,
    val width: Int,
    val height: Int,
)
