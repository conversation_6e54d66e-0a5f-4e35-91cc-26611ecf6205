package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.border
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.core.util.SizeFCompat
import com.buque.wakoo.app.OnAction
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.HintExpansionExtra
import com.buque.wakoo.im_business.message.MessageC2CUserScaffold
import com.buque.wakoo.im_business.message.MessageHint
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupItemWrapper
import com.buque.wakoo.ui.screens.chatgroup.chat.ChatGroupRoles
import com.buque.wakoo.ui.widget.image.NetworkImage
import com.buque.wakoo.ui.widget.media.previewer.MediaPreviewState
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerItem
import me.saket.telephoto.zoomable.rememberZoomablePeekOverlayState
import me.saket.telephoto.zoomable.zoomablePeekOverlay

data class ImageMsgEntry(
    val mediaItem: MediaViewerItem.Image,
    val width: Int,
    val height: Int,
    val user: IMUser,
    val hint: HintExpansionExtra? = null,
) : MsgUIEntry

@Composable
fun ImageMsgEntry.C2CContent(
    previewState: MediaPreviewState,
    message: UCInstanceMessage,
    onAction: IIMAction,
) {
    val density = LocalDensity.current
    val (widthDp, heightDp) =
        remember(width, height) {
            if (width > 0 && height > 0) {
                val size =
                    UIMessageUtils.resizeImage(
                        originalSize =
                            with(density) {
                                SizeFCompat(width.toDp().value, height.toDp().value)
                            },
                    )
                size.width.dp to size.height.dp
            } else {
                Dp.Unspecified to Dp.Unspecified
            }
        }

    MessageC2CUserScaffold(message, onAction, bottomWidget = {
        if (hint != null) {
            MessageHint(hint, message.isSelf, modifier = Modifier.padding(top = 5.dp))
        }
    }) {
        NetworkImage(
            data = mediaItem.localFilePath ?: mediaItem.placeholderImageUrl,
            modifier =
                with(previewState) {
                    Modifier
                        .border(1.5.dp, Color.White, RoundedCornerShape(10.dp))
                        .size(width = widthDp, height = heightDp)
                        .sizeIn(maxWidth = UIMessageUtils.MaxWidthDp.dp, maxHeight = UIMessageUtils.MaxHeightDp.dp)
                        .clip(RoundedCornerShape(10.dp))
                        .registerGridItem(mediaItem.key)
                        .zoomablePeekOverlay(rememberZoomablePeekOverlayState())
                        .combinedClickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null,
                            onLongClick = {
                                it.showMessageMenu(message)
                            },
                            onClick = {
                                onAction.onPreview(message)
                            },
                        )
                },
            contentScale = ContentScale.Crop,
            memoryCacheKey = mediaItem.localFilePath ?: mediaItem.placeholderImageUrl,
        )
    }
}

@Composable
fun ChatGroupMsgEntry.GroupImage.Content(
    previewState: MediaPreviewState,
    entry: UIMessageEntry,
    onAction: IIMAction,
    roleState: State<ChatGroupRoles>,
    onAvatarClick: OnAction = {},
) {
    val density = LocalDensity.current
    val (widthDp, heightDp) =
        remember(width, height) {
            if (width > 0 && height > 0) {
                val size =
                    UIMessageUtils.resizeImage(
                        originalSize =
                            with(density) {
                                SizeFCompat(width.toDp().value, height.toDp().value)
                            },
                    )
                size.width.dp to size.height.dp
            } else {
                Dp.Unspecified to Dp.Unspecified
            }
        }

    ChatGroupItemWrapper(entry, onAction, roleState, onAvatarClick) {
        NetworkImage(
            data = mediaItem.localFilePath ?: mediaItem.placeholderImageUrl,
            modifier =
                with(previewState) {
                    Modifier
                        .size(width = widthDp, height = heightDp)
                        .sizeIn(maxWidth = UIMessageUtils.MaxWidthDp.dp, maxHeight = UIMessageUtils.MaxHeightDp.dp)
                        .clip(RoundedCornerShape(10.dp))
                        .registerGridItem(mediaItem.key)
                        .zoomablePeekOverlay(rememberZoomablePeekOverlayState())
                        .noEffectClick {
                            onAction.onPreview(entry.message)
                        }
                },
            contentScale = ContentScale.Crop,
            memoryCacheKey = mediaItem.localFilePath ?: mediaItem.placeholderImageUrl,
        )
    }
}
