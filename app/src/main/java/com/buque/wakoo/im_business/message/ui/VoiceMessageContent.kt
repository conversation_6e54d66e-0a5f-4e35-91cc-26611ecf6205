package com.buque.wakoo.im_business.message.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.ui.widget.VoicePlayingAnimation
import com.buque.wakoo.ui.widget.media.manager.MediaPlayerManager
import com.buque.wakoo.ui.widget.media.manager.PlayMediaItem
import java.io.File

data class VoiceMessageContent(
    val message: UCInstanceMessage,
    val singleSide: Boolean = false,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        val voiceMessage = message as UCVoiceMessage
        val duration = message.duration

        var read by remember {
            mutableStateOf(message.isPlayed)
        }

        val playItem =
            remember(voiceMessage.url, voiceMessage.localPath) {
                if (!voiceMessage.localPath.isNullOrBlank() && File(voiceMessage.localPath).exists()) {
                    PlayMediaItem.prefixTagAudio(voiceMessage.localPath, "voiceMsg-${message.id}")
                } else {
                    PlayMediaItem.prefixTagAudio(voiceMessage.url, "voiceMsg-${message.id}")
                }
            }

        baseBox {
            bubbleBox {
                CompositionLocalProvider(
                    LocalLayoutDirection provides if (message.isSelf && !singleSide) LayoutDirection.Rtl else LayoutDirection.Ltr,
                ) {
                    Row(
                        modifier =
                            Modifier.noEffectClick {
                                MediaPlayerManager.toggle(playItem)
                            },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        VoicePlayingAnimation(
                            isPlaying = MediaPlayerManager.currentPlayingTag.value == playItem.tag,
                            modifier =
                                Modifier
                                    .size(20.dp)
                                    .rotate(if (message.isSelf) 180f else 0f),
                            iconColor = Color(0xff111111),
                        )
                        Spacer(modifier = Modifier.width(24.dp.plus(duration.times(1.2f).dp)))
                        Text(text = "$duration ″", fontSize = 14.sp)
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                }
            }

            if (!message.isSelf && !read) {
                Spacer(
                    modifier =
                        Modifier
                            .padding(start = 6.dp)
                            .size(6.dp)
                            .clip(CircleShape)
                            .background(Color(0xFFF76560)),
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewVoice() {
//    C2CVoiceContent(
//        messageItem = MessageExt.fakerMessage(
//            message = MessageExt.createVoiceMessage(Uri.parse("/"), 5)
//        ).toMessageItem(userForPreview)!!,
//        isDownloading = false,
//        isPlaying = false,
//        read = false
//    )
}
