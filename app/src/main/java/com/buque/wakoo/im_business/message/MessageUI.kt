package com.buque.wakoo.im_business.message

import android.graphics.Rect
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.toColorInt
import coil3.asImage
import coil3.compose.AsyncImagePainter
import coil3.compose.rememberAsyncImagePainter
import coil3.toBitmap
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.ChatBubble
import com.buque.wakoo.ext.LaunchOnceEffect
import com.buque.wakoo.ext.click
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.getExtraBoolean
import com.buque.wakoo.im.isFailure
import com.buque.wakoo.im.isSending
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.richtext.EntryRichText
import com.buque.wakoo.utils.ninepatch.NinePatchPainter
import com.buque.wakoo.utils.ninepatch.NinePathLoader
import com.buque.wakoo.utils.ninepatch.bitmapToNinePatchPainter
import com.google.accompanist.drawablepainter.DrawablePainter
import kotlinx.coroutines.launch

//region 消息脚手架 用户信息. 可以根据自己的需求定制在MessageContent中使用

/**
 * 消息UI脚手架
 *
 * @param message 消息实体
 * @param onAction 动作回调
 * @param edgeSpace 消息最后的留白距离
 * @param showReadStatus 是否显示发送状态
 * @param content 消息详细UI
 */
@Composable
fun MessageC2CUserScaffold(
    message: UCInstanceMessage,
    onAction: IIMAction,
    edgeSpace: Dp = 35.dp,
    showReadStatus: Boolean = false,
    bottomWidget: @Composable ColumnScope.() -> Unit = {},
    content: @Composable RowScope.(MenuActionScope) -> Unit,
) {
    val isSelf = message.isSelf
    val context = LocalContext.current
    CompositionLocalProvider(LocalLayoutDirection provides if (isSelf) LayoutDirection.Rtl else LayoutDirection.Ltr) {
        Row(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(end = edgeSpace),
        ) {
            val menuActionScope =
                remember(context) {
                    MenuActionScope(context)
                }
            Spacer(modifier = Modifier.width(16.dp))
            AvatarNetworkImage(message.requireUser(), size = 40.dp)
            Spacer(modifier = Modifier.width(8.dp))
            Column(modifier = Modifier.weight(1f, false)) {
                Row(verticalAlignment = Alignment.Bottom) {
                    Row(
                        modifier =
                            Modifier.combinedClickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null,
                                onLongClick = {
                                    menuActionScope.showMessageMenu(message)
                                },
                                onClick = {},
                            ),
                    ) {
                        CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                            content(menuActionScope)
                        }
                        MessageContextMenu(menuActionScope.menuListState)
                    }
                    MessageStatus(
                        message = message,
                        showReadStatus = showReadStatus && isSelf,
                        onAction = onAction,
                        modifier =
                            Modifier
                                .align(Alignment.Bottom)
                                .padding(start = 4.dp, bottom = 4.dp),
                    )
                }
                CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                    bottomWidget()
                }
            }
        }
    }
}

//endregion

//region 消息气泡皮肤

val UCInstanceMessage.bubbleColor: Color
    get() = if (isSelf) Color(0xFF84F288) else Color.White

val UCInstanceMessage.bubbleContentColor
    get() = Color(0xff111111)

val UCInstanceMessage.bubbleShape
    get() =
        RoundedCornerShape(
            topStart = if (!isSelf) 0.dp else 12.dp,
            topEnd = if (!isSelf) 12.dp else 0.dp,
            bottomEnd = 12.dp,
            bottomStart = 12.dp,
        )

@Stable
data class MessageTheme(
    val painter: Painter,
    val paddingValues: PaddingValues,
    val shape: Shape?,
    val contentColor: Color,
    val fontSize: TextUnit,
    val left: Boolean = true,
)

data class MessageSkin(
    val background: String,
    val paddingRect: Rect,
    val textColor: String,
)

//region 普通气泡

@Composable
inline fun RowMessageBubble(
    message: UCInstanceMessage,
    modifier: Modifier = Modifier,
    bubbleColor: Color = message.bubbleColor,
    bubbleContentColor: Color = message.bubbleContentColor,
    bubbleShape: Shape = message.bubbleShape,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    Row(
        modifier =
            modifier
                .background(bubbleColor, bubbleShape)
                .padding(horizontal = 16.dp, vertical = 9.5.dp),
        content = {
            CompositionLocalProvider(LocalContentColor provides bubbleContentColor) {
                content()
            }
        },
        verticalAlignment = Alignment.CenterVertically,
    )
}

@Composable
inline fun ColumnMessageBubble(
    message: UCInstanceMessage,
    modifier: Modifier = Modifier,
    bubbleColor: Color = message.bubbleColor,
    bubbleContentColor: Color = message.bubbleContentColor,
    bubbleShape: Shape = message.bubbleShape,
    crossinline content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier =
            modifier
                .background(bubbleColor, bubbleShape)
                .padding(horizontal = 16.dp, vertical = 9.5.dp),
        content = {
            CompositionLocalProvider(LocalContentColor provides bubbleContentColor) {
                content()
            }
        },
        horizontalAlignment = Alignment.CenterHorizontally,
    )
}
//endregion

@Composable
inline fun MessageThemeBubble(
    entry: UIMessageEntry? = null,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    bubbleColor: Color = entry?.message?.bubbleColor ?: Color(0xFF84F288),
    bubbleContentColor: Color = entry?.message?.bubbleContentColor ?: Color(0xff111111),
    bubbleShape: Shape = entry?.message?.bubbleShape ?: RoundedCornerShape(0.dp),
    crossinline content: @Composable RowScope.() -> Unit,
) {
    MessageThemeBubble(
        entry = entry,
        defaultMsgTheme =
            MessageTheme(
                painter = ColorPainter(bubbleColor),
                paddingValues = PaddingValues(horizontal = 16.dp, vertical = 9.5.dp),
                shape = bubbleShape,
                contentColor = bubbleContentColor,
                fontSize = 14.sp,
                left = entry?.isSelf ?: false,
            ),
        modifier = modifier,
        childModifier = childModifier,
        content = content,
    )
}

@Composable
inline fun MessageThemeBubble(
    entry: UIMessageEntry?,
    defaultMsgTheme: MessageTheme,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    val chatBubble =
        if (entry?.message is UCTextMessage) {
            entry.message.user?.chatBubble
        } else {
            null
        }
    MessageThemeBubble(chatBubble, defaultMsgTheme, modifier, childModifier, content)
}

@Composable
inline fun MessageThemeBubble(
    chatBubble: ChatBubble? = null,
    defaultMsgTheme: MessageTheme,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    val left = defaultMsgTheme.left
    var hasSkin = false
    val messageTheme =
        run {
            if (chatBubble != null) {
                val url =
                    if (left) {
                        chatBubble.leftImg
                    } else {
                        chatBubble.rightImg
                    }
                if (url.isNotEmpty()) {
                    LaunchOnceEffect {
                        NinePathLoader.recordChatBubble(chatBubble)
                    }
                    hasSkin = true
                    return@run rememberSkinMessageTheme(
                        MessageSkin(
                            url,
                            if (left) {
                                chatBubble.leftPadding
                            } else {
                                chatBubble.rightPadding
                            },
                            chatBubble.fontColor,
                        ),
                        defaultMsgTheme,
                    ).value
                }
            }
            defaultMsgTheme
        }

    Box(modifier) {
        MessageThemeBubble(childModifier, messageTheme, content)

        if (hasSkin) {
            val targetDensity = NinePathLoader.PIC_DENSITY_DPI

            val images = if (left) chatBubble?.startImages else chatBubble?.endImages

            for (i in 0..3) {
                images
                    ?.getOrNull(i)
                    ?.takeIf {
                        it.isNotEmpty()
                    }?.also { source ->
                        BubbleRoundImage(
                            source,
                            targetDensity,
                            Modifier.align(
                                if (i == 0) {
                                    Alignment.TopStart
                                } else if (i == 1) {
                                    Alignment.TopEnd
                                } else if (i == 2) {
                                    Alignment.BottomStart
                                } else {
                                    Alignment.BottomEnd
                                },
                            ),
                        )
                    }
            }
        }
    }
}

@Composable
inline fun MessageThemeBubble(
    modifier: Modifier,
    messageTheme: MessageTheme,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    Row(
        modifier =
            modifier
                .heightIn(min = 40.dp)
                .run {
                    val shape = messageTheme.shape
                    if (shape != null) {
                        clip(shape)
                    } else {
                        this
                    }
                }.run {
                    if (LocalInspectionMode.current) {
                        background((messageTheme.painter as ColorPainter).color)
                    } else {
                        paint(messageTheme.painter, contentScale = ContentScale.FillBounds)
                    }
                }.padding(messageTheme.paddingValues),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val mergedStyle =
            LocalTextStyle.current.merge(TextStyle(color = messageTheme.contentColor, fontSize = messageTheme.fontSize))
        CompositionLocalProvider(
            LocalContentColor provides messageTheme.contentColor,
            LocalTextStyle provides mergedStyle,
        ) {
            content()
        }
    }
}

//endregion

//region 消息状态UI
@Composable
fun MessageStatus(
    message: UCInstanceMessage,
    showReadStatus: Boolean,
    modifier: Modifier = Modifier,
    onAction: IIMAction,
) {
    if (LocalInspectionMode.current) {
        return
    }

    when {
        message.isSending -> {
            CircularProgressIndicator(
                modifier = modifier.size(16.dp),
                color = MaterialTheme.colorScheme.primary,
                strokeWidth = 2.dp,
            )
        }

        message.isFailure -> {
            Image(
                painter = painterResource(id = R.drawable.ic_send_msg_failure),
                contentDescription = null,
                modifier =
                    modifier
                        .size(16.dp)
                        .click {
                            onAction.onResendMessage(message)
                        },
            )
        }

        message.getExtraBoolean(UIMessageUtils.CONTENT_ILLEGAL, false) -> Unit

        showReadStatus && message.isC2CRead -> {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_msg_read),
                contentDescription = null,
                modifier = modifier.size(16.dp),
            )
        }

        showReadStatus && message.isSelf -> {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_msg_unread),
                contentDescription = null,
                modifier = modifier.size(16.dp),
            )
        }
    }
}

@Composable
fun MessageReminder(
    extra: MsgUIExtra,
    isSelf: Boolean,
    modifier: Modifier = Modifier,
) {
    val extra = extra?.expansionExtra?.hint
    if (extra?.hintVisible == true) {
        val hintRichList = extra.hintRichList
        if (hintRichList != null) {
            EntryRichText(
                rich = hintRichList,
                modifier = modifier,
//                color = if (isSelf) Color(0xFFFF5E8B) else Color(0xFFFF9300),
                color = Color(0xFFFF9300),
                fontSize = 11.sp,
                textAlign = if (isSelf) TextAlign.End else TextAlign.Start,
                lineHeight = 13.5.sp,
            )
        } else {
            val text = extra.hintText
            if (text != null) {
                Text(
                    text = text,
                    modifier = modifier,
//                    color = if (isSelf) Color(0xFFFF5E8B) else Color(0xFFFF9300),
                    color = Color(0xFFFF9300),
                    fontSize = 11.sp,
                    textAlign = if (isSelf) TextAlign.End else TextAlign.Start,
                    lineHeight = 13.5.sp,
                )
            }
        }
    }
}

@Composable
fun MessageHint(
    hint: HintExpansionExtra?,
    isSelf: Boolean,
    modifier: Modifier = Modifier,
) {
    if (hint?.hintVisible == true) {
        val hintRichList = hint.hintRichList
        if (hintRichList != null) {
            EntryRichText(
                rich = hintRichList,
                modifier = modifier,
                color = Color(0xFFFF9300),
                fontSize = 11.sp,
                textAlign = if (isSelf) TextAlign.End else TextAlign.Start,
                lineHeight = 13.5.sp,
            )
        } else {
            val text = hint.hintText
            if (text != null) {
                Text(
                    text = text,
                    modifier = modifier,
                    color = Color(0xFFFF9300),
                    fontSize = 11.sp,
                    textAlign = if (isSelf) TextAlign.End else TextAlign.Start,
                    lineHeight = 13.5.sp,
                )
            }
        }
    }
}

@Composable
fun rememberSkinMessageTheme(
    msgSkin: MessageSkin,
    defaultMsgTheme: MessageTheme,
): State<MessageTheme> {
    val context = LocalContext.current
    val density = LocalDensity.current
    val scope = rememberCoroutineScope()
    val result =
        remember(msgSkin) {
            val cache = NinePathLoader.getChatBubbleCache(msgSkin.background)
            if (cache != null) {
                mutableStateOf(cache.bitmapToNinePatchPainter(context, density).toMessageTheme(msgSkin, defaultMsgTheme))
            } else {
                mutableStateOf(defaultMsgTheme).also { state ->
                    scope.launch {
                        val ninePatch =
                            NinePathLoader
                                .load(
                                    msgSkin.background,
                                    msgSkin.paddingRect,
                                )?.bitmapToNinePatchPainter(context, density)
                        if (ninePatch != null) {
                            state.value = ninePatch.toMessageTheme(msgSkin, defaultMsgTheme)
                        }
                    }
                }
            }
        }
    return result
}

private fun NinePatchPainter.toMessageTheme(
    msgSkin: MessageSkin,
    defaultMsgTheme: MessageTheme,
) = MessageTheme(
    DrawablePainter(drawable),
    padding,
    null,
    try {
        Color(msgSkin.textColor.toColorInt())
    } catch (e: Throwable) {
        defaultMsgTheme.contentColor
    },
    defaultMsgTheme.fontSize,
)
//endregion

@Composable
fun BubbleRoundImage(
    url: String,
    targetDensity: Int,
    modifier: Modifier = Modifier,
) {
    val painter =
        rememberAsyncImagePainter(
            model = url,
            transform = { state ->
                if (state is AsyncImagePainter.State.Success) {
                    val result = state.result
                    val bmp =
                        result.image.toBitmap().apply {
                            density = targetDensity
                        }
                    return@rememberAsyncImagePainter AsyncImagePainter.State.Success(
                        state.painter,
                        result.copy(image = bmp.asImage()),
                    )
                }
                state
            },
        )
    Image(
        painter = painter,
        contentDescription = null,
        modifier = modifier,
        contentScale = ContentScale.Fit,
    )
}
