package com.buque.wakoo.im_business.message.types

import com.buque.wakoo.bean.GiftWrapper
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.IMUser
import com.buque.wakoo.im.inter.IUCCustomMessage
import com.buque.wakoo.im.inter.IUCMessage

data class UCGiftMessage constructor(
    val rawInstanceMessage: UCCustomMessage,
    val gift: GiftWrapper,
    override val base: UCMessage = rawInstanceMessage.base,
) : UCInstanceMessage,
    IUCMessage by rawInstanceMessage,
    IUCCustomMessage by rawInstanceMessage {
    override val user: IMUser? = IMUser.fromResponse(gift.userResponse)

    override fun getSummaryString(): String = "礼物消息, id: ${gift.gift.id}, 名称: ${gift.gift.name}"
}
