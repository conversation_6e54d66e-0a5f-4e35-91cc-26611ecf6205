package com.buque.wakoo.im_business.message.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.text.HtmlCompat
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.ui.widget.richtext.toAnnotatedString

/**
 * 单聊文本消息
 */
class SystemMessageContent(
    val message: UCCustomMessage,
    onAction: IIMAction = IIMAction.EMPTY,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 25.dp, vertical = 11.dp),
            contentAlignment = Alignment.Center,
        ) {
            val htmlText =
                remember(message) {
                    val content =
                        message.getJsonString("rich_text").takeIsNotEmpty()
                            ?: message.getJsonString("text").takeIsNotEmpty()
                            ?: message.getContentText()
                    try {
                        HtmlCompat.fromHtml(content.orEmpty(), HtmlCompat.FROM_HTML_OPTION_USE_CSS_COLORS).toAnnotatedString()
                    } catch (e: Exception) {
                        buildAnnotatedString { append(content.orEmpty()) }
                    }
                }
            Text(
                text = htmlText,
                modifier =
                    Modifier
                        .clip(RoundedCornerShape(8.dp))
                        .background(Color(0xFFEBEEF4))
                        .padding(8.dp),
                color = Color(0xFF999999),
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
            )
        }
    }
}
