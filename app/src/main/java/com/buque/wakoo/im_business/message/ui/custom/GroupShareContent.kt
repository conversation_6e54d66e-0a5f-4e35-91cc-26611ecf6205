package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.chatgroup.WakooChatGroup
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.manager.localized
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.ui.widget.ButtonStyles
import com.buque.wakoo.ui.widget.SolidButton
import com.buque.wakoo.ui.widget.image.NetworkImage
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.jsonPrimitive

data class GroupShareContent(
    val group: WakooChatGroup,
    val isSelf: Boolean,
    val receiverExtra: String,
    val onClick: () -> Unit,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        baseBox {
            val isApply =
                remember(receiverExtra) {
                    if (receiverExtra.isNotBlank()) {
                        val receiverJsonObj = Json.decodeFromString<JsonObject>(receiverExtra)
                        receiverJsonObj.getOrNull("is_apply")?.jsonPrimitive?.booleanOrNull ?: false
                    } else {
                        false
                    }
                }

            Row(
                modifier =
                    Modifier
                        .widthIn(max = 220.dp)
                        .background(
                            color = Color.White,
                            shape = RoundedCornerShape(15.dp),
                        ).padding(horizontal = 8.dp),
            ) {
                NetworkImage(
                    group.avatarUrl,
                    modifier =
                        Modifier
                            .padding(vertical = 16.dp)
                            .padding(end = 8.dp)
                            .size(48.dp)
                            .clip(CircleShape),
                )

                Column(
                    horizontalAlignment = Alignment.End,
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    Column(
                        verticalArrangement = Arrangement.SpaceAround,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .padding(top = 8.dp)
                                .heightIn(64.dp),
                    ) {
                        Text(
                            "邀请你加入我的群组".localized,
                            style = TextStyle(fontSize = 14.sp, color = Color(0xff111111), lineHeight = 14.sp),
                        )
                        Text(
                            text = "群组昵称: %s".localizedFormat(group.name),
                            style = TextStyle(fontSize = 12.sp, color = Color(0xff999999), lineHeight = 16.sp),
                            overflow = TextOverflow.Ellipsis,
                            maxLines = 2,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }

                    if (!isSelf) {
                        HorizontalDivider(color = Color(0xffe5e5e5), modifier = Modifier.padding(vertical = 8.dp))
                        SolidButton(
                            text = if (isApply) "已申请".localized else "申请加入".localized,
                            onClick = onClick,
                            height = 32.dp,
                            textColor = if (isApply) Color(0xff999999) else Color(0xFF111111),
                            backgroundColor = if (isApply) Color(0xffE9EAEF) else Color(0xFF66FE6B),
                            fontSize = 14.sp,
                            config = ButtonStyles.Solid.copy(minWidth = 72.dp),
                            paddingValues = PaddingValues(horizontal = 12.dp),
                        )
                        Spacer(Modifier.height(8.dp))
                    }
                }
            }
        }
    }
}
