package com.buque.wakoo.im_business

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.buque.wakoo.WakooApplication
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.noEffectClick
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.getExtraBoolean
import com.buque.wakoo.im.getExtraString
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.manager.localized
import com.buque.wakoo.navigation.AppNavKey
import com.buque.wakoo.navigation.HomeTabRoute
import com.buque.wakoo.navigation.LocalAppNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.screens.home.HomeBottomNavCtrlKey
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage
import com.buque.wakoo.ui.widget.richtext.toAnnotatedString
import com.buque.wakoo.utils.Preference
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlin.math.roundToInt



private data class MessageNotification(
    val user: User,
    val name: String,
    val avatar: String,
    val applyBlurAvatar: Boolean,
    val desc: AnnotatedString,
    val extra: String?,
)


object GlobalNotification {

    private val callback = object : IMCompatListener {
        override fun onMessageExpansionUpdate(message: UCInstanceMessage, add: Boolean, expansions: Map<String, String>) {
            if (!add || !message.isC2CMsg || message.isSelf) {
                return
            }
            if (expansions["in_app_notify"]?.toBoolean() != true) {
                return
            }

            val user = message.user ?: return

            val content = MessageNotification(
                user = user,
                name = expansions["ian_mask_nickname"]?.takeIsNotEmpty()
                    ?: message.getExtraString("ian_mask_nickname").ifEmpty { user.name },
                avatar = user.avatar,
                applyBlurAvatar = expansions["ian_mask_avatar"]?.toBoolean()
                    ?: message.getExtraBoolean("ian_mask_avatar", false),
                desc = buildAnnotatedString {
                    (expansions["ian_preview_msg"]?.takeIsNotEmpty() ?: message.getExtraValue("ian_preview_msg"))?.also {
                        append(it)
                    } ?: run {
                        append(
                            UIMessageUtils.getMessageSummary(
                                context = WakooApplication.instance,
                                hasUnread = false,
                                message = message,
                            ).toAnnotatedString()
                        )
                    }
                },
                extra = expansions["ian_action_hint"]?.takeIsNotEmpty() ?: message.getExtraValue("ian_action_hint")
            )

            appCoroutineScope.launch {
                showCupidMessageNotification(content)
            }
        }
    }

    fun register() {
        IMCompatCore.addIMListener(callback)
    }

    fun unregister() {
        IMCompatCore.removeIMListener(callback)
    }
}


private fun showCupidMessageNotification(content: MessageNotification) {
    if (!enableAppNotification) {
        return
    }
    cupidNotifyFlow.update {
        if (it != null) {
            MsgNotify.Replace(it.content, MsgNotify.Normal(content))
        } else {
            MsgNotify.Normal(content)
        }
    }
}

private sealed class MsgNotify(val content: MessageNotification) {

    class Normal(content: MessageNotification) : MsgNotify(content)

    class Replace(from: MessageNotification, val to: Normal) : MsgNotify(from)
}

private val cupidNotifyFlow = MutableStateFlow<MsgNotify?>(null)

sealed interface IVisible {

    interface Animating : IVisible

    interface Final : IVisible

    object Enter : Animating

    object Exit : Animating

    object Show : Final

    object Hide : Final

    data object Idea : IVisible

    val isVisible: Boolean
        get() = this == Enter || this == Show
}

@Composable
fun rememberDialogState(onDismiss: ((DialogState) -> Unit)? = null): DialogState = remember {
    DialogState(onDismiss)
}.apply {
    onDismissState.value = onDismiss
}

class DialogState(updatedOnDismiss: ((DialogState) -> Unit)? = null) {

    var onDismissState = mutableStateOf(updatedOnDismiss)

    var dialogStatus by mutableStateOf<IVisible>(IVisible.Idea)

    internal val canShowing: Boolean
        get() = dialogStatus != IVisible.Hide

    internal val isVisible: Boolean
        get() = dialogStatus.isVisible

    fun close() {
        if (dialogStatus == IVisible.Idea) {
            onDismissState.value?.invoke(this) ?: dismiss()
        } else if (dialogStatus != IVisible.Exit && dialogStatus != IVisible.Hide) {
            dialogStatus = IVisible.Exit
        }
    }

    fun dismiss() {
        dialogStatus = IVisible.Hide
    }

    fun reset() {
        dialogStatus = IVisible.Idea
    }

    internal fun enter() {
        if (dialogStatus == IVisible.Idea) {
            dialogStatus = IVisible.Enter
        }
    }

    internal fun toFinalStatus(state: MutableTransitionState<Boolean>) {
        if (dialogStatus == IVisible.Exit) {
            if (state.isIdle && !state.currentState) {
                onDismissState.value?.invoke(this) ?: dismiss()
            }
        } else if (dialogStatus == IVisible.Enter) {
            if (state.isIdle && state.currentState) {
                dialogStatus = IVisible.Show
            }
        }
    }
}

@Composable
fun GlobalMessageNotification() {

    val navController = LocalAppNavController.root

    val curRouteKey = navController.getLastNavKey()
    val curNotifyMsg = cupidNotifyFlow.collectAsStateWithLifecycle().value ?: return
    if (!enableAppNotification) {
        return
    }
    val fromUser by remember {
        derivedStateOf {
            curNotifyMsg.content.user
        }
    }

    val dialogState = rememberDialogState {
        it.dismiss()
        if (!curRouteKey.isNotificationEnabled(fromUser.id)) {
            cupidNotifyFlow.value = null
        } else if (curNotifyMsg is MsgNotify.Replace) {
            cupidNotifyFlow.value = curNotifyMsg.to
            it.reset()
        } else {
            cupidNotifyFlow.value = null
        }
    }

    if (curNotifyMsg is MsgNotify.Replace) {
        LaunchedEffect(Unit) {
            dialogState.close()
        }
    }

    val msgContent = curNotifyMsg.content

    LaunchedEffect(key1 = curRouteKey) {
        snapshotFlow {
            curRouteKey.isNotificationEnabled(fromUser.id)
        }.filter { !it }.collectLatest {
            dialogState.close()
        }
    }

    LaunchedEffect(key1 = msgContent) {
        delay(5000)
        dialogState.close()
    }

    AnimatedPopup(dialogState = dialogState, alignment = Alignment.TopCenter) {
        MessageNotificationLayout(msgContent, {
            dialogState.close()
            navController.push(Route.Chat(msgContent.user.toBasic()))
        }) {
            dialogState.close()
        }
    }
}

@Composable
private fun AnimatedPopup(
    dialogState: DialogState,
    alignment: Alignment = Alignment.TopStart,
    content: @Composable () -> Unit,
) {
    if (!dialogState.canShowing) {
        return
    }

    Popup(
        alignment = alignment, onDismissRequest = {
            dialogState.close()
        }, properties = PopupProperties(
            focusable = false,
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        )
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(), contentAlignment = alignment
        ) {
            val state = remember {
                MutableTransitionState(false)
            }.apply {
                targetState = dialogState.isVisible
            }

            AnimatedVisibility(
                visibleState = state,
                enter = slideInVertically(initialOffsetY = { -it }),
                exit = fadeOut() + slideOutVertically(targetOffsetY = { -it })
            ) {
                content()
            }

            dialogState.toFinalStatus(state)
        }

        SideEffect {
            dialogState.enter()
        }
    }
}


@Composable
private fun MessageNotificationLayout(
    content: MessageNotification,
    onClick: () -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    var offsetY by remember { mutableFloatStateOf(0f) }
    Row(
        modifier = Modifier
            .offset { IntOffset(0, offsetY.roundToInt()) }
            .statusBarsPadding()
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .aspectRatio(4.2875f)
            .graphicsLayer {
                shadowElevation = 4.dp.toPx()
                shape = RoundedCornerShape(12.dp)
            }
            .clip(RoundedCornerShape(12.dp))
            .background(
                brush = Brush.horizontalGradient(
                    listOf(
                        Color(0xffEBFFD5),
                        Color(0xffCAFFEA)
                    )
                )
            )
            .draggable(orientation = Orientation.Vertical, state = rememberDraggableState { delta ->
                if (offsetY <= 0) {
                    offsetY += delta
                    if (offsetY > 0) {
                        offsetY = 0f
                    }
                }
            }, onDragStopped = {
                if (offsetY != 0f) {
                    onDismiss()
                }
            })
            .noEffectClick(onClick = onClick), verticalAlignment = Alignment.CenterVertically
    ) {
        SizeWidth(12.dp)

        AvatarNetworkImage(content.user, size = (44.dp), enabled = false)

        SizeWidth(10.dp)

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = content.name,
                fontSize = 15.sp,
                color = Color(0xFF1D2129),
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(3.dp))

            Text(
                text = content.desc,
                fontSize = 13.sp,
                color = Color(0xFF1D2129),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
        Column(modifier = Modifier.padding(horizontal = 28.dp)) {
//            Text(
//                text = "回复",
//                color = Color(0xFFFF5E8B),
//                fontSize = 14.sp,
//                fontWeight = FontWeight.Medium
//            )
            GradientButton(text = "回复".localized, onClick, fontSize = 12.sp, modifier = Modifier.sizeIn(56.dp, 22.dp))

//            if (content.extra != null) {
//                Row(
//                    modifier = Modifier.padding(top = 5.dp),
//                    verticalAlignment = Alignment.CenterVertically
//                ) {
//                    Text(
//                        text = content.extra, fontSize = 12.sp, color = Color(0xFFFF5E8B)
//                    )
//                    Spacer(modifier = Modifier.width(1.dp))
//                    Image(
//                        painter = painterResource(id = R.drawable.ic_cpd_diamond),
//                        contentDescription = null,
//                        modifier = Modifier.size(12.dp)
//                    )
//                }
//            }
        }
    }
}

@Preview
@Composable
private fun PreviewMessageNotificationLayout() {
    MessageNotificationLayout(
        MessageNotification(
            user = BasicUser.sampleBoy,
            name = "哈哈哈大地方",
            avatar = "",
            applyBlurAvatar = false,
            desc = buildAnnotatedString { append("哈哈哈大地方") },
            extra = "100"
        ), {}) {}
}

private fun AppNavKey?.isNotificationEnabled(msgFromId: String): Boolean {
    return when (this) {
        is Route.Chat -> {//如果已经是这个用户的私聊
            this.user.id != msgFromId
        }
//        is Route.LiveRoom->{//语音房的私聊dialog
//
//        }
        is Route.Home -> {//主页的消息列表时不显示
            LocalAppNavController[HomeBottomNavCtrlKey]?.getLastNavKey() !is HomeTabRoute.Message
        }

        else -> true
    }
}

var enableAppNotification by Preference("app_notification", true)