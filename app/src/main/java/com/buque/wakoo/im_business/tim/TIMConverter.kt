package com.buque.wakoo.im_business.tim

import com.buque.wakoo.bean.GiftWrapper
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IConverter
import com.buque.wakoo.im.inter.UCConversation
import com.buque.wakoo.im.utils._isAppRelease
import com.buque.wakoo.im.utils.asInstance
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.ImageElem
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCUnknownMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.tencent.imsdk.v2.V2TIMConversation
import com.tencent.imsdk.v2.V2TIMImageElem
import com.tencent.imsdk.v2.V2TIMManager
import com.tencent.imsdk.v2.V2TIMMessage
import com.tencent.imsdk.v2.V2TIMValueCallback
import kotlinx.coroutines.runBlocking
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

object TIMConverter : IConverter<V2TIMMessage, V2TIMConversation, TIMMessage, TIMConversation> {
    //region 转换逻辑
    fun List<V2TIMConversation>.toUCConversationListByTIM(): List<UCConversation> {
        return mapNotNull { conversation ->
            if (!conversation.checkValid()) {
                return@mapNotNull null
            }
            if (conversation.type == V2TIMConversation.V2TIM_GROUP) {
                if (conversation.groupType == V2TIMManager.GROUP_TYPE_PUBLIC) { // 群组会话中的public类型
                    parseConvFromOrigin(conversation)
                } else {
                    null
                }
            } else { // C2C会话
                parseConvFromOrigin(conversation)
            }
        }
    }

    fun TIMMessage.parseCustomMessage(customMessage: UCCustomMessage): UCInstanceMessage =
        try {
            when (customMessage.cmd) {
                "" -> {
                    UCUnknownMessage(base = this)
                }

                IMEvent.TXT_MSG -> {
                    UCTextMessage(base = this, text = customMessage.getContentText().orEmpty())
                }

                IMEvent.GIVE_GIFT, IMEvent.GIVE_LUCKY_GIFT -> {
                    val gift = customMessage.parseDataJson<GiftWrapper>()
                    if (gift != null) {
                        if (customMessage.cmd == IMEvent.GIVE_LUCKY_GIFT) {
                            gift.giftType = 2
                        }
                        UCGiftMessage(rawInstanceMessage = customMessage, gift = gift)
                    } else {
                        UCUnknownMessage(base = this)
                    }
                }

                IMEvent.GIVE_GIFT_COMBO_FINISHED -> {
//                    if (AppUserPartition.current.isCupid) {
//                        val gift = customMessage.parseDataJson<GiftWrapper>()
//                        if (gift != null) {
//                            gift.giftType = 6
//                            UCGiftMessage(rawInstanceMessage = customMessage, gift = gift)
//                        } else {
//                            UCUnknownMessage(base = this)
//                        }
//                    } else {
//                        UCUnknownMessage(base = this)
//                    }
                    UCUnknownMessage(base = this)
                }

                else -> {
                    customMessage
                }
            }
        } catch (e: Exception) {
            customMessage
        }

    fun TIMMessage.toUCInstanceMessage(): UCInstanceMessage {
        return try {
            return when (rawMessage.elemType) {
                V2TIMMessage.V2TIM_ELEM_TYPE_TEXT -> {
                    UCTextMessage(
                        base = this,
                        text = rawMessage.textElem.text.orEmpty(),
                    )
                }

                V2TIMMessage.V2TIM_ELEM_TYPE_SOUND -> {
                    val soundElem = rawMessage.soundElem
                    UCVoiceMessage(
                        base = this,
                        duration = soundElem.duration,
                        url =
                            runBlocking {
                                suspendCoroutine {
                                    // 不知道sdk为什么要设计成一个异步回调，看sdk完全就可以写成一个简单同步方法，不是一个耗时操作
                                    // 目前这么写没任何问题，没有挂起
                                    soundElem.getUrl(
                                        object : V2TIMValueCallback<String?> {
                                            override fun onSuccess(result: String?) {
                                                it.resume(result.orEmpty())
                                            }

                                            override fun onError(
                                                code: Int,
                                                desc: String?,
                                            ) {
                                                it.resume("")
                                            }
                                        },
                                    )
                                }
                            },
                        localPath = soundElem.path,
                        uuid = soundElem.uuid,
                    )
                }

                V2TIMMessage.V2TIM_ELEM_TYPE_IMAGE -> {
                    val imageElem = rawMessage.imageElem

                    var thumbElem: ImageElem? = null
                    var largeElem: ImageElem? = null
                    var originElem: ImageElem? = null

                    val fallbackImageSize = getImageSize()

                    for (item in imageElem.imageList) {
                        val width =
                            if (item.width == 0) {
                                fallbackImageSize?.width ?: 0
                            } else {
                                item.width
                            }

                        val height =
                            if (item.height == 0) {
                                fallbackImageSize?.height ?: 0
                            } else {
                                item.height
                            }

                        when (item.type) {
                            V2TIMImageElem.V2TIM_IMAGE_TYPE_THUMB -> {
                                thumbElem =
                                    ImageElem(
                                        width = width,
                                        height = height,
                                        source = item.url.orEmpty(),
                                        uuid = item.uuid,
                                    )
                            }

                            V2TIMImageElem.V2TIM_IMAGE_TYPE_LARGE -> {
                                largeElem =
                                    ImageElem(
                                        width = width,
                                        height = height,
                                        source = item.url.orEmpty(),
                                        uuid = item.uuid,
                                    )
                            }

                            V2TIMImageElem.V2TIM_IMAGE_TYPE_ORIGIN -> {
                                originElem =
                                    ImageElem(
                                        width = width,
                                        height = height,
                                        source = item.url.orEmpty(),
                                        uuid = item.uuid,
                                    )
                            }
                        }
                    }

                    UCImageMessage(
                        base = this,
                        thumbElem = thumbElem,
                        largeElem = largeElem,
                        originElem = originElem,
                        localElem =
                            imageElem.path?.takeIf { File(it).exists() }?.let {
                                ImageElem(
                                    width = fallbackImageSize?.width ?: 0,
                                    height = fallbackImageSize?.height ?: 0,
                                    source = it,
                                    uuid = it,
                                )
                            },
                    )
                }

                V2TIMMessage.V2TIM_ELEM_TYPE_CUSTOM -> {
                    parseCustomMessage(UCCustomMessage(base = this, String(rawMessage.customElem.data)))
                }

                else -> {
                    UCUnknownMessage(base = this)
                }
            }
        } catch (e: Exception) {
            if (_isAppRelease) {
                UCUnknownMessage(base = this)
            } else {
                throw e
            }
        }
    }

    override fun parseConvFromOrigin(conversation: V2TIMConversation): TIMConversation = TIMConversation(conversation)

    override fun convertToConversation(conversation: UCConversation?): V2TIMConversation? {
        if (conversation is TIMConversation) {
            return conversation.rawConversation
        }
        return null
    }

    override fun convertToMessage(message: UCMessage?): V2TIMMessage? = message?.asInstance<TIMMessage>()?.rawMessage

    override fun parseMsgFromOrigin(message: V2TIMMessage): TIMMessage = TIMMessage(message)

    private const val C2C_PREFIX = "c2c_"

    private const val GROUP_PREFIX = "group_"

    fun String.id2ConvId(type: ConversationType): String {
        val id = this
        return if (type == ConversationType.C2C) {
            if (id.startsWith(C2C_PREFIX)) {
                id
            } else {
                "$C2C_PREFIX$id"
            }
        } else {
            if (id.startsWith(GROUP_PREFIX)) {
                id
            } else {
                "$GROUP_PREFIX$id"
            }
        }
    }

    fun String.convId2Id(): String {
        val convId = this
        return when {
            convId.startsWith(C2C_PREFIX) -> convId.substring(C2C_PREFIX.length)
            convId.startsWith(GROUP_PREFIX) -> convId.substring(GROUP_PREFIX.length)
            else -> convId
        }
    }
}
