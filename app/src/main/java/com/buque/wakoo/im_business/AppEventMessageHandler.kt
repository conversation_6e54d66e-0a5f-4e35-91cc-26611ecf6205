package com.buque.wakoo.im_business

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.navigation.RootNavController
import com.buque.wakoo.navigation.Route
import com.buque.wakoo.ui.dialog.EnterPrivateRoomNotificationDialogContent
import com.buque.wakoo.ui.dialog.InviteToPrivateRoomDialogContent
import com.buque.wakoo.ui.dialog.NewFlowRecommendPanel
import com.buque.wakoo.ui.dialog.UserTaskFinishedDialog
import com.buque.wakoo.ui.screens.japan.boost.AwardType
import com.buque.wakoo.ui.screens.japan.boost.MissionCompleteContent
import com.buque.wakoo.ui.screens.liveroom.screen.GuideInfo
import com.buque.wakoo.utils.eventBus.EventBus
import com.buque.wakoo.utils.eventBus.tryEasyPostDialog
import com.buque.wakoo.utils.eventBus.tryToLink

/**
 * 全局app信令处理
 */
class AppEventMessageHandler(
    val controller: RootNavController,
) : IMCompatListener {
    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        when (message.cmd) {
            IMEvent.JAPAN_COMMON_BONUS_POPUP -> {
                val json = message.customJson
                val desc = json.parseValue<String>("desc").orEmpty()
                val btnText = json.parseValue<String>("btn_text").orEmpty()
                val type = json.parseValue<Int>("account_type") ?: AwardType.inc
                val count = json.parseValue<Int>("count") ?: 0
                val awardType = if (type == 21) AwardType.dia else AwardType.inc
                EventBus.tryEasyPostDialog {
                    MissionCompleteContent(title = "+$count", desc, awardType, btnText) {
                        dismiss()
                    }
                }
            }

            IMEvent.MEMBER_JOIN -> {
                // 自己加入群组，更新userInfo
                if (message.user?.sIsSelf == true) {
                    UserManager.refreshSelfUserInfo()
                }
            }

            IMEvent.PRIVATECHAT_RECOMMEND_POPUP -> {
                if (offline) {
                    return
                }
                val json = message.customJson
                val cityLabel = json.parseValue("city_label", "")
                val user = json.parseValue<BasicUser>("user")
                if (user != null) {
                    EventBus.tryEasyPostDialog {
                        NewFlowRecommendPanel(user, cityLabel, {
                            dismiss()
                        }, {
                            dismiss()
                            controller.push(Route.Chat(user.toBasic()))
                        })
                    }
                }
            }

//            MsgEventCmd.FIRSTCLASS_WELCOME_MESSAGE -> {
//                val data = message.parseDataJson<FirstClassNotificationBean>() ?: return
//                showComposeDialog { dialog ->
//                    FirstClassWelcomeWidget(data) {
//                        dialog.dismiss()
//                    }
//                }
//            }
//
//            MsgEventCmd.FIRSTCLASS_ACTIVE_MESSAGE -> {
//                val data = message.parseDataJson<FirstClassNotificationBean>() ?: return
//                showComposeDialog { dialog ->
//                    FirstClassActiveWidget(data) {
//                        dialog.dismiss()
//                    }
//                }
//            }

            IMEvent.PRIVATE_ROOM_GUIDE_POPUP -> {
                message.parseDataJson<GuideInfo>()?.also {
                    controller.push(Route.InviteToPrivateRoom(it, false))
                }
            }

            IMEvent.PRIVATE_ROOM_INVITE_POPUP -> {
                val roomId = message.getJsonString("room_id") ?: return
                val user = message.getJsonValue<BasicUser>("inviter") ?: return
                val hint = message.getJsonValue<String>("invitee_hint", "")
                val isFree = message.getJsonValue<Boolean>("can_free_chat") ?: false
                EventBus.tryEasyPostDialog {
                    InviteToPrivateRoomDialogContent(roomId, user, hint, isFree)
                }
            }

            IMEvent.CP_INTER_PRIVATE_ROOM -> {
                val roomId = message.getJsonInt("private_room_id")?.toString() ?: return
                val user =
                    (
                        message.getJsonValue<BasicUser>("newbie_user")?.takeIf { !it.sIsSelf }
                            ?: message.getJsonValue<BasicUser>("service_user")?.takeIf { !it.sIsSelf }
                    ) ?: return

                EventBus.tryEasyPostDialog {
                    EnterPrivateRoomNotificationDialogContent(roomId, user)
                }
            }

            IMEvent.COMMON_H5_DIALOG_2 -> {
//                "{\"cmd\": \"broadcaster_task_finished_popup\", \"data\": {\"user\": {\"userid\": 4847, \"public_id\": \"107233\", \"nickname\": \"\\u534e\\u8bed\\u533a0001\\u5973\\u7684\\u80e1\\u561f\\u561f\\u561f\\u561f\\u51fa\\u561f\\u561f\\u561f\\u561f\\u561f\", \"avatar_url\": \"https://s.test.wakooclub.com/aaceLj?x-oss-process=image/format,webp\", \"gender\": 2, \"age\": 18, \"height\": 0, \"avatar_frame\": \"\", \"medal\": {\"icon\": \"https://s.wakooclub.com/aabOPn\", \"width\": 72, \"height\": 24, \"product_series\": 0}, \"medal_list\": [{\"icon\": \"https://s.wakooclub.com/aabOPn\", \"width\": 72, \"height\": 24, \"product_series\": 0}], \"level\": 50, \"country_flag\": \"https://media.wakooclub.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\", \"exp_level_info\": {\"charm_level\": 50, \"wealth_level\": 50}, \"have_certified\": true}, \"title\": \"\\u606d\\u559c\\u804a\\u5929\\u4efb\\u52a1\\u5b8c\\u6210\", \"desc\": \"\\u83b7\\u5f97100\\u79ef\\u5206\\u5956\\u52b1\", \"btn_text\": \"\\u9886\\u53d6\\u73b0\\u91d1\", \"link\": \"https://apitest.wakooclub.com/h5/auth/my_bonus\", \"rich_desc\": [{\"type\": 1, \"client_jump_link\": \"\", \"client_action_type\": \"\", \"ios_jump_link\": \"\", \"android_jump_link\": \"\", \"rich_text\": \"<font color='#061C20'>\\u83b7\\u5f97</font>\"}, {\"type\": 1, \"client_jump_link\": \"\", \"client_action_type\": \"\", \"ios_jump_link\": \"\", \"android_jump_link\": \"\", \"rich_text\": \"<font color='#FF2B64'>100</font>\"}, {\"type\": 1, \"client_jump_link\": \"\", \"client_action_type\": \"\", \"ios_jump_link\": \"\", \"android_jump_link\": \"\", \"rich_text\": \"<font color='#061C20'>\\u79ef\\u5206\\u5956\\u52b1!</font>\"}]}}"
                val title = message.getJsonString("title") ?: return
                val desc = message.getJsonString("desc") ?: return
                val btn_text = message.getJsonString("btn_text") ?: return
                val link = message.getJsonString("link") ?: return
                val richItems = message.getJsonValue<List<RichItem>>("rich_desc", listOf())

                EventBus.tryEasyPostDialog {
                    UserTaskFinishedDialog(title, desc, btn_text, richItems) {
                        link.tryToLink()
                        dismiss()
                    }
                }
            }

            else -> Unit
        }
    }

    private fun onRecvRTMGLobalMessage(message: UCCustomMessage) {
    }

    companion object {
        /**
         * 模拟主播任务完成弹窗
         */
        fun mockDialog() {
            val richItems =
                AppJson.decodeFromString<List<RichItem>>(
                    "[{\"type\": 1, \"client_jump_link\": \"\", \"client_action_type\": \"\", \"ios_jump_link\": \"\", \"android_jump_link\": \"\", \"rich_text\": \"<font color='#061C20'>\\u83b7\\u5f97</font>\"}, {\"type\": 1, \"client_jump_link\": \"\", \"client_action_type\": \"\", \"ios_jump_link\": \"\", \"android_jump_link\": \"\", \"rich_text\": \"<font color='#FF2B64'>100</font>\"}, {\"type\": 1, \"client_jump_link\": \"\", \"client_action_type\": \"\", \"ios_jump_link\": \"\", \"android_jump_link\": \"\", \"rich_text\": \"<font color='#061C20'>\\u79ef\\u5206\\u5956\\u52b1!</font>\"}]",
                )
            val btnText = "测试按钮"
            val title = "标题"
            val desc = "描述"
            val link = "https://apitest.wakooclub.com/h5/auth/my_bonus"
            EventBus.tryEasyPostDialog {
                UserTaskFinishedDialog(title, desc, btnText, richItems) {
                    link.tryToLink()
                    dismiss()
                }
            }
        }
    }
}
