package com.buque.wakoo.im_business.message.ui.entry

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.manager.LiveRoomManager
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.SolidButton

data class VoiceCallEventEntry(
    val id: String,
    val content: String,
    val buttonText: String?,
) : MsgUIEntry

@Composable
fun VoiceCallEventEntry.C2CContent(modifier: Modifier = Modifier) {
    Column(
        modifier =
            modifier
                .padding(horizontal = 40.dp)
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(
                    brush =
                        Brush.verticalGradient(
                            0f to Color(0xFFFFEEF5),
                            1f to Color(0xFFFFFFFF),
                        ),
                ).border(0.5.dp, Color(0xFFFFD2E2), RoundedCornerShape(12.dp))
                .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        Text(
            text = content,
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFFFE669E),
            textAlign = TextAlign.Center,
        )

        if (!buttonText.isNullOrBlank()) {
            SolidButton(
                text = buttonText,
                backgroundColor = Color(0xFFFE669E),
                textColor = WakooWhite,
                fontSize = 14.sp,
                minWidth = 136.dp,
                height = 32.dp,
                onClick = {
                    LiveRoomManager.joinRoom(roomId = id, isPrivateRoom = true)
                },
            )
        }
    }
}

@Preview
@Composable
private fun PreviewVoiceCallEventEntryC2cContent() {
    VoiceCallEventEntry(
        "",
        "对方挂断了语音,本次通话已结束点击下方按钮可继续聊天",
        "继续聊天",
    ).C2CContent()
}
