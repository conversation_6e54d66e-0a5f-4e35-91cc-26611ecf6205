package com.buque.wakoo.im_business.conversation

import androidx.collection.mutableScatterMapOf
import com.buque.wakoo.app.appCoroutineScope
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.SelfUserInfo
import com.buque.wakoo.bean.user.TempUserInfo
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.UserProfileInfo
import com.buque.wakoo.im.IMConversationController
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.inter.IUCConversation
import com.buque.wakoo.im.inter.UCConversation
import com.buque.wakoo.im.utils.IMLogUtils
import com.buque.wakoo.im.utils.IMUtils
import com.buque.wakoo.im.utils.asInstance
import com.buque.wakoo.im_business.conversation.ConvLabel.OfficialLabel
import com.buque.wakoo.manager.AccountManager
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.UserInfoUpdateListener
import com.buque.wakoo.manager.UserManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.utils.LogUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * 工作流程:
 *
 * 1. 在IM登录时,调用login(), 绑定全局的IMConversationController.listFlow, 这个flow会在登录成功后进行刷新
 * 2. 在用户退出时,调用release清空所有会话消息
 *
 * 3. 在login方法中调用bindList绑定IM的会话列表,
 *    然后在combine组合方法中对所有的IM会话进行基础信息获取(标签/状态/用户信息)
 * 4. 在IM会话基础信息获取完毕后, 依次构建异形Conversation, 例如部落/会话/最近查看你的人 等等
 * 5. 监听最新消息, 在收到最新消息时, 使用本地的timestamp进行对比, 在初始化之后的强提醒消息进行具体的逻辑操作
 *
 */
object AppConversationManger : IMCompatListener, UserInfoUpdateListener {
    private val _conversationsFlow: MutableStateFlow<List<IUCConversation>> = MutableStateFlow(emptyList())

    // 上次更新信息时间map
    private val lastUpdateInfoTimestampMap by lazy(LazyThreadSafetyMode.NONE) {
        mutableScatterMapOf<String, Long>()
    }

    // 会话flow
    val conversationsFlow = _conversationsFlow.asStateFlow()

    // 未读数
    val unReadCountFlow: StateFlow<Int> =
        conversationsFlow
            .map {
                it.fold(0) { count, conversation ->
                    count.plus(conversation.unreadCount)
                }
            }.stateIn(scope = appCoroutineScope, started = SharingStarted.Eagerly, initialValue = 0)

    // 私聊会话flow
    val c2cConvFlow: StateFlow<List<C2CConversation>> =
        conversationsFlow
            .map { list ->
                list.filterIsInstance<C2CConversation>()
            }.stateIn(
                scope = appCoroutineScope,
                started = SharingStarted.Eagerly,
                initialValue = emptyList(),
            )

    init {
        IMCompatCore.addIMListener(this)
    }

    override fun onUserInfoUpdated(temp: TempUserInfo) {
        _conversationsFlow.update { list ->
            // 不知道为什么要保留着三个信息
            list.map {
                if (it is C2CConversation && it.user.id == temp.id) {
                    it.copy(user = temp.user)
                } else {
                    it
                }
            }
        }
    }

    override fun onCallUserLogin() {
        setup()
    }

    override fun onUserLogout() {
        clearConversations()
    }

    override fun onClientOffline(
        reason: String,
        isKick: Boolean,
    ) {
        appCoroutineScope.launch {
            if (isKick) {
                AccountManager.logout("该账号已在其他设备登录".localized)
            } else {
                AccountManager.logout("会话过期，请重新登录".localized)
            }
        }
    }

    //region 基础
    fun setup() {
        UserManager.registerUserInfoUpdateListener(this)

        IMLogUtils.i("AppConversationManager setup success")

        IMUtils.launchOnMain {
            IMLogUtils.i("开始加载会话列表")
            bind(IMConversationController.listFlow)
        }
    }

    /**
     * 清空会话
     * release
     *
     */
    fun clearConversations() {
        UserManager.unregisterUserInfoUpdateListener(this)
        _conversationsFlow.value = emptyList()
    }

    fun hasConversation(
        id: String,
        type: ConversationType,
    ): Boolean =
        IMConversationController.listFlow.value.any {
            it.id == id && it.type == type
        }

    fun getUIConversations(id: String): IUCConversation? = conversationsFlow.value.find { it.id == id }
    //endregion

    //region 会话信息更新
    // 刷新间隔
    private val UPDATE_INTERVAL = 120_0000

    fun getLastUpdateInfoTimestamp(uid: String): Long =
        lastUpdateInfoTimestampMap.getOrElse(uid) {
            0
        }

    fun setLastUpdateInfoTimestamp(
        uid: String,
        timeStamp: Long,
    ) {
        lastUpdateInfoTimestampMap.put(uid, timeStamp)
    }

    /**
     * 升级用户状态信息,id是用,拼接成的
     * @param ids
     */
    fun updateUsersOnlineStatus(userIds: String) {
        if (userIds.isEmpty()) {
            return
        }
        IMUtils.launchCoroutine(IMUtils.imConvCoroutineContext) {
            executeApiCallExpectingData { UserApiService.instance.getUserExtendInfo(userIds) }
                .onSuccess {
                    val statusList = it.users
                    if (statusList.isEmpty()) {
                        return@launchCoroutine
                    }
                    val onlineMap =
                        statusList.associateBy {
                            it.id.toString()
                        }
                    _conversationsFlow.update { list ->
                        list.map {
                            if (it is C2CConversation) {
                                val onlineStatus = onlineMap[it.id]
                                val ret =
                                    if (onlineStatus != null && it.user is UserProfileInfo) {
                                        val newSocialInfo =
                                            it.user.socialInfo.copy(
                                                onlineStatus = onlineStatus.status,
                                                intimateScore = onlineStatus.intimateScore,
                                                cityName = onlineStatus.cityName,
                                                isSameCity = onlineStatus.isSameCity,
                                                roomId = onlineStatus.roomId,
                                                isCoinTrade = onlineStatus.isCoinTrade,
                                            )
                                        it.copy(user = it.user.copy(socialInfo = newSocialInfo))
                                    } else {
                                        it
                                    }
                                ret
                            } else {
                                it
                            }
                        }
                    }
                }
        }
    }

    suspend fun bind(imListFlow: StateFlow<List<UCConversation>>) {
        //region 1. 准备数据
        // 系统预留账号
        val designatedServiceAccounts =
            AppConfigManager.featureConfigFlow
                .map { state ->
                    Triple(
                        state.defaultCsId.takeIf { it != -1 }?.toString(),
                        state.officialUserIds,
                        state.publicServiceUids,
                    )
                }.distinctUntilChanged()

//        // 部落会话
//        val tribeFlow = UCOOTribeManager.tribeFlow.distinctUntilChanged { old, new ->
//            old?.id == new?.id
//                && old?.name == new?.name
//                && old?.conversationId == new?.conversationId
//                && old?.dontDisturbEnable == new?.dontDisturbEnable // 注意逻辑变化这里也要更新，这么做的目的减少数据更新计算
//        }
//
//        // 自己的用户信息flow, 会影响标签
//        val selfUserFlow = sUserFlow.distinctUntilChanged { old, new ->
//            old.id == new.id && old.cp == new.cp && old.bestMatchUser?.id == new.bestMatchUser?.id
//                && old.isVip == new.isVip && old.hquType == new.hquType // 注意逻辑变化这里也要更新，这么做的目的减少数据更新计算
//                && old.tribe?.showTreasureBoxIcon == new.tribe?.showTreasureBoxIcon
//        }
        //endregion

        combine(
            imListFlow,
//            AnonymousConfig.anonymousUserFlow,
//            NewerItemProvider.topConvFlow,
//            tribeFlow,
//            LikeYouItemProvider.flow,
//            FriendsHelper.friendIdsFlow,
            designatedServiceAccounts,
            AccountManager.userStateFlow,
        ) { params ->
            val imList = params[0] as List<UCConversation>
            // 分别是 充值君 / 官方id / 客服
            val (csId, officials, publicServices) = params[1] as Triple<String?, List<Int>?, List<Int>?>
            val userInfo = params[2] as SelfUserInfo?

            // 群组信息
            val groupInfo = userInfo?.myGroup

            // 是否已经添加过充值君
            var hasCsConv = false
            var hasTribeConv = false

            val cacheMap = conversationsFlow.value.associateBy { it.id }

            buildList<IUCConversation> {
//                system?.entries?.forEach {
//                    add(SystemConversation(it))
//                }
//                likeYouInfo?.also {
//                    add(LikeYouConversation(it))
//                }

                //region 判断所有的IM Conversation
                imList.forEach {
                    when (it.type) {
                        ConversationType.C2C -> {
                            val intId = it.id.toIntOrNull() ?: 0

//                            val anoInfo = anonymous[intId]
//
//                            if (csId != it.id && anoInfo?.isHidden == true) {
//                                // 隐藏会话
//                                return@forEach
//                            }
//
//                            val visible = it.lastMessage?.let { msg -> UIMessageUtils.isVisible(msg) } ?: true
//
//                            if (!visible) { // 隐藏会话
//                                return@forEach
//                            }
//
                            val user = getC2CUser(cacheMap, it)
                            if (!hasCsConv && csId == it.id) {
                                hasCsConv = true
                            }
//
                            val labels =
                                buildList {
                                    val lbs = getLabels(csId, it, publicServices, intId, officials).orEmpty()
                                    addAll(lbs)
                                    if (user.id == userInfo?.myCpUserId) {
                                        add(ConvLabel.MyCPLabel)
                                    }
                                }

                            add(C2CConversation(user, it, labels))
                        }

                        ConversationType.GROUP -> {
                            when {
                                it.id.contains(CHAT_TRIBE_PREFIX) -> {
                                    if (!hasTribeConv && groupInfo?.timGroupId == it.id) {
                                        hasTribeConv = true
                                        add(
                                            TribeConversation.Instance(
                                                group = groupInfo,
                                                showTreasureBoxIcon = false,
                                                imConversation = it,
                                            ),
                                        )
                                    }
                                }

                                //                                it.id.contains(CHAT_GROUP_PREFIX) -> {
                                //                                    val info = getChatGroupBrief(it)
                                //                                    if (info != null) {
                                //                                        add(GroupConversation(info, it))
                                //                                    }
                                //                                }

                                else -> {
                                    LogUtils.d("IMCompatCore", "未知类型的会话: ${it.id}")
                                    return@forEach
                                }
                            }
                        }

                        else -> return@forEach
                    }
                }
                //endregion

                if (!hasTribeConv) {
                    if (groupInfo != null) { // 有家族的时候
                        val conversation =
                            IMCompatCore.getIMConversation(groupInfo.timGroupId, ConversationType.GROUP, true)
                        add(
                            TribeConversation.Instance(
                                group = groupInfo,
                                showTreasureBoxIcon = false,
                                imConversation = conversation ?: VirtualConversation(groupInfo.timGroupId),
                            ),
                        )
                    }

//                    else if (userInfo?.isVip == false) { // 不是vip的时候
//                        BecomeToMemberConversation
//                    }

                    else {
                        add(TribeConversation.Empty)
                    }

                    hasTribeConv = true
                }

                if (!hasCsConv && csId != null) {
                    val conversation = IMCompatCore.getIMConversation(csId, ConversationType.C2C, true)
                    if (conversation != null) {
                        add(C2CConversation(getC2CUser(cacheMap, conversation), conversation))
                    }
                }
            }.sortedByDescending { it.orderKey }
        }.collectLatest {
            _conversationsFlow.emit(it)
        }
    }

    private suspend fun getC2CUser(
        cacheMap: Map<String, IUCConversation>,
        conversation: UCConversation,
    ): User {
        val cacheConversation = cacheMap[conversation.id]?.asInstance<C2CConversation>()

        val cacheUser = cacheConversation?.user ?: UserManager.getLocalUserInfo(conversation.id).getOrNull()

        val user =
            cacheUser ?: conversation.lastMessage
                ?.takeIf {
                    it.sender == conversation.id && !it.isSelf
                }?.requireUser()
                ?.toBasic() ?: BasicUser.fromUid(conversation.id)

        if (cacheUser == null) {
            UserManager.refreshUserInfo(conversation.id)
        }

        return UserProfileInfo(user)
    }

    private fun getLabels(
        csId: String?,
        conversation: UCConversation,
        publicServices: List<Int>?,
        intId: Int,
        officials: List<Int>?,
    ): List<ConvLabel>? {
        val labels = arrayListOf<ConvLabel>()

//        if (csId == conversation.id || publicServices?.contains(intId) == true) {
//            labels.add(ConvLabel.SupportStaff)
//        }

        // 左标签
        if (officials?.contains(intId) == true) { // 官方
            labels.add(OfficialLabel)
        }

        return if (labels.isEmpty()) null else labels
    }
}
