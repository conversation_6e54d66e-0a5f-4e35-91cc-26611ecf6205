package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.bean.GiftWrapper
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.MessageTheme
import com.buque.wakoo.im_business.message.MessageThemeBubble
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.manager.localizedFormat
import com.buque.wakoo.ui.widget.image.NetworkImage

data class GiftContent(
    val message: UCGiftMessage,
) : MsgLayoutContent() {
    private val giftWrapper: GiftWrapper = message.gift

    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        baseBox {
            MessageThemeBubble(
                defaultMsgTheme =
                    MessageTheme(
                        painter = ColorPainter(Color.White),
                        paddingValues = PaddingValues(12.dp),
                        shape =
                            RoundedCornerShape(
                                topStart = if (message.isSelf) 12.dp else 0.dp,
                                topEnd = if (message.isSelf) 0.dp else 12.dp,
                                12.dp,
                                12.dp,
                            ),
                        contentColor = Color(0xff111111),
                        16.sp,
                    ),
            ) {
                Row(modifier = Modifier.width(220.dp), verticalAlignment = Alignment.CenterVertically) {
                    NetworkImage(giftWrapper.gift.icon, modifier = Modifier.size(48.dp))
                    Spacer(Modifier.width(12.dp))
                    Column {
                        Text(
                            "送给%s".localizedFormat(giftWrapper.receiverName),
                            maxLines = 1,
                            fontSize = 16.sp,
                            lineHeight = 16.sp,
                            color = Color(0xff1d2129),
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            "${giftWrapper.gift.name}x${giftWrapper.count}",
                            color = Color(0xff86909c),
                            fontSize = 12.sp,
                        )
                    }
                }
            }
        }
    }
}
