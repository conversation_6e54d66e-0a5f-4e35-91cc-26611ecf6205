package com.buque.wakoo.im_business.conversation

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.im.inter.IUCConversation
import com.buque.wakoo.manager.localized

enum class ConvLabelType {
    Hidden,
    VisibleLeft,
    VisibleRight,
}

sealed interface ConvLabel {
    companion object {
        val GroupLabel =
            ComposeLabel(
                "群组".localized,
                backgroundColor = Brush.horizontalGradient(listOf(Color(0xFF56CCFF), Color(0xFF159AFF))),
                style =
                    TextStyle(
                        color = Color.White,
                        fontWeight = FontWeight.Medium,
                        fontSize = 10.sp,
                        lineHeight = 10.sp,
                    ),
            )
    }

    val type: ConvLabelType

    interface Left : ConvLabel {
        override val type: ConvLabelType
            get() = ConvLabelType.VisibleLeft

        fun getText(context: Context): String? = null

        fun getBackground(
            context: Context,
            conversation: IUCConversation,
        ): Drawable? = null
    }

    interface Right : ConvLabel {
        override val type: ConvLabelType
            get() = ConvLabelType.VisibleRight
    }

    class ComposeLabel(
        val label: String,
        val style: TextStyle = TextStyle(Color.White, fontSize = 10.sp),
        val backgroundColor: Brush,
    ) : ConvLabel {
        override val type: ConvLabelType = ConvLabelType.VisibleRight

        @Composable
        fun LabelContent(modifier: Modifier = Modifier) {
            Box(
                modifier =
                    modifier
                        .height(20.dp)
                        .background(backgroundColor, RoundedCornerShape(4.dp))
                        .padding(horizontal = 10.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(text = label, style = style)
            }
        }
    }

    open class ComposeUILabel(
        val text: String,
        val style: TextStyle = TextStyle(Color.White, fontSize = 10.sp),
        val backgroundColor: Brush,
    ) : ConvLabel {
        override val type: ConvLabelType = ConvLabelType.VisibleRight

        @Composable
        fun LabelContent(modifier: Modifier = Modifier) {
            Box(
                modifier =
                    modifier
                        .height(20.dp)
                        .background(backgroundColor, RoundedCornerShape(4.dp))
                        .padding(horizontal = 10.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(text = text, style = style)
            }
        }
    }

    data object OfficialLabel : ComposeUILabel(
        "官方".localized,
        backgroundColor = SolidColor(Color(0xFF66FE6B)),
        style =
            TextStyle(
                color = Color(0xFF111111),
                fontWeight = FontWeight.Medium,
                fontSize = 10.sp,
                lineHeight = 10.sp,
            ),
    )

    data object MyCPLabel : ComposeUILabel(
        "我的CP".localized,
        backgroundColor = Brush.horizontalGradient(listOf(Color(0xFFFF1D99), Color(0xFFFFB1DE))),
        style =
            TextStyle(
                color = Color.White,
                fontWeight = FontWeight.Medium,
                fontSize = 10.sp,
                lineHeight = 10.sp,
            ),
    )

    // 我的好友
    data object MyFriends : ConvLabel {
        override val type: ConvLabelType
            get() = ConvLabelType.Hidden
    }

    // 客服
    data object SupportStaff : ConvLabel {
        override val type: ConvLabelType
            get() = ConvLabelType.Hidden
    }
}
