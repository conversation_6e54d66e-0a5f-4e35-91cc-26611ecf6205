package com.buque.wakoo.im_business.conversation

import android.content.Context
import android.text.Spanned
import android.text.SpannedString
import com.buque.wakoo.bean.chatgroup.WakooChatGroup
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.im.bean.ConversationAtInfo
import com.buque.wakoo.im.inter.IUCConversation
import com.buque.wakoo.im.inter.UCConversation
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.im_business.UIMessageUtils
import com.buque.wakoo.im_business.message.HintExpansionExtra
import com.buque.wakoo.manager.localized

const val CHAT_GROUP_PREFIX = "chatgroup"

const val CHAT_TRIBE_PREFIX = "tribe"

val String.isGroupId: Boolean
    get() = startsWith(CHAT_GROUP_PREFIX)

val String.groupId: Int?
    get() =
        if (isGroupId) {
            substring(CHAT_GROUP_PREFIX.length).toIntOrNull()
        } else {
            null
        }

data class C2CConversation constructor(
    val user: User,
    val imConversation: UCConversation,
    val labels: List<ConvLabel>? = null,
) : IUCConversation by imConversation {
    override val name: String
        get() = user.name

    override val iconUrl: String
        get() = user.avatar

    override val timestamp: Long
        get() = imConversation.timestamp

    override fun getDisplayName(
        context: Context,
        default: String,
    ): CharSequence = name

    override fun getDisplaySummary(
        context: Context,
        default: String,
    ): Spanned =
        super.getDisplaySummary(context, "").takeIsNotEmpty() ?: UIMessageUtils.getMessageSummary(
            context = context,
            hasUnread = unreadCount > 0,
            message = imConversation.lastMessage,
        )

    // 是否是好友
    val isFriends: Boolean

    // 是否是客服
    val isSupportStaff: Boolean

    val leftLabel: ConvLabel.Left?

    val rightLabels: List<ConvLabel.Right>?

    val hintExtra: HintExpansionExtra? =
        imConversation.lastMessage?.let {
            UIMessageUtils.parseIMConversationExtra(it)
        }

    init {
        var isFriends = false
        var isSupportStaff = false
        var leftLabel: ConvLabel.Left? = null

        rightLabels =
            labels
                ?.mapNotNull {
                    if (it == ConvLabel.MyFriends) {
                        isFriends = true
                    }
                    if (it == ConvLabel.SupportStaff) {
                        isSupportStaff = true
                    }
                    if (it is ConvLabel.Left) {
                        leftLabel = it
                    }
                    it as? ConvLabel.Right
                }?.takeIf { it.isNotEmpty() }

        this.isFriends = isFriends
        this.isSupportStaff = isSupportStaff
        this.leftLabel = leftLabel
    }
}

// data class GroupConversation(
//    val info: ChatGroupBrief,
//    override val imConversation: UCConversation,
// ) : UCIMConversationOwner, IUCConversation by imConversation {
//
//    override val name: String
//        get() = info.name
//
//    override val iconUrl: String
//        get() = info.avatarUrl
//
//    override val timestamp: Long
//        get() = imConversation.timestamp
//
//    override val unreadCount: Int
//        get() = if (info.iEnableDontDisturb) 0 else imConversation.unreadCount
//
//    override val hintExtra: HintExpansionExtra? = imConversation.lastMessage?.let {
//        UIMessageUtils.parseIMConversationExtra(it)
//    }
//
// }
//
sealed interface TribeConversation : IUCConversation {
    data class Instance(
        val group: WakooChatGroup,
        val showTreasureBoxIcon: Boolean,
        val imConversation: IUCConversation,
    ) : TribeConversation,
        IUCConversation by imConversation {
        override val stableId: String
            get() = "tribe_$group"

        override val timestamp: Long
            get() = if (imConversation is UCConversation) imConversation.timestamp else super<TribeConversation>.timestamp

        override val unreadCount: Int
            get() = imConversation.unreadCount

        override val isPinned: Boolean
            get() = true

        override val orderKey: Long
            get() = Long.MAX_VALUE

        override val name: String
            get() = group.name

        override val iconUrl: String
            get() = group.avatarUrl

        val hintExtra: HintExpansionExtra? =
            (imConversation as? UCConversation)?.lastMessage?.let {
                UIMessageUtils.parseIMConversationExtra(it)
            }

        override fun getDisplayName(
            context: Context,
            default: String,
        ): CharSequence = group.name.takeIsNotEmpty() ?: (super<IUCConversation>.getDisplayName(context, default))

        override fun getDisplaySummary(
            context: Context,
            default: String,
        ): Spanned =
            super<TribeConversation>.getDisplaySummary(context, "").takeIsNotEmpty()
                ?: UIMessageUtils.getMessageSummary(
                    context = context,
                    hasUnread = unreadCount > 0,
                    message = (imConversation as? UCConversation)?.lastMessage,
                )
    }

    data object Empty : TribeConversation {
        override val id: String
            get() = "empty_tribe"

        override val isPinned: Boolean
            get() = true

        override val orderKey: Long
            get() = Long.MAX_VALUE

        override val firstUnreadMsgSequence: Long
            get() = 0

        override val atInfoList: ConversationAtInfo?
            get() = null

        override fun getDisplayName(
            context: Context,
            default: String,
        ): String = "兴趣群组".localized

        override fun getDisplaySummary(
            context: Context,
            default: String,
        ): Spanned = SpannedString("兴趣同好交流".localized)
    }
}

data object BecomeToMemberConversation : IUCConversation {
    override val id: String
        get() = "become_to_member"

    override val isPinned: Boolean
        get() = true

    override val orderKey: Long
        get() = Long.MAX_VALUE

    override val firstUnreadMsgSequence: Long
        get() = 0

    override val atInfoList: ConversationAtInfo?
        get() = null

    override fun getDisplayName(
        context: Context,
        default: String,
    ): String = "开通wakoo尊享会员".localized

    override fun getDisplaySummary(
        context: Context,
        default: String,
    ): Spanned = SpannedString("多项超值特权,尽享聊天交友乐趣".localized)
}

// data class LikeYouConversation(
//    val likeInfo: LikeYouInfo,
// ) : IUCConversation {
//
//    override val id: String
//        get() = "like_for_you"
//
//    override val isPinned: Boolean
//        get() = true
//
//    override val orderKey: Long
//        get() = Long.MAX_VALUE - 1
//
//    override val unreadCount: Int
//        get() = likeInfo.newRecomCnt
//
//    override val firstUnreadMsgSequence: Long
//        get() = 0
//
//    override val atInfoList: ConversationAtInfo?
//        get() = null
//
//    override fun getDisplayName(context: Context, default: String): String {
//        return context.getString(R.string.title_like_you)
//    }
//
//    override fun getDisplaySummary(context: Context, default: String): Spanned {
//        return SpannedString(context.getString(R.string.desc_like_you))
//    }
// }
//
// data class SystemConversation(
//    val entry: SystemTopConvEntry,
// ) : IUCConversation {
//
//    override val id: String
//        get() = "system_conversation_${entry.title}_${entry.digest}_${entry.timeLabel}"
//
//    override val isPinned: Boolean
//        get() = true
//
//    override val orderKey: Long
//        get() = Long.MAX_VALUE - 2
//
//    override val name: String
//        get() = entry.title
//
//    override val desc: String
//        get() = entry.digest
//
//    override val unreadCount
//        get() = entry.unreadCnt
//
//    override val iconUrl: String
//        get() = entry.avatarUrl
//
//    override val firstUnreadMsgSequence: Long
//        get() = 0
//
//    override val atInfoList: ConversationAtInfo?
//        get() = null
//
// }
//
// data object VoiceConversation : IUCConversation {
//
//    override val id: String = "message-list-voice-room"
//
//    override val isPinned: Boolean
//        get() = true
//
//    override val orderKey: Long
//        get() = Long.MAX_VALUE - 1
//
//    override val firstUnreadMsgSequence: Long
//        get() = 0
//
//    override val atInfoList: ConversationAtInfo?
//        get() = null
// }
//
// data object VisitorHistoryConversation : IUCConversation {
//
//    override val id: String = "message-list-visitor-history"
//
//    override val isPinned: Boolean
//        get() = true
//
//    override val orderKey: Long
//        get() = Long.MAX_VALUE - 2
//
//    override val firstUnreadMsgSequence: Long
//        get() = 0
//
//    override val atInfoList: ConversationAtInfo?
//        get() = null
// }

// data object Official : Left {
//
//    override fun getText(context: Context): String {
//        return context.getString(R.string.官方)
//    }
//
//    override fun getBackground(context: Context, conversation: IUCConversation): Drawable {
//        return linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFF9E3CFA.toInt(), 0xFF3108FF.toInt())
//    }
// }
//
// data class RecommendFriend(@StringRes private val textResId: Int) : Left {
//
//    override fun getText(context: Context): String {
//        return context.getString(textResId)
//    }
//
//    override fun getBackground(context: Context, conversation: IUCConversation): Drawable? {
//        if (conversation !is C2CConversation) return null
//        return if (conversation.user.isBoy) {
//            linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFF70BBFF.toInt(), 0xFF2972FF.toInt())
//        } else {
//            linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFFF79CA.toInt(), 0xFFFF47AA.toInt())
//        }
//    }
// }
//
// data class SimpleLeft(private val text: String, private val background: Drawable?) : Left {
//
//    override fun getText(context: Context): String {
//        return text
//    }
//
//    override fun getBackground(context: Context, conversation: IUCConversation): Drawable? {
//        return background
//    }
// }
//
// data object MyCp : Right {
//
//    context (TextView, SpannableStringBuilder)
//    override fun appendSpan() {
//        append(' ')
//        iconSpan(R.drawable.label_my_cp, 64.dp, 20.dp)
//    }
// }
//

// //    // 匿名信息
// //    data class Ano(val anoItem: AnoItem) : ConvLabel {
// //
// //        override val type: ConvLabelType
// //            get() = ConvLabelType.Hidden
// //    }

data class VirtualConversation(
    override val id: String,
    override val name: String = "",
    override val iconUrl: String = "",
    override val unreadCount: Int = 0,
    override val draftText: String? = null,
    override val isPinned: Boolean = false,
    override val orderKey: Long = 0,
    override val firstUnreadMsgSequence: Long = 0,
    override val atInfoList: ConversationAtInfo? = null,
) : IUCConversation
