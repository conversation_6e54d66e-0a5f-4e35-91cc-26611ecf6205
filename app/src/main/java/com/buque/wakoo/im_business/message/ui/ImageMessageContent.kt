//package com.buque.wakoo.im_business.message.ui
//
//import androidx.compose.foundation.border
//import androidx.compose.foundation.combinedClickable
//import androidx.compose.foundation.interaction.MutableInteractionSource
//import androidx.compose.foundation.layout.padding
//import androidx.compose.foundation.layout.size
//import androidx.compose.foundation.layout.sizeIn
//import androidx.compose.material3.MaterialTheme
//import androidx.compose.runtime.Composable
//import androidx.compose.runtime.remember
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.draw.clip
//import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.layout.ContentScale
//import androidx.compose.ui.platform.LocalDensity
//import androidx.compose.ui.tooling.preview.Preview
//import androidx.compose.ui.unit.Dp
//import androidx.compose.ui.unit.dp
//import androidx.core.util.SizeFCompat
//import com.buque.wakoo.im.UCInstanceMessage
//import com.buque.wakoo.im_business.UIMessageUtils
//import com.buque.wakoo.im_business.interf.IIMAction
//import com.buque.wakoo.im_business.message.MessageC2CUserScaffold
//import com.buque.wakoo.im_business.message.MessageReminder
//import com.buque.wakoo.im_business.message.MsgUIExtra
//import com.buque.wakoo.im_business.message.types.UCImageMessage
//import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
//import com.buque.wakoo.ui.widget.image.NetworkImage
//
//data class ImageMessageContent constructor(
//    val message: UCInstanceMessage,
//    val extra: MsgUIExtra? = null,
//) : MsgLayoutContent() {
//    @Composable
//    override fun RenderDefault(
//        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
//        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
//        onAction: IIMAction,
//    ) {
//        val density = LocalDensity.current
//        val message = message as UCImageMessage
//        val imageElem = message.previewElem
//
//        val (widthDp, heightDp) =
//            remember(imageElem, density) {
//                if (imageElem.width > 0 && imageElem.height > 0) {
//                    val size =
//                        UIMessageUtils.resizeImage(
//                            originalSize =
//                                with(density) {
//                                    SizeFCompat(imageElem.width.toDp().value, imageElem.height.toDp().value)
//                                },
//                        )
//                    size.width.dp to size.height.dp
//                } else {
//                    Dp.Unspecified to Dp.Unspecified
//                }
//            }
//
//        MessageC2CUserScaffold(message, onAction, bottomWidget = {
//            if (extra != null) {
//                MessageReminder(extra, message.isSelf, modifier = Modifier.padding(top = 5.dp))
//            }
//        }) {
//            NetworkImage(
//                imageElem.localUri ?: imageElem.url,
//                modifier =
//                    Modifier
//                        .border(1.5.dp, Color.White, MaterialTheme.shapes.small)
//                        .size(width = widthDp, height = heightDp)
//                        .sizeIn(maxWidth = UIMessageUtils.MaxWidthDp.dp, maxHeight = UIMessageUtils.MaxHeightDp.dp)
//                        .clip(MaterialTheme.shapes.small)
//                        .combinedClickable(
//                            interactionSource = remember { MutableInteractionSource() },
//                            indication = null,
//                            onLongClick = {
////                        it.showMessageMenu(message)
//                            },
//                            onClick = {
//                                onAction.onPreview(message)
//                            },
//                        ),
//                contentScale = ContentScale.Fit,
//            )
//        }
//    }
//}
//
//@Preview
//@Composable
//private fun PreviewImage() {
//}
