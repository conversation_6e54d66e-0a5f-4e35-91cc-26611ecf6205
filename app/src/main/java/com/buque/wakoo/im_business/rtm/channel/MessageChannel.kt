package com.buque.wakoo.im_business.rtm.channel

import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im_business.message.types.UCCustomMessage

// channel
interface IMessageChannel {
    interface Listener {
        fun onReceivedMessage(message: String)
    }

    val channelName: String

    /**
     * 加入频道
     */
    suspend fun join(): Boolean

    /**
     * 退出频道
     */
    suspend fun quit()

    fun setListener(listener: Listener)
}

class TIMessageChannel(
    override val channelName: String,
) : IMessageChannel {
    private var mListener: IMessageChannel.Listener? = null

    private val messageListener =
        object : IMCompatListener {
            override fun onRecvNewMessage(
                message: UCInstanceMessage,
                offline: Boolean,
            ) {
                if (!offline && !message.isC2CMsg && message is UCCustomMessage) {
                    if (message.targetId == channelName) {
                        mListener?.onReceivedMessage(message.rawContent)
                    }
                }
            }
        }

    override suspend fun join(): Boolean {
        IMCompatCore.addIMListener(messageListener)
        return IMCompatCore.joinConversation(channelName, ConversationType.RTM, true)
    }

    override suspend fun quit() {
        IMCompatCore.quitConversation(channelName, ConversationType.RTM, true)
        mListener = null
        IMCompatCore.removeIMListener(messageListener)
    }

    override fun setListener(listener: IMessageChannel.Listener) {
        mListener = listener
    }
}
