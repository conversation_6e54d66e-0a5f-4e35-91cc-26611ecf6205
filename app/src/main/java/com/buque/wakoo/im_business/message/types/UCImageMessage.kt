package com.buque.wakoo.im_business.message.types

import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.UCMessage
import com.buque.wakoo.im.inter.IUCMessage
import com.buque.wakoo.ui.widget.media.previewer.MediaViewerItem

data class ImageElem(
    val width: Int,
    val height: Int,
    val source: String,
    val uuid: String?,
)

data class UCImageMessage(
    override val base: UCMessage,
    val thumbElem: ImageElem?,
    val largeElem: ImageElem?,
    val originElem: ImageElem?,
    val localElem: ImageElem?,
) : UCInstanceMessage,
    IUCMessage by base {
    init {
        require(thumbElem != null || largeElem != null || originElem != null || localElem != null)
    }

    private val previewElem = thumbElem ?: largeElem ?: originElem ?: localElem!!

    val previewKey = previewElem.uuid ?: base.id

    override fun getSummaryString(): String {
        val elem = largeElem ?: localElem
        return "图片消息, 宽: ${elem?.width}, 高: ${elem?.height}, url: ${elem?.source}"
    }

    fun toMediaViewerImageItem(): MediaViewerItem.Image {
        val aspectRatio =
            localElem?.let {
                if (it.width > 0 && it.height > 0) {
                    it.width.toFloat().div(it.height)
                } else {
                    1f
                }
            } ?: 1f

        return MediaViewerItem
            .Image(
                fullSizedUrl = largeElem?.source ?: originElem?.source ?: thumbElem?.source,
                placeholderImageUrl = thumbElem?.source ?: largeElem?.source,
                localFilePath = localElem?.source,
                aspectRatio = aspectRatio,
                overrideKey = previewKey,
            )
    }
}
