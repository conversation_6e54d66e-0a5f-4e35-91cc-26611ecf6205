package com.buque.wakoo.im_business.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.SelfUser
import com.buque.wakoo.app.currentUserKV
import com.buque.wakoo.bean.ConversationConfig
import com.buque.wakoo.bean.CoupleInfo
import com.buque.wakoo.bean.chatgroup.ChatGroupBean
import com.buque.wakoo.bean.user.BasicUser
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.consts.SceneType
import com.buque.wakoo.ext.parseValue
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.im.MessageBundle
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.bean.ConversationType
import com.buque.wakoo.im.bean.MsgSendCondition
import com.buque.wakoo.im.bean.SendParams
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.getExtraString
import com.buque.wakoo.im_business.message.IMEvent
import com.buque.wakoo.im_business.message.UIMessageEntry
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.message.types.UCGiftMessage
import com.buque.wakoo.im_business.message.types.UCImageMessage
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.types.UCTimestampMessage
import com.buque.wakoo.im_business.message.types.UCVoiceMessage
import com.buque.wakoo.im_business.message.ui.NoProviderMessageContent
import com.buque.wakoo.im_business.message.ui.SystemMessageContent
import com.buque.wakoo.im_business.message.ui.TextMessageContent
import com.buque.wakoo.im_business.message.ui.TimeLineContent
import com.buque.wakoo.im_business.message.ui.VoiceMessageContent
import com.buque.wakoo.im_business.message.ui.custom.AudioRoom
import com.buque.wakoo.im_business.message.ui.custom.AudioRoomShareContent
import com.buque.wakoo.im_business.message.ui.custom.CommonTextButtonContent
import com.buque.wakoo.im_business.message.ui.custom.GroupShareContent
import com.buque.wakoo.im_business.message.ui.custom.InviteUserMemberContent
import com.buque.wakoo.im_business.message.ui.custom.MsgInterceptContent
import com.buque.wakoo.im_business.message.ui.entry.CPInviteME
import com.buque.wakoo.im_business.message.ui.entry.GuideCPSuccessME
import com.buque.wakoo.im_business.message.ui.entry.ImageMsgEntry
import com.buque.wakoo.im_business.message.ui.entry.MakCPSuccessME
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.im_business.message.ui.entry.PrivateRoomEventEntry
import com.buque.wakoo.im_business.message.ui.entry.VoiceCallEventEntry
import com.buque.wakoo.im_business.wigets.AudioPanel
import com.buque.wakoo.im_business.wigets.EmojiPanel
import com.buque.wakoo.im_business.wigets.IMPanel
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.service.C2CChatApi
import com.buque.wakoo.network.api.service.ChatGroupApi
import com.buque.wakoo.network.api.service.UserApiService
import com.buque.wakoo.network.executeApiCallExpectingData
import com.buque.wakoo.repository.GlobalRepository
import com.buque.wakoo.utils.eventBus.toLink
import com.buque.webview.putValueByUrl
import com.buque.wakoo.im_business.message.ui.RecallMessageContent
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.encodeToJsonElement
import kotlinx.serialization.json.jsonObject

class C2CChatViewModel(
    private val user: User,
) : ListStateMessageViewModel(
        SendParams(receiver = user.id, type = ConversationType.C2C),
        IMMessageConfig(autoPlayNoPlayedGiftEffect = true, autoMarkReadReceipt = true),
    ) {
    val panels: Array<IMPanel> =
        arrayOf(
            IMPanel(AudioPanel, forceClearFocus = true), // 录音面板
            IMPanel(EmojiPanel, forceRequestFocus = true), // 表情面板
        )

    val c2cConfig = mutableStateOf(ConversationConfig())

    val coupleInfo = mutableStateOf<CoupleInfo?>(null)

    init {
        refresh()
    }

    fun refresh() {
        viewModelScope.launch {
            executeApiCallExpectingData {
                C2CChatApi.instance.getUserConfig(user.id)
            }.onSuccess {
                c2cConfig.value = it
            }
        }

        refreshCoupleInfo()
    }

    suspend fun toCp() {
        GlobalRepository.relationRepo.checkBuddyPossible(user.id).onSuccess { obj ->
            val isPossible = obj.parseValue<Boolean>("is_possible") ?: false
            if (isPossible) {
                val link = obj.parseValue<String>("link").orEmpty()
                appendMakeBuddyParams(link, user).toLink()
            } else {
                showToast(obj.parseValue<String>("msg").orEmpty())
            }
        }
    }

    companion object {
        private val supportCmds =
            listOf(
                IMEvent.PRIVATE_SYSTEM_HINT,
                IMEvent.PRIVATE_SYSTEM_HINT_V2,
                IMEvent.INVITE_TO_TRIBE,
                IMEvent.INVITE_TO_ROOM,
                IMEvent.EXCHANGE_CONTACT,
//                IMEvent.GUIDE_VIDEO_CHAT,
//                IMEvent.GUARD_CHANGE_EVENT,
//                IMEvent.JAPAN_FIRST_CHARGE_AWARD,
//                IMEvent.JAPAN_WITHDRAW_COUPON_CODE,
//                IMEvent.PRIVATECHAT_JP_INTIMATE_GIFT_MSG,
//                IMEvent.PRIVATECHAT_JP_TRANSFER_RECORD,
                IMEvent.INVITE_TO_JOIN_RELATIVE_GROUP,
                IMEvent.ACCEPT_TO_JOIN_RELATIVE_GROUP,
                IMEvent.RELATIVE_GROUP_BROKEN,
                IMEvent.CONFIRM_CP,
                IMEvent.GUIDE_CP,
                IMEvent.GIVE_CONFESSION_GIFT,
                IMEvent.NEED_ADD_FRIENDS,
                IMEvent.WE_BECAME_FRIENDS,
                IMEvent.VOICE_CALL_FINISH,
                IMEvent.PRIVATE_ROOM_PARTNER_ENTER,
                IMEvent.INVITE_TO_JOIN_UCMEMBER,
                IMEvent.COMMON_TEXT_WITH_ACTION_CARD,
            )

        fun appendMakeBuddyParams(
            url: String,
            targetUser: User,
            inviteCode: String? = null,
        ): String =
            url
                .putValueByUrl("target_uid", targetUser.id)
                .putValueByUrl("scene_type", SceneType.WAKOO_PRIVATE_CHAT.toString())
                .putValueByUrl("scene_id", targetUser.id)
                .putValueByUrl("self_uid", SelfUser?.id ?: "")
                .let {
                    if (inviteCode.isNullOrBlank()) {
                        it
                    } else {
                        it.putValueByUrl("invite_code", inviteCode)
                    }
                }
    }

    override fun filterShownMessage(message: UCInstanceMessage): Boolean {
        if (message is UCCustomMessage) {
            return message.cmd in supportCmds
        } else {
            return message is UCTextMessage || message is UCImageMessage || message is UCVoiceMessage || message is UCGiftMessage
        }
    }

    override fun onMessageCheckFail(
        condition: MsgSendCondition?,
        throwable: Throwable?,
    ) {
        if (condition != null) {
            showNotSendMessageTip(user.id, condition)
        }
    }

    private fun showNotSendMessageTip(
        userId: String,
        condition: MsgSendCondition,
    ) {
        val hasTips =
            currentUserKV.getBoolean(
                "add_friends_tips_$userId",
                false,
            )
        if (!hasTips) {
            // 这个abValue干嘛的? 你在问我吗?
            val bundle =
                MessageBundle.Custom.create(
                    IMEvent.NEED_ADD_FRIENDS,
                    AppJson.encodeToJsonElement(condition).jsonObject,
                    condition.hint,
                )
            IMCompatCore.insertLocalMessage(userId, ConversationType.C2C, bundle)
            currentUserKV.putBoolean("add_friends_tips_$userId", true)
        }
    }

    override fun onRecvNewCustomMessage(
        message: UCCustomMessage,
        offline: Boolean,
    ) {
        when (message.cmd) {
            IMEvent.CONFIRM_CP -> {
                refreshCoupleInfo()
            }
        }
    }

    private fun refreshCoupleInfo() {
        if (SelfUser?.isJP == true) {
            viewModelScope.launch {
                executeApiCallExpectingData {
                    C2CChatApi.instance.getCpInfoForJP(user.id)
                }.onSuccess {
                    coupleInfo.value = it
                }
            }
        }
    }

    fun blackUser(userId: String) {
        viewModelScope.launch {
            executeApiCallExpectingData {
                UserApiService.instance.blackUser(fields = mapOf("userid" to userId, "black" to "true"))
            }
        }
    }

    override fun createRecallUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry = RecallMessageContent(message)

    override fun sceneConvertToUIMessageEntry(
        message: UCInstanceMessage,
        includeExtensions: Boolean,
        oldEntry: UIMessageEntry?,
    ): MsgUIEntry? =
        when (message) {
            is UCTextMessage -> {
                TextMessageContent(message, getMessageUIExtra(message, includeExtensions))
            }

            is UCImageMessage -> {
                val imageElem = message.largeElem ?: message.localElem!!
                ImageMsgEntry(
                    mediaItem = message.toMediaViewerImageItem(),
                    width = imageElem.width,
                    height = imageElem.height,
                    user = message.requireUser(),
                    hint = getMessageUIExtra(message, includeExtensions)?.expansionExtra?.hint,
                )
            }

            is UCTimestampMessage -> {
                TimeLineContent(message)
            }

            is UCVoiceMessage -> {
                VoiceMessageContent(message)
            }

            is UCCustomMessage -> {
                when (message.cmd) {
                    IMEvent.INVITE_TO_ROOM -> {
                        val audioRoom = message.getJsonValue<AudioRoom>("audioroom")!!
                        AudioRoomShareContent(audioRoom)
                    }

                    IMEvent.INVITE_TO_TRIBE -> {
                        val audioRoom = message.getJsonValue<ChatGroupBean>("tribe")!!
                        val inviteCode = message.getJsonString("invite_code") ?: ""
                        val isSelf = message.isSelf
//                        val hasAccepted = message.getExtraBoolean("invite_accepted") ?: false
                        val receiverExtra = message.getExtraString("receiver_extra")
                        GroupShareContent(audioRoom, isSelf, receiverExtra) {
                            acceptJoinGroup(audioRoom.id)
                        }
                    }

                    IMEvent.GIVE_CONFESSION_GIFT -> {
                        try {
                            val entry = message.parseDataJson<CPInviteME>()
                            entry ?: NoProviderMessageContent(message)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            NoProviderMessageContent(message)
                        }
                    }

                    IMEvent.CONFIRM_CP -> {
                        try {
                            val entry = message.parseDataJson<MakCPSuccessME>()
                            entry ?: NoProviderMessageContent(message)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            NoProviderMessageContent(message)
                        }
                    }

                    IMEvent.GUIDE_CP -> {
                        try {
                            val entry = message.parseDataJson<GuideCPSuccessME>()
                            entry ?: NoProviderMessageContent(message)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            NoProviderMessageContent(message)
                        }
                    }

                    IMEvent.WE_BECAME_FRIENDS,
                    IMEvent.PRIVATE_SYSTEM_HINT,
                    IMEvent.PRIVATE_SYSTEM_HINT_V2,
                    -> {
                        SystemMessageContent(message)
                    }

                    IMEvent.VOICE_CALL_FINISH -> { // 私密小屋通话结束
                        val privateRoomId = message.getJsonString("room_id", "")
                        val content = message.getJsonString("reason_text", "")
                        val buttonText =
                            message
                                .getJsonBoolean("is_newbie_guide", false)
                                .takeIf {
                                    it
                                }?.let {
                                    "继续聊天".localized
                                }
                        VoiceCallEventEntry(
                            privateRoomId,
                            content,
                            buttonText,
                        )
                    }

                    IMEvent.PRIVATE_ROOM_PARTNER_ENTER -> { // 有人进入私密小屋
                        val privateRoomId = message.getJsonString("room_id", "")
                        val content = message.getJsonString("digest", "")
                        val buttonText = "加入互动".localized
                        PrivateRoomEventEntry(
                            id = privateRoomId,
                            leftUser = message.getJsonValue<BasicUser>("fan_user")!!,
                            rightUser = message.getJsonValue<BasicUser>("star_user")!!,
                            content = content,
                            buttonText = buttonText,
                        )
                    }

                    IMEvent.NEED_ADD_FRIENDS -> {
                        val condition = AppJson.decodeFromJsonElement<MsgSendCondition>(message.customJson)
                        val hint = message.summary ?: ""
                        MsgInterceptContent(condition, hint)
                    }

                    IMEvent.INVITE_TO_JOIN_UCMEMBER -> {
                        val inviter = message.getJsonValue<BasicUser>("inviter") // 邀请者
                        val invitee = message.getJsonValue<BasicUser>("invitee") // 被邀请者
                        InviteUserMemberContent(inviter, invitee)
                    }

                    IMEvent.COMMON_TEXT_WITH_ACTION_CARD -> {
                        val content = message.getJsonString("content", "")
                        val btnText = message.getJsonString("btn_txt", "")
                        val applink = message.getJsonString("app_link", "")
                        CommonTextButtonContent(content, btnText, applink)
                    }

                    else -> {
                        NoProviderMessageContent(message)
                    }
                }
            }

            else -> NoProviderMessageContent(message)
        }

    private fun acceptJoinGroup(groupId: String) {
        // todo 申请 or 直接加入?
//        viewModelScope.launch {
//            executeApiCallExpectingData {
//                ChatGroupApi.instance.acceptInvite(mapOf("invite_code" to inviteCode))
//            }.onSuccess {
//
//            }
//        }
        viewModelScope.launch {
            executeApiCallExpectingData {
                ChatGroupApi.instance.applyJoinChatGroup(mapOf("group_id" to groupId))
            }
        }
    }
}
