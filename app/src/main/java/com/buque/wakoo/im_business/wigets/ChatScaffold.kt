package com.buque.wakoo.im_business.wigets

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.zIndex

@Composable
fun ChatScaffold(
    panelState: KeyboardPanelState,
    modifier: Modifier = Modifier,
    topBar: @Composable ColumnScope.() -> Unit = {},
    bottomBar: @Composable ColumnScope.() -> Unit = {},
    panelContent: @Composable BoxScope.() -> Unit = {},
    overlayContent: @Composable BoxScope.() -> Unit = {},
    safeContent: @Composable BoxScope.() -> Unit = {},
    messageContent: @Composable BoxScope.() -> Unit = {},
) {
    Box(modifier = modifier) {
        KeyboardLayout(panelState = panelState) {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .statusBarsPadding()
                        .keyboardSystemBarsPadding(panelState),
            ) {
                topBar()
                Box(
                    modifier =
                        Modifier
                            .zIndex(2f)
                            .fillMaxWidth()
                            .weight(1f),
                ) {
                    messageContent()
                    safeContent()
                }
                bottomBar()
            }
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.TopCenter,
            ) {
                panelContent()
            }
        }

        overlayContent()
    }
}
