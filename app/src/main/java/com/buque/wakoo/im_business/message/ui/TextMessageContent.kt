package com.buque.wakoo.im_business.message.ui

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.MessageC2CUserScaffold
import com.buque.wakoo.im_business.message.MessageReminder
import com.buque.wakoo.im_business.message.MsgUIExtra
import com.buque.wakoo.im_business.message.types.UCTextMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent

data class TextMessageContent(
    val message: UCInstanceMessage,
    val extra: MsgUIExtra? = null,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        MessageC2CUserScaffold(message, onAction, bottomWidget = {
            if (extra != null) {
                MessageReminder(extra, message.isSelf, modifier = Modifier.padding(top = 5.dp))
            }
        }) {
            bubbleBox {
                val msg = message as UCTextMessage
                Text(text = msg.text)
            }
        }
    }
}

@Preview
@Composable
private fun PreviewText() {
}
