package com.buque.wakoo.im_business.message

import com.buque.wakoo.bean.RichItem
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im_business.message.ui.entry.MsgUIEntry
import com.buque.wakoo.utils.TypeRefresh

/**
 * 消息的ui显示需要的额外字段, 根据业务需求可自行扩展
 * @param expansionExtra 额外的扩展字段，可以使用时获取，也可以提前获取
 */
data class MsgUIExtra(
    val expansionExtra: MsgExpansionExtra? = null,
) {
    companion object {
        val CACHE = MsgUIExtra()
    }
}

/**
 * 扩展字段发生变化，可能会引起下列字段发生变化，需要重新生成, 可以根据业务自行扩展
 * @param hint 是否显示hint扩展
 */
data class MsgExpansionExtra(
    val hint: HintExpansionExtra? = null,
)

/**
 * hint相关的数据
 */
data class HintExpansionExtra(
    val hintVisible: Boolean = false,
    val isSelf: Boolean = true,
    val hintRichList: List<RichItem>? = null,
    val hintText: String? = null,
    val hintIcon: String? = null,
)

/**
 *
 * @param msgIndex 某些情况一条消息要裂变成多个item显示，通过这个索引去对应item, 默认为null，如果有裂变
 */
data class UIMessageEntry(
    val message: UCInstanceMessage,
    val uiEntry: MsgUIEntry,
    val refresh: TypeRefresh = TypeRefresh.Init,
    val msgIndex: Int? = null,
    val isFakerMessage: Boolean = false,
) {
    val key: Any
        get() {
            return if (isFakerMessage) {
                var id = message.id
                if (id == "0") {
                    id = message.timestamp.toString()
                }
                if (msgIndex == null) id else "index_${msgIndex}_$id"
            } else {
                message.id
            }
        }

    val requireMsgIndex: Int
        get() = msgIndex ?: 0

    val isSelf: Boolean
        get() = message.isSelf

    /**
     * 只改变refresh字段，其他字段不变，目前就是为了让ui自动刷新
     */
    fun onlyRefreshCopy() = copy(refresh = refresh.nextTypeRefresh())

    val user get() = message.requireUser()
}
