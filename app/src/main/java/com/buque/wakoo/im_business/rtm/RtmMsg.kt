package com.buque.wakoo.im_business.rtm

import com.buque.wakoo.app.AppJson
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.utils.LogUtils
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

class RtmMsg(
    val json: String,
) {
    private var jsonObject: JsonObject? = null
    var dataObject: JsonObject? = null
        private set

    init {
        try {
            jsonObject = AppJson.decodeFromString<JsonObject>(json)
            dataObject = jsonObject?.get("data")?.jsonObject
        } catch (e: Exception) {
            LogUtils.e("RtmMsg", "rtm msg : %s", e)
        }
    }

    val cmd: String? by lazy { jsonObject?.get("cmd")?.jsonPrimitive?.content }

    inline fun <reified T> getEntity(key: String): T? = dataObject?.getOrNull(key)?.let { AppJson.decodeFromJsonElement(it) }

    inline fun <reified T> fromDataJson(): T? = dataObject?.let { AppJson.decodeFromJsonElement(it) }
}
