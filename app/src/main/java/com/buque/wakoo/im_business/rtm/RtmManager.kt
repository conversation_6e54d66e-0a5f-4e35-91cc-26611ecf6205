package com.buque.wakoo.im_business.rtm

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import com.buque.wakoo.ext.resumeIfActive
import com.buque.wakoo.im.UCInstanceMessage
import com.buque.wakoo.im.compat.IMCompatCore
import com.buque.wakoo.im.inter.IMCompatListener
import com.buque.wakoo.im.utils.IMLogUtils
import com.buque.wakoo.im_business.message.types.UCCustomMessage
import com.buque.wakoo.im_business.rtm.channel.IMessageChannel
import com.buque.wakoo.im_business.rtm.channel.TIMessageChannel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

object RtmManager : IMCompatListener {
    private const val RTM_UNAVAILABLE_KEY = "rtm_unavailable_key"

    // 国区直播群 id 接收国区飘屏消息
    private const val TIM_UCOO_DEFAULT_CHANNEL = "rtmwakoo_global"

    // 日区直播群 id 接收日区飘屏消息
    private const val TIM_CUPID_DEFAULT_CHANNEL = "rtmwakoo_jp_global"

    private val _message: MutableSharedFlow<RtmMsg> = MutableSharedFlow()

    val message = _message.asSharedFlow()

    init {
        IMCompatCore.addIMListener(this)
    }

    fun getTimGlobalGroupId(isJP: Boolean): String = if (isJP) TIM_CUPID_DEFAULT_CHANNEL else TIM_UCOO_DEFAULT_CHANNEL

    /**
     * 加入了一个新的通道, 一般在webview或其他地方调用
     *
     * @param channelName 新通道的id
     */
    suspend fun joinMessageChannel(channelName: String): IMessageChannel {
        var retryCount = 3
        var joinResult = false
        val channel = TIMessageChannel(channelName)
        return channel.also {
            do {
                joinResult = it.join()
                if (!joinResult) {
                    suspendAvailable()
                }
                retryCount -= 1
            } while (joinResult.not() && retryCount > 0)
            if (!joinResult) {
                IMLogUtils.w("join channel failed...")
            }
        }
    }

    private suspend fun suspendAvailable() {
        val connectivityManager =
            IMCompatCore.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkRequest =
            NetworkRequest
                .Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()
        suspendCancellableCoroutine {
            val callback =
                object : ConnectivityManager.NetworkCallback() {
                    override fun onAvailable(network: Network) {
                        if (!it.isCancelled) {
                            connectivityManager.unregisterNetworkCallback(this)
                        }
                        it.resumeIfActive(Unit)
                    }

                    override fun onLost(network: Network) {
                    }
                }
            connectivityManager.registerNetworkCallback(networkRequest, callback)
            it.invokeOnCancellation {
                connectivityManager.unregisterNetworkCallback(callback)
            }
        }
    }

    override fun onRecvNewMessage(
        message: UCInstanceMessage,
        offline: Boolean,
    ) {
        if (!offline && !message.isC2CMsg && message is UCCustomMessage) {
            val id = getTimGlobalGroupId(IMCompatCore.currentUser?.isJP == true)
            if (message.targetId == id) {
                handleRtmMsg(message)
            }
        }
    }

    private fun handleRtmMsg(message: UCCustomMessage) {
        IMCompatCore.obtainLoginCoroutineScope().launch {
            _message.emit(RtmMsg(message.rawContent))
        }
    }
}
