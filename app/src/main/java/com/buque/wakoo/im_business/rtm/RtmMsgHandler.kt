package com.buque.wakoo.im_business.rtm

import android.graphics.Color
import androidx.core.graphics.toColorInt
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import com.buque.wakoo.app.AppJson
import com.buque.wakoo.app.Const
import com.buque.wakoo.core.pay.GoogleBillingManager.logI
import com.buque.wakoo.ext.getOrNull
import com.buque.wakoo.ext.getStringOrNull
import com.buque.wakoo.im.utils.takeIsNotEmpty
import com.buque.wakoo.manager.AppConfigManager
import com.buque.wakoo.manager.L10nManager
import com.buque.wakoo.manager.localized
import com.buque.wakoo.network.api.bean.UserResponse
import com.buque.wakoo.ui.floating.CPMadeFloatingBannerTask
import com.buque.wakoo.ui.floating.FloatingBannerTask
import com.buque.wakoo.utils.LogUtils
import com.buque.wakoo.utils.eventBus.EventBus
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonObject

abstract class IRtmMsgHandler {
    companion object {
        const val CP_PUBLIC = "cp_public"

        const val GIVE_BIG_GIFT = "give_big_gift"

        const val LUCKY_GIFT_BIG_REWARD = "lucky_gift_big_reward" // 幸运球抽奖

        const val SPEED_RACING_WIN = "speed_racing_win" // 极速赛车

        const val RELATIONSHIP_BIG_GIFT_GIVEN = "relationship_big_gift_given"

        const val COMMON_FLOAT_SCREEN_MESSAGES = "common_float_screen_messages"
        const val COMMON_FLOAT_SCREEN_MESSAGE_V2 = "common_float_screen_messages_v2"

        const val CPD_LUCKY_JACKPOT_REWARD = "japan_lucky_gift_float_screen_messages"
    }

    abstract fun handleMessage(msg: RtmMsg)
}

class RtmMsgHandler : IRtmMsgHandler() {
    private val conf
        get() = AppConfigManager.uiConfigFlow.value
    private val colorYellow = "#FFFFD362".toColorInt()

    private fun isCmdAllowed(cmd: String): Boolean {
        if (!conf.shiftBannerEnabled) { // 之前是没有这个逻辑的,可能是新加的
            return false
        }
        return when (cmd) {
            CP_PUBLIC -> conf.goodPartnerShiftBannerEnabled
            GIVE_BIG_GIFT -> conf.superBonusShiftBannerEnabled
            LUCKY_GIFT_BIG_REWARD -> conf.luckyShiftBannerEnabled
//            SPEED_RACING_WIN -> conf.showSpeedRacing
            CPD_LUCKY_JACKPOT_REWARD -> true
            else -> conf.hotTasteEnabled
        }
    }

    private fun RtmMsg.getPriority(): Int = getEntity<Int>("priority") ?: 0

    override fun handleMessage(msg: RtmMsg) {
        val cmd = msg.cmd ?: return

        if (!isCmdAllowed(cmd)) {
            logI("skip! $cmd was not allowed.")
            return
        }

        // 现在已经不严格根据信令和分区来做不同的逻辑了, 也许日区也会有CP通知了?
        when (cmd) {
            COMMON_FLOAT_SCREEN_MESSAGES, COMMON_FLOAT_SCREEN_MESSAGE_V2 -> { // 赠送了礼物
                showCommonBanner(msg)
            }

            RELATIONSHIP_BIG_GIFT_GIVEN -> { // 关系团送了一个大礼物
            }

            CP_PUBLIC -> { // 组成CP了
                showCPMadeBanner(msg)
            }

            GIVE_BIG_GIFT -> { // 送了一个大礼物
            }

            LUCKY_GIFT_BIG_REWARD -> { // 幸运礼物大将
            }

            SPEED_RACING_WIN -> { // 赛车赢了
            }

            else -> {
            }
        }
    }

    /**
     * 通用飘屏
     */
    private fun showCommonBanner(msg: RtmMsg) {
        try {
            val array = msg.getEntity<JsonArray>("messages")
            msg.getEntity<List<DynamicBannerInfo>>("messages")?.let { list ->
                (
                    if (msg.cmd == COMMON_FLOAT_SCREEN_MESSAGE_V2) {
                        list.filter {
                            if (it.businessCode.startsWith("0101")) {
                                AppConfigManager.uiConfigFlow.value.luckyShiftBannerEnabled
                            } else if (it.businessCode == Const.BusinessCode.hongbao) {
                                AppConfigManager.uiConfigFlow.value.packetEnabled
                            } else {
                                true
                            }
                        }
                    } else {
                        list
                    }
                ).mapIndexed { index, info ->
                    val element = array?.getOrNull(index)?.jsonObject
                    val jump =
                        element?.getStringOrNull("android_jump_link").takeIsNotEmpty() ?: element?.getStringOrNull("jump_link")
                    val bgList = element?.getOrNull("btn_imgs")?.let { AppJson.decodeFromJsonElement<List<String>>(it) }
                    val link =
                        if (!jump.isNullOrEmpty() && !bgList.isNullOrEmpty()) {
                            if (L10nManager.languageTag == "zh-Hant") {
                                bgList.getOrNull(1).takeIsNotEmpty() ?: bgList[0]
                            } else {
                                bgList[0]
                            } to jump
                        } else {
                            null
                        }
                    FloatingBannerTask(info, link)
                }.also {
                    it.forEach {
                        EventBus.trySend(it)
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtils.e("RTM", "${e.message},${msg.json}")
        }
    }

    private fun showCPMadeBanner(msg: RtmMsg) {
        val fromUser = msg.getEntity<UserResponse>("from_user")
        val toUser = msg.getEntity<UserResponse>("to_user")
        val level = msg.getEntity<Int>("public_cp_level") ?: 0
        val content =
            buildSpannedString {
                append("恭喜".localized)
                color(colorYellow) {
                    append(" ${fromUser?.nickname} ")
                }
                append("和".localized)
                color(colorYellow) {
                    append(" ${toUser?.nickname} ")
                }
                append(
                    when (level) {
                        2 -> "成为旷世官宣".localized
                        3 -> "成为至臻官宣".localized
                        else -> "成为真爱官宣".localized
                    },
                )
                append("CP")
            }

        val jump = msg.getEntity<String>("jump_link")
        val bgImg = msg.getEntity<String>("bg_img")
        val bgList = msg.getEntity<List<String>>("btn_imgs")
        val link =
            if (!jump.isNullOrEmpty() && !bgList.isNullOrEmpty()) {
                if (L10nManager.languageTag == "zh-Hant") {
                    bgList.getOrNull(1).takeIsNotEmpty() ?: bgList[0]
                } else {
                    bgList[0]
                } to jump
            } else {
                null
            }

        EventBus.trySend(
            CPMadeFloatingBannerTask(
                listOf(fromUser?.avatarUrl ?: "", toUser?.avatarUrl ?: ""),
                content,
                bgImg ?: "",
                link,
                msg.getPriority(),
            ),
        )
    }
}
