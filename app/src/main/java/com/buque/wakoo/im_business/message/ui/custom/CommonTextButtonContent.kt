package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.utils.eventBus.tryToLink

class CommonTextButtonContent(
    val content: String,
    val btnText: String,
    val link: String,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        baseBox {
            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .background(color = Color.White, shape = RoundedCornerShape(12.dp))
                        .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    content,
                    style =
                        TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 20.sp,
                            color = Color(0xFF1D2129),
                        ),
                )
                SizeHeight(10.dp)
                GradientButton(
                    btnText,
                    onClick = {
                        link.tryToLink()
                    },
                    modifier = Modifier.size(151.dp, 25.dp),
                    textColor = Color(0xff111111),
                    fontSize = 14.sp,
                )
            }
        }
    }
}

@Preview
@Composable
fun Preview(modifier: Modifier = Modifier) {
    GradientButton(
        "哈哈哈哈哈",
        onClick = {

        },
        modifier = Modifier.size(151.dp, 25.dp),
        textColor = Color(0xff111111),
        fontSize = 14.sp,
    )
}
