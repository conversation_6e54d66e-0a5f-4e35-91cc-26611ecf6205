package com.buque.wakoo.im_business.message.ui.custom

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.bean.user.User
import com.buque.wakoo.bean.user.isSelf
import com.buque.wakoo.im_business.interf.IIMAction
import com.buque.wakoo.im_business.message.ui.entry.MsgLayoutContent
import com.buque.wakoo.manager.localized
import com.buque.wakoo.ui.widget.GradientVerticalButton
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.image.AvatarNetworkImage

class InviteUserMemberContent(
    val inviter: User?,
    val invitee: User?,
) : MsgLayoutContent() {
    @Composable
    override fun RenderDefault(
        baseBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        bubbleBox: @Composable ((@Composable (() -> Unit)) -> Unit),
        onAction: IIMAction,
    ) {
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.TopCenter) {
            Box(
                modifier =
                    Modifier
                        .padding(top = 16.dp)
                        .fillMaxWidth(0.72f)
                        .background(
                            brush = Brush.verticalGradient(listOf(Color(0xffFFCBE1), Color(0xffFFFDFE))),
                            shape = RoundedCornerShape(12.dp),
                        )
                        .border(
                            1.dp,
                            color = Color(0xFFFFD2E2),
                            shape = RoundedCornerShape(12.dp),
                        ),
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_invite_msg_bg),
                    modifier =
                        Modifier
                            .align(Alignment.TopEnd)
                            .padding(18.5.dp)
                            .size(64.dp),
                    contentDescription = null,
                )
                if (inviter?.id?.isSelf == true) {
                    InviterView(
                        modifier =
                            Modifier
                                .padding(top = 56.dp, bottom = 16.dp)
                                .padding(horizontal = 16.dp)
                                .fillMaxWidth(),
                    )
                } else {
                    InviteeView(
                        modifier =
                            Modifier
                                .padding(top = 56.dp, bottom = 16.dp)
                                .padding(horizontal = 16.dp)
                                .fillMaxWidth(),
                    )
                }
            }
            Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
                inviter?.let {
                    AvatarNetworkImage(inviter, size = 56.dp, modifier = Modifier.border(1.dp, color = Color.White, CircleShape))
                }
                invitee?.let {
                    AvatarNetworkImage(invitee, size = 56.dp, modifier = Modifier.border(1.dp, color = Color.White, CircleShape))
                }
            }
        }
    }

    // 主播视角
    @Composable
    fun BoxScope.InviteeView(modifier: Modifier = Modifier) {
        Column(
            modifier = modifier,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                "对方觉得和你聊天很愉快,希望与你成为好友".localized,
                Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(listOf(Color.Transparent, Color(0xA1ffffff), Color.Transparent)),
                    )
                    .padding(horizontal = 20.dp, vertical = 8.dp),
                fontSize = 14.sp,
                lineHeight = 21.sp,
                color = Color(0xfff34182),
                textAlign = TextAlign.Center,
            )
            SizeHeight(18.dp)
            Box {
                GradientVerticalButton(
                    "添加好友".localized,
                    onClick = {},
                    gradientColors = listOf(Color(0xffFF679F), Color(0xffF52B76)),
                    modifier =
                        Modifier
                            .padding(top = 8.dp)
                            .size(190.dp, 36.dp),
                    fontSize = 14.sp,
                    textColor = Color.White,
                )
                Text(
                    "会员权限".localized,
                    modifier =
                        Modifier
                            .align(Alignment.TopEnd)
                            .background(
                                brush = Brush.horizontalGradient(listOf(Color(0xffffe072), Color(0xffFFF799))),
                                shape = CircleShape,
                            )
                            .padding(horizontal = 4.dp, vertical = 2.dp),
                    color = Color(0xff593a0c),
                    fontSize = 10.sp,
                )
            }
        }
    }

    // 用户视角
    @Composable
    fun BoxScope.InviterView(modifier: Modifier = Modifier) {
        Column(
            modifier = modifier,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                "已邀请对方开通会员并添加你为好友".localized,
                Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.horizontalGradient(listOf(Color.Transparent, Color(0xA1ffffff), Color.Transparent)),
                    )
                    .padding(horizontal = 20.dp, vertical = 8.dp),
                fontSize = 14.sp,
                lineHeight = 21.sp,
                color = Color(0xfff34182),
                textAlign = TextAlign.Center,
            )
            SizeHeight(14.dp)
        }
    }
}
