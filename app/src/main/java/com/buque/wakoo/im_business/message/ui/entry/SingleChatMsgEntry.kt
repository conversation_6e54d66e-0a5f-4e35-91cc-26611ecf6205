package com.buque.wakoo.im_business.message.ui.entry

import com.buque.wakoo.bean.user.BasicUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 邀请组CP
 */
@Serializable
data class CPInviteME(
    @SerialName("invite_code")
    val inviteCode: String,
    val link: String,
    @SerialName("inviter")
    val inviter: BasicUser,
    @SerialName("inviter_msg")
    val message: String,
    @SerialName("expire_timestamp")
    val expireTimeStamp: Long,
) : MsgUIEntry

/**
 * 组成CP成功，查看Cp空间
 */
@Serializable
data class MakCPSuccessME(
    val inviter: BasicUser,
    val invitee: BasicUser,
    val digest: String,
) : MsgUIEntry

/**
 * 引导组CP
 */
@Serializable
data class GuideCPSuccessME(
    val digest: String,
    val link: String,
) : MsgUIEntry
