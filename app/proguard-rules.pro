# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keep class com.tencent.** { *; }
-keep class com.tencent.imsdk.** { *; }
-dontwarn com.tencentcloudapi.cls.android.producer.Result
-keep class com.ishumei.** {*;}
-keep class androidx.compose.material3.TabRowKt { *; }

# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn com.bumptech.glide.Glide
-dontwarn com.bumptech.glide.RequestBuilder
-dontwarn com.bumptech.glide.RequestManager
-dontwarn com.bumptech.glide.request.FutureTarget

# jsbridge
 -keep class com.smallbuer.jsbridge.core.** { *; }
 #
 -keep @androidx.annotation.Keep class * {*;}
 # 坑
 -keep class androidx.viewpager2.widget.** { *; }
 #CacheWebview
 -dontwarn ren.yale.android.cachewebviewlib.**
 -keep class ren.yale.android.cachewebviewlib.**{*;}

 -keep class com.tencent.qcloud.** { *; }
 -keep class com.tencent.timpush.** { *; }

 -keep class cn.thinkingdata.** { *; }
 -dontwarn cn.thinkingdata.ta_apt.TRoute