{"formatVersion": 1, "database": {"version": 1, "identityHash": "8865d3c2d58614706146c712cb0d9b73", "entities": [{"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userid` TEXT NOT NULL, `public_id` TEXT NOT NULL, `nickname` TEXT NOT NULL, `avatar_url` TEXT NOT NULL, `gender` INTEGER NOT NULL, `age` INTEGER NOT NULL, `birthday` TEXT NOT NULL, `is_member` INTEGER NOT NULL, `height` INTEGER NOT NULL, `short_intro` TEXT NOT NULL, `type` INTEGER NOT NULL, PRIMARY KEY(`userid`))", "fields": [{"fieldPath": "id", "columnName": "userid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "publishId", "columnName": "public_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "nickname", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "age", "columnName": "age", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "birthday", "columnName": "birthday", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_member", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "height", "columnName": "height", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "intro", "columnName": "short_intro", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userid"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '8865d3c2d58614706146c712cb0d9b73')"]}}