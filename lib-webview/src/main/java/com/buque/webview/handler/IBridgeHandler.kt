package com.buque.webview.handler

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.buque.webview.utils.Logger
import com.buque.webview.utils.callFailure
import com.buque.webview.utils.scope
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.longOrNull
import org.json.JSONObject

val sJson =
    Json {
        ignoreUnknownKeys = true // 忽略 API 返回中未在数据模型中定义的字段
        isLenient = true // 允许一些不严格的 JSON 格式
        prettyPrint = true // 开发时可以设置为 true 来格式化打印 JSON 日志，发布时建议 false
        coerceInputValues = true // 如果API返回的类型与模型不完全匹配（例如string对应int），尝试转换
        encodeDefaults = true
        explicitNulls = false
    }

abstract class BaseBridgeHandler : BridgeHandler() {
    var tag = this::class.java.simpleName

    abstract fun handlerV2(
        context: Context,
        data: JsonObject,
        callback: CallBackFunction,
    )

    final override fun handler(
        context: Context,
        data: String,
        callback: CallBackFunction,
    ) {
        Logger.d("BridgeHandler", "tag: $tag, receive data: $data")
        obtainCoroutineScope(context).launch(Dispatchers.Main) {
            try {
                if (data.isBlank()) {
                    handlerV2(context, buildJsonObject { }, callback)
                } else {
                    handlerV2(
                        context,
                        withContext(Dispatchers.IO) {
                            sJson.decodeFromString(data)
                        },
                        callback,
                    )
                }
            } catch (e: Exception) {
                Logger.e("BridgeHandler", e)
                callback.sendFailure(context, msg = e.message)
            }
        }
    }

    inline fun <reified T> CallBackFunction.sendSuccess(
        context: Context,
        data: T? = null,
        msg: String? = "success",
    ) {
        obtainCoroutineScope(context).launch(Dispatchers.IO) {
            try {
//                val responseContent = sJson.encodeToString(JsBridgeResponse(0, msg, data))
                val obj = JSONObject()
                obj.put("status", 0)
                obj.put("msg", msg)
                obj.put("data", data?.toString())
                val responseContent = obj.toString()
                onCallBack(responseContent)
                Logger.i("BridgeHandler", "tag: $tag, success callback: $responseContent")
            } catch (e: Exception) {
                Logger.e("BridgeHandler", e)
            }
        }
    }

    fun CallBackFunction.sendFailure(
        context: Context,
        status: Int = -1,
        msg: String? = "failure",
    ) {
        obtainCoroutineScope(context).launch(Dispatchers.IO) {
            callFailure(msg = msg)
        }
    }

    protected fun JsonObject.getString(key: String): String? = get(key)?.jsonPrimitive?.contentOrNull

    protected fun JsonObject.getInt(key: String): Int? = get(key)?.jsonPrimitive?.intOrNull

    protected fun JsonObject.getLong(key: String): Long? = get(key)?.jsonPrimitive?.longOrNull

    protected fun JsonObject.getBoolean(key: String): Boolean? = get(key)?.jsonPrimitive?.booleanOrNull

    protected fun JsonObject.getStringOrElse(
        key: String,
        default: String,
    ): String = getString(key) ?: default

    protected fun JsonObject.getIntOrElse(
        key: String,
        default: Int,
    ): Int = getInt(key) ?: default

    protected fun JsonObject.getLongOrElse(
        key: String,
        default: Long,
    ): Long = getLong(key) ?: default

    protected fun JsonObject.getBooleanOrElse(
        key: String,
        default: Boolean,
    ): Boolean = getBoolean(key) ?: default

    fun obtainCoroutineScope(context: Context): CoroutineScope = (context as? LifecycleOwner)?.lifecycleScope ?: scope
}

