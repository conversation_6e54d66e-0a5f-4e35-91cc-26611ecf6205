package com.buque.webview

import android.app.Activity
import android.content.Context
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebView.setWebContentsDebuggingEnabled
import com.buque.webview.client.IWebViewClientProxy
import com.buque.webview.client.LibWebChromeClient
import com.buque.webview.client.LibWebViewClient
import com.buque.webview.utils.Logger
import com.kevinnzou.web.AccompanistWebChromeClient
import com.kevinnzou.web.AccompanistWebViewClient
import com.smallbuer.jsbridge.core.Bridge
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeTiny

class WebViewProxy(
    private val activity: Activity,
    private val eventHandlers: List<Pair<String, BridgeHandler>> = emptyList(),
    private val onLackCameraPermission: () -> Unit = {},
    private val webViewStore: (() -> AppBridgeWebView?)? = null,
    private val onWebViewCreated: (AppBridgeWebView) -> Unit = {},
    private val iWebViewClientProxy: IWebViewClientProxy? = null,
    private val onWebViewDispose: (WebView) -> Unit = {}
) {
    private lateinit var bridgeTiny: BridgeTiny
    val client: AccompanistWebViewClient by lazy {
        LibWebViewClient().apply {
            proxy = iWebViewClientProxy
        }
    }

    val bridge: BridgeTiny?
        get() {
            return if (::bridgeTiny.isInitialized) {
                bridgeTiny
            } else null
        }

    val chromeClient: AccompanistWebChromeClient by lazy { LibWebChromeClient(activity, onLackCameraPermission) }

    val factory: (Context) -> WebView = {
        val webView =
            webViewStore?.invoke() ?: AppBridgeWebView(it).apply {

                if (Bridge.INSTANCE.debug) {
                    setWebContentsDebuggingEnabled(true)
                }
            }.also { view ->
                (view.parent as? ViewGroup)?.removeAllViews()
                onWebViewCreated(view)
            }

        bridgeTiny = BridgeTiny(webView)

        webView.bridgeTiny = bridgeTiny
        eventHandlers.forEach { pair ->
            webView.addHandlerLocal(pair.first, pair.second)
        }
        if (Bridge.INSTANCE.debug) {
            Logger.i("Bridge", "bridgeTiny handlers:${bridgeTiny.messageHandlers.keys}")
            Logger.i("Bridge", "webview handlers:${webView.localMessageHandlers.keys}")
        }
        (client as? LibWebViewClient)?.bridge = bridgeTiny
        (chromeClient as? LibWebChromeClient)?.bridge = bridgeTiny
        webView
    }

    val onCreated: (WebView) -> Unit = {
        with(it.settings) {
            setSupportZoom(false)
            builtInZoomControls = false
            defaultTextEncodingName = "utf-8"
            defaultFontSize = 14
            layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
            // 两者都可以
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            domStorageEnabled = true
            cacheMode = WebSettings.LOAD_NO_CACHE
            javaScriptCanOpenWindowsAutomatically = true
            useWideViewPort = true
            javaScriptEnabled = true
        }

        with(it) {
            clearCache(true)
            clearHistory()
            if (Bridge.INSTANCE.debug) {
                setWebContentsDebuggingEnabled(true)
            }
            android.webkit.CookieManager
                .getInstance()
                .setAcceptThirdPartyCookies(this, true)
            setVerticalScrollbarOverlay(false)
            isHorizontalScrollBarEnabled = false
            setHorizontalScrollbarOverlay(false)
            overScrollMode = WebView.OVER_SCROLL_NEVER
            isFocusable = true
            isVerticalScrollBarEnabled = false
            isHorizontalScrollBarEnabled = false


            WBH5FaceVerifySDK.getInstance().setWebViewSettings(this, context.applicationContext)
        }
    }
    val onDispose: (WebView) -> Unit = { view ->
        (view.parent as? ViewGroup)?.removeAllViews()
        onWebViewDispose(view)
    }
}
