package com.buque.webview.utils

import com.smallbuer.jsbridge.core.CallBackFunction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import org.json.JSONObject

internal val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())


fun CallBackFunction.callSuccess(data: JSONObject? = null, msg: String = "success") {
    val jsonObject = JSONObject().apply {
        put("status", 0)
        put("msg", msg)
        put("data", data)
    }
    onCallBack(jsonObject.toString())
}

fun CallBackFunction.callFailure(data: JSONObject? = null, msg: String? = "failure") {
    val jsonObject = JSONObject().apply {
        put("status", -1)
        put("msg", msg)
        put("data", data)
    }
    onCallBack(jsonObject.toString())
}