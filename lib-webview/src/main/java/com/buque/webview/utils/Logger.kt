package com.buque.webview.utils

import android.util.Log

object Logger {
    fun i(
        tag: String,
        message: String,
    ) {
        Log.i(tag, message)
    }

    fun d(
        tag: String,
        message: String,
    ) {
        Log.d(tag, message)
    }

    fun e(
        tag: String,
        exception: Exception,
    ) {
        Log.e(tag, exception.message.orEmpty().ifEmpty { exception.stackTraceToString() })
    }
}
