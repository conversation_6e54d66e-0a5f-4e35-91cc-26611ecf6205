package com.buque.webview.client

import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import com.buque.webview.asIWebVIew
import com.kevinnzou.web.AccompanistWebViewClient
import com.smallbuer.jsbridge.core.BridgeTiny


interface IWebViewClientProxy {
    fun shouldInterceptRequest(view: WebView?, request: WebResourceRequest?): WebResourceResponse?

    fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean?
}


open class LibWebViewClient : AccompanistWebViewClient() {
    var bridge: BridgeTiny? = null

    var proxy: IWebViewClientProxy? = null

    override fun shouldInterceptRequest(view: WebView?, request: WebResourceRequest?): WebResourceResponse? {
        return proxy?.shouldInterceptRequest(view, request) ?: super.shouldInterceptRequest(view, request)
    }

    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
        return proxy?.shouldOverrideUrlLoading(view, request?.url?.toString()) ?: super.shouldOverrideUrlLoading(view, request)
    }

    override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
        return proxy?.shouldOverrideUrlLoading(view, url) ?: super.shouldOverrideUrlLoading(view, url)
    }

    override fun onPageFinished(view: WebView, url: String?) {
        super.onPageFinished(view, url)
        (view.asIWebVIew())?.also {
            bridge?.webViewLoadJs(it)
        }
    }
}