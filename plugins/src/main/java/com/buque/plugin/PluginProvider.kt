package com.buque.plugin

import com.android.build.api.variant.AndroidComponentsExtension
import com.android.build.api.variant.ApplicationAndroidComponentsExtension
import com.android.build.api.variant.LibraryAndroidComponentsExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import kotlin.jvm.java

import java.io.ByteArrayOutputStream


// --- Git 信息数据类 ---
data class GitCommit(
    val hash: String,
    val shortHash: String,
    val message: String,
    val author: String,
    val date: String,
)

data class GitInfo(
    val branch: String,
    val commitHash: String,
    val shortCommitHash: String,
    val commitMessage: String,
    val commitAuthor: String,
    val commitDate: String,
    val recentCommits: List<GitCommit>,
    val buildTime: Long = System.currentTimeMillis(),
)

/**
 * 执行 Git 命令并返回结果
 */
private fun executeGitCommand(
    project: Project,
    command: String,
): String =
    try {
        val output = ByteArrayOutputStream()
        project.exec {
            commandLine("git", *command.split(" ").toTypedArray())
            standardOutput = output
            isIgnoreExitValue = true
        }
        output.toString().trim()
    } catch (e: Exception) {
        project.logger.warn("Failed to execute git command: $command", e)
        ""
    }


/**
 * 获取 Git 信息
 */
private fun getGitInfo(project: Project): GitInfo {
    // 获取当前分支
    val branch = executeGitCommand(project, "rev-parse --abbrev-ref HEAD").ifEmpty { "unknown" }

    // 获取当前提交信息
    val commitHash = executeGitCommand(project, "rev-parse HEAD").ifEmpty { "unknown" }
    val shortCommitHash = executeGitCommand(project, "rev-parse --short HEAD").ifEmpty { "unknown" }
    val commitMessage = executeGitCommand(project, "log -1 --pretty=format:%s").ifEmpty { "unknown" }
    val commitAuthor = executeGitCommand(project, "log -1 --pretty=format:%an").ifEmpty { "unknown" }
    val commitDate = executeGitCommand(project, "log -1 --pretty=format:%ci").substringBefore(" +").ifEmpty { "unknown" }

    // 获取最近5条提交记录
    val recentCommits = mutableListOf<GitCommit>()
    try {
        val commitHashes = executeGitCommand(project, "log -5 --pretty=format:%H").split("\n").filter { it.isNotEmpty() }

        for (hash in commitHashes) {
            if (hash.isNotEmpty()) {
                val shortHash = executeGitCommand(project, "rev-parse --short $hash")
                val message = executeGitCommand(project, "log -1 --pretty=format:%s $hash")
                val author = executeGitCommand(project, "log -1 --pretty=format:%an $hash")
                val date = executeGitCommand(project, "log -1 --pretty=format:%ci $hash")

                recentCommits.add(
                    GitCommit(
                        hash = hash,
                        shortHash = shortHash,
                        message = message.replace("\"", "\\\""),
                        author = author,
                        date = date,
                    ),
                )
            }
        }
    } catch (e: Exception) {
        project.logger.warn("Failed to get recent commits", e)
    }

    return GitInfo(
        branch = branch,
        commitHash = commitHash,
        shortCommitHash = shortCommitHash,
        commitMessage = commitMessage.replace("\"", "\\\""),
        commitAuthor = commitAuthor,
        commitDate = commitDate,
        recentCommits = recentCommits,
    )
}

class PluginProvider : Plugin<Project> {
    override fun apply(project: Project) {

        val extension = project.extensions.getByType(AndroidComponentsExtension::class.java)

        // parse json

        if (extension is ApplicationAndroidComponentsExtension) {
            //是application
            extension.onVariants { variant ->

                // 1. 读取配置文件
                val configFile = project.rootProject.file("config/env.config.json")
                if (!configFile.exists()) {
                    throw IllegalStateException("Main config file not found at: ${configFile.path}")
                }

                // 2. 获取 Git 信息
                val gitInfo = getGitInfo(project)

                // 从 variant 名称中提取环境信息
                val variantName = variant.name
                val environmentName =
                    when {
                        variantName.startsWith("development") -> "development"
                        variantName.startsWith("production") -> "production"
                        else -> "unknown"
                    }

                // 3. 注入到 BuildConfig
                variant.buildConfigFields?.apply {
                    // 注入完整的配置 JSON 字符串
                    val configJsonString =
                        configFile
                            .readText()
                            .replace("\"", "\\\"") // 转义双引号
                            .replace("\n", "\\n") // 转义换行符
                            .replace("\r", "\\r") // 转义回车符
                            .replace("\t", "\\t") // 转义制表符

                    put(
                        "ENV_CONFIG_JSON",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"$configJsonString\"",
                            "Complete environment configuration JSON",
                        ),
                    )

                    // 注入当前编译时环境名称
                    put(
                        "COMPILE_TIME_ENVIRONMENT",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"$environmentName\"",
                            "Compile time environment name",
                        ),
                    )

                    // 注入 Git 分支信息
                    put(
                        "GIT_BRANCH",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"${gitInfo.branch}\"",
                            "Current Git branch",
                        ),
                    )

                    // 注入当前提交哈希
                    put(
                        "GIT_COMMIT_HASH",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"${gitInfo.commitHash}\"",
                            "Current Git commit hash",
                        ),
                    )

                    // 注入短提交哈希
                    put(
                        "GIT_SHORT_COMMIT_HASH",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"${gitInfo.shortCommitHash}\"",
                            "Current Git short commit hash",
                        ),
                    )

                    // 注入提交消息
                    put(
                        "GIT_COMMIT_MESSAGE",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"${gitInfo.commitMessage}\"",
                            "Current Git commit message",
                        ),
                    )

                    // 注入提交作者
                    put(
                        "GIT_COMMIT_AUTHOR",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"${gitInfo.commitAuthor}\"",
                            "Current Git commit author",
                        ),
                    )

                    // 注入提交日期
                    put(
                        "GIT_COMMIT_DATE",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"${gitInfo.commitDate}\"",
                            "Current Git commit date",
                        ),
                    )

                    // 注入构建时间
                    put(
                        "GIT_BUILD_TIME",
                        com.android.build.api.variant.BuildConfigField(
                            "long",
                            "${gitInfo.buildTime}L",
                            "Build timestamp",
                        ),
                    )

                    // 注入最近5条提交记录（手动构建JSON格式）
                    val recentCommitsJson =
                        buildString {
                            append("[")
                            gitInfo.recentCommits.forEachIndexed { index, commit ->
                                if (index > 0) append(",")
                                append("{")
                                append("\\\"hash\\\":\\\"${commit.hash}\\\",")
                                append("\\\"shortHash\\\":\\\"${commit.shortHash}\\\",")
                                append("\\\"message\\\":\\\"${commit.message}\\\",")
                                append("\\\"author\\\":\\\"${commit.author}\\\",")
                                append("\\\"date\\\":\\\"${commit.date}\\\"")
                                append("}")
                            }
                            append("]")
                        }

                    put(
                        "GIT_RECENT_COMMITS_JSON",
                        com.android.build.api.variant.BuildConfigField(
                            "String",
                            "\"$recentCommitsJson\"",
                            "Recent 5 Git commits in JSON format",
                        ),
                    )
                }
            }

        } else if (extension is LibraryAndroidComponentsExtension) {


        } else {
            println(extension.toString())
        }
    }
}

