# 文件名: api_client.py

import hashlib
import json
import random
import time
import requests
from typing import Dict, Optional, Any


# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ucoo.settings')
# import django
# django.setup()
#
# from apps.ucuser.service import UserService

# TEST_SERVER = "https://api.test.ucoofun.com"

# --- API 相关配置 (保持不变) ---
TEST_SERVER = "https://fastapi.wakooclub.com"
ACCESS_KEY = "aa8396d1c7bc42bc89462a196879f9bd"
SECRET_KEY = "dd1ad7ddb8d84801ae182794732003e5"

def _sign_param(param: Dict[str, Any]) -> None:
    """内部函数，为请求参数生成签名。"""
    param["timestamp"] = str(int(time.time()))
    param["nonce"] = str(random.randint(1000000000, 9999999999))
    param["access_key"] = ACCESS_KEY
    string_a = '&'.join([f'{key}={param[key]}' for key in sorted(param.keys())])
    string_sign_temp = string_a + f'&secret_key={SECRET_KEY}'
    sign = hashlib.sha1(string_sign_temp.encode('utf-8')).hexdigest().upper()
    param["sign"] = sign


class ApiClient:
    """API客户端，封装所有网络请求逻辑。"""
    def __init__(self):
        self.access_token = ""
        self.session = requests.Session()
        print("🚀 API客户端已初始化。")

    def login(self, user_token: str, verify_code: str) -> bool:
        # (此函数无变化，保持原样)
        print(f"🔄 正在尝试使用账号 {user_token} 登录...")
        url = f"{TEST_SERVER}/api/ucuser/v1/user/ensure"
        data = {
            "channel_t": 1, "user_token": user_token,
            "phone_area_code": "86", "verify_code": verify_code,
        }
        _sign_param(data)
        data["HTTP_DEVICE_ID"] = "main_script_1"
        try:
            res = self.session.post(url, json=data, timeout=10)
            res.raise_for_status()
            tokens = res.json()
            if tokens.get("status") == 0 and "access_token" in tokens.get("data", {}):
                self.access_token = tokens["data"]["access_token"]
                self.session.headers.update({"Access-Token": self.access_token})
                print("✅ 登录成功，已获取 Access Token。")
                return True
            else:
                print(f"❌ 登录失败: {res.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 登录请求时发生网络错误: {e}")
            return False

    def get_translation_data(self, language_code: str, platform: str) -> Optional[Dict[str, str]]:
        """
        获取翻译数据。此版本增强了错误处理能力。
        """
#         if not self.access_token:
#             print(f"  - ❌ 获取失败: 未登录 (Access Token 为空)。")
#             return None

        print(f"  - 正在从API获取 {platform.upper()} 平台的 '{language_code}' 数据...")

        info_data = None
        download_link = None

        try:
            # --- 第一步: 获取下载链接 ---
            url = f'{TEST_SERVER}/api/xya/config/v1/translate/latest'
            params = {"language_code": language_code}
            _sign_param(params)
            headers = {
                "Client-Platform": platform, "App-Version": "1.0.0",
                "Package-Name": "com.site.wakoo"
            }
            res_info = self.session.get(url, params=params, headers=headers, timeout=10)
            res_info.raise_for_status() # 检查HTTP错误 (如 404, 500)
            info_data = res_info.json()

            if info_data.get("status") != 0 or "link" not in info_data.get("data", {}):
                print(f"  - ❌ API返回错误: {info_data.get('message', '未知错误')}")
                return None

            download_link = info_data["data"]["link"]
            print(f"    > 获取到下载链接: {download_link}")

            # --- 第二步: 从链接下载文件内容 ---
            res_content = self.session.get(download_link, timeout=20)
            res_content.raise_for_status()

            # ★★★ 新增的关键检查 ★★★
            # 在尝试解析JSON之前，检查内容是否为空
            if not res_content.text.strip():
                print(f"    > ⚠️ 警告: 从链接下载的内容为空，无法解析。")
                return None

            # --- 第三步: 解析JSON ---
            print(f"    > ✅ 成功下载数据，正在解析...")
            return res_content.json()

        except requests.exceptions.RequestException as e:
            # 捕获所有网络相关的错误
            print(f"  - ❌ 请求或下载时发生网络错误: {e}")
            return None
        except json.JSONDecodeError as e:
            # 现在可以准确地捕获JSON解析错误
            print(f"  - ❌ 解析失败: 下载的内容不是有效的JSON格式。错误: {e}")
            return None