# 文件名: generate_comparison.py

import os
import json
import csv
import time
from typing import List, Dict, Tuple

# 从我们自己创建的 api_client.py 文件中导入 ApiClient 类
from api_client import ApiClient

# --- 配置区域 ---

# 用户认证配置 (由主脚本控制)
USER_TOKEN = "18211110007"
VERIFY_CODE = "555555"

# 1. 基准/原文JSON文件
SOURCE_JSON_FILE = 'l10n/localizable.json'

# 2. 需要对比的语言代码列表
LANGUAGES_TO_COMPARE = [
    'en',
    'ja'
]

# 3. 输出的CSV文件名
OUTPUT_CSV_FILE = 'l10n/translations_comparison.csv'

# 4. 记录Android和iOS翻译差异的日志文件名
CONFLICT_LOG_FILE = 'l10n/translation_conflicts.log'


# --- 脚本主逻辑 ---

def load_source_json_file(filepath: str) -> Dict[str, str]:
    """安全地加载基准JSON文件，如果失败则返回空字典并退出。"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 致命错误: 基准文件未找到 '{filepath}'。请检查路径和文件名。")
        exit() # 直接退出程序
    except json.JSONDecodeError:
        print(f"❌ 致命错误: 基准文件 '{filepath}' 不是一个有效的JSON格式。")
        exit()
    except Exception as e:
        print(f"❌ 加载基准文件 '{filepath}' 时发生未知错误: {e}")
        exit()


def main():
    """主函数，执行整个流程。"""
    print("🚀 开始生成多语言翻译对比文件...")

    # --- 步骤 1: 初始化API客户端并登录 ---
    client = ApiClient()
#     if not client.login(USER_TOKEN, VERIFY_CODE):
#         print("❌ 无法继续，请检查API和用户认证配置。")
#         return

    # --- 步骤 2: 加载基准原文文件 ---
    print(f"\n📘 正在加载基准文件: {SOURCE_JSON_FILE}")
    source_data = load_source_json_file(SOURCE_JSON_FILE)

    # --- 步骤 3: 调用API获取、合并翻译数据并记录冲突 ---
    all_translations: Dict[str, Dict[str, str]] = {}
    all_conflicts: List[Dict[str, str]] = []
    print("\n🌍 开始调用API获取并处理各语言翻译...")

    for lang_code in LANGUAGES_TO_COMPARE:
           print(f"\n--- 处理语言: {lang_code.upper()} ---")

           android_data = client.get_translation_data(lang_code, "android") or {}
           ios_data = client.get_translation_data(lang_code, "ios") or {}

           merged_data = ios_data.copy()
           lang_conflicts = []
           for key, android_value in android_data.items():
               ios_value = merged_data.get(key)

               if ios_value is not None:
                   # --- ★ 标准化处理 ★ ---
                   # 1. 移除首尾空白
                   # 2. 将所有 '\r\n' 替换为 '\n' 来统一换行符
                   normalized_android_value = android_value.strip().replace('\r\n', '\n')
                   normalized_ios_value = ios_value.strip().replace('\r\n', '\n')

                   # 用标准化之后的值进行比较
                   if normalized_android_value != normalized_ios_value:
                       # 记录冲突时，依然记录原始值，方便排查
                       lang_conflicts.append({
                           "language": lang_code, "key": key,
                           "android_value": android_value, # 记录原始值
                           "ios_value": ios_value        # 记录原始值
                       })

               # 无论是否有冲突，最终都以 Android 的原始值为准
               merged_data[key] = android_value

           all_translations[lang_code] = merged_data
           if lang_conflicts:
               print(f"  - ⚠️ 在 '{lang_code}' 语言中发现 {len(lang_conflicts)} 个内容冲突。")
               all_conflicts.extend(lang_conflicts)
           else:
               print(f"  - ✅ '{lang_code}' 语言已合并，无内容冲突。")

    # --- 后续步骤 (写入日志, 排序, 生成CSV) 与之前版本完全相同 ---

    # 步骤 4: 写入冲突日志文件
    if all_conflicts:
        print(f"\n📝 正在将 {len(all_conflicts)} 条冲突写入日志: {CONFLICT_LOG_FILE}")
        with open(CONFLICT_LOG_FILE, 'w', encoding='utf-8') as f:
            f.write(f"翻译冲突日志 - 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            for conflict in all_conflicts:
                f.write(f"语言: {conflict['language']}, Key: {conflict['key']}\n")
                f.write(f"  Android: {conflict['android_value']}\n")
                f.write(f"  iOS    : {conflict['ios_value']}\n" + "-"*20 + "\n")
        print("✅ 冲突日志写入成功。")

    # 步骤 5: 准备并排序CSV数据
    print("\n📊 正在准备并排序CSV数据...")
    all_keys = sorted(source_data.keys())
    csv_data_with_score: List[Tuple[int, List[str]]] = []
    source_header = os.path.splitext(os.path.basename(SOURCE_JSON_FILE))[0]
    header = ['key', source_header] + LANGUAGES_TO_COMPARE

    for key in all_keys:
        row = [key, source_data.get(key, '')]
        translated_count = 0
        for lang_code in LANGUAGES_TO_COMPARE:
            translated_value = all_translations.get(lang_code, {}).get(key, '')
            row.append(translated_value)
            if translated_value: translated_count += 1
        csv_data_with_score.append((translated_count, row))

    csv_data_with_score.sort(key=lambda item: item[0], reverse=True)

    # 步骤 6: 写入最终的CSV文件
    print(f"✍️ 正在将数据写入CSV文件: {OUTPUT_CSV_FILE}")
    with open(OUTPUT_CSV_FILE, 'w', encoding='utf-8-sig', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(header)
        for _, row_data in csv_data_with_score:
            writer.writerow(row_data)

    print(f"\n🎉🎉🎉 成功！排序后的对比文件已生成: {OUTPUT_CSV_FILE}")

if __name__ == '__main__':
    # 确保 l10n 目录存在
    if not os.path.exists('l10n'):
        os.makedirs('l10n')
        print("📁 已创建 'l10n' 目录。")
    main()