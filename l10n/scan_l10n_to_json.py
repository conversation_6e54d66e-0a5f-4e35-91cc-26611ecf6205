import os
import re
import json
import textwrap
from collections import defaultdict

# --- 配置 ---
PROJECT_ROOT = '.'
SCAN_DIRECTORIES = ['app/src/main/kotlin', 'app/src/main/java']
OUTPUT_FILE = 'l10n/localizable.json'
SUSPICIOUS_OUTPUT_FILE = 'l10n/suspicious_l10n.txt'


# --- 正则表达式 (分为“加法”模块) ---

# 模块1: if/else 表达式
PATTERN_IF_ELSE = re.compile(
    r'\bif\b\s*\(.*?\)\s*\{\s*"((?:\\.|[^"\\])*)"\s*\}\s*else\s*\{\s*"((?:\\.|[^"\\])*)"\s*\}\s*\.localized(?:Format)?\b',
    re.DOTALL
)
# 模块2: 简单直接调用
PATTERN_SIMPLE = re.compile(r'"((?:\\.|[^"\\])*)"\s*\.localized(?:Format)?\b')
# 模块3: 带Key的调用
PATTERN_WITH_KEY_CALL = re.compile(
    r'("""(.*?)"""|"(.*?)")'
    r'\s*(?:\.trimIndent\(\))?\s*'
    r'\.localized(?:Format)?WithKey\s*'
    r'\((.*?)\)',
    re.DOTALL
)
PATTERN_KEY_FROM_ARGS = re.compile(r'(?:key\s*=\s*)?"(.*?)"')

# 审计模块
PATTERN_SUSPICIOUS = re.compile(r'\blocalized(?:Format)?(?:WithKey)?\b')
PATTERN_STRING_LITERAL = re.compile(r'"((?:\\.|[^"\\])*)"')


def safe_process_string(s):
    """手动、安全地替换已知的转义序列。"""
    s = s.replace('\\\\', '\\')
    s = s.replace('\\n', '\n')
    s = s.replace('\\t', '\t')
    s = s.replace('\\"', '"')
    s = s.replace("\\'", "'")
    return s


def scan_project_files(root_path, directories):
    """扫描整个项目，返回成功解析的翻译 和 未能解析的可疑代码行。"""
    translations = {}
    suspicious_items = {}
    print("\n🚀 开始扫描项目代码 (使用“加法”模式)...")

    for directory in directories:
        scan_path = os.path.join(root_path, directory)
        if not os.path.exists(scan_path): continue
        print(f"🔍 正在扫描目录: {scan_path}")

        for dirpath, _, filenames in os.walk(scan_path):
            for filename in filenames:
                if filename.endswith(('.kt', '.java')):
                    file_path = os.path.join(dirpath, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        content = "".join(lines)

                        handled_line_counts = defaultdict(int)
                        def get_line_num_from_pos(pos):
                            return content.count('\n', 0, pos) + 1

                        # **核心修正: 每个解析器独立运行，做加法**

                        # 1. 运行 if/else 解析器
                        for match in PATTERN_IF_ELSE.finditer(content):
                            string1 = safe_process_string(match.group(1))
                            string2 = safe_process_string(match.group(2))
                            translations[string1] = string1
                            translations[string2] = string2
                            handled_line_counts[get_line_num_from_pos(match.end())] += 1

                        # 2. 运行简单模式解析器
                        for match in PATTERN_SIMPLE.finditer(content):
                            translations[safe_process_string(match.group(1))] = safe_process_string(match.group(1))
                            handled_line_counts[get_line_num_from_pos(match.start())] += 1

                        # 3. 运行带Key模式解析器
                        for match in PATTERN_WITH_KEY_CALL.finditer(content):
                            key_match = PATTERN_KEY_FROM_ARGS.search(match.group(4))
                            if key_match:
                                handled_line_counts[get_line_num_from_pos(match.start())] += 1
                                key = safe_process_string(key_match.group(1))
                                value = textwrap.dedent(match.group(2)).strip() if match.group(2) is not None else safe_process_string(match.group(3))
                                translations[key] = value

                        # 执行最终的可疑审计
                        for i, line in enumerate(lines):
                            suspicious_matches = PATTERN_SUSPICIOUS.findall(line)
                            if not suspicious_matches: continue

                            line_num = i + 1
                            if len(suspicious_matches) <= handled_line_counts.get(line_num, 0):
                                continue

                            stripped_line = line.strip()
                            if stripped_line.startswith(('import ', 'fun ', 'val ')): continue

                            if not PATTERN_STRING_LITERAL.search(line):
                                start_line = max(0, i - 5)
                                context_block = "".join(lines[start_line : i+1])
                                suspicious_items[line_num] = f"--- Context Block ---\n{file_path}:{start_line+1}-{line_num}:\n{context_block}"
                            else:
                                suspicious_items[line_num] = f"{file_path}:{line_num}: {stripped_line}"

                    except Exception as e:
                        print(f"❌ 读取文件时出错: {file_path}, 错误: {e}")

    final_suspicious_list = [suspicious_items[k] for k in sorted(suspicious_items.keys())]

    print(f"✅ 扫描完成。共发现 {len(translations)} 个明确的字符串。")
    if final_suspicious_list:
        print(f"⚠️  发现 {len(final_suspicious_list)} 个可疑代码点，请检查 '{SUSPICIOUS_OUTPUT_FILE}' 文件。")

    return translations, final_suspicious_list


def generate_json_file(translations, output_filepath):
    """生成最终的JSON文件"""
    if not translations:
        print("🤷 没有发现明确的翻译字符串，未生成JSON文件。")
        return
    print(f"\n✍️  正在生成JSON文件...")
    try:
        with open(output_filepath, 'w', encoding='utf-8') as f:
            json.dump(translations, f, sort_keys=True, ensure_ascii=False, indent=4)
        print(f"🎉 成功！结果已保存到: {output_filepath}")
    except Exception as e:
        print(f"❌ 写入JSON文件时出错: {output_filepath}, 错误: {e}")


def generate_suspicious_file(lines, output_filepath):
    """生成记录可疑代码的TXT文件"""
    if not lines:
        print("👍 未发现可疑代码行。")
        if os.path.exists(output_filepath):
            try:
                open(output_filepath, 'w').close()
            except IOError:
                pass
        return
    print(f"✍️  正在生成可疑代码日志...")
    try:
        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write("# --- Suspicious L10n Code --- #\n")
            f.write("# The following lines/blocks contain '.localized' calls but were not fully parsed by the script.\n")
            f.write("# Please review them manually.\n\n")
            for line in lines:
                f.write(line + '\n\n')
        print(f"🎉 成功！可疑代码已记录到: {output_filepath}")
    except Exception as e:
        print(f"❌ 写入可疑代码文件时出错: {output_filepath}, 错误: {e}")


if __name__ == '__main__':
     parsed_translations, unhandled_lines = scan_project_files(PROJECT_ROOT, SCAN_DIRECTORIES)
     generate_json_file(parsed_translations, OUTPUT_FILE)
     generate_suspicious_file(unhandled_lines, SUSPICIOUS_OUTPUT_FILE)