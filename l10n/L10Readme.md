# L10n 国际化工具集

本目录包含用于Android项目国际化（L10n）的完整工具链，主要用于扫描代码中的翻译字符串、生成基准翻译文件，并与远程翻译服务进行对比。

## 📁 文件结构

```
l10n/
├── scan_l10n_to_json.py          # 核心脚本：扫描代码生成基准翻译文件
├── generate_translation_csv.py    # 生成多语言对比CSV文件
├── api_client.py                  # API客户端，用于获取远程翻译数据
├── compare_json_keys.py           # 对比两个JSON文件的Key差异
├── localizable.json               # 基准中文翻译文件（自动生成）
├── translations_comparison.csv    # 多语言对比表格（自动生成）
├── suspicious_l10n.txt           # 可疑翻译代码记录（自动生成）
├── translation_conflicts.log     # 翻译冲突日志（自动生成）
├── key_differences.txt           # JSON Key差异报告（自动生成）
└── L10Readme.md                  # 本说明文档
```

## 🚀 主要工作流程

### 第一步：扫描代码生成基准翻译文件

使用 `scan_l10n_to_json.py` 扫描项目代码，提取所有翻译字符串并生成基准中文翻译文件：

```bash
python l10n/scan_l10n_to_json.py
```

**功能说明：**
- 扫描 `app/src/main/kotlin` 和 `app/src/main/java` 目录
- 支持多种翻译模式识别：
  - `if/else` 表达式：`if(condition) {"文本1"} else {"文本2"}.localized`
  - 简单调用：`"文本".localized` 或 `"文本".localizedFormat`
  - 带Key调用：`"文本".localizedWithKey(key = "key_name")`
- 生成 `localizable.json` 基准翻译文件
- 记录无法解析的可疑代码到 `suspicious_l10n.txt`

### 第二步：生成多语言对比CSV文件

使用 `generate_translation_csv.py` 获取远程翻译数据并生成对比表格：

```bash
python l10n/generate_translation_csv.py
```

**功能说明：**
- 基于 `localizable.json` 作为基准
- 通过API获取指定语言的翻译数据（默认：英语、日语）
- 合并Android和iOS平台的翻译数据，以Android为准
- 生成 `translations_comparison.csv` 多语言对比表格
- 记录平台间翻译冲突到 `translation_conflicts.log`
- 按翻译完成度排序输出

## 🛠️ 辅助工具

### JSON Key差异对比

使用 `compare_json_keys.py` 对比两个JSON文件的Key差异：

```bash
python l10n/compare_json_keys.py
```

**配置说明：**
- 修改脚本中的 `FILE_1` 和 `FILE_2` 变量指定要对比的文件
- 结果输出到 `key_differences.txt`

### API客户端

`api_client.py` 提供了与远程翻译服务的接口：
- 用户认证和登录
- 获取指定语言和平台的翻译数据
- 处理网络请求和JSON解析错误

## ⚙️ 配置说明

### scan_l10n_to_json.py 配置

```python
PROJECT_ROOT = '.'                                    # 项目根目录
SCAN_DIRECTORIES = ['app/src/main/kotlin', 'app/src/main/java']  # 扫描目录
OUTPUT_FILE = 'l10n/localizable.json'               # 输出文件
SUSPICIOUS_OUTPUT_FILE = 'l10n/suspicious_l10n.txt' # 可疑代码记录
```

### generate_translation_csv.py 配置

```python
USER_TOKEN = "18211110007"        # 用户认证令牌
VERIFY_CODE = "555555"            # 验证码
SOURCE_JSON_FILE = 'l10n/localizable.json'  # 基准文件
LANGUAGES_TO_COMPARE = ['en', 'ja']         # 要对比的语言列表
OUTPUT_CSV_FILE = 'l10n/translations_comparison.csv'  # 输出CSV文件
```

### api_client.py 配置

```python
TEST_SERVER = "https://fastapi.wakooclub.com"        # API服务器地址
ACCESS_KEY = "aa8396d1c7bc42bc89462a196879f9bd"       # 访问密钥
SECRET_KEY = "dd1ad7ddb8d84801ae182794732003e5"       # 签名密钥
```

## 📋 使用示例

### 完整工作流程

1. **扫描代码生成基准文件：**
   ```bash
   python l10n/scan_l10n_to_json.py
   ```

2. **生成多语言对比表格：**
   ```bash
   python l10n/generate_translation_csv.py
   ```

3. **检查可疑代码（可选）：**
   ```bash
   cat l10n/suspicious_l10n.txt
   ```

4. **查看翻译冲突（可选）：**
   ```bash
   cat l10n/translation_conflicts.log
   ```

### 对比不同版本的翻译文件

```bash
# 修改 compare_json_keys.py 中的文件路径
# FILE_1 = 'l10n/localizable.json'
# FILE_2 = 'l10n/zh-Tw.json'
python l10n/compare_json_keys.py
```

## 📊 输出文件说明

- **localizable.json**: 基准中文翻译文件，包含所有从代码中提取的翻译字符串
- **translations_comparison.csv**: 多语言对比表格，按翻译完成度排序
- **suspicious_l10n.txt**: 包含无法自动解析的翻译相关代码，需要人工检查
- **translation_conflicts.log**: 记录Android和iOS平台间的翻译差异
- **key_differences.txt**: 两个JSON文件间的Key差异报告

## 🔧 故障排除

### 常见问题

1. **扫描结果为空**
   - 检查 `SCAN_DIRECTORIES` 配置是否正确
   - 确认代码中使用了支持的翻译调用模式

2. **API请求失败**
   - 检查网络连接
   - 验证 `USER_TOKEN` 和 `VERIFY_CODE` 是否正确
   - 确认API服务器地址和密钥配置

3. **JSON解析错误**
   - 检查生成的JSON文件格式是否正确
   - 确认文件编码为UTF-8

### 调试建议

- 查看控制台输出的详细日志信息
- 检查生成的 `suspicious_l10n.txt` 文件
- 验证API返回的数据格式

## 📝 注意事项

1. 确保项目代码使用标准的翻译调用模式
2. 定期更新API配置和认证信息
3. 检查可疑代码文件，手动处理无法自动识别的翻译字符串
4. 翻译冲突需要人工审核和决策
5. 建议在代码变更后重新运行扫描流程

---

*最后更新：2025-08-12*